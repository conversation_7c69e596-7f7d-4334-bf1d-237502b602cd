package com.tzstcl.buildingrenovation;

import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.filing.tripartitePush.utils.CallAPI;
import com.tzstcl.filing.utils.XmlModel;
import com.tzstcl.filing.utils.XmlUtil;
import org.apache.poi.xwpf.usermodel.*;
import org.junit.Test;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;

public class test {
    public static void main(String[] args) throws IOException {
        XWPFDocument docxDocument = new XWPFDocument();
        //设置段落
        // TODO: 2019/12/13 抬头
        XWPFParagraph paragraphX = docxDocument.createParagraph();
        paragraphX.setAlignment(ParagraphAlignment.CENTER);//段落整体居中
        //设置文本内容
        XWPFRun runX1 = paragraphX.createRun();
        String value = "建筑起重机械设备安装告知书";
        //设置属性
        runX1.setText(value);
        runX1.setFontFamily("黑体");//字体
        runX1.setBold(true);//加粗
        runX1.setFontSize(18);//字体大小

        //设置段落
        // TODO: 2019/12/13 正文
        XWPFParagraph paragraphX2 = docxDocument.createParagraph();
        paragraphX.setAlignment(ParagraphAlignment.CENTER);//段落整体居中
        //设置文本内容
        XWPFRun runX2 = paragraphX.createRun();
        String value2 = "建筑起重机械设备安装告知书";
        //设置属性
        runX2.setText(value);
        runX2.setFontFamily("黑体");//字体
        runX2.setBold(true);//加粗
        runX2.setFontSize(18);//字体大小

        // 设置段落
        // TODO: 2019/12/13 盖章
        XWPFParagraph paragraphX3 = docxDocument.createParagraph();
        paragraphX3.setAlignment(ParagraphAlignment.CENTER);//段落整体居中
        //设置文本内容
        XWPFRun runX3 = paragraphX.createRun();
        String value3 = "建筑起重机械设备安装告知书";
        //设置属性
        runX3.setText(value);
        runX3.setBold(true);//加粗
        runX3.setFontSize(18);//字体大小

        // 设置段落
        // TODO: 2019/12/13  申请日期
        XWPFParagraph paragraphX4 = docxDocument.createParagraph();
        paragraphX.setAlignment(ParagraphAlignment.CENTER);//段落整体居中
        //设置文本内容
        XWPFRun runX4 = paragraphX.createRun();
        String value4 = "建筑起重机械设备安装告知书";
        //设置属性
        runX4.setText(value);
        runX4.setBold(true);//加粗
        runX4.setFontSize(18);//字体大小



        File importDir = new File("D:\\export\\simple.docx");
        if (importDir.exists()) {
            importDir.delete();
        }
        FileOutputStream out = new FileOutputStream("D:\\export\\simple.docx");
            docxDocument.write(out);
            out.close();
    }
    @Test
    public void test() throws ParserConfigurationException, TransformerException {
        XmlModel xmlModel = new XmlModel();
        xmlModel.setLicense_baseFlag("");
        XmlModel.Basic basic = new XmlModel().new Basic();
        basic.setSurface_baseFlag("");
//        basic.setBaseFlag(true);

        XmlModel.CatalogModel catalogModel = new XmlModel().new CatalogModel();
        catalogModel.setCatalog_baseFlag("");
        catalogModel.setCatalogId("dsa45dsad");
        catalogModel.setCatalogName("danamename");
        catalogModel.setTemplateName("Dasdwr");
        catalogModel.setTemplateId("das_id");
        xmlModel.setCatalogModel(catalogModel);

        ArrayList<XmlModel.SurfaceModel> surfaceModelArrayList = new ArrayList<>();
        XmlModel.SurfaceModel surfaceModel = new XmlModel().new SurfaceModel();
        surfaceModel.setItem_baseFlag("");
        surfaceModel.setAlias("dashjhg");
        surfaceModel.setCode("dakjhng");
        surfaceModelArrayList.add(surfaceModel);
        XmlModel.SurfaceModel surfaceModel2 = new XmlModel().new SurfaceModel();
        surfaceModel2.setItem_baseFlag("");
        surfaceModel2.setAlias("dasheqwewqe23213jhg");
        surfaceModel2.setCode("rferfsrdakjhrng");
        surfaceModelArrayList.add(surfaceModel2);
        basic.setSurfaceList(surfaceModelArrayList);
        xmlModel.setLicenseModel(basic);
        String xmlByModel = XmlUtil.createXmlByModel(xmlModel);
        System.out.println(xmlByModel);

    }
    @Test
    public void test77(){
        String accessToken = CallAPI.getToken(true).getAccessToken();
        System.out.println(accessToken);
    }
}
