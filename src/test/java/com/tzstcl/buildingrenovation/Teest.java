package com.tzstcl.buildingrenovation;

import com.tzstcl.archetype.ArchetypeApplication;
import com.tzstcl.filing.tripartitePush.utils.CallAPI;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * dd
 *
 * <AUTHOR>
 * @date 2024-09-05 16:58
 **/

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ArchetypeApplication.class)
public class Teest {
    @Test
    public void test77(){
        String accessToken = CallAPI.getToken(true).getAccessToken();
        System.out.println(accessToken);
    }
}
