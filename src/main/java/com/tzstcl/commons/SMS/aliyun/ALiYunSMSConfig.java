package com.tzstcl.commons.SMS.aliyun;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 公司：天筑科技股份有限公司
 * 作者：朱国强
 * 日期：2019年01月11日
 * 说明：
 */
@Component
public class ALiYunSMSConfig {
    private static String accessKeyId;

    private static String accessKeySecret;


    private static String signName;

    private static String captchaTemplateCode;

    private static String workflowTemplateCode;

    public static String getAccessKeyId() {
        return accessKeyId;
    }

    @Value("${aliyun.sms.accessKeyId}")
    public void setAccessKeyId(String accessKeyId) {
        ALiYunSMSConfig.accessKeyId = accessKeyId;
    }

    public static String getAccessKeySecret() {
        return accessKeySecret;
    }

    @Value("${aliyun.sms.accessKeySecret}")
    public void setAccessKeySecret(String accessKeySecret) {
        ALiYunSMSConfig.accessKeySecret = accessKeySecret;
    }

    public static String getSignName() {
        return signName;
    }

    @Value("${aliyun.sms.signName}")
    public void setSignName(String signName) {
        ALiYunSMSConfig.signName = signName;
    }

    public static String getCaptchaTemplateCode() {
        return captchaTemplateCode;
    }

    @Value("${aliyun.sms.captcha.templateCode:123456}")
    public  void setCaptchaTemplateCode(String captchaTemplateCode) {
        ALiYunSMSConfig.captchaTemplateCode = captchaTemplateCode;
    }

    public static String getWorkflowTemplateCode() {
        return workflowTemplateCode;
    }

    @Value("${aliyun.sms.workflow.templateCode:abc}")
    public  void setWorkflowTemplateCode(String workflowTemplateCode) {
        ALiYunSMSConfig.workflowTemplateCode = workflowTemplateCode;
    }
}
