package com.tzstcl.commons.SMS.aliyun;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.*;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 公司：天筑科技股份有限公司
 * 作者：朱国强
 * 日期：2019年01月11日
 * 说明：阿里大鱼短信发送工具类
 */
@Component
public class ALiYunSMSUtil {

    //产品名称:云通信短信API产品,开发者无需替换
    static final String product = "Dysmsapi";
    //产品域名,开发者无需替换
    static final String domain = "dysmsapi.aliyuncs.com";

    // 此处需要替换成开发者自己的AK(在阿里云访问控制台寻找)
//    static final String accessKeyId = "LTAIoPxxckusOc5w";
//    static final String accessKeySecret = "mIR58MT8APyiWpCZop5rAUUwoy7eg3";
    //模板code 现只用一个模板 之后多个模板可拆
//    static final String templateCode = "SMS_136394968";
    //签名名称
//    static final String signName = "河南丹枫信息科技有限公司";

    /**
     * 短信验证码发送
     *
     * @param phoneNo 手机号码
     * @param code    验证码
     * @return true 发送成功 false 发送失败
     */
    public static boolean sendSms(String phoneNo, String code, String templateCode) {
        boolean returnFlag = false;
        //可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");
        try {
            //初始化acsClient,暂不支持region化
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", ALiYunSMSConfig.getAccessKeyId(), ALiYunSMSConfig.getAccessKeySecret());
            DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", product, domain);
            IAcsClient acsClient = new DefaultAcsClient(profile);

            //组装请求对象-具体描述见控制台-文档部分内容
            SendSmsRequest request = new SendSmsRequest();
            //必填:待发送手机号
            request.setPhoneNumbers(phoneNo);
            //必填:短信签名-可在短信控制台中找到
            request.setSignName(ALiYunSMSConfig.getSignName());
            //必填:短信模板-可在短信控制台中找到
            request.setTemplateCode(templateCode);
            //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
            request.setTemplateParam("{ \"code\":\"" + code + "\"}");

            //选填-上行短信扩展码(无特殊需求用户请忽略此字段)
            //request.setSmsUpExtendCode("90997");

            //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
            //        request.setOutId("yourOutId");

            //hint 此处可能会抛出异常，注意catch
            SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
            if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode().equals("OK")) {
                returnFlag = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return returnFlag;
    }

    /**
     * 短信批量发送
     *
     * @param PhoneNumberJson   手机号码jjson串"[\"1500000000\",\"1500000001\"]"
     * @param signNameJson      短信签名,JSON格式 [“云通信”,”云通信”]
     * @param templateCode      短信模板ID
     * @param templateParamJson 模板参数 json "[{\"name\":\"Tom\", \"code\":\"123\"},{\"name\":\"Jack\", \"code\":\"456\"}]"
     * @return true 发送成功 false 发送失败
     */
    public static boolean sendSms(String PhoneNumberJson, String signNameJson, String templateCode, String templateParamJson) {
        boolean returnFlag = false;
        try {
            //设置超时时间-可自行调整
            System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
            System.setProperty("sun.net.client.defaultReadTimeout", "10000");
            //初始化ascClient需要的几个参数
            //            final String product = "Dysmsapi";//短信API产品名称（短信产品名固定，无需修改）
            //            final String domain = "dysmsapi.aliyuncs.com";//短信API产品域名（接口地址固定，无需修改）
            //替换成你的AK
            //            final String accessKeyId = "yourAccessKeyId";//你的accessKeyId,参考本文档步骤2
            //            final String accessKeySecret = "yourAccessKeySecret";//你的accessKeySecret，参考本文档步骤2
            //初始化ascClient,暂时不支持多region（请勿修改）
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", ALiYunSMSConfig.getAccessKeyId(),
                    ALiYunSMSConfig.getAccessKeySecret());
            DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", product, domain);
            IAcsClient acsClient = new DefaultAcsClient(profile);
            //组装请求对象
            SendBatchSmsRequest request = new SendBatchSmsRequest();
            //使用post提交
            request.setMethod(MethodType.POST);
            //必填:待发送手机号。支持JSON格式的批量调用，批量上限为100个手机号码,批量调用相对于单条调用及时性稍有延迟,验证码类型的短信推荐使用单条调用的方式
            request.setPhoneNumberJson(PhoneNumberJson);
            //必填:短信签名-支持不同的号码发送不同的短信签名
            request.setSignNameJson(signNameJson);
            //必填:短信模板-可在短信控制台中找到
            request.setTemplateCode(templateCode);
            //必填:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
            //友情提示:如果JSON中需要带换行符,请参照标准的JSON协议对换行符的要求,比如短信内容中包含\r\n的情况在JSON中需要表示成\\r\\n,否则会导致JSON在服务端解析失败
            request.setTemplateParamJson(templateParamJson);
            //可选-上行短信扩展码(扩展码字段控制在7位或以下，无特殊需求用户请忽略此字段)
            //request.setSmsUpExtendCodeJson("[\"90997\",\"90998\"]");
            //请求失败这里会抛ClientException异常
            SendBatchSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
            System.out.println("短信发送完成");
            System.out.println(sendSmsResponse.getMessage()+sendSmsResponse.getCode());
            if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode().equals("OK")) {
                returnFlag = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return returnFlag;
    }

    /**
     * 短信状态查询
     *
     * @param phoneNumber 手机号码
     * @param bizId       流水号
     * @return
     * @throws ClientException
     */
    public static QuerySendDetailsResponse querySendDetails(String phoneNumber, String bizId, Long currentPage, Long pageSize) throws ClientException {

        //可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");

        //初始化acsClient,暂不支持region化
        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", ALiYunSMSConfig.getAccessKeyId(), ALiYunSMSConfig.getAccessKeySecret());
        DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", product, domain);
        IAcsClient acsClient = new DefaultAcsClient(profile);

        //组装请求对象
        QuerySendDetailsRequest request = new QuerySendDetailsRequest();
        //必填-号码
        request.setPhoneNumber(phoneNumber);
        //可选-流水号
        request.setBizId(bizId);
        //必填-发送日期 支持30天内记录查询，格式yyyyMMdd
        SimpleDateFormat ft = new SimpleDateFormat("yyyyMMdd");
        request.setSendDate(ft.format(new Date()));
        //必填-页大小
        request.setPageSize(pageSize);
        //必填-当前页码从1开始计数
        request.setCurrentPage(currentPage);

        //hint 此处可能会抛出异常，注意catch
        QuerySendDetailsResponse querySendDetailsResponse = acsClient.getAcsResponse(request);

        return querySendDetailsResponse;
    }


    public static void main(String[] args) throws ClientException {

        sendSms("15670621817", "3256", "SMS_136394968");
    }
}
