package com.tzstcl.commons.utils;

import com.google.gson.Gson;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.*;


public class ResponseUtils {
	private static final Logger log = LoggerFactory.getLogger(HttpUtils.class);

	/**
	 *<ul>
	 * <li>作者： 岳林辉</li>
	 * <li>日期：2017年12月6日 下午4:05:20</li>
	 * <li>功能：向前端响应普通文本数据</li>
	 * <li>流程：</li>
	 * @param content
	 * @param response
	 *</ul>
	 */
	public static void response(String content, HttpServletResponse response) {
		try {
			response.setHeader("Content-type", "application/json;charset=UTF-8");
			response.setCharacterEncoding("UTF-8");
			response.getWriter().write(content);
			response.getWriter().flush();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			log.error("ResponseUtils.response", e);
		}
	}
	
	/**
	 *<ul>
	 * <li>作者： 岳林辉</li>
	 * <li>日期：2017年12月6日 下午4:04:39</li>
	 * <li>功能：向请求端响应json 字符串</li>
	 * <li>流程：</li>
	 * @param obj
	 * @param response
	 *</ul>
	 */
	public static void responseJson(Object obj, HttpServletResponse response) {
		try {
			response.setHeader("Content-type", "application/json;charset=UTF-8");
			response.setCharacterEncoding("UTF-8");
			response.getWriter().write(new Gson().toJson(obj));
			response.getWriter().flush();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			log.error(e.getMessage());
		}
	}

	/**
	 *<ul>
	 * <li>作者： 岳林辉</li>
	 * <li>日期：2017年12月6日 下午4:01:02</li>
	 * <li>功能：向请求端响应文件信息流</li>
	 * <li>流程：</li>
	 * @param file  响应的文件对象
	 * @param fileName 
	 * @param response
	 *</ul>
	 */
	public static void response(File file, String fileName, HttpServletResponse response) {
		try {
			response.setContentType("application/octet-stream");
			response.setHeader("Content-disposition",
					"attachment; filename=" + new String((fileName).getBytes("utf-8"), "ISO8859-1"));
			IOUtils.copy(new FileInputStream(file), response.getOutputStream());
			
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
			log.error(e.getMessage());
		} catch (FileNotFoundException e) {
			// TODO Auto-generated catch block
			log.error(e.getMessage());
		} catch (IOException e) {
			// TODO Auto-generated catch block
			log.error(e.getMessage());
		}
	}
}
