package com.tzstcl.commons.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

/**
* @Date: 2022/5/19 16:07
* @Description: create appId and appSecret
*/
public class AppUtils {

    private final static String[] chars = new String[]{"a", "b", "c", "d", "e", "f",
            "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s",
            "t", "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5",
            "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I",
            "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V",
            "W", "X", "Y", "Z"};


    public static String getAppId() {
        StringBuffer shortBuffer = new StringBuffer();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < 8; i++) {
            String str = uuid.substring(i * 4, i * 4 + 4);
            int x = Integer.parseInt(str, 16);
            shortBuffer.append(chars[x % 0x3E]);
        }
        return shortBuffer.toString();
    }

    /**

     算法： sha256(appid+uuid) 生成AppSecret
     */
    public static String getAppSecret(String appId) {
        try {
            StringBuffer sb = new StringBuffer();
            String uuid = UUID.randomUUID().toString();

            sb.append(appId).append(uuid);

            String str = sb.toString();
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(str.getBytes());
            byte[] digest = md.digest();

            StringBuffer hexstr = new StringBuffer();
            String shaHex = "";
            for (int i = 0; i < digest.length; i++) {
                shaHex = Integer.toHexString(digest[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexstr.append(0);
                }
                hexstr.append(shaHex);
            }
            return hexstr.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return appId;
    }

    public static void main(String[] args) {
        //String appId = getAppId();
        //String appSecret = getAppSecret(appId);
        //System.out.println("appId: "+appId);
        //System.out.println("appSecret: "+appSecret);

       // SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        //String timestamp = sdf.format(new Date());
        String guid = UUID.randomUUID().toString().replace("-","");
        System.out.println(guid);


        //hIVNq06l
        //535fde32e4f9f8ced29065951ff5a6f83d229917fc2f8f875df3e9d0a724a551
        //20220520144146
        //dda305809be84d7c88820277524bb840
        //22b9b17f53a398ae1e058bd3ce288d00e51d6e6ed08b4ebc394832862c7d2ca5
    }
}
