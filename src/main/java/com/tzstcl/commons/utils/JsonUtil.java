package com.tzstcl.commons.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

import java.io.InputStream;

/**
 * 公司：天筑科技股份有限公司
 * 作者：朱国强
 * 日期：2019年01月08日
 * 说明：
 */

public class JsonUtil {

    /**
     * <P>解析JSon字符串. 失败时返回 NULL .</P>
     *
     * @param <T>
     * @param json  Json字符串
     * @param clazz
     * @return
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (json == null) {
            return null;
        }
        try {
            return JSON.parseObject(json, clazz);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * <P>解析JSon字符串. 失败时返回 NULL .</P>
     *
     * @param <T>
     * @param json  Json字符串
     * @param clazz
     * @return
     */
    public static <T> T fromJson(InputStream json, Class<T> clazz) {
        if (json == null) {
            return null;
        }
        try {
            return JSON.parseObject(json, clazz);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * <P>解析JSon字符串. 失败时返回 NULL .</P>
     *
     * @param <T>
     * @param json Json字符串
     * @param type
     * @return
     */
    public static <T> T fromJson(String json, TypeReference<T> type) {
        if (json == null) {
            return null;
        }
        try {
            return JSON.parseObject(json, type);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
