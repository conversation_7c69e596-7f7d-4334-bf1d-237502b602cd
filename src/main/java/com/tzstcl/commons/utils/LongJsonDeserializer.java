package com.tzstcl.commons.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;

/**
 * 公司：天筑科技股份有限公司
 * 作者：朱国强
 * 日期：2019年05月14日
 * 说明：
 */
public class LongJsonDeserializer extends JsonDeserializer<Long> {
    @Override
    public Long deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
        String value = jsonParser.getText();
        try {
            return StringUtils.isBlank( value)  ? null : Long.parseLong(value);
        } catch (NumberFormatException e) {
//            logger.error("解析长整形错误", e);
            return null;
        }
    }
}
