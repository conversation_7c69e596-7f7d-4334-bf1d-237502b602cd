package com.tzstcl.filing.publicInquiry;

import com.github.pagehelper.PageInfo;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling;
import com.tzstcl.filing.equipmentFiling.service.FilingEquipmentFilingService;
import com.tzstcl.filing.equipmentUse.model.FilingEquipmentUseRegistration;
import com.tzstcl.filing.equipmentUse.service.FilingEquipmentUseRegistrationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


@Controller
@RequestMapping("/checkMessage")
public class PublicInquiryCtrl extends BaseCtrl {
    @Autowired
    private FilingEquipmentFilingService filingEquipmentFilingService;
    @Autowired
    private FilingEquipmentUseRegistrationService filingEquipmentUseRegistrationService;

    @RequestMapping("/filingInquiry_bf")
    public String filingInquiry() {
        return "admin/filing/equipmentFiling/filingEquipmentFilingList";
    }

    @RequestMapping("/registrationInquiry_bf")
    public String registrationInquiry() {
        return "admin/filing/equipmentUse/filingEquipmentUseRegistrationList";
    }
    @RequestMapping("/installationInquiry_bf")
    public String installInquiry() {
        return "admin/filing/installation/equipmentInstallationFormList";
    }

    @RequestMapping("/filingList")
    @ResponseBody
    public PageInfo<FilingEquipmentFiling> filingList(String manufacturingLicenseNumber,String equipmentType, Boolean cancel) {
        FilingEquipmentFiling filingEquipmentFiling = new FilingEquipmentFiling();
        filingEquipmentFiling.setEquipmentType(equipmentType);
        filingEquipmentFiling.setManufacturingLicenseNumber(manufacturingLicenseNumber);
        if (cancel==null) {
            return filingEquipmentFilingService.selectPage(filingEquipmentFiling);
        }if (cancel) {
            filingEquipmentFiling.setCancellationStatus(1);
        } else {
            filingEquipmentFiling.setCancellationStatus(0);
        }
        return filingEquipmentFilingService.selectPage(filingEquipmentFiling);
    }

    @RequestMapping("/useList")
    @ResponseBody
    public PageInfo<FilingEquipmentUseRegistration> useList(String equipmentType,String deviceFilingCode,  Boolean cancel) {
        FilingEquipmentUseRegistration filingEquipmentUseRegistration = new FilingEquipmentUseRegistration();
        filingEquipmentUseRegistration.setDeviceFilingCode(deviceFilingCode);
        filingEquipmentUseRegistration.setEquipmentType(equipmentType);
        if (cancel==null){
            return filingEquipmentUseRegistrationService.selectPage(filingEquipmentUseRegistration);
        }
        if (cancel) {
            filingEquipmentUseRegistration.setCancellationStatus(1);
        } else {
            filingEquipmentUseRegistration.setCancellationStatus(0);
        }
        return filingEquipmentUseRegistrationService.selectPage(filingEquipmentUseRegistration);
    }


}
