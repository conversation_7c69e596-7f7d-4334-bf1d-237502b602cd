package com.tzstcl.filing.equipmentUseCancellation.service;

import com.tzstcl.base.service.BaseService;
import com.tzstcl.filing.equipmentUseCancellation.model.FilingEquipmentUseCancellation;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：xuchengjie
 * 日期：2019年11月11日
 * 说明：设备使用注销表Servic
 */
public interface FilingEquipmentUseCancellationService extends BaseService<FilingEquipmentUseCancellation> {

    /**
    *
    * 批量增加设备使用注销表
    * @param filingEquipmentUseCancellationList
    * <AUTHOR>
    * @date 2019年11月11日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<FilingEquipmentUseCancellation> filingEquipmentUseCancellationList);

    Integer cancel(FilingEquipmentUseCancellation filingEquipmentUseCancellation);

    List<FilingEquipmentUseCancellation> selectListByAreaCode(List<Long> areaList, String auditStatus);

    List<FilingEquipmentUseCancellation> selectListByCondition(String year, List<Long> areaList);

    List<FilingEquipmentUseCancellation> selectListGroupByFilingCode(String year, List<Long> areaList);

    FilingEquipmentUseCancellation selectByDeviceFilingCode(String deviceFilingCode);
}
