package com.tzstcl.filing.equipmentUseCancellation.service.impl;

import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.filing.equipmentFiling.mapper.FilingEquipmentFilingMapper;
import com.tzstcl.filing.equipmentUse.mapper.FilingEquipmentUseRegistrationMapper;
import com.tzstcl.filing.equipmentUse.model.FilingEquipmentUseRegistration;
import com.tzstcl.filing.equipmentUseCancellation.service.FilingEquipmentUseCancellationService;
import com.tzstcl.filing.equipmentUseCancellation.model.FilingEquipmentUseCancellation;
import com.tzstcl.filing.equipmentUseCancellation.mapper.FilingEquipmentUseCancellationMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：xuchengjie
 * 日期：2019年11月11日
 * 说明：设备使用注销表ServiceImpl
 */
@Service
public class FilingEquipmentUseCancellationServiceImpl extends BaseServiceImpl<FilingEquipmentUseCancellationMapper, FilingEquipmentUseCancellation> implements FilingEquipmentUseCancellationService {


    @Resource
    private FilingEquipmentUseRegistrationMapper filingEquipmentUseRegistrationMapper;

    /**
     * 批量增加设备使用注销表
     *
     * @param filingEquipmentUseCancellationList
     * @return Integer 插入的记录数
     * <AUTHOR>
     * @date 2019年11月11日
     */
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<FilingEquipmentUseCancellation> filingEquipmentUseCancellationList) {
        return this.mapper.insertBatch(filingEquipmentUseCancellationList);
    }

    @Transactional(readOnly = false, rollbackFor = Exception.class)
    @Override
    public Integer cancel(FilingEquipmentUseCancellation filingEquipmentUseCancellation) {
        String code = filingEquipmentUseCancellation.getUseFilingCode();
        FilingEquipmentUseRegistration filingEquipmentUseRegistration = filingEquipmentUseRegistrationMapper.getByCode(code);
        filingEquipmentUseRegistration.setCancellationStatus(1);
        filingEquipmentUseCancellation.setUpdateTime(new Date());
        Integer flag = this.update(filingEquipmentUseCancellation);
        if (flag == 1) {
            return filingEquipmentUseRegistrationMapper.update(filingEquipmentUseRegistration);
        }
        return 0;
    }

    @Override
    public List<FilingEquipmentUseCancellation> selectListByAreaCode(List<Long> areaList, String auditStatus) {
        return mapper.selectListByAreaCode(areaList,auditStatus);
    }

    @Override
    public List<FilingEquipmentUseCancellation> selectListByCondition(String year, List<Long> areaList) {
        return mapper.selectListByCondition(year,areaList);
    }

    @Override
    public List<FilingEquipmentUseCancellation> selectListGroupByFilingCode(String year, List<Long> areaList) {
        return mapper.selectListGroupByFilingCode(year,areaList);
    }

    @Override
    public FilingEquipmentUseCancellation selectByDeviceFilingCode(String deviceFilingCode) {
        return mapper.selectByDeviceFilingCode(deviceFilingCode);
    }
}
