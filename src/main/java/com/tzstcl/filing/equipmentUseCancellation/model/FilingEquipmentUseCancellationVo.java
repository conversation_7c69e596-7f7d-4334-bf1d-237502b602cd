package com.tzstcl.filing.equipmentUseCancellation.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tzstcl.base.model.BaseModel;
import com.tzstcl.commons.utils.LongJsonDeserializer;
import com.tzstcl.commons.utils.LongJsonSerializer;
import lombok.Data;
import lombok.NonNull;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/24
 */
@Data
public class FilingEquipmentUseCancellationVo {

    private static final long serialVersionUID = 1L;
    /**
     *申请人id
     */
    private String userId;
    /**
     *使用单位
     */
    @NonNull
    private String useUnit;
    /**
     *产权单位
     */
    @NonNull
    private String propertyUnit;
    /**
     *设备名称
     */
    //@NonNull
    private String deviceName;
    /**
     *规格型号
     */
    @NonNull
    private String specificationModel;
    /**
     *设备备案编号
     */
    @NonNull
    private String deviceFilingCode;
    /**
     *工程名称
     */
    @NonNull
    private String engineeringName;
    /**
     *项目经理
     */
    @NonNull
    private String projectManager;
    /**
     *设备类别，T塔式起重机，S施工升降机，W物料提升机，Q其他
     */
    @NonNull
    private String equipmentType;
    /**
     *使用备案编号
     */
    @NonNull
    private String useFilingCode;
    /**
     *注销申请表文件路径
     */
    @NonNull
    private String cancellationApplicationPath;
    /**
     *使用备案证文件路径
     */
    @NonNull
    private String useRegistrationPath;
    /**
     *审核驳回意见
     */
    private String auditRejectReason;
    /**
     *审核状态，0未审核，1审核通过，2审核驳回
     */
    @NonNull
    private String auditStatus;
    /**
     *审核时间，yyyy-MM-dd HH:mm:ss
     */
    private Date auditTime;
    /**
     *业务ID
     */
    @NonNull
    private String businessId;
    /**
     * 工程所属地区域编码
     */
    @NonNull
    private Long engineeringAreaCode;
    /**
     *appId
     */
    private String appId;
    /**
     *appKey
     */
    private String appKey;
    /**
     * id
     */
    @JsonSerialize(using = LongJsonSerializer.class)
    @JsonDeserialize(using = LongJsonDeserializer.class)
    private Long id;

    /**
     * 创建人
     */
    //@JsonIgnore
    private String createBy;

    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    //@JsonIgnore
    private String updateBy;

    /**
     * 更新人
     */
//    @JsonIgnore
    private String updateTime;

    /**
     * 删除标记
     */
    @JsonIgnore
    private String delFlag;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 分页（当前页）
     */
    @JsonIgnore
    private Integer pageNum;

    /**
     * 分页（页面大小）
     */
    @JsonIgnore
    private Integer pageSize;
}
