package com.tzstcl.filing.equipmentUseCancellation.model;

import com.tzstcl.base.model.BaseModel;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 公司：天筑科技股份有限公司
 * 作者：xuchengjie
 * 日期：2019年11月11日
 * 说明：设备使用注销表实体类
 */
@Data
public class FilingEquipmentUseCancellation extends BaseModel<FilingEquipmentUseCancellation> implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
     *申请人id
     */
    private String userId;
    /**
     *使用单位
     */
    private String useUnit;
    /**
     *产权单位
     */
    private String propertyUnit;
    /**
     *设备名称
     */
    private String deviceName;
    /**
     *规格型号
     */
    private String specificationModel;
    /**
     *设备备案编号
     */
    private String deviceFilingCode;
    /**
     *工程名称
     */
    private String engineeringName;
    /**
     *项目经理
     */
    private String projectManager;
    /**
     *设备类别，T塔式起重机，S施工升降机，W物料提升机，Q其他
     */
    private String equipmentType;
    /**
     *使用备案编号
     */
    private String useFilingCode;
    /**
     *注销申请表文件路径
     */
    private String cancellationApplicationPath;
    /**
     *使用备案证文件路径
     */
    private String useRegistrationPath;
    /**
     *审核驳回意见
     */
    private String auditRejectReason;
    /**
     *审核状态，0未审核，1审核通过，2审核驳回
     */
    private String auditStatus;
    /**
     *审核时间，yyyy-MM-dd HH:mm:ss
     */
    private Date auditTime;
    /**
     *业务ID
     */
    private String businessId;
    /**
     * 工程所属地区域编码
     */
    private Long engineeringAreaCode;
}
