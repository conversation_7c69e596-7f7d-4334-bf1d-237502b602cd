package com.tzstcl.filing.equipmentUseCancellation.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import com.tzstcl.filing.equipmentUseCancellation.model.FilingEquipmentUseCancellation;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：xuchengjie
 * 日期：2019年11月11日
 * 说明：设备使用注销表Mapper
 */
@Mapper
public interface FilingEquipmentUseCancellationMapper extends BaseMapper<FilingEquipmentUseCancellation>  {

    /**
    *
    * 批量增加设备使用注销表
    * @param filingEquipmentUseCancellationList
    * <AUTHOR>
    * @date 2019年11月11日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<FilingEquipmentUseCancellation> filingEquipmentUseCancellationList);

    List<FilingEquipmentUseCancellation> selectListByAreaCode(List<Long> areaList, String auditStatus);

    List<FilingEquipmentUseCancellation> selectListByCondition(String year, List<Long> areaList);

    List<FilingEquipmentUseCancellation> selectListGroupByFilingCode(String year, List<Long> areaList);

    FilingEquipmentUseCancellation selectByDeviceFilingCode(String deviceFilingCode);
}
