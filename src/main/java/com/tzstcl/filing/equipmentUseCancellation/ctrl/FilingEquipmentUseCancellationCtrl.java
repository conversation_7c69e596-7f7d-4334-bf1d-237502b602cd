package com.tzstcl.filing.equipmentUseCancellation.ctrl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.SnowflakeIdWorker;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.commons.utils.UploadFileUtil;
import com.tzstcl.filing.access.model.AccessKey;
import com.tzstcl.filing.access.service.IAccessKeyService;
import com.tzstcl.filing.annotation.VerifySign;
import com.tzstcl.filing.area.mapper.AreaMapper;
import com.tzstcl.filing.area.model.Area;
import com.tzstcl.filing.area.service.AreaService;
import com.tzstcl.filing.equipmentUse.model.FilingEquipmentUseRegistration;
import com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationForm;
import com.tzstcl.filing.equipmentUseApplication.service.FilingEquipmentRegistrationFormService;
import com.tzstcl.filing.equipmentUseCancellation.model.FilingEquipmentUseCancellation;
import com.tzstcl.filing.equipmentUseCancellation.model.FilingEquipmentUseCancellationVo;
import com.tzstcl.filing.equipmentUseCancellation.service.FilingEquipmentUseCancellationService;
import com.tzstcl.filing.equipmentUser.model.FilingEquipmentUser;
import com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellation;
import com.tzstcl.filing.file.service.FilingFileService;
import com.tzstcl.framework.shiro.ShiroUtils;
import com.tzstcl.sys.user.model.SysUser;
import com.tzstcl.sys.user.service.impl.SysDictServiceImpl;
import org.apache.shiro.spring.config.ShiroAnnotationProcessorConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：xuchengjie
 * 日期：2019年11月11日
 * 说明：设备使用注销表Controller
 */
@Controller
@RequestMapping("/admin/filingEquipmentUseCancellation")
public class FilingEquipmentUseCancellationCtrl extends BaseCtrl {

    @Autowired
    private FilingEquipmentUseCancellationService filingEquipmentUseCancellationService;
    @Autowired
    private FilingFileService filingFileService;
    @Autowired
    private IAccessKeyService accessKeyService;
    @Autowired
    private SysDictServiceImpl sysDictService;
    @Autowired
    private FilingEquipmentRegistrationFormService filingEquipmentRegistrationFormService;
    @Autowired
    private AreaMapper areaMapper;
    /**
    * list页面导航
    * @return
    */
    @RequiresPermissions("useCancel:view")
    @RequestMapping("/toList")
    public String toList(Model model) {
        model.addAttribute("dict",sysDictService.findByType("city"));
        return "admin/filing/equipmentUseCancellation/filingEquipmentUseCancellationList";
    }

    /**
    * 获取查询的分页数据
    * @param filingEquipmentUseCancellation
    * @return
    */
    @RequestMapping("/list")
    @RequiresPermissions("useCancel:view")
    @ResponseBody
    public PageInfo<FilingEquipmentUseCancellation> list(FilingEquipmentUseCancellation filingEquipmentUseCancellation) {
//        SysUser sysUser = ShiroUtils.getUser();
//        if (!sysUser.getId().equals(1L)){
//            if (sysUser.getUserType().equals("0")){
//                filingEquipmentUseCancellation.setUserId(sysUser.getMobile());
//            }
//        }
        PageInfo<FilingEquipmentUseCancellation> page = filingEquipmentUseCancellationService.selectPage(filingEquipmentUseCancellation);
        List<FilingEquipmentUseCancellation> list = page.getList();
        for (FilingEquipmentUseCancellation cancellation : list) {
            if (null!=cancellation.getEngineeringAreaCode()){
                Area area = areaMapper.selectAreaById(cancellation.getEngineeringAreaCode());
                cancellation.setRemarks(area.getName());
                if (3==area.getLevel()){
                    Area area1 = areaMapper.selectAreaById(area.getParentId());
                    cancellation.setRemarks(area1.getName()+"/"+area.getName());
                }
            }
        }
        page.setList(list);
        return  page;
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"useCancel:edite","useCancel:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        SysUser user = ShiroUtils.getUser();
        model.addAttribute("user",user);
        model.addAttribute("userId", ShiroUtils.getUser().getMobile());
        model.addAttribute("id", SnowflakeIdWorker.getInstance().nextId());
        model.addAttribute("dict",sysDictService.findByType("city"));
        if(null != id){
            model.addAttribute("filingEquipmentUseCancellation" ,filingEquipmentUseCancellationService.getOne(id));
        }
        return "admin/filing/equipmentUseCancellation/filingEquipmentUseCancellationForm";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("useCancel:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentUseCancellation" ,filingEquipmentUseCancellationService.getOne(id));
            model.addAttribute("filepath",filingFileService.getFiles(id));
            model.addAttribute("dict",sysDictService.findByType("city"));
        }
        return "admin/filing/equipmentUseCancellation/filingEquipmentUseCancellationDetail";
    }

    /**
    * form页面导航 新增
    * @return
    */
    @RequiresPermissions("useCancel:add")
    @GetMapping("/add")
    public String toAdd(Model model) {
        SysUser user = ShiroUtils.getUser();
        model.addAttribute("user",user);
        model.addAttribute("dict",sysDictService.findByType("city"));
        return "admin/filing/equipmentUseCancellation/filingEquipmentUseCancellationAdd";
    }

    /**
     * 新增
     * @param filingEquipmentUseCancellation
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"useCancel:add"})
    @ResponseBody
    public AjaxResult save(@Valid FilingEquipmentUseCancellation filingEquipmentUseCancellation) {
        if (null!=filingEquipmentUseCancellation.getId()){ //判断是否为重新提交，是的话将状态改为 3重新提交
            FilingEquipmentUseCancellation cancellation = new FilingEquipmentUseCancellation();
            cancellation.setId(filingEquipmentUseCancellation.getId());
            cancellation.setAuditStatus("3");
            filingEquipmentUseCancellationService.update(cancellation);
        }
        filingEquipmentUseCancellation.setCreateTime(new Date());
        Integer i = filingEquipmentUseCancellationService.add(filingEquipmentUseCancellation);
        if (i>0){
            //需要根据备案编号将备案申请表设备注销状态改为1
            FilingEquipmentRegistrationForm registrationForm1 = filingEquipmentRegistrationFormService.getByCode(filingEquipmentUseCancellation.getUseFilingCode());
            registrationForm1.setIsUseCancel("1");
            filingEquipmentRegistrationFormService.update(registrationForm1);
        }
        return toAjax(i);
    }

    /**
     * 推送使用登记注销数据
     * @param filingEquipmentUseCancellation
     * @return
     */
    @CrossOrigin
    @RequestMapping("/addUseCancellation")
    @ResponseBody
    public AjaxResult addUseCancellation(@RequestParam("appId") String appId,
                                         @RequestParam("appKey") String appKey,
//                                         @RequestParam("auditStatus") String auditStatus,   //审核状态 0未审核，1审核通过，2驳回
//                                         @RequestParam("createTime") String createTime,   //申请日期
//                                         @RequestParam("useFilingCode") String useFilingCode, //使用备案编号
//                                         @RequestParam("equipmentType") String equipmentType, //设备类型
//                                         @RequestParam("deviceName") String deviceName, //设备名称
//                                         @RequestParam("specificationModel") String specificationModel, //规格型号
//                                         @RequestParam("deviceFilingCode") String deviceFilingCode, //设备备案编号
//                                         @RequestParam("propertyUnit") String propertyUnit, //产权单位名称
//                                         @RequestParam("useUnit") String useUnit, //使用单位名称
//                                         @RequestParam("engineeringName") String engineeringName, //工程名称
//                                         @RequestParam("cancellationApplicationPath") String cancellationApplicationPath, //注销申请表文件路径
//                                         @RequestParam("useRegistrationPath") String useRegistrationPath, //使用备案证文件路径
//                                         @RequestParam("projectManager") String projectManager, //项目经理
//                                         @RequestParam("businessId") String businessId, //业务ID
                                         @RequestBody FilingEquipmentUseCancellationVo useCancellationVo

    ) {
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key){
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())){
            return AjaxResult.error("appKey输入有误");
        }
        //判断该数据是否重复
        FilingEquipmentUseCancellation useCancellation1 = new FilingEquipmentUseCancellation();
        useCancellation1.setDeviceFilingCode(useCancellationVo.getDeviceFilingCode());
        List<FilingEquipmentUseCancellation> list = filingEquipmentUseCancellationService.selectList(useCancellation1);
        if (list.size()>0){
            return AjaxResult.error("ID为"+useCancellationVo.getBusinessId()+"或者编号为"+useCancellationVo.getDeviceFilingCode()+"的数据已存在,请勿重复推送");
        }

        FilingEquipmentUseCancellation useCancellation = new FilingEquipmentUseCancellation();
        try {
            useCancellation.setCreateTime(new SimpleDateFormat("yyyy-MM-dd").parse(useCancellationVo.getCreateTime()));
        }catch (Exception e){
            e.printStackTrace();
            return AjaxResult.error("请输入正确的日期格式");
        }
        useCancellation.setBusinessId(useCancellationVo.getBusinessId());
        useCancellation.setAuditStatus(useCancellationVo.getAuditStatus());
        useCancellation.setUseFilingCode(useCancellationVo.getUseFilingCode());
        useCancellation.setEquipmentType(useCancellationVo.getEquipmentType());
        if (null!=useCancellationVo.getDeviceName()){
            useCancellation.setDeviceName(useCancellationVo.getDeviceName());
        }
        useCancellation.setSpecificationModel(useCancellationVo.getSpecificationModel());
        useCancellation.setDeviceFilingCode(useCancellationVo.getDeviceFilingCode());
        useCancellation.setPropertyUnit(useCancellationVo.getPropertyUnit());
        useCancellation.setUseUnit(useCancellationVo.getUseUnit());
        useCancellation.setEngineeringName(useCancellationVo.getEngineeringName());
        useCancellation.setCancellationApplicationPath(useCancellationVo.getCancellationApplicationPath());
        useCancellation.setUseRegistrationPath(useCancellationVo.getUseRegistrationPath());
        useCancellation.setProjectManager(useCancellationVo.getProjectManager());
        useCancellation.setEngineeringAreaCode(useCancellationVo.getEngineeringAreaCode());

        useCancellation.setUserId(appId);
        useCancellation.setCreateBy(appId);
        useCancellation.setCreateTime(new Date());
        Long id  = SnowflakeIdWorker.getInstance().nextId();
        useCancellation.setId(id);
        int flag = filingEquipmentUseCancellationService.add(useCancellation);
        if (flag ==1){
            return success("使用登记注销推送成功",useCancellation);
        }else{
            return error("ID为"+useCancellationVo.getBusinessId()+"的使用登记注销推送失败");
        }
    }

    /**
     * 更新使用登记注销数据
     * @param filingEquipmentUseCancellation
     * @return
     */
    @CrossOrigin
    @RequestMapping("/updateUseCancellation")
    @ResponseBody
    public AjaxResult updateUseCancellation(@RequestParam("appId") String appId,
                                         @RequestParam("appKey") String appKey,
                                        // @RequestParam("auditStatus") String auditStatus,   //审核状态 0未审核，1审核通过，2驳回
                                         @RequestBody FilingEquipmentUseCancellationVo useCancellationVo

    ) {
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key){
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())){
            return AjaxResult.error("appKey输入有误");
        }
        //判断该数据是否重复
        FilingEquipmentUseCancellation useCancellation1 = new FilingEquipmentUseCancellation();
        useCancellation1.setDeviceFilingCode(useCancellationVo.getDeviceFilingCode());
        List<FilingEquipmentUseCancellation> list = filingEquipmentUseCancellationService.selectList(useCancellation1);
        if (list.size()==0){
            return AjaxResult.error("ID为"+useCancellationVo.getBusinessId()+"的数据不存在,请先推送数据");
        }

        FilingEquipmentUseCancellation useCancellation = new FilingEquipmentUseCancellation();
        try {
            useCancellation.setCreateTime(new SimpleDateFormat("yyyy-MM-dd").parse(useCancellationVo.getCreateTime()));
        }catch (Exception e){
            e.printStackTrace();
            return AjaxResult.error("请输入正确的日期格式");
        }
        useCancellation.setBusinessId(useCancellationVo.getBusinessId());
        useCancellation.setAuditStatus(useCancellationVo.getAuditStatus());
        useCancellation.setUseFilingCode(useCancellationVo.getUseFilingCode());
        useCancellation.setEquipmentType(useCancellationVo.getEquipmentType());
        if (null!=useCancellationVo.getDeviceName()){
            useCancellation.setDeviceName(useCancellationVo.getDeviceName());
        }
        useCancellation.setSpecificationModel(useCancellationVo.getSpecificationModel());
        useCancellation.setDeviceFilingCode(useCancellationVo.getDeviceFilingCode());
        useCancellation.setPropertyUnit(useCancellationVo.getPropertyUnit());
        useCancellation.setUseUnit(useCancellationVo.getUseUnit());
        useCancellation.setEngineeringName(useCancellationVo.getEngineeringName());
        useCancellation.setCancellationApplicationPath(useCancellationVo.getCancellationApplicationPath());
        useCancellation.setUseRegistrationPath(useCancellationVo.getUseRegistrationPath());
        useCancellation.setProjectManager(useCancellationVo.getProjectManager());
        useCancellation.setEngineeringAreaCode(useCancellationVo.getEngineeringAreaCode());

        useCancellation.setId(list.get(0).getId());
        useCancellation.setUpdateBy(appId);
        useCancellation.setUpdateTime(new Date());
        int flag = filingEquipmentUseCancellationService.update(useCancellation);
        if (flag ==1){
            return success("使用登记注销修改成功",useCancellation);
        }else{
            return error("ID为"+useCancellationVo.getBusinessId()+"的使用登记注销修改失败");
        }
    }

    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("useCancel:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentUseCancellation" ,filingEquipmentUseCancellationService.getOne(id));
            model.addAttribute("filepath",filingFileService.getFiles(id));
            model.addAttribute("dict",sysDictService.findByType("city"));
        }
        return "admin/filing/equipmentUseCancellation/filingEquipmentUseCancellationEdit";
    }

//    /**
//    * 更新
//    * @param filingEquipmentUseCancellation
//    * @return
//    */
//    @PostMapping("/update")
//    @RequiresPermissions("useCancel:edit")
//    @ResponseBody
//    public AjaxResult update(@Valid FilingEquipmentUseCancellation filingEquipmentUseCancellation) {
//        return toAjax(filingEquipmentUseCancellationService.update(filingEquipmentUseCancellation));
//    }

    /**
     * 更新,再次提交申请的时候，审核状态重置为：0审核，驳回原因清空；
     * @param filingEquipmentUseCancellation
     * @return
     */
    @PostMapping("/update")
    @RequiresPermissions("useCancel:edit")
    @ResponseBody
    public AjaxResult update(@Valid FilingEquipmentUseCancellation filingEquipmentUseCancellation) {
        filingEquipmentUseCancellation.setAuditStatus("0");
        filingEquipmentUseCancellation.setAuditRejectReason("");
        return toAjax(filingEquipmentUseCancellationService.update(filingEquipmentUseCancellation));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("useCancel:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        String[] split = ids.split(",");
        for (String s : split) {
            FilingEquipmentUseCancellation cancellation = filingEquipmentUseCancellationService.getOne(Long.parseLong(s));
            //需要根据备案编号将备案申请表设备注销状态改为1
            FilingEquipmentRegistrationForm registrationForm1 = filingEquipmentRegistrationFormService.getByCode(cancellation.getUseFilingCode());
            registrationForm1.setIsUseCancel("0");
            filingEquipmentRegistrationFormService.update(registrationForm1);
        }
        return toAjax(filingEquipmentUseCancellationService.deleteBatchIds(ids));
    }

    /**
     * 审核详情
     * @return
     */
    @RequiresPermissions("useCancel:view")
    @GetMapping("/audit/{id}")
    public String audit(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentUseCancellation" ,filingEquipmentUseCancellationService.getOne(id));
            model.addAttribute("filepath",filingFileService.getFiles(id));
            model.addAttribute("dict",sysDictService.findByType("city"));
        }
        return "admin/filing/equipmentUseCancellation/filingUseCancellationAudit";

    }
    /**
     *  审核通过
     * @param
     * @return
     */
    @PostMapping("/auditpass")
    @RequiresPermissions("useCancel:examine")
    @ResponseBody
    public AjaxResult auditpass(Long id) {
        FilingEquipmentUseCancellation filingEquipmentUseCancellation  = filingEquipmentUseCancellationService.getOne(id);
        if (!filingEquipmentUseCancellation.getAuditStatus().equals("0")){
            return error("已审核的申请，不可重复审核");
        }
        filingEquipmentUseCancellation.setAuditStatus("1");
        filingEquipmentUseCancellation.setAuditRejectReason("");
        filingEquipmentUseCancellation.setAuditTime(new Date());
        return toAjax(filingEquipmentUseCancellationService.cancel(filingEquipmentUseCancellation));
    }
    /**
     *  审核驳回
     * @param
     * @return
     */
    @PostMapping("/auditupdate")
    @RequiresPermissions("useCancel:examine")
    @ResponseBody
    public AjaxResult auditupdate(@Valid FilingEquipmentUseCancellation filingEquipmentUseCancellation) {
        filingEquipmentUseCancellation.setAuditStatus("2");
        filingEquipmentUseCancellation.setAuditTime(new Date());
        Integer i = filingEquipmentUseCancellationService.update(filingEquipmentUseCancellation);
        if (i>0){
            FilingEquipmentUseCancellation cancellation = filingEquipmentUseCancellationService.getOne(filingEquipmentUseCancellation.getId());
            //需要根据备案编号将备案申请表设备注销状态改为1
            FilingEquipmentRegistrationForm registrationForm1 = filingEquipmentRegistrationFormService.getByCode(cancellation.getUseFilingCode());
            registrationForm1.setIsUseCancel("0");
            filingEquipmentRegistrationFormService.update(registrationForm1);
        }
        return toAjax(i);
    }


    /**
     *  文件上传
     */
    @RequestMapping("/uploadFile")
    @RequiresPermissions(value={"useCancel:edite","useCancel:add"},logical=Logical.OR)
    @ResponseBody
    public AjaxResult uploadFile(MultipartFile uploadFile) throws IOException {
        String type = "filing";
        Map<String, Object> result = UploadFileUtil.saveFile(uploadFile, type);
        return AjaxResult.success("操作成功", result);
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("useCancel:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",filingEquipmentUseCancellationService.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param filingEquipmentUseCancellation
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"useCancel:add", "useCancel:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(FilingEquipmentUseCancellation filingEquipmentUseCancellation) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = filingEquipmentUseCancellation.getId();
        filingEquipmentUseCancellation.setId(null);
        List<FilingEquipmentUseCancellation> filingEquipmentUseCancellationList = filingEquipmentUseCancellationService.selectList(filingEquipmentUseCancellation);
        if(StringUtils.isNotEmpty(filingEquipmentUseCancellationList)){
            if(filingEquipmentUseCancellationList.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(filingEquipmentUseCancellationList.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 设备使用登记注销申请推送接口
     *
     * @return
     */
    @PostMapping("/syncUseCancel")
    @ResponseBody
    @VerifySign
    public AjaxResult syncFilingUse(
            @RequestParam("appId") String appId,
            @RequestParam("appKey") String appKey,
            @RequestBody() String  data
    ){
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key){
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())){
            return AjaxResult.error("appKey输入有误");
        }
        String str1 = data.replaceAll("\\[","");
        String str2 = str1.replaceAll("\\]","");
        JSONObject jsonObject = JSONObject.parseObject(str2);
        FilingEquipmentUseCancellation useCancellation = jsonObject.toJavaObject(FilingEquipmentUseCancellation.class);
        //判断该数据是否重复
        FilingEquipmentUseCancellation application = filingEquipmentUseCancellationService.selectByDeviceFilingCode(useCancellation.getDeviceFilingCode());
        if (null==application){
            //新增
            if (null==useCancellation.getUserId()){
                useCancellation.setUserId(appId);
            }
            useCancellation.setCreateBy(appId);
            useCancellation.setCreateTime(new Date());
            Long id  = SnowflakeIdWorker.getInstance().nextId();
            useCancellation.setId(id);
            int flag = filingEquipmentUseCancellationService.add(useCancellation);
            if (flag ==1){
                return success("使用登记注销申请推送成功",useCancellation);
            }else{
                return error("设备"+useCancellation.getDeviceFilingCode()+"使用登记注销申请推送失败");
            }
        }else {
            //修改
            useCancellation.setUpdateTime(new Date());
            useCancellation.setUpdateBy(appId);
            useCancellation.setId(application.getId());
            int flag = filingEquipmentUseCancellationService.update(useCancellation);
            if (flag ==1){
                return success("使用登记注销申请修改成功",useCancellation);
            }else{
                return error("设备"+useCancellation.getDeviceFilingCode()+"使用登记注销申请修改失败");
            }
        }
    }

}
