package com.tzstcl.filing.tripartitePush.utils;

import com.alibaba.fastjson.JSONObject;
import com.epoint.sso.client.util.AssertionUtil;
import com.epoint.sso.client.util.HttpClientUtil;
import com.epoint.sso.client.validation.Assertion;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 根据得到的Token,调用接口,上传数据
 *
 * <AUTHOR>
 * @date 2022-12-16 9:46
 **/
@Component("CallAPI")
public class CallAPI {

    //客户端账号标识,请修正为实际值

    static String AppKey;
    //客户端账号密码,请修正为实际值

    static String AppSecret;
    //认证平台地址，具备token接口，只需要配置到认证平台应用系统名即可,请修正为实际值
    static String buildingAppKey;
    //客户端账号密码,请修正为实际值

    static String buildingAppSecret;
    static String SSOUrl ;
    @Value("${nation.key}")
    public void setAppKey(String appKey){
        CallAPI.AppKey=appKey;
    }
    @Value("${nation.Secret}")
    public  void setAppSecret(String appSecret) {
        CallAPI.AppSecret = appSecret;
    }
    @Value("${national.quality.afety.push.SSOUrl}")
    public void setSSOUrl(String SSOUrl) {
        CallAPI.SSOUrl = SSOUrl;
    }
    @Value("${nation.building.key}")
    public void setBuildingAppKey(String buildingAppKey) {
        CallAPI.buildingAppKey = buildingAppKey;
    }
    @Value("${nation.building.Secret}")
    public  void setBuildingAppSecret(String buildingAppSecret) {
        CallAPI.buildingAppSecret = buildingAppSecret;
    }

    //调用凭证，考虑凭证需要远程调用接口获取，需要静态化，不用每次调用重新获取
    static Assertion TokenInfo;

    //生成调用凭证
    public  static Assertion getToken(boolean refresh) {
        //如果凭证已经生成且不需要强制更新的情况下，则只需要获取原有凭证即可，避免不必要的远程调用
        if (true) {
            //利用SDK方法获取调用凭证，传入客户端账号、密码和认证平台地址，采用的是客户端认证模式
            TokenInfo = AssertionUtil.getAssertionStateless(AppKey, AppSecret, SSOUrl);
        }
        return TokenInfo;
    }
    //生成调用凭证
    public  static Assertion getToken2(boolean refresh) {
        //如果凭证已经生成且不需要强制更新的情况下，则只需要获取原有凭证即可，避免不必要的远程调用
        if (true) {
            //利用SDK方法获取调用凭证，传入客户端账号、密码和认证平台地址，采用的是客户端认证模式
            TokenInfo = AssertionUtil.getAssertionStateless(buildingAppKey, buildingAppSecret, SSOUrl);
        }
        return TokenInfo;
    }

    //调用最终API方法
    public static String APIInvoke(String Url, String Access_Token, Map<String, Object> Params, Object jsonObjectList) {
        //实际需要调用的API的地址，将调用凭证token作为参数传入
        Url = Url + "?access_token=" + Access_Token;
        //通过SDK的HttpClientUtil调用API，获得返回值
        HashMap<String, String>  headers  = new HashMap<>();
        headers.put("Authorization", "Bearer " + Access_Token);
      /*  return (String) HttpClientUtil.post(Url,headers, Params, 2);*/
        JSONObject jsonObject =new JSONObject();
        jsonObject.put("AcceptData",jsonObjectList);
       return HttpClientUtil.postBody(Url,headers,jsonObject.toJSONString());
    }
    //调用最终API方法
    public static String APIInvokeJson(String Url, String Access_Token, Map<String, Object> Params, JSONObject jsonObjectList) {
        //实际需要调用的API的地址，将调用凭证token作为参数传入
        Url = Url + "?access_token=" + Access_Token;
        //通过SDK的HttpClientUtil调用API，获得返回值
        HashMap<String, String>  headers  = new HashMap<>();
        headers.put("Authorization", "Bearer " + Access_Token);
        /*  return (String) HttpClientUtil.post(Url,headers, Params, 2);*/
        //JSONObject jsonObject =new JSONObject();
        //jsonObject.put("AcceptData",jsonObjectList);
        return HttpClientUtil.postBody(Url,headers,jsonObjectList.toJSONString());
    }

   /* public static void main(String[] args) throws Exception {
        //实际API地址,请修正为实际值
        String Url = "http://219.142.101.192/share/epoint-web-dzzz/rest/axzcheckrest/Axz_Check";
        //调用API的参数,请修正为实际值
        Map<String, Object> Params = new HashMap<>();
        Params.put("provinceNum", "410000");
        Params.put("creditCode", "91410102MA447D0M1P");
        //考虑调用凭证缓存化，一定时间后调用凭证肯定会过期，当凭证过期时，引入重试机制，即凭证需要强制更新，并重新调用API接口
        //本示例重试最多3次，3次失败则不再继续重试。
        for (int i = 0; i < 3; i++) {
            String Access_Token = getToken(i > 0).getAccessToken();
            System.out.println(Access_Token);
            String result = APIInvoke(Url, Access_Token, Params, null);
            System.out.println(result);
            if("1".equals(result)){
                break;
            }
        }
    }*/

}
