package com.tzstcl.filing.tripartitePush.api;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonArray;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.JsonUtil;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.filing.annotation.VerifySign;

import com.tzstcl.filing.collectionCert.service.ZjtQzjService;
import com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling;
import com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellation;
import com.tzstcl.filing.equipmentfilingcancellation.service.FilingEquipmentFilingCancellationService;
import com.tzstcl.filing.pushData.model.PushData;
import com.tzstcl.filing.pushData.service.PushDataService;
import com.tzstcl.filing.pushPersonData.service.PushPersonDataService;
import com.tzstcl.filing.updatePushData.service.UpdatePushDataService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 三方推送
 *
 * <AUTHOR>
 * @date 2024-05-20 16:14
 **/
@Controller
@RequestMapping("/tripartite")
public class tripartitePushCtr extends BaseCtrl {
    @Autowired
    private PushDataService pushDataService;
    @Autowired
    private UpdatePushDataService updatePushDataService;
    @Autowired
    private ZjtQzjService zjtQzjService;
    @Autowired
    private PushPersonDataService pushPersonDataService;
    @Autowired
    private FilingEquipmentFilingCancellationService filingEquipmentFilingCancellationService;
   /* *
     * 起重机证书校验
     * @param pushDataList
     * @return*/
    @RequestMapping("/craneCertVerify")
    @ResponseBody
    @VerifySign
    public AjaxResult craneCertVerify(@RequestParam("appId") String appid, @RequestParam("appKey") String appkey, @RequestBody() String  pushDataList) {

        List<JSONObject> list = JSONObject.parseArray(pushDataList,JSONObject.class);
        if(list!=null&&list.size()>0){
            if (list.size() <= 10) {
                return pushDataService.craneCertVerify(list,pushDataList);

            }
            else {
                return AjaxResult.error("不能超过10条");
            }
        }else {
            return AjaxResult.error("不能为空");
        }
    }
    /**
     * [起重机赋码]
     * @param appid
     * @param appkey
     * @param pushDataList
     * @return com.tzstcl.base.model.AjaxResult
     * <AUTHOR>
     * @date 2024/5/24 15:33
     */
    @RequestMapping("/encodeCert")
    @ResponseBody
    @VerifySign
    public AjaxResult encodeCert(@RequestParam("appId") String appid, @RequestParam("appKey") String appkey, @RequestBody() String  pushDataList){
        List<JSONObject> list = JSONObject.parseArray(pushDataList,JSONObject.class);
        if(list!=null&&list.size()>0){
            if (list.size() <= 10) {
                return pushDataService.encodeCert(list,pushDataList);

            }
            else {
                return AjaxResult.error("不能超过10条");
            }
        }else {
            return AjaxResult.error("不能为空");
        }
    }
    /**
     * [起重机变更]
     * @param appid
     * @param appkey
     * @param updatePushDataList
     * @return com.tzstcl.base.model.AjaxResult
     * <AUTHOR>
     * @date 2024/5/24 15:33
     */

    @RequestMapping("/changeCert")
    @ResponseBody
    @VerifySign
    public AjaxResult changeCert(@RequestParam("appId") String appid, @RequestParam("appKey") String appkey, @RequestBody() String  updatePushDataList){
        List<JSONObject> list = JSONObject.parseArray(updatePushDataList,JSONObject.class);
        if(list!=null&&list.size()>0){
            if (list.size() <= 10) {
                return updatePushDataService.changeCert(list,updatePushDataList);

            }
            else {
                return AjaxResult.error("不能超过10条");
            }
        }else {
            return AjaxResult.error("不能为空");
        }
    }
    /**
     * [起重机归集]
     * @param appid
     * @param appkey
     * @param zjtQZJDataList
     * @return com.tzstcl.base.model.AjaxResult
     * <AUTHOR>
     * @date 2024/5/24 15:34
     */
    @RequestMapping("/collectionCert")
    @ResponseBody
    @VerifySign
    public AjaxResult collectionCert(@RequestParam("appId") String appid, @RequestParam("appKey") String appkey, @RequestBody() String  zjtQZJDataList){
        List<JSONObject> list = JSONObject.parseArray(zjtQZJDataList,JSONObject.class);
        if(list!=null&&list.size()>0){
            if (list.size() <= 10) {
                return zjtQzjService.collectionCert(list,zjtQZJDataList);

            }
            else {
                return AjaxResult.error("不能超过10条");
            }
        }else {
            return AjaxResult.error("不能为空");
        }
    }
    /**
     * [起重机相关人员]
     * @param appid
     * @param appkey
     * @param personDataList
     * @return com.tzstcl.base.model.AjaxResult
     * <AUTHOR>
     * @date 2024/5/24 15:34
     */
    @RequestMapping("/pushPersonData")
    @ResponseBody
    @VerifySign
    public AjaxResult pushPersonData(@RequestParam("appId") String appid, @RequestParam("appKey") String appkey, @RequestBody() String  personDataList){
        List<JSONObject> list = JSONObject.parseArray(personDataList,JSONObject.class);
        if(list!=null&&list.size()>0){
            if (list.size() <= 10) {
                return pushPersonDataService.pushPersonData(list,personDataList);

            }
            else {
                return AjaxResult.error("不能超过10条");
            }
        }else {
            return AjaxResult.error("不能为空");
        }
    }
    /**
     * [起重机设备备案编号是否已经注销]
     * @param appid
     * @param appkey
     * @param zjtQZJDataList
     * @return com.tzstcl.base.model.AjaxResult
     * <AUTHOR>
     * @date 2024/5/24 15:34
     */
    @RequestMapping("/inspectionRecord")
    @ResponseBody
    @VerifySign
    public AjaxResult inspectionRecord(@RequestParam("appId") String appid, @RequestParam("appKey") String appkey, @RequestBody() String  data){
        JSONObject jsonObject = JSONObject.parseObject(data);
        if(jsonObject==null){
            AjaxResult ajaxResult=new AjaxResult();
            return ajaxResult.put("code",-1).put("ReturnMsg","数据不能为空");
        }else {
            if(StringUtils.isNotEmpty(jsonObject.getString("filingCode"))){
               FilingEquipmentFilingCancellation filingEquipmentFilingCancellation = filingEquipmentFilingCancellationService.getOneByCode(jsonObject.getString("filingCode"));
                if(filingEquipmentFilingCancellation==null){
                    //判断是否已经被
                    AjaxResult ajaxResult=new AjaxResult();
                    return ajaxResult.put("code",0).put("ReturnMsg","校验通过");
                }else {
                    AjaxResult ajaxResult=new AjaxResult();
                    return ajaxResult.put("code",-2).put("ReturnMsg","校验不通过，该设备备案编号已被注销").put("ReturnData",filingEquipmentFilingCancellation);
                }
            }else{
                AjaxResult ajaxResult=new AjaxResult();
                return ajaxResult.put("code",-1).put("ReturnMsg","上传数据缺少参数：filingCode 信息");

            }


        }
    }
}
