package com.tzstcl.filing.tripartitePush.api;

import com.alibaba.fastjson.JSONObject;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.SnowflakeIdWorker;
import com.tzstcl.filing.annotation.VerifySign;
import com.tzstcl.filing.area.model.Area;
import com.tzstcl.filing.buildingCraneCert.service.BuildingCraneCertService;
import com.tzstcl.filing.collectionCert.service.ZjtQzjService;
import com.tzstcl.filing.constructionMachineryCert.service.ConstructionMachineryCertService;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.List;

/**
 * 备案三方接口
 *
 * <AUTHOR>
 * @date 2025-03-20 10:44
 **/
@Slf4j
@Controller
@RequestMapping("/FilingTripartite")
public class FilingTripartiteApi {
    @Autowired
    private BuildingCraneCertService buildingCraneCertService;
    @Autowired
    private ZjtQzjService zjtQzjService;
    @Autowired
    private ConstructionMachineryCertService constructionMachineryCertService;
    /* *
     * 设备唯一编号查验接口
     * @param pushDataList
     * @return*/
    @RequestMapping("/craneCertVerify")
    @ResponseBody
    @VerifySign
    public AjaxResult craneCertVerify(@RequestParam("appId") String appid, @RequestParam("appKey") String appkey, @RequestBody() String buildingData) {

        JSONObject jsonObject = JSONObject.parseObject(buildingData,JSONObject.class);
        log.info("jsonObject:{}", jsonObject);
        if(jsonObject!=null){
                return buildingCraneCertService.craneCertVerify(jsonObject,buildingData);
            }
            else {
                return AjaxResult.error("不能为空");
            }

    }

    /* *
     * 业务办理初步校验接口
     * @param pushDataList
     * @return*/
    @RequestMapping("/preBussinessVerify")
    @ResponseBody
    @VerifySign
    public AjaxResult preBussinessVerify(@RequestParam("appId") String appid, @RequestParam("appKey") String appkey, @RequestBody() String buildingDataList) {

        List<JSONObject> list = JSONObject.parseArray(buildingDataList,JSONObject.class);
        if(list!=null&&list.size()>0){
            if (list.size() <= 10) {
                return buildingCraneCertService.preBussinessVerify(list,buildingDataList);

            }
            else {
                return AjaxResult.error("不能超过10条");
            }
        }else {
            return AjaxResult.error("不能为空");
        }
    }
    /* *
     * 业务办理初步校验接口
     * @param pushDataList
     * @return*/
    @RequestMapping("/bussinessVerify")
    @ResponseBody
    @VerifySign
    public AjaxResult bussinessVerify(@RequestParam("appId") String appid, @RequestParam("appKey") String appkey, @RequestBody() String buildingDataList) {

        List<JSONObject> list = JSONObject.parseArray(buildingDataList,JSONObject.class);
        if(list!=null&&list.size()>0){
            if (list.size() <= 10) {
                return buildingCraneCertService.bussinessVerify(list,buildingDataList);

            }
            else {
                return AjaxResult.error("不能超过10条");
            }
        }else {
            return AjaxResult.error("不能为空");
        }
    }

    /* *
     * 业务办理赋码
     * @param pushDataList
     * @return*/
    @RequestMapping("/assignCode")
    @ResponseBody
    @VerifySign
    public AjaxResult assignCode(@RequestParam("appId") String appid, @RequestParam("appKey") String appkey, @RequestBody() String buildingDataList) {

        List<JSONObject> list = JSONObject.parseArray(buildingDataList,JSONObject.class);
        if(list!=null && !list.isEmpty()){
            if (list.size() <= 10) {

                return buildingCraneCertService.assignCode(list, buildingDataList,appid);

            }
            else {
                return AjaxResult.error("不能超过10条");
            }
        }else {
            return AjaxResult.error("不能为空");
        }
    }
    /* *
     * 归集接口
     * @param pushDataList
     * @return*/
    @RequestMapping("/collectData")
    @ResponseBody
    @VerifySign
    public AjaxResult collectData(@RequestParam("appId") String appid, @RequestParam("appKey") String appkey, @RequestBody() String  zjtQZJDataList) {

        List<JSONObject> list = JSONObject.parseArray(zjtQZJDataList,JSONObject.class);
        if(list!=null&&list.size()>0){
            if (list.size() <= 10) {
                return zjtQzjService.collectData(list,zjtQZJDataList);

            }
            else {
                return AjaxResult.error("不能超过10条");
            }
        }else {
            return AjaxResult.error("不能为空");
        }
    }
    /* *
     * 5.7业务信息变更接口
     * @param pushDataList
     * @return*/
    @RequestMapping("/changeInfo")
    @ResponseBody
    @VerifySign
    public AjaxResult changeInfo(@RequestParam("appId") String appid, @RequestParam("appKey") String appkey, @RequestBody() String constructionDataList) {

        List<JSONObject> list = JSONObject.parseArray(constructionDataList,JSONObject.class);
        if(list!=null&&list.size()>0){
            if (list.size() <= 10) {
                return constructionMachineryCertService.changeInfo(list,constructionDataList);

            }
            else {
                return AjaxResult.error("不能超过10条");
            }
        }else {
            return AjaxResult.error("不能为空");
        }
    }
    /* *
     * 5.7业务信息变更接口
     * @param pushDataList
     * @return*/
    @RequestMapping("/changeStatus")
    @ResponseBody
    @VerifySign
    public AjaxResult changeStatus(@RequestParam("appId") String appid, @RequestParam("appKey") String appkey, @RequestBody() String constructionDataList) {

        List<JSONObject> list = JSONObject.parseArray(constructionDataList,JSONObject.class);
        if(list!=null&&list.size()>0){
            if (list.size() <= 10) {
                return constructionMachineryCertService.changeStatus(list,constructionDataList);

            }
            else {
                return AjaxResult.error("不能超过10条");
            }
        }else {
            return AjaxResult.error("不能为空");
        }
    }
    /* *
     * 5.9业务数据更正接口
     * @param pushDataList
     * @return*/
    @RequestMapping("/amend")
    @ResponseBody
    @VerifySign
    public AjaxResult amend(@RequestParam("appId") String appid, @RequestParam("appKey") String appkey, @RequestBody() String constructionDataList) {

        List<JSONObject> list = JSONObject.parseArray(constructionDataList,JSONObject.class);
        if(list!=null&&list.size()>0){
            if (list.size() <= 10) {
                return constructionMachineryCertService.amend(list,constructionDataList);

            }
            else {
                return AjaxResult.error("不能超过10条");
            }
        }else {
            return AjaxResult.error("不能为空");
        }
    }

}
