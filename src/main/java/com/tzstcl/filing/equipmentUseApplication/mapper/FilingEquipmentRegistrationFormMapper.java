package com.tzstcl.filing.equipmentUseApplication.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling;
import com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationForm;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：xuchengjie
 * 日期：2019年11月09日
 * 说明：设备使用登记表Mapper
 */
@Mapper
public interface FilingEquipmentRegistrationFormMapper extends BaseMapper<FilingEquipmentRegistrationForm>  {

    /**
    *
    * 批量增加设备使用登记表
    * @param filingEquipmentRegistrationFormList
    * <AUTHOR>
    * @date 2019年11月09日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<FilingEquipmentRegistrationForm> filingEquipmentRegistrationFormList);
    FilingEquipmentRegistrationForm getByCode(String useFilingCode);

    List<FilingEquipmentRegistrationForm> selectListByAreaCode(List<Long> areaList, String auditStatus);

    List<FilingEquipmentRegistrationForm> selectListByCondition(String year, List<Long> areaList);

    FilingEquipmentRegistrationForm getOneByFilingCode(String deviceFilingCode);

    List<FilingEquipmentRegistrationForm> selectListGroupByFilingCode(String year, List<Long> areaList);
}
