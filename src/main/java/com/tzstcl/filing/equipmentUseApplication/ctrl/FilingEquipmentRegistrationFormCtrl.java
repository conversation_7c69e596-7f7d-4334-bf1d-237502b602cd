package com.tzstcl.filing.equipmentUseApplication.ctrl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.tzstcl.archetype.detail.model.SysNewsDetails;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.service.BaseService;
import com.tzstcl.commons.utils.SnowflakeIdWorker;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.commons.utils.UploadFileUtil;
import com.tzstcl.filing.access.model.AccessKey;
import com.tzstcl.filing.access.service.IAccessKeyService;
import com.tzstcl.filing.annotation.VerifySign;
import com.tzstcl.filing.area.mapper.AreaMapper;
import com.tzstcl.filing.area.model.Area;
import com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling;
import com.tzstcl.filing.equipmentFiling.service.FilingEquipmentFilingService;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication;
import com.tzstcl.filing.equipmentUse.model.FilingEquipmentUseRegistration;
import com.tzstcl.filing.equipmentUse.service.FilingEquipmentUseRegistrationService;
import com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationForm;
import com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationFormVo;
import com.tzstcl.filing.equipmentUseApplication.service.FilingEquipmentRegistrationFormService;
import com.tzstcl.filing.equipmentUseCancellation.model.FilingEquipmentUseCancellation;
import com.tzstcl.filing.equipmentUseCancellation.service.FilingEquipmentUseCancellationService;
import com.tzstcl.filing.equipmentUser.model.FilingEquipmentUser;
import com.tzstcl.filing.equipmentUser.service.FilingEquipmentUserService;
import com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellation;
import com.tzstcl.filing.file.service.FilingFileService;
import com.tzstcl.framework.shiro.ShiroUtils;
import com.tzstcl.sys.user.model.SysDict;
import com.tzstcl.sys.user.model.SysUser;
import com.tzstcl.sys.user.service.impl.SysDictServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：xuchengjie
 * 日期：2019年11月09日
 * 说明：设备使用登记表Controller
 */
@Slf4j
@Controller
@RequestMapping("/admin/filingEquipmentRegistrationForm")
public class FilingEquipmentRegistrationFormCtrl extends BaseCtrl {

    @Autowired
    private FilingEquipmentRegistrationFormService filingEquipmentRegistrationFormService;
    @Autowired
    private FilingEquipmentUseRegistrationService filingEquipmentUseRegistrationService;
    @Autowired
    private FilingFileService filingFileService;
    @Autowired
    private SysDictServiceImpl sysDictService;
    @Autowired
    private FilingEquipmentUserService filingEquipmentUserService;
    @Autowired
    private FilingEquipmentUseCancellationService filingEquipmentUseCancellationService;
    @Autowired
    private IAccessKeyService accessKeyService;
    @Autowired
    private AreaMapper areaMapper;

    /**
     * list页面导航
     *
     * @return
     */
    @RequiresPermissions("use:view")
    @RequestMapping("/toList")
    public String toList(Model model) {
        model.addAttribute("dict",sysDictService.findByType("city"));
        return "admin/filing/equipmentUseApplication/filingEquipmentRegistrationFormList";
    }

    /**
     * 获取查询的分页数据
     *
     * @param filingEquipmentRegistrationForm
     * @return
     */
    @RequestMapping("/list")
    @RequiresPermissions("use:view")
    @ResponseBody
    public PageInfo<FilingEquipmentRegistrationForm> list(FilingEquipmentRegistrationForm filingEquipmentRegistrationForm) {
//        SysUser sysUser = ShiroUtils.getUser();
//        if (!sysUser.getId().equals(1L)){
//            if (sysUser.getUserType().equals("0")){
//                filingEquipmentRegistrationForm.setUserId(sysUser.getMobile());
//            }
//        }
        return filingEquipmentRegistrationFormService.selectPage(filingEquipmentRegistrationForm);
    }


    /**
     * 获取查询的分页数据
     *
     * @param filingEquipmentRegistrationForm
     * @return
     */
    @RequestMapping("/seeList")
    @ResponseBody
    public PageInfo<FilingEquipmentRegistrationForm> seeList(FilingEquipmentRegistrationForm filingEquipmentRegistrationForm) {
        return filingEquipmentRegistrationFormService.selectPage(filingEquipmentRegistrationForm);
    }

    /**
     * form页面导航
     *
     * @return
     */
    @RequiresPermissions(value = {"use:edit", "use:add"}, logical = Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value = "id", required = false) Long id, Model model) {
        SysUser user = ShiroUtils.getUser();
        model.addAttribute("user",user);
        model.addAttribute("userId", ShiroUtils.getUser().getMobile());
        model.addAttribute("id", SnowflakeIdWorker.getInstance().nextId());
        model.addAttribute("dict",sysDictService.findByType("city"));
        if (null != id) {
            model.addAttribute("filingEquipmentRegistrationForm", filingEquipmentRegistrationFormService.getOne(id));
        }
        return "admin/filing/equipmentUseApplication/filingEquipmentRegistrationFormForm";
    }

    /**
     * form页面导航 详情
     *
     * @return
     */
    @RequiresPermissions("use:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id") Long id, Model model) {
        model.addAttribute("files",filingFileService.getFiles(id));
        model.addAttribute("dict",sysDictService.findByType("city"));
        addAttr(id,model);
        if (null != id) {
            FilingEquipmentRegistrationForm filingEquipmentRegistrationForm = filingEquipmentRegistrationFormService.getOne(id);
            if(StringUtils.isNotEmpty(filingEquipmentRegistrationForm.getRemarks())){
                SysDict city = sysDictService.getByValue("city", filingEquipmentRegistrationForm.getRemarks());
                if (null!=city){
                    filingEquipmentRegistrationForm.setRemarks(city.getDictLabel());
                }
            }
            model.addAttribute("filingEquipmentRegistrationForm", filingEquipmentRegistrationForm);
            model.addAttribute("files",filingFileService.getFiles(id));
        }
        return "admin/filing/equipmentUseApplication/filingEquipmentRegistrationFormDetail";
    }

    /**
     * 公示查询
     * */

    @GetMapping("/seeDetail/{id}")
    public String seeDetail(@PathVariable("id") Long id, Model model) {
        model.addAttribute("files",filingFileService.getFiles(id));
        model.addAttribute("dict",sysDictService.findByType("city"));
        addAttr(id,model);
        if (null != id) {
            model.addAttribute("filingEquipmentRegistrationForm", filingEquipmentRegistrationFormService.getOne(id));
        }
        return "admin/filing/equipmentUseApplication/filingEquipmentRegistrationFormDetail";
    }


    /**
     * form页面导航 新增
     *
     * @return
     */
    @RequiresPermissions("use:add")
    @GetMapping("/add")
    public String toAdd(Model model) {
        SysUser user = ShiroUtils.getUser();
        model.addAttribute("user",user);
        model.addAttribute("dict",sysDictService.findByType("city"));
        return "admin/filing/equipmentUseApplication/filingEquipmentRegistrationFormAdd";
    }

    /**
     * 新增
     *
     * @param filingEquipmentRegistrationForm
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value = {"use:add"})
    @ResponseBody
    public AjaxResult save(@Valid FilingEquipmentRegistrationForm filingEquipmentRegistrationForm) {
        FilingEquipmentRegistrationForm registrationForm =filingEquipmentRegistrationFormService.getOneByFilingCode(filingEquipmentRegistrationForm.getDeviceFilingCode());
        if (null!=registrationForm){
            return error("该设备已申请登记，请勿重复添加！");
        }
        filingEquipmentRegistrationForm.setApplicationDate(new Date());
        filingEquipmentRegistrationForm.setAuditStatus("0");
        return toAjax(filingEquipmentRegistrationFormService.insert(filingEquipmentRegistrationForm));
    }

    /**
     * 推送使用登记申请数据
     *
     * @param filingEquipmentRegistrationForm
     * @return
     */
    @CrossOrigin
    @PostMapping("/addRegistrationForm")
    @ResponseBody
    public AjaxResult addRegistrationForm(@RequestParam("appId") String appId,
                                          @RequestParam("appKey") String appKey,
//                                          @RequestParam("auditStatus") String auditStatus,   //审核状态 0未审核，1审核通过，2驳回
//                                          @RequestParam("applicationDate") String applicationDate,   //申请日期
//                                          @RequestParam("equipmentType") String equipmentType, //设备类型
//                                          @RequestParam("deviceName") String deviceName, //设备名称
//                                          @RequestParam("specificationModel") String specificationModel, //规格型号
//                                          @RequestParam("factoryTime") String factoryTime, //出厂日期
//                                          @RequestParam("factoryNumber") String factoryNumber, //出厂编号
//                                          @RequestParam("deviceFilingCode") String deviceFilingCode, //设备备案编号
//                                          @RequestParam("testingDate") String testingDate, //检测日期
//                                          @RequestParam("installationAddress") String installationAddress, //安装位置
//                                          @RequestParam(name = "firstInstallationHeight",required = false) String firstInstallationHeight, //首次安装高度，单位：米
//                                          @RequestParam(name = "finalUseHeight",required = false) String finalUseHeight, //最终使用高度，单位：米
//                                          @RequestParam(name = "userName",required = false) String userName, //设备使用人员信息-姓名
//                                          @RequestParam(name = "userType",required = false) String userType, //设备使用人员信息-工种
//                                          @RequestParam(name = "userCode",required = false) String userCode, //设备使用人员信息-上岗证号
//                                          @RequestParam("propertyUnit") String propertyUnit, //产权单位名称
//                                          @RequestParam("propertyUnitCode") String propertyUnitCode, //产权单位社会统一信用代码
//                                          @RequestParam("useUnit") String useUnit, //使用单位名称
//                                          @RequestParam("useUnitCode") String useUnitCode, //使用单位统一社会信用代码
//                                          @RequestParam("userContact") String userContact, //使用项目负责人
//                                          @RequestParam("userContactCard") String userContactCard, //使用单位项目负责人身份证号码
//                                          @RequestParam("produceUnit") String produceUnit, //制造单位
//                                          @RequestParam("produceUnitCode") String produceUnitCode, //制造单位统一社会信用代码
//                                          @RequestParam("upkeepUnit") String upkeepUnit, //维保单位
//                                          @RequestParam("upkeepUnitCode") String upkeepUnitCode, //维保单位统一社会信用代码
//                                          @RequestParam("installationUnit") String installationUnit, //安装单位
//                                          @RequestParam("installationUnitCode") String installationUnitCode, //安装单位统一社会信用代码
//                                          @RequestParam(name = "installationUnitQualificationCertificateNumber",required = false) String installationUnitQualificationCertificateNumber, //安装单位资质证号
//                                          @RequestParam(name = "installationUnitLicenseSafetyPermitNumber",required = false) String installationUnitLicenseSafetyPermitNumber, //安装单位安全许可证号
//                                          @RequestParam(name = "siteInstallationLeader",required = false) String siteInstallationLeader, //现场安装负责人
//                                          @RequestParam(name = "siteInstallationLeaderCard",required = false) String siteInstallationLeaderCard, //现场安装负责人身份证号
//                                          @RequestParam("testingUnit") String testingUnit, //检验检测单位
//                                          @RequestParam("testingUnitCode") String testingUnitCode, //检测单位统一社会信用代码
//                                          @RequestParam(name = "testingLeadere",required = false) String testingLeadere, //检验检测负责人
//                                          @RequestParam(name = "testingLeadereCard",required = false) String testingLeadereCard, //检验检测负责人身份证号
//                                          @RequestParam("engineeringName") String engineeringName, //工程名称
//                                          @RequestParam("remarks") String remarks, //工程项目地址
//                                          @RequestParam("isBuildLicense") String isBuildLicense, //是否办理施工许可证 0:否 1:是  默认为0
//                                          @RequestParam(name = "buildLicenseCode",required = false) String buildLicenseCode, //建筑工程施工许可证编号
//                                          @RequestParam("installationStartTime") String installationStartTime, //安装开始时间
//                                          @RequestParam("installationEndTime") String installationEndTime, //安装终止时间
//                                          @RequestParam(name = "projectManager",required = false) String projectManager, //项目经理
//                                          @RequestParam("businessId") String businessId, //业务ID
//                                          @RequestParam("filingApplicationFormPath") String filingApplicationFormPath, //使用备案申请表路径
//                                          @RequestParam("equipmentRecordCertificate") String equipmentRecordCertificate, //起重机备案证文件路径
//                                          @RequestParam("equipmentLeasingContract") String equipmentLeasingContract, //设备租赁合同文件路径
//                                          @RequestParam("inspectionReportPath") String inspectionReportPath, //定期检验及安装检验检测报告文件路径
//                                          @RequestParam("installationAcceptanceReport") String installationAcceptanceReport, //安装验收报告文件路径
//                                          @RequestParam("operatorsQualificationCertificate") String operatorsQualificationCertificate, //现场特种作业人员资格证书文件路径
//                                          @RequestParam("equipmentMaintenanceManagementSystemPath") String equipmentMaintenanceManagementSystemPath, //设备保养制度文件路径
//                                          @RequestParam("safetyAccidentsEmergencyPlanPath") String safetyAccidentsEmergencyPlanPath, //安全事故应急预案文件路径
                                          @RequestBody FilingEquipmentRegistrationFormVo registrationFormVo
    ) {
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key){
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())){
            return AjaxResult.error("appKey输入有误");
        }
        //判断该数据是否重复
        FilingEquipmentRegistrationForm registrationForm1 = new FilingEquipmentRegistrationForm();
        registrationForm1.setDeviceFilingCode(registrationFormVo.getDeviceFilingCode());
        List<FilingEquipmentRegistrationForm> list = filingEquipmentRegistrationFormService.selectList(registrationForm1);
        if (list.size()>0){
            return AjaxResult.error("ID为"+registrationFormVo.getBusinessId()+"的数据已存在,请勿重复推送");
        }

        FilingEquipmentRegistrationForm registrationForm = new FilingEquipmentRegistrationForm();

        if (null!=registrationFormVo.getDeviceName()){
            registrationForm.setDeviceName(registrationFormVo.getDeviceName());
        }
        registrationForm.setBusinessId(registrationFormVo.getBusinessId());
        registrationForm.setAuditStatus(registrationFormVo.getAuditStatus());
        registrationForm.setEquipmentType(registrationFormVo.getEquipmentType());
        registrationForm.setSpecificationModel(registrationFormVo.getSpecificationModel());
        registrationForm.setFactoryTime(registrationFormVo.getFactoryTime());
        registrationForm.setFactoryNumber(registrationFormVo.getFactoryNumber());
        registrationForm.setDeviceFilingCode(registrationFormVo.getDeviceFilingCode());
        try {
            registrationForm.setTestingDate(new SimpleDateFormat("yyyy-MM-dd").parse(registrationFormVo.getTestingDate()));
            registrationForm.setInstallationStartTime(new SimpleDateFormat("yyyy-MM-dd").parse(registrationFormVo.getInstallationStartTime()));
            registrationForm.setInstallationEndTime(new SimpleDateFormat("yyyy-MM-dd").parse(registrationFormVo.getInstallationEndTime()));
            registrationForm.setApplicationDate(new SimpleDateFormat("yyyy-MM-dd").parse(registrationFormVo.getApplicationDate()));
        }catch (Exception e){
            e.printStackTrace();
            return AjaxResult.error("请输入正确的日期格式");
        }
        registrationForm.setInstallationAddress(registrationFormVo.getInstallationAddress());
        if (null!=registrationFormVo.getFirstInstallationHeight()){
            registrationForm.setFirstInstallationHeight(registrationFormVo.getFirstInstallationHeight());
        }
        if (null!=registrationFormVo.getFinalUseHeight()){
            registrationForm.setFinalUseHeight(registrationFormVo.getFinalUseHeight());
        }
        //这三个参数没用
        if (null!=registrationFormVo.getUserName()){
        }
        if (null!=registrationFormVo.getUserType()){
        }
        if (null!=registrationFormVo.getUserCode()){
        }
        registrationForm.setPropertyUnit(registrationFormVo.getPropertyUnit());
        registrationForm.setProduceUnitCode(registrationFormVo.getProduceUnitCode());
        registrationForm.setUseUnit(registrationFormVo.getUseUnit());
        registrationForm.setUseUnitCode(registrationFormVo.getUseUnitCode());
        registrationForm.setUserContact(registrationFormVo.getUserContact());
        registrationForm.setUserContactCard(registrationFormVo.getUserContactCard());
        registrationForm.setProduceUnit(registrationFormVo.getProduceUnit());
        registrationForm.setProduceUnitCode(registrationFormVo.getProduceUnitCode());
        registrationForm.setUpkeepUnit(registrationFormVo.getUpkeepUnit());
        registrationForm.setUpkeepUnitCode(registrationFormVo.getUpkeepUnitCode());
        registrationForm.setInstallationUnit(registrationFormVo.getInstallationUnit());
        registrationForm.setInstallationUnitCode(registrationFormVo.getInstallationUnitCode());
        if (null!=registrationFormVo.getInstallationUnitQualificationCertificateNumber()){
            registrationForm.setInstallationUnitQualificationCertificateNumber(registrationFormVo.getInstallationUnitQualificationCertificateNumber());
        }
        if (null!=registrationFormVo.getInstallationUnitLicenseSafetyPermitNumber()){
            registrationForm.setInstallationUnitLicenseSafetyPermitNumber(registrationFormVo.getInstallationUnitLicenseSafetyPermitNumber());
        }
        if (null!=registrationFormVo.getSiteInstallationLeader()){
            registrationForm.setSiteInstallationLeader(registrationFormVo.getSiteInstallationLeader());
        }
        if (null!=registrationFormVo.getSiteInstallationLeaderCard()){
            registrationForm.setSiteInstallationLeaderCard(registrationFormVo.getSiteInstallationLeaderCard());
        }
        registrationForm.setTestingUnit(registrationFormVo.getTestingUnit());
        registrationForm.setTestingUnitCode(registrationFormVo.getTestingUnitCode());
        if (null!=registrationFormVo.getTestingLeadere()){
            registrationForm.setTestingLeadere(registrationFormVo.getTestingLeadere());
        }
        if (null!=registrationFormVo.getTestingLeadereCard()){
            registrationForm.setTestingLeadereCard(registrationFormVo.getTestingLeadereCard());
        }
        registrationForm.setEngineeringName(registrationFormVo.getEngineeringName());
        registrationForm.setEngineeringAreaCode(registrationFormVo.getEngineeringAreaCode());
        Area area = areaMapper.selectAreaById(registrationFormVo.getEngineeringAreaCode());
        registrationForm.setRemarks(area.getName());
        if (3==area.getLevel()){
            Area area1 = areaMapper.selectAreaById(area.getParentId());
            registrationForm.setRemarks(area1.getName()+"/"+area.getName());
        }
        if (null!=registrationFormVo.getProjectManager()){
            registrationForm.setProjectManager(registrationFormVo.getProjectManager());
        }
        if ("1".equals(registrationFormVo.getIsBuildLicense())){
            if ("".equals(registrationFormVo.getBuildLicenseCode())||null==registrationFormVo.getBuildLicenseCode()){
                return AjaxResult.error("请输入建筑工程施工许可证编号");
            }
        }else if (!"0".equals(registrationFormVo.getIsBuildLicense())){
            return AjaxResult.error("是否办理施工许可证格式有误,0:否 1:是");
        }
        registrationForm.setFilingApplicationFormPath(registrationFormVo.getFilingApplicationFormPath());
        registrationForm.setEquipmentRecordCertificate(registrationFormVo.getEquipmentRecordCertificate());
        registrationForm.setEquipmentMaintenanceManagementSystemPath(registrationFormVo.getEquipmentMaintenanceManagementSystemPath());
        registrationForm.setEquipmentLeasingContract(registrationFormVo.getEquipmentLeasingContract());
        registrationForm.setInstallationAcceptanceReport(registrationFormVo.getInstallationAcceptanceReport());
        registrationForm.setInspectionReportPath(registrationFormVo.getInspectionReportPath());
        registrationForm.setOperatorsQualificationCertificate(registrationFormVo.getOperatorsQualificationCertificate());
        registrationForm.setSafetyAccidentsEmergencyPlanPath(registrationFormVo.getSafetyAccidentsEmergencyPlanPath());
        if (null!=registrationFormVo.getCertId()){
            registrationForm.setCertId(registrationFormVo.getCertId());
        }
        if ("T".equals(registrationForm.getEquipmentType())){
            if (null==registrationFormVo.getArmLength()){
                return AjaxResult.error("请输入塔式起重机起重臂长度（单位：m）");
            }
            if (null==registrationFormVo.getMaxHeight()){
                return AjaxResult.error("请输入塔式起重机拟安装最大高度（单位：m）");
            }
            if (null==registrationFormVo.getMaxWeight()){
                return AjaxResult.error("请输入塔式起重机最大起重量（单位：T）");
            }
            if (null==registrationFormVo.getMaximumCapacity()){
                return AjaxResult.error("请输入塔式起重机最大幅度起重量（几倍率）（单位：T）");
            }
            registrationForm.setArmLength(registrationFormVo.getArmLength());
            registrationForm.setMaxHeight(registrationFormVo.getMaxHeight());
            registrationForm.setMaxWeight(registrationFormVo.getMaxWeight());
            registrationForm.setMaximumCapacity(registrationFormVo.getMaximumCapacity());
        }else if ("S".equals(registrationForm.getEquipmentType())){
            if (null==registrationFormVo.getSRegularWeight()){
                return AjaxResult.error("请输入施工升降机额定载重量（单位：kg)");
            }
            if (null==registrationFormVo.getSRegularSpeed()){
                return AjaxResult.error("请输入施工升降机额定速度（单位：m/min）");
            }
            registrationForm.setSRegularWeight(registrationFormVo.getSRegularWeight());
            registrationForm.setSRegularSpeed(registrationFormVo.getSRegularSpeed());
        }else if ("W".equals(registrationForm.getEquipmentType())){
            if (null==registrationFormVo.getWRegularWeight()){
                return AjaxResult.error("请输入物料提升机额定载重量（单位：kg)");
            }
            if (null==registrationFormVo.getWRegularSpeed()){
                return AjaxResult.error("请输入物料提升机额定速度(单位：m/min）");
            }
            registrationForm.setSRegularWeight(registrationFormVo.getWRegularWeight());
            registrationForm.setSRegularSpeed(registrationFormVo.getWRegularSpeed());
        }
        registrationForm.setEngineeringAddressDetail(registrationFormVo.getEngineeringAddressDetail());
        registrationForm.setInstallationTechnician(registrationFormVo.getInstallationTechnician());
        registrationForm.setInstallationTechnicianCard(registrationFormVo.getInstallationTechnicianCard());
        registrationForm.setInstallationManager(registrationFormVo.getInstallationManager());
        registrationForm.setInstallationManagerCard(registrationFormVo.getInstallationManagerCard());
        registrationForm.setUseProduceSafeManager(registrationFormVo.getUseProduceSafeManager());
        registrationForm.setUseProduceSafeManagerCard(registrationFormVo.getUseProduceSafeManagerCard());
        registrationForm.setUseProduceDeviceManager(registrationFormVo.getUseProduceDeviceManager());
        registrationForm.setUseProduceDeviceManagerCard(registrationFormVo.getUseProduceDeviceManagerCard());
        registrationForm.setUseSignalWorker(registrationFormVo.getUseSignalWorker());
        registrationForm.setUseSignalWorkerCard(registrationFormVo.getUseSignalWorkerCard());
        if (null!=registrationFormVo.getTestingContent()){
            registrationForm.setTestingContent(registrationFormVo.getTestingContent());
        }

        registrationForm.setUserId(appId);
        registrationForm.setCreateBy(appId);
        registrationForm.setCreateTime(new Date());
        Long id  = SnowflakeIdWorker.getInstance().nextId();
        registrationForm.setId(id);
        int flag = filingEquipmentRegistrationFormService.insert(registrationForm);
        if (flag ==1){
            return success("使用登记申请推送成功",registrationForm);
        }else{
            return error("ID为"+registrationFormVo.getBusinessId()+"的使用登记申请推送失败");
        }
    }

    /**
     * 更新使用登记申请数据
     *
     * @param filingEquipmentRegistrationForm
     * @return
     */
    @CrossOrigin
    @PostMapping("/updateRegistrationForm")
    @ResponseBody
    public AjaxResult updateRegistrationForm(@RequestParam("appId") String appId,
                                          @RequestParam("appKey") String appKey,
                                          @RequestBody FilingEquipmentRegistrationFormVo registrationFormVo
    ) {
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key){
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())){
            return AjaxResult.error("appKey输入有误");
        }
        //判断该数据是否重复
        FilingEquipmentRegistrationForm registrationForm1 = new FilingEquipmentRegistrationForm();
        registrationForm1.setDeviceFilingCode(registrationFormVo.getDeviceFilingCode());
        List<FilingEquipmentRegistrationForm> list = filingEquipmentRegistrationFormService.selectList(registrationForm1);
        if (list.size()==0){
            return AjaxResult.error("ID为"+registrationFormVo.getBusinessId()+"的数据不存在,请先推送数据");
        }

        FilingEquipmentRegistrationForm registrationForm = new FilingEquipmentRegistrationForm();
        registrationForm.setId(list.get(0).getId());
        if (null!=registrationFormVo.getDeviceName()){
            registrationForm.setDeviceName(registrationFormVo.getDeviceName());
        }
        registrationForm.setBusinessId(registrationFormVo.getBusinessId());
        registrationForm.setAuditStatus(registrationFormVo.getAuditStatus());
        registrationForm.setEquipmentType(registrationFormVo.getEquipmentType());
        registrationForm.setSpecificationModel(registrationFormVo.getSpecificationModel());
        registrationForm.setFactoryTime(registrationFormVo.getFactoryTime());
        registrationForm.setFactoryNumber(registrationFormVo.getFactoryNumber());
        registrationForm.setDeviceFilingCode(registrationFormVo.getDeviceFilingCode());
        try {
            registrationForm.setTestingDate(new SimpleDateFormat("yyyy-MM-dd").parse(registrationFormVo.getTestingDate()));
            registrationForm.setInstallationStartTime(new SimpleDateFormat("yyyy-MM-dd").parse(registrationFormVo.getInstallationStartTime()));
            registrationForm.setInstallationEndTime(new SimpleDateFormat("yyyy-MM-dd").parse(registrationFormVo.getInstallationEndTime()));
            registrationForm.setApplicationDate(new SimpleDateFormat("yyyy-MM-dd").parse(registrationFormVo.getApplicationDate()));
        }catch (Exception e){
            e.printStackTrace();
            return AjaxResult.error("请输入正确的日期格式");
        }
        registrationForm.setInstallationAddress(registrationFormVo.getInstallationAddress());
        if (null!=registrationFormVo.getFirstInstallationHeight()){
            registrationForm.setFirstInstallationHeight(registrationFormVo.getFirstInstallationHeight());
        }
        if (null!=registrationFormVo.getFinalUseHeight()){
            registrationForm.setFinalUseHeight(registrationFormVo.getFinalUseHeight());
        }
        //这三个参数没用
        if (null!=registrationFormVo.getUserName()){
        }
        if (null!=registrationFormVo.getUserType()){
        }
        if (null!=registrationFormVo.getUserCode()){
        }
        registrationForm.setPropertyUnit(registrationFormVo.getPropertyUnit());
        registrationForm.setProduceUnitCode(registrationFormVo.getProduceUnitCode());
        registrationForm.setUseUnit(registrationFormVo.getUseUnit());
        registrationForm.setUseUnitCode(registrationFormVo.getUseUnitCode());
        registrationForm.setUserContact(registrationFormVo.getUserContact());
        registrationForm.setUserContactCard(registrationFormVo.getUserContactCard());
        registrationForm.setProduceUnit(registrationFormVo.getProduceUnit());
        registrationForm.setProduceUnitCode(registrationFormVo.getProduceUnitCode());
        registrationForm.setUpkeepUnit(registrationFormVo.getUpkeepUnit());
        registrationForm.setUpkeepUnitCode(registrationFormVo.getUpkeepUnitCode());
        registrationForm.setInstallationUnit(registrationFormVo.getInstallationUnit());
        registrationForm.setInstallationUnitCode(registrationFormVo.getInstallationUnitCode());
        if (null!=registrationFormVo.getInstallationUnitQualificationCertificateNumber()){
            registrationForm.setInstallationUnitQualificationCertificateNumber(registrationFormVo.getInstallationUnitQualificationCertificateNumber());
        }
        if (null!=registrationFormVo.getInstallationUnitLicenseSafetyPermitNumber()){
            registrationForm.setInstallationUnitLicenseSafetyPermitNumber(registrationFormVo.getInstallationUnitLicenseSafetyPermitNumber());
        }
        if (null!=registrationFormVo.getSiteInstallationLeader()){
            registrationForm.setSiteInstallationLeader(registrationFormVo.getSiteInstallationLeader());
        }
        if (null!=registrationFormVo.getSiteInstallationLeaderCard()){
            registrationForm.setSiteInstallationLeaderCard(registrationFormVo.getSiteInstallationLeaderCard());
        }
        registrationForm.setTestingUnit(registrationFormVo.getTestingUnit());
        registrationForm.setTestingUnitCode(registrationFormVo.getTestingUnitCode());
        if (null!=registrationFormVo.getTestingLeadere()){
            registrationForm.setTestingLeadere(registrationFormVo.getTestingLeadere());
        }
        if (null!=registrationFormVo.getTestingLeadereCard()){
            registrationForm.setTestingLeadereCard(registrationFormVo.getTestingLeadereCard());
        }
        registrationForm.setEngineeringName(registrationFormVo.getEngineeringName());
        registrationForm.setEngineeringAreaCode(registrationFormVo.getEngineeringAreaCode());
        if (null!=registrationFormVo.getProjectManager()){
            registrationForm.setProjectManager(registrationFormVo.getProjectManager());
        }
        if ("1".equals(registrationFormVo.getIsBuildLicense())){
            if ("".equals(registrationFormVo.getBuildLicenseCode())||null==registrationFormVo.getBuildLicenseCode()){
                return AjaxResult.error("请输入建筑工程施工许可证编号");
            }
        }else if (!"0".equals(registrationFormVo.getIsBuildLicense())){
            return AjaxResult.error("是否办理施工许可证格式有误,0:否 1:是");
        }
        registrationForm.setFilingApplicationFormPath(registrationFormVo.getFilingApplicationFormPath());
        registrationForm.setEquipmentRecordCertificate(registrationFormVo.getEquipmentRecordCertificate());
        registrationForm.setEquipmentMaintenanceManagementSystemPath(registrationFormVo.getEquipmentMaintenanceManagementSystemPath());
        registrationForm.setEquipmentLeasingContract(registrationFormVo.getEquipmentLeasingContract());
        registrationForm.setInstallationAcceptanceReport(registrationFormVo.getInstallationAcceptanceReport());
        registrationForm.setInspectionReportPath(registrationFormVo.getInspectionReportPath());
        registrationForm.setOperatorsQualificationCertificate(registrationFormVo.getOperatorsQualificationCertificate());
        registrationForm.setSafetyAccidentsEmergencyPlanPath(registrationFormVo.getSafetyAccidentsEmergencyPlanPath());
        if (null!=registrationFormVo.getCertId()){
            registrationForm.setCertId(registrationFormVo.getCertId());
        }
        if ("T".equals(registrationForm.getEquipmentType())){
            if (null==registrationFormVo.getArmLength()){
                return AjaxResult.error("请输入塔式起重机起重臂长度（单位：m）");
            }
            if (null==registrationFormVo.getMaxHeight()){
                return AjaxResult.error("请输入塔式起重机拟安装最大高度（单位：m）");
            }
            if (null==registrationFormVo.getMaxWeight()){
                return AjaxResult.error("请输入塔式起重机最大起重量（单位：T）");
            }
            if (null==registrationFormVo.getMaximumCapacity()){
                return AjaxResult.error("请输入塔式起重机最大幅度起重量（几倍率）（单位：T）");
            }
            registrationForm.setArmLength(registrationFormVo.getArmLength());
            registrationForm.setMaxHeight(registrationFormVo.getMaxHeight());
            registrationForm.setMaxWeight(registrationFormVo.getMaxWeight());
            registrationForm.setMaximumCapacity(registrationFormVo.getMaximumCapacity());
        }else if ("S".equals(registrationForm.getEquipmentType())){
            if (null==registrationFormVo.getSRegularWeight()){
                return AjaxResult.error("请输入施工升降机额定载重量（单位：kg)");
            }
            if (null==registrationFormVo.getSRegularSpeed()){
                return AjaxResult.error("请输入施工升降机额定速度（单位：m/min）");
            }
            registrationForm.setSRegularWeight(registrationFormVo.getSRegularWeight());
            registrationForm.setSRegularSpeed(registrationFormVo.getSRegularSpeed());
        }else if ("W".equals(registrationForm.getEquipmentType())){
            if (null==registrationFormVo.getWRegularWeight()){
                return AjaxResult.error("请输入物料提升机额定载重量（单位：kg)");
            }
            if (null==registrationFormVo.getWRegularSpeed()){
                return AjaxResult.error("请输入物料提升机额定速度(单位：m/min）");
            }
            registrationForm.setSRegularWeight(registrationFormVo.getWRegularWeight());
            registrationForm.setSRegularSpeed(registrationFormVo.getWRegularSpeed());
        }
        registrationForm.setEngineeringAddressDetail(registrationFormVo.getEngineeringAddressDetail());
        registrationForm.setInstallationTechnician(registrationFormVo.getInstallationTechnician());
        registrationForm.setInstallationTechnicianCard(registrationFormVo.getInstallationTechnicianCard());
        registrationForm.setInstallationManager(registrationFormVo.getInstallationManager());
        registrationForm.setInstallationManagerCard(registrationFormVo.getInstallationManagerCard());
        registrationForm.setUseProduceSafeManager(registrationFormVo.getUseProduceSafeManager());
        registrationForm.setUseProduceSafeManagerCard(registrationFormVo.getUseProduceSafeManagerCard());
        registrationForm.setUseProduceDeviceManager(registrationFormVo.getUseProduceDeviceManager());
        registrationForm.setUseProduceDeviceManagerCard(registrationFormVo.getUseProduceDeviceManagerCard());
        registrationForm.setUseSignalWorker(registrationFormVo.getUseSignalWorker());
        registrationForm.setUseSignalWorkerCard(registrationFormVo.getUseSignalWorkerCard());
        if (null!=registrationFormVo.getTestingContent()){
            registrationForm.setTestingContent(registrationFormVo.getTestingContent());
        }

        registrationForm.setUpdateBy(appId);
        registrationForm.setUpdateTime(new Date());
        int flag = filingEquipmentRegistrationFormService.update(registrationForm);
        if (flag ==1){
            return success("使用登记申请修改成功",registrationForm);
        }else{
            return error("ID为"+registrationFormVo.getBusinessId()+"的使用登记申请修改失败");
        }
    }

    /**
     * form页面导航 更新
     *
     * @return
     */
    @RequiresPermissions("use:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id") Long id, Model model) {
        addAttr(id,model);
        model.addAttribute("dict",sysDictService.findByType("city"));
        if (null != id) {
            model.addAttribute("filingEquipmentRegistrationForm", filingEquipmentRegistrationFormService.getOne(id));
            model.addAttribute("files",filingFileService.getFiles(id));
        }
        return "admin/filing/equipmentUseApplication/filingEquipmentRegistrationFormEdit";
    }

    /**
     * 更新
     *
     * @param filingEquipmentRegistrationForm
     * @return
     */
    @PostMapping("/update")
    @RequiresPermissions("use:edit")
    @ResponseBody
    public AjaxResult update(@Valid FilingEquipmentRegistrationForm filingEquipmentRegistrationForm) {
        filingEquipmentRegistrationForm.setAuditStatus("0");
        filingEquipmentRegistrationForm.setUpdateTime(new Date());
        return toAjax(filingEquipmentRegistrationFormService.update(filingEquipmentRegistrationForm));
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("use:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(filingEquipmentRegistrationFormService.deleteBatchIds(ids));
    }


    /**
     * @Anthor: juziqiang
     * @Description: 使用备案审查通过
     * @Date: 10:32 2019/11/6
     */
    @RequestMapping("/examine")
    @RequiresPermissions("use:examine")
    @ResponseBody
    public AjaxResult examine(Long id) {
        FilingEquipmentRegistrationForm filingEquipmentRegistrationForm = filingEquipmentRegistrationFormService.getOne(id);
        if(!filingEquipmentRegistrationForm.getAuditStatus().equals("0")){
            return error("已审核的申请，不可重复审核");
        }
        filingEquipmentRegistrationForm.setAuditStatus("1");
        filingEquipmentRegistrationForm.setAuditRejectReason("");
        filingEquipmentRegistrationForm.setAuditTime(new Date());
        filingEquipmentRegistrationForm.setUseFilingCode(filingEquipmentUseRegistrationService.code2(filingEquipmentRegistrationForm));
        Integer flag = filingEquipmentRegistrationFormService.update(filingEquipmentRegistrationForm);
        if (flag == 1) {
            return toAjax(filingEquipmentUseRegistrationService.addFling(filingEquipmentRegistrationForm));
        } else {
            return error("操作失败");
        }
    }


    /**
     * @Anthor: juziqiang
     * @Description: 备案审查驳回
     * @Date: 10:34 2019/11/6
     */
    @RequiresPermissions("use:examine")
    @RequestMapping("/reject")
    @ResponseBody
    public AjaxResult reject(Long id, String rejectReason) {
        FilingEquipmentRegistrationForm filingEquipmentRegistrationForm = filingEquipmentRegistrationFormService.getOne(id);
        filingEquipmentRegistrationForm.setAuditStatus("2");
        filingEquipmentRegistrationForm.setAuditRejectReason(rejectReason);
        filingEquipmentRegistrationForm.setAuditTime(new Date());
        filingEquipmentRegistrationForm.setUpdateTime(new Date());
        return toAjax(filingEquipmentRegistrationFormService.update(filingEquipmentRegistrationForm));
    }


    /**
     * 获取单条信息
     *
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("use:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return success("获取信息成功", filingEquipmentRegistrationFormService.getOne(id));
    }


    @ResponseBody
    @RequestMapping("/search")
    public AjaxResult search(String code){
        FilingEquipmentUseCancellation filingEquipmentUseCancellation = new FilingEquipmentUseCancellation();
        filingEquipmentUseCancellation.setUseFilingCode(code);
        Integer size = filingEquipmentUseCancellationService.selectList(filingEquipmentUseCancellation).size();
        if (size>0){
            return error("操作失败");
        }
        FilingEquipmentRegistrationForm filingEquipmentRegistrationForm = filingEquipmentRegistrationFormService.getByCode(code);
        if (filingEquipmentRegistrationForm!= null){
            return success("操作成功",filingEquipmentRegistrationForm);
        }else{
            return error("操作失败");
        }
    }

    /**
     * 校验唯一性
     * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
     *
     * @param filingEquipmentRegistrationForm
     * @return
     */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value = {"use:add", "use:edit"}, logical = Logical.OR)
    @ResponseBody
    public Map<String, Boolean> checkUnique(FilingEquipmentRegistrationForm filingEquipmentRegistrationForm) {
        Map<String, Boolean> result = new HashMap<String, Boolean>(4);
        result.put("valid", true);
        Long id = filingEquipmentRegistrationForm.getId();
        filingEquipmentRegistrationForm.setId(null);
        List<FilingEquipmentRegistrationForm> filingEquipmentRegistrationFormList = filingEquipmentRegistrationFormService.selectList(filingEquipmentRegistrationForm);
        if (StringUtils.isNotEmpty(filingEquipmentRegistrationFormList)) {
            if (filingEquipmentRegistrationFormList.size() > 1) {
                result.put("valid", false);
            } else {
                if (id != null) {
                    // 新增
                    result.put("valid", false);
                } else {
                    // 修改
                    if (!id.equals(filingEquipmentRegistrationFormList.get(0).getId())) {
                        result.put("valid", false);
                    }
                }
            }
        }
        return result;
    }

    private void addAttr(Long id,Model model){
        FilingEquipmentUser filingEquipmentuser = new FilingEquipmentUser();
        filingEquipmentuser.setUseRegistrationId(id.toString());
        List<FilingEquipmentUser> list = filingEquipmentUserService.selectList(filingEquipmentuser);
        model.addAttribute("users",list);
        model.addAttribute("dict",sysDictService.findByType("city"));
    }

    /**
     *  文件上传
     */
    @RequestMapping("/uploadFile")
    @ResponseBody
    public AjaxResult uploadFile(MultipartFile uploadFile) throws IOException {
        String type = "filing";
        Map<String, Object> result = UploadFileUtil.saveFile(uploadFile, type);
        return AjaxResult.success("操作成功", result);
    }

    /**
     * 设备使用登记申请推送接口
     *
     * @return
     */
    @PostMapping("/syncFilingUse")
    @ResponseBody
    @VerifySign
    public AjaxResult syncFilingUse(
            @RequestParam("appId") String appId,
            @RequestParam("appKey") String appKey,
            @RequestBody() String  data
    ){
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key){
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())){
            return AjaxResult.error("appKey输入有误");
        }
        String str1 = data.replaceAll("\\[","");
        String str2 = str1.replaceAll("\\]","");
        JSONObject jsonObject = JSONObject.parseObject(str2);
        FilingEquipmentRegistrationForm registrationForm = jsonObject.toJavaObject(FilingEquipmentRegistrationForm.class);
        //判断该数据是否重复
        FilingEquipmentRegistrationForm application = filingEquipmentRegistrationFormService.getOneByFilingCode(registrationForm.getDeviceFilingCode());
        if (null==application){
            //新增
            if (null==registrationForm.getUserId()){
                registrationForm.setUserId(appId);
            }
            registrationForm.setCreateBy(appId);
            registrationForm.setCreateTime(new Date());
            Long id  = SnowflakeIdWorker.getInstance().nextId();
            registrationForm.setId(id);
            int flag = filingEquipmentRegistrationFormService.insert(registrationForm);
            if (flag ==1){
                return success("使用登记申请推送成功",registrationForm);
            }else{
                return error("设备"+registrationForm.getDeviceFilingCode()+"使用登记申请推送失败");
            }
        }else {
            //修改
            registrationForm.setUpdateTime(new Date());
            registrationForm.setUpdateBy(appId);
            registrationForm.setId(application.getId());
            int flag = filingEquipmentRegistrationFormService.update(registrationForm);
            if (flag ==1){
                return success("使用登记申请修改成功",registrationForm);
            }else{
                return error("设备"+registrationForm.getDeviceFilingCode()+"使用登记申请修改失败");
            }
        }
    }

}
