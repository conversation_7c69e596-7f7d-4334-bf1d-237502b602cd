package com.tzstcl.filing.equipmentUseApplication.model;

import com.tzstcl.base.model.BaseModel;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 公司：天筑科技股份有限公司
 * 作者：xuchengjie
 * 日期：2019年11月09日
 * 说明：设备使用登记表实体类
 */
@Data
public class FilingEquipmentRegistrationForm extends BaseModel<FilingEquipmentRegistrationForm> implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
     *申请人id
     */
    private String userId;
    /**
     *申请日期
     */
    private Date applicationDate;
    /**
     *使用单位
     */
    private String useUnit;
    /**
     *使用单位联系人/使用项目负责人
     */
    private String userContact;
    /**
     *使用单位联系电话
     */
    private String userContactNumber;
    /**
     *产权单位
     */
    private String propertyUnit;
    /**
     *设备名称
     */
    private String deviceName;
    /**
     *规格型号
     */
    private String specificationModel;
    /**
     *出厂日期
     */
    private String factoryTime;
    /**
     *设备备案编号
     */
    private String deviceFilingCode;
    /**
     *工程名称
     */
    private String engineeringName;
    /**
     *项目经理
     */
    private String projectManager;
    /**
     *安装单位
     */
    private String installationUnit;
    /**
     *安装单位资质等级
     */
    private String installationUnitQualificationLevel;
    /**
     *现场安装负责人
     */
    private String siteInstallationLeader;
    /**
     *安装单位资质证号
     */
    private String installationUnitQualificationCertificateNumber;
    /**
     *安装单位安全许可证号
     */
    private String installationUnitLicenseSafetyPermitNumber;
    /**
     *安装开始时间
     */
    private Date installationStartTime;
    /**
     *安装终止时间
     */
    private Date installationEndTime;
    /**
     *检验检测单位
     */
    private String testingUnit;
    /**
     *检测日期
     */
    private Date testingDate;
    /**
     *检验检测负责人
     */
    private String testingLeadere;
    /**
     *联合验收日期
     */
    private Date jointInspectionDate;
    /**
     *首次安装高度，单位：米
     */
    private String firstInstallationHeight;
    /**
     *最终使用高度，单位：米
     */
    private String finalUseHeight;
    /**
     *使用备案申请表路径
     */
    private String filingApplicationFormPath;
    /**
     *起重机备案证文件路径
     */
    private String equipmentRecordCertificate;
    /**
     *设备租赁合同文件路径
     */
    private String equipmentLeasingContract;
    /**
     *定期检验及安装检验检测报告文件路径
     */
    private String inspectionReportPath;
    /**
     *安装验收报告文件路径
     */
    private String installationAcceptanceReport;
    /**
     *现场特种作业人员资格证书文件路径
     */
    private String operatorsQualificationCertificate;
    /**
     *设备保养制度文件路径
     */
    private String equipmentMaintenanceManagementSystemPath;
    /**
     *安全事故应急预案文件路径
     */
    private String safetyAccidentsEmergencyPlanPath;
    /**
     *审核状态，0未审核，1审核通过，2审核驳回
     */
    private String auditStatus;
    /**
     *审核时间，yyyy-MM-dd HH:mm:ss
     */
    private Date auditTime;
    /**
     *审核驳回意见
     */
    private String auditRejectReason;
    /**
     *使用备案编号
     */
    private String useFilingCode;
    /**
     * 机械类别，T塔式起重机，S施工升降机，W物料提升机，Q其他
     */
    private String equipmentType;
    /**
     * 安装位置
     */
    private String installationAddress;
    /**
     * 产权单位社会统一信用代码
     */
    private String propertyUnitCode;
    /**
     * 使用单位统一社会信用代码
     */
    private String useUnitCode;
    /**
     * 使用单位项目负责人身份证号码
     */
    private String userContactCard;
    /**
     * 制造单位
     */
    private String produceUnit;
    /**
     * 制造单位统一社会信用代码
     */
    private String produceUnitCode;
    /**
     * 维保单位
     */
    private String upkeepUnit;
    /**
     * 维保单位统一社会信用代码
     */
    private String upkeepUnitCode;
    /**
     * 安装统一社会信用代码
     */
    private String installationUnitCode;
    /**
     * 现场安装负责人身份证号
     */
    private String siteInstallationLeaderCard;
    /**
     * 检测单位统一社会信用代码
     */
    private String testingUnitCode;
    /**
     * 检验检测负责人身份证号
     */
    private String testingLeadereCard;
    /**
     * 是否办理施工许可证 0:否 1:是  默认为0
     */
    private String isBuildLicense;
    /**
     * 建筑工程施工许可证编号
     */
    private String buildLicenseCode;
    /**
     * 出厂编号
     */
    private String factoryNumber;
    /**
     *业务ID
     */
    private String businessId;
    /**
     * 证照标识
     */
    private String certId;
    /**
     * 塔式起重机起重臂长度（单位：m）
     */
    private String armLength;
    /**
     * 塔式起重机最大起重量（单位：T）
     */
    private String maxWeight;
    /**
     * 塔式起重机拟安装最大高度（单位：m）
     */
    private String maxHeight;
    /**
     * 塔式起重机最大幅度起重量（几倍率）（单位：T）
     */
    private String maximumCapacity;
    /**
     * 施工升降机额定载重量（单位：kg)
     */
    private String sRegularWeight;
    /**
     * 施工升降机额定速度（单位：m/min）
     */
    private String sRegularSpeed;
    /**
     * 物料提升机额定载重量（单位：kg)
     */
    private String wRegularWeight;
    /**
     * 物料提升机额定速度(单位：m/min）
     */
    private String wRegularSpeed;
    /**
     * 项目工程详细地址
     */
    private String engineeringAddressDetail;
    /**
     * 工程所属地区域编码
     */
    private Long engineeringAreaCode;
    /**
     * 安装单位技术负责人
     */
    private String installationTechnician;
    /**
     * 安装单位技术负责人身份证号
     */
    private String installationTechnicianCard;
    /**
     * 安装单位现场管理人员
     */
    private String installationManager;
    /**
     * 安装单位现场管理人员身份证号
     */
    private String installationManagerCard;
    /**
     * 使用单位专职安全生产管理人员
     */
    private String useProduceSafeManager;
    /**
     * 使用单位专职安全生产管理人员身份证号
     */
    private String useProduceSafeManagerCard;
    /**
     * 使用单位建筑起重机械管理人员
     */
    private String useProduceDeviceManager;
    /**
     * 使用单位建筑起重机械管理人员身份证号
     */
    private String useProduceDeviceManagerCard;
    /**
     * 建筑起重司索信号工
     */
    private String useSignalWorker;
    /**
     * 建筑起重司索信号工身份证号
     */
    private String useSignalWorkerCard;
    /**
     * 建筑起重机械司机
     */
    private String useDeviceDriver;
    /**
     * 建筑起重机械司机身份证号
     */
    private String useDeviceDriverCard;
    /**
     * 是否申请使用登记注销
     */
    private String isUseCancel;
    /**
     * 检测内容
     */
    private String testingContent;
}
