package com.tzstcl.filing.equipmentUseApplication.service;

import com.tzstcl.base.service.BaseService;
import com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling;
import com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationForm;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：xuchengjie
 * 日期：2019年11月09日
 * 说明：设备使用登记表Servic
 */
public interface FilingEquipmentRegistrationFormService extends BaseService<FilingEquipmentRegistrationForm> {

    /**
    *
    * 批量增加设备使用登记表
    * @param filingEquipmentRegistrationFormList
    * <AUTHOR>
    * @date 2019年11月09日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<FilingEquipmentRegistrationForm> filingEquipmentRegistrationFormList);
    FilingEquipmentRegistrationForm getByCode(String code);

    int insert(FilingEquipmentRegistrationForm filingEquipmentRegistrationForm);

    List<FilingEquipmentRegistrationForm> selectListByAreaCode(List<Long> areaList, String auditStatus);

    List<FilingEquipmentRegistrationForm> selectListByCondition(String year, List<Long> areaList);

    FilingEquipmentRegistrationForm getOneByFilingCode(String deviceFilingCode);

    List<FilingEquipmentRegistrationForm> selectListGroupByFilingCode(String year, List<Long> areaList);
}
