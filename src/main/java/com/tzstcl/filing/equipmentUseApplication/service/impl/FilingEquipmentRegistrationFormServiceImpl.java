package com.tzstcl.filing.equipmentUseApplication.service.impl;

import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling;
import com.tzstcl.filing.equipmentUseApplication.service.FilingEquipmentRegistrationFormService;
import com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationForm;
import com.tzstcl.filing.equipmentUseApplication.mapper.FilingEquipmentRegistrationFormMapper;
import com.tzstcl.framework.shiro.ShiroUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：xuchengjie
 * 日期：2019年11月09日
 * 说明：设备使用登记表ServiceImpl
 */
@Service
public class FilingEquipmentRegistrationFormServiceImpl extends BaseServiceImpl<FilingEquipmentRegistrationFormMapper, FilingEquipmentRegistrationForm> implements FilingEquipmentRegistrationFormService {

    /**
     * 批量增加设备使用登记表
     *
     * @param filingEquipmentRegistrationFormList
     * @return Integer 插入的记录数
     * <AUTHOR>
     * @date 2019年11月09日
     */
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<FilingEquipmentRegistrationForm> filingEquipmentRegistrationFormList) {
        return this.mapper.insertBatch(filingEquipmentRegistrationFormList);
    }

    @Override
    public FilingEquipmentRegistrationForm getByCode(String code) {
        return this.mapper.getByCode(code);
    }
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public int insert(FilingEquipmentRegistrationForm filingEquipmentRegistrationForm) {
        try {
            Long createBy = ShiroUtils.getUserId();
            filingEquipmentRegistrationForm.setCreateBy(createBy + "");
        }catch (Exception e){
            e.printStackTrace();
        }
        filingEquipmentRegistrationForm.setCreateTime(new Date());
        if(filingEquipmentRegistrationForm.getAuditStatus()==null){
            filingEquipmentRegistrationForm.setAuditStatus("0");
        }
        return this.mapper.insert(filingEquipmentRegistrationForm);
    }

    @Override
    public List<FilingEquipmentRegistrationForm> selectListByAreaCode(List<Long> areaList, String auditStatus) {
        return mapper.selectListByAreaCode(areaList,auditStatus);
    }

    @Override
    public List<FilingEquipmentRegistrationForm> selectListByCondition(String year, List<Long> areaList) {
        return mapper.selectListByCondition(year,areaList);
    }

    @Override
    public FilingEquipmentRegistrationForm getOneByFilingCode(String deviceFilingCode) {
        return mapper.getOneByFilingCode(deviceFilingCode);
    }

    @Override
    public List<FilingEquipmentRegistrationForm> selectListGroupByFilingCode(String year, List<Long> areaList) {
        return mapper.selectListGroupByFilingCode(year,areaList);
    }
}
