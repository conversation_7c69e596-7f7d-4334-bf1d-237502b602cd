package com.tzstcl.filing.annotation;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.filing.access.model.AccessKey;
import com.tzstcl.filing.access.service.IAccessKeyService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;


/**
 * 接口签名验证AOP
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class VerifySignAspect {
    private static final Logger log = LoggerFactory.getLogger(VerifySignAspect.class);
    @Autowired
    private IAccessKeyService accessKeyService;


   @Pointcut("@annotation(com.tzstcl.filing.annotation.VerifySign)")
   public void verifySignAspect(){

   }
    @Before("verifySignAspect()")
    public void beforePointcut(){
       log.info("===============beforePointcut================");
    }


   @Around("verifySignAspect()")
   public Object around(ProceedingJoinPoint joinPoint) throws IOException {

       // 2.获取参数
       ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
       HttpServletRequest request = Objects.requireNonNull(attributes).getRequest();
       String appid = request.getParameter("appId");
       String appKey= request.getParameter("appKey");
       log.info("appid:{},appkey:{}",appid,appKey);
       String data=null;
       Object[] args = joinPoint.getArgs();
       if(request.getParameterMap().get("data")==null){
           data = JSON.toJSON(joinPoint.getArgs()[joinPoint.getArgs().length-1]).toString();
       }else {
           data=JSONObject.toJSON(request.getParameterMap().get("data")[0]).toString();
           args[args.length-1]=data;
       }
       log.info("data:{}",data);
       //String data = JSON.toJSON(joinPoint.getArgs()[joinPoint.getArgs().length-1]).toString();
       AccessKey accessKey = accessKeyService.selectAccessKeyByAppId(appid);
       if (accessKey == null){
           AjaxResult ajaxResult=new AjaxResult();
           ajaxResult.put("code",-1);
           ajaxResult.put("ReturnMsg","appid不存在");
           return ajaxResult;
       }
       if(!accessKey.getAppKey().equals(appKey)){
           AjaxResult ajaxResult=new AjaxResult();
           ajaxResult.put("code",-1);
           ajaxResult.put("ReturnMsg","appKey不正确");
           return ajaxResult;
       }



       try {
           //todo 可以把请求码提出来从这里传到参数里
           return joinPoint.proceed(args);
       } catch (Throwable throwable) {

           return AjaxResult.error("失败");
       }
   }


    // 时间戳不是14位
    // 不是一个时间戳
    // 不是当天的数据
    // true 合法的时间戳
    private static boolean verifyTimestamp(String timestamp) {

        if (timestamp.length() != 14 ) {
            return false;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            sdf.parse(timestamp);
            String current = sdf.format(new Date()).substring(0, 8);
            String origin = timestamp.substring(0, 8);
            if (current.equals(origin)){
                return true;
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return false;

    }
    //数据是否为json
    private static boolean verifyData(String jsonData) {
        if (jsonData == null){
           return false;
        }
        try {
           /* String decode = URLDecoder.decode(jsonData, "UTF-8");*/
            JSONObject.parse(jsonData);
            return true;
        }catch (Exception e){
            return false;
        }

    }

    //校验签名
//    private static boolean verifySign(String sign, String appid,
//                               String data, String format,
//                               String method, String nonce,
//                               String timestamp, String version,
//                               String appSecret) {
//        String sb = "appid=" + appid +
//                "&data=" + data +
//                "&format=" + format +
//                "&method=" + method +
//                "&nonce=" + nonce +
//                "&timestamp=" + timestamp +
//                "&version=" + version +
//                "&appsecret=" + appSecret;
//       log.info(sb);
//        String lowerCase = sb.toLowerCase();
//        String sha256Str = EncryptSha256Util.getSha256Str(lowerCase);
//
//        log.info("转小写:{}",lowerCase);
//
//        log.info("sign:{}",sha256Str);
//        return sign.equals(sha256Str);
//
//    }


    @After("verifySignAspect()")
    public void afterPointcut(){
       log.info("===============afterPointcut================");
    }

//    public static void main(String[] args) {
//        //verifySign("6692f61e958f63f3aba3da18c9b3d0efa2b3e1575c47c2792ca0f7296ba2d2fe","791RMo4z","%7B%22areaCode%22%3A%22410000%22%2C%22corpName%22%3A%22%E4%B8%AD%E9%93%81%E5%BB%BA%E8%AE%BE%E9%9B%86%E5%9B%A2%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%22%2C%22corpCode%22%3A%229111000010228709XY%22%2C%22registerDate%22%3A%221979-08-01%22%7D","json","addCorpBasic","20230516163209","2023051616320913","1.0","ac3881524fe0c7f676fafeecf02ab20a98cded9ecef313a2fba4b9651de74115");
//        String str = "appid=791RMo4z&data={\"pageIndex\":0,\"pageSize\":1,\"corpName\":\"中铁建设集团有限公司\",\"corpCode\":\"9111000010228709XY\"}&format=json&method=selectCorpBasicInfo&nonce=2023051715582056&timestamp=20230517155820&version=1.0&appsecret=ac3881524fe0c7f676fafeecf02ab20a98cded9ecef313a2fba4b9651de74115";
//        String lowerCase = str.toLowerCase();
//        String sha256Str = EncryptSha256Util.getSha256Str(str);
//        String sha256Strl = EncryptSha256Util.getSha256Str(lowerCase);
//        System.out.println(sha256Str);
//        System.out.println(sha256Strl);
//    }

}
