package com.tzstcl.filing.updatePushData.ctrl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.filing.updatePushData.model.UpdatePushData;
import com.tzstcl.filing.updatePushData.service.UpdatePushDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：更新全国数据Controller
 */
@Controller
@RequestMapping("/admin/updatePushData")
public class UpdatePushDataCtrl extends BaseCtrl {

    @Autowired
    private UpdatePushDataService updatePushDataService;

    /**
    * list页面导航
    * @return
    */
    @RequiresPermissions("updatePushData:view")
    @RequestMapping("/toList")
    public String toList() {
        return "admin/filing/updatePushData/updatePushDataList";
    }

    /**
    * 获取查询的分页数据
    * @param updatePushData
    * @return
    */
    @RequestMapping("/list")
    @RequiresPermissions("updatePushData:view")
    @ResponseBody
    public PageInfo<UpdatePushData> list(UpdatePushData updatePushData) {
        return  updatePushDataService.selectPage(updatePushData);
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"updatePushData:edite","updatePushData:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        if(null != id){
            model.addAttribute("updatePushData" ,updatePushDataService.getOne(id));
        }
        return "admin/filing/updatePushData/updatePushDataForm";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("updatePushData:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("updatePushData" ,updatePushDataService.getOne(id));
        }
        return "admin/filing/updatePushData/updatePushDataDetail";
    }

    /**
     * 新增
     * @param updatePushData
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"updatePushData:add"})
    @ResponseBody
    public AjaxResult save(@Valid UpdatePushData updatePushData) {
         return toAjax(updatePushDataService.add(updatePushData));
    }

    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("updatePushData:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("updatePushData" ,updatePushDataService.getOne(id));
        }
        return "admin/filing/updatePushData/updatePushDataEdit";
    }

    /**
    * 更新
    * @param updatePushData
    * @return
    */
    @PostMapping("/update")
    @RequiresPermissions("updatePushData:edit")
    @ResponseBody
    public AjaxResult update(@Valid UpdatePushData updatePushData) {
        return toAjax(updatePushDataService.update(updatePushData));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("updatePushData:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(updatePushDataService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("updatePushData:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",updatePushDataService.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param updatePushData
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"updatePushData:add", "updatePushData:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(UpdatePushData updatePushData) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = updatePushData.getId();
        updatePushData.setId(null);
        List<UpdatePushData> updatePushDataList = updatePushDataService.selectList(updatePushData);
        if(StringUtils.isNotEmpty(updatePushDataList)){
            if(updatePushDataList.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(updatePushDataList.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }

}
