package com.tzstcl.filing.updatePushData.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import com.tzstcl.filing.updatePushData.model.UpdatePushData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：更新全国数据Mapper
 */
@Mapper
public interface UpdatePushDataMapper extends BaseMapper<UpdatePushData>  {

    /**
    *
    * 批量增加更新全国数据
    * @param updatePushDataList
    * <AUTHOR>
    * @date 2024年05月23日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<UpdatePushData> updatePushDataList);

}