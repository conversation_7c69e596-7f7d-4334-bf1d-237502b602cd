package com.tzstcl.filing.updatePushData.service;

import com.alibaba.fastjson.JSONObject;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.service.BaseService;
import com.tzstcl.filing.updatePushData.model.UpdatePushData;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：更新全国数据Servic
 */
public interface UpdatePushDataService extends BaseService<UpdatePushData> {

    /**
    *
    * 批量增加更新全国数据
    * @param updatePushDataList
    * <AUTHOR>
    * @date 2024年05月23日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<UpdatePushData> updatePushDataList);
    /**
     * [变更数据]
     * @param list
     * @param updatePushDataList
     * @return com.tzstcl.base.model.AjaxResult
     * <AUTHOR>
     * @date 2024/5/24 9:04
     */
    AjaxResult changeCert(List<JSONObject> list, String updatePushDataList);
}
