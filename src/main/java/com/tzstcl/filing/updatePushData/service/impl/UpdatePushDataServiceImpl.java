package com.tzstcl.filing.updatePushData.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.commons.utils.DateUtils;
import com.tzstcl.filing.pushData.model.PushData;
import com.tzstcl.filing.pushResult.mapper.PushResultMapper;
import com.tzstcl.filing.pushResult.model.PushResult;
import com.tzstcl.filing.tripartitePush.utils.CallAPI;
import com.tzstcl.filing.updatePushData.service.UpdatePushDataService;
import com.tzstcl.filing.updatePushData.model.UpdatePushData;
import com.tzstcl.filing.updatePushData.mapper.UpdatePushDataMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.tzstcl.filing.tripartitePush.utils.CallAPI.APIInvoke;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：更新全国数据ServiceImpl
 */
@Service
public class UpdatePushDataServiceImpl extends BaseServiceImpl<UpdatePushDataMapper,UpdatePushData> implements UpdatePushDataService{

    @Autowired
    PushResultMapper pushResultMapper;
    @Value("${nation.update.url}")
    private String changeUrl;
    /**
    *
    * 批量增加更新全国数据
    * @param updatePushDataList
    * <AUTHOR>
    * @date 2024年05月23日
    * @return Integer 插入的记录数
    */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<UpdatePushData> updatePushDataList){return this.mapper.insertBatch(updatePushDataList);}
    @Transactional
    @Override
    public AjaxResult changeCert(List<JSONObject> list, String updatePushDataList) {
        String accessToken = CallAPI.getToken(true).getAccessToken();
        String s = APIInvoke(changeUrl, accessToken, null, list);
        JSONObject jsonObject =new JSONObject();

        try{
            jsonObject=JSONObject.parseObject(s);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.put("code",jsonObject.get("ReturnCode"))
                    .put("ReturnMsg",jsonObject.get("ReturnMsg"))
                    .put("ReturnData",jsonObject.get("ReturnData"));
            PushResult pushResult = JSONObject.parseObject(s, PushResult.class);
            List<UpdatePushData> updatePushDataList2 = JSONObject.parseArray(updatePushDataList,UpdatePushData.class);
            updatePushDataList2.forEach(item->{
                item.setPushFlag("S");
                item.setPushDate(DateUtils.getNowDate());
                pushResult.setAssociationId(item.getBusinessDataId());
            });
            this.mapper.insertBatch(updatePushDataList2);
            pushResultMapper.insert(pushResult);
            return ajaxResult;
        }catch (Exception e){
            return AjaxResult.error().put("接口调用异常",s);
        }

    }
}
