package com.tzstcl.filing.updatePushData.model;

import com.tzstcl.base.model.BaseModel;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：更新全国数据实体类
 */
@Data
public class UpdatePushData extends BaseModel<UpdatePushData> implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
     *业务数据ID
     */
    private String businessDataId;
    /**
     *证照标识
     */
    private String certId;
    /**
     *行政区划代码
     */
    private String areaCode;
    /**
     *证照编号
     */
    private String certNum;
    /**
     *类别代码
     */
    private String deviceCategoryCode;
    /**
     *规格型号
     */
    private String deviceModel;
    /**
     *出厂编号
     */
    private String factoryNum;
    /**
     *制造单位统一社会信用代码
     */
    private String manufactureCorpCode;
    /**
     *证书状态代码
     */
    private String certStatus;
    /**
     *证书状态描述
     */
    private String certStatusDescription;
    /**
     *操作类型
     */
    private String operateType;
    /**
     *创建时间
     */
    private Date creatDate;
    /**
     *创建人
     */
    private String creatAt;
    /**
     *推送标识
     */
    private String pushFlag;
    /**
     *push_date
     */
    private Date pushDate;

}