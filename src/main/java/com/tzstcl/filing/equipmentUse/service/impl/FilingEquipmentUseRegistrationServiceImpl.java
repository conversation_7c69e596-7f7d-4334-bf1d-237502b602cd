package com.tzstcl.filing.equipmentUse.service.impl;

import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.commons.utils.DateUtils;
import com.tzstcl.filing.code.mapper.CodeMapper;
import com.tzstcl.filing.code.model.Code;
import com.tzstcl.filing.code.service.CodeService;
import com.tzstcl.filing.equipmentUse.service.FilingEquipmentUseRegistrationService;
import com.tzstcl.filing.equipmentUse.model.FilingEquipmentUseRegistration;
import com.tzstcl.filing.equipmentUse.mapper.FilingEquipmentUseRegistrationMapper;
import com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationForm;
import com.tzstcl.filing.installation.model.FilingEquipmentInstallationForm;
import com.tzstcl.framework.shiro.ShiroUtils;
import com.tzstcl.sys.user.model.SysDept;
import com.tzstcl.sys.user.model.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：xuchengjie
 * 日期：2019年11月11日
 * 说明：设备使用备案表ServiceImpl
 */
@Service
public class FilingEquipmentUseRegistrationServiceImpl extends BaseServiceImpl<FilingEquipmentUseRegistrationMapper,FilingEquipmentUseRegistration> implements FilingEquipmentUseRegistrationService {

    @Autowired
    private CodeService codeService;

    private static  final String codeType = "use";

    /**
     * 批量增加设备使用备案表
     *
     * @param filingEquipmentUseRegistrationList
     * @return Integer 插入的记录数
     * <AUTHOR>
     * @date 2019年11月11日
     */
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<FilingEquipmentUseRegistration> filingEquipmentUseRegistrationList) {
        return this.mapper.insertBatch(filingEquipmentUseRegistrationList);
    }
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public Integer addFling(FilingEquipmentRegistrationForm filingEquipmentRegistrationForm) {
        filingEquipmentRegistrationForm.setUpdateTime(new Date());
        return this.mapper.insertApplication(filingEquipmentRegistrationForm);
    }

    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public String code(FilingEquipmentRegistrationForm filingEquipmentRegistrationForm) {
        //获取当前日期
        Calendar cal = Calendar.getInstance();
        //编号第四五位年份
        int year = cal.get(Calendar.YEAR) % 100;
        //编号第六七位月份
        int month = cal.get(Calendar.MONTH) + 1;
        //月份数字 字符串化
        String monthStr = new DecimalFormat("00").format(month);
        Code code = codeService.getCode(codeType, filingEquipmentRegistrationForm.getEquipmentType());
        try {
            //编号序列化
            String codeStr = new DecimalFormat("0000").format(code.getValue());
            //更新编号，+1顺延
            code.setValue(code.getValue()+1);
            codeService.update(code);
            //拼接编号
            String deviceCode = "豫登" + filingEquipmentRegistrationForm.getRemarks() + filingEquipmentRegistrationForm.getEquipmentType() + year + monthStr + codeStr;
            FilingEquipmentUseRegistration equipmentRegistrationForm = new FilingEquipmentUseRegistration();
            equipmentRegistrationForm.setUseFilingCode(deviceCode);
            List<FilingEquipmentUseRegistration> list = mapper.select(equipmentRegistrationForm);
            //判断重复编号
            for (int i=1;list.size()>0;i++){
                codeStr = new DecimalFormat("0000").format(code.getValue());
                code.setValue(code.getValue()+1);
                codeService.update(code);
                deviceCode = "豫登" + filingEquipmentRegistrationForm.getRemarks() + filingEquipmentRegistrationForm.getEquipmentType() + year + monthStr + codeStr;
                equipmentRegistrationForm = new FilingEquipmentUseRegistration();
                equipmentRegistrationForm.setUseFilingCode(deviceCode);
                list = mapper.select(equipmentRegistrationForm);
            }
            return deviceCode;
        } catch (Exception e) {
            return "";
        }
    }
    /**
     * 生成使用登记编号新规则
     */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public String code2(FilingEquipmentRegistrationForm filingEquipmentRegistrationForm) {;
        SysUser user = ShiroUtils.getUser();
        //获取当前日期
        Calendar cal = Calendar.getInstance();
        //编号第四五位年份
        String year = "（"+cal.get(Calendar.YEAR)+"）";
        Code code = codeService.getCode2(codeType, filingEquipmentRegistrationForm.getEquipmentType(), filingEquipmentRegistrationForm.getEngineeringAreaCode());
        //Code code = codeService.getCode2(codeType, filingEquipmentRegistrationForm.getEquipmentType(), 410200L);
        try {
            //编号序列化
            String codeStr = "";
            if (null==code){
                Code code1 = new Code();
                code1.setType("use");
                code1.setDeviceType(filingEquipmentRegistrationForm.getEquipmentType());
                code1.setValue(1L);
                //code1.setProjectAreaCode(sysDept.getProjectAreaCode());
                code1.setProjectAreaCode(410200L);
                code1.setCreateBy(user.getName());
                code1.setCreateTime(DateUtils.getNowDate());
                codeService.add(code1);
                codeStr = new DecimalFormat("0000000").format(1);
            }else {
                codeStr = new DecimalFormat("0000000").format(code.getValue());
                //更新编号，+1顺延
                code.setValue(code.getValue()+1);
                codeService.update(code);
            }
            //拼接编号
            String deviceCode = filingEquipmentRegistrationForm.getEngineeringAreaCode()+filingEquipmentRegistrationForm.getEquipmentType()+year
                    + codeStr;
            FilingEquipmentUseRegistration equipmentRegistrationForm = new FilingEquipmentUseRegistration();
            equipmentRegistrationForm.setUseFilingCode(deviceCode);
            List<FilingEquipmentUseRegistration> list = mapper.select(equipmentRegistrationForm);
            //判断重复编号
            for (int i=1;list.size()>0;i++){
                codeStr = new DecimalFormat("0000000").format(code.getValue());
                code.setValue(code.getValue()+1);
                codeService.update(code);
                deviceCode = filingEquipmentRegistrationForm.getEngineeringAreaCode()+filingEquipmentRegistrationForm.getEquipmentType()+year + codeStr;
                //deviceCode = "410200"+filingEquipmentRegistrationForm.getEquipmentType()+year + codeStr;
                equipmentRegistrationForm = new FilingEquipmentUseRegistration();
                equipmentRegistrationForm.setUseFilingCode(deviceCode);
                list = mapper.select(equipmentRegistrationForm);
            }
            return deviceCode;
        } catch (Exception e) {
            return "";
        }
    }

    @Override
    public FilingEquipmentUseRegistration getByCode(String code) {
        return this.mapper.getByCode(code);
    }
}
