package com.tzstcl.filing.equipmentUse.service;

import com.tzstcl.base.service.BaseService;
import com.tzstcl.filing.equipmentUse.model.FilingEquipmentUseRegistration;
import com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationForm;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：xuchengjie
 * 日期：2019年11月11日
 * 说明：设备使用备案表Servic
 */
public interface FilingEquipmentUseRegistrationService extends BaseService<FilingEquipmentUseRegistration> {

    /**
    *
    * 批量增加设备使用备案表
    * @param filingEquipmentUseRegistrationList
    * <AUTHOR>
    * @date 2019年11月11日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<FilingEquipmentUseRegistration> filingEquipmentUseRegistrationList);

    Integer addFling(FilingEquipmentRegistrationForm filingEquipmentRegistrationForm);


    String code(FilingEquipmentRegistrationForm filingEquipmentRegistrationForm);
    String code2(FilingEquipmentRegistrationForm filingEquipmentRegistrationForm);

    FilingEquipmentUseRegistration getByCode(String code);
}
