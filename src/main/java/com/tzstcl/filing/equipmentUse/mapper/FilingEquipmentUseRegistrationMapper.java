package com.tzstcl.filing.equipmentUse.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import com.tzstcl.filing.equipmentUse.model.FilingEquipmentUseRegistration;
import com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationForm;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：xuchengjie
 * 日期：2019年11月11日
 * 说明：设备使用备案表Mapper
 */
@Mapper
public interface FilingEquipmentUseRegistrationMapper extends BaseMapper<FilingEquipmentUseRegistration>  {

    /**
    *
    * 批量增加设备使用备案表
    * @param filingEquipmentUseRegistrationList
    * <AUTHOR>
    * @date 2019年11月11日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<FilingEquipmentUseRegistration> filingEquipmentUseRegistrationList);
    Integer insertApplication(FilingEquipmentRegistrationForm filingEquipmentRegistrationForm);

    Integer getCode();

    void addCode(int i);

    FilingEquipmentUseRegistration getByCode(String code);
}