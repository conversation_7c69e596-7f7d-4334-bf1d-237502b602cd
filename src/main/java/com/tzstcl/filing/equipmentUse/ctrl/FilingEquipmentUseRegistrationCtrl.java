package com.tzstcl.filing.equipmentUse.ctrl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.filing.equipmentUse.model.FilingEquipmentUseRegistration;
import com.tzstcl.filing.equipmentUse.service.FilingEquipmentUseRegistrationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：xuchengjie
 * 日期：2019年11月11日
 * 说明：设备使用备案表Controller
 */
@Controller
@RequestMapping("/admin/filingEquipmentUseRegistration")
public class FilingEquipmentUseRegistrationCtrl extends BaseCtrl {

    @Autowired
    private FilingEquipmentUseRegistrationService filingEquipmentUseRegistrationService;

    /**
    * list页面导航
    * @return
    */
    @RequiresPermissions("filingEquipmentUseRegistration:view")
    @RequestMapping("/toList")
    public String toList() {
        return "admin/filing/equipmentUse/filingEquipmentUseRegistrationList";
    }

    /**
    * 获取查询的分页数据
    * @param filingEquipmentUseRegistration
    * @return
    */
    @RequestMapping("/list")
//    @RequiresPermissions("filingEquipmentUseRegistration:view")
    @ResponseBody
    public PageInfo<FilingEquipmentUseRegistration> list(FilingEquipmentUseRegistration filingEquipmentUseRegistration) {
        return  filingEquipmentUseRegistrationService.selectPage(filingEquipmentUseRegistration);
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"filingEquipmentUseRegistration:edite","filingEquipmentUseRegistration:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentUseRegistration" ,filingEquipmentUseRegistrationService.getOne(id));
        }
        return "admin/filing/equipmentUse/filingEquipmentUseRegistrationForm";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("filingEquipmentUseRegistration:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentUseRegistration" ,filingEquipmentUseRegistrationService.getOne(id));
        }
        return "admin/filing/equipmentUse/filingEquipmentUseRegistrationDetail";
    }

    /**
     * 新增
     * @param filingEquipmentUseRegistration
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"filingEquipmentUseRegistration:add"})
    @ResponseBody
    public AjaxResult save(@Valid FilingEquipmentUseRegistration filingEquipmentUseRegistration) {
         return toAjax(filingEquipmentUseRegistrationService.add(filingEquipmentUseRegistration));
    }

    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("filingEquipmentUseRegistration:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentUseRegistration" ,filingEquipmentUseRegistrationService.getOne(id));
        }
        return "admin/filing/equipmentUse/filingEquipmentUseRegistrationEdit";
    }

    /**
    * 更新
    * @param filingEquipmentUseRegistration
    * @return
    */
    @PostMapping("/update")
    @RequiresPermissions("filingEquipmentUseRegistration:edit")
    @ResponseBody
    public AjaxResult update(@Valid FilingEquipmentUseRegistration filingEquipmentUseRegistration) {
        return toAjax(filingEquipmentUseRegistrationService.update(filingEquipmentUseRegistration));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("filingEquipmentUseRegistration:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(filingEquipmentUseRegistrationService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("filingEquipmentUseRegistration:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",filingEquipmentUseRegistrationService.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param filingEquipmentUseRegistration
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"filingEquipmentUseRegistration:add", "filingEquipmentUseRegistration:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(FilingEquipmentUseRegistration filingEquipmentUseRegistration) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = filingEquipmentUseRegistration.getId();
        filingEquipmentUseRegistration.setId(null);
        List<FilingEquipmentUseRegistration> filingEquipmentUseRegistrationList = filingEquipmentUseRegistrationService.selectList(filingEquipmentUseRegistration);
        if(StringUtils.isNotEmpty(filingEquipmentUseRegistrationList)){
            if(filingEquipmentUseRegistrationList.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(filingEquipmentUseRegistrationList.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }

    @RequestMapping("/card")
    public String card(String code,Model model){
        model.addAttribute("filingUse",filingEquipmentUseRegistrationService.getByCode(code));
        return "admin/filing/publicInquiry/use";
    }
}