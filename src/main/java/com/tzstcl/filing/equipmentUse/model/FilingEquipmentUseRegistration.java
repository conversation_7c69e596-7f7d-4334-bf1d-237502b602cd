package com.tzstcl.filing.equipmentUse.model;

import com.tzstcl.base.model.BaseModel;

import java.awt.List;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 公司：天筑科技股份有限公司
 * 作者：xuchengjie
 * 日期：2019年11月11日
 * 说明：设备使用备案表实体类
 */
@Data
public class FilingEquipmentUseRegistration extends BaseModel<FilingEquipmentUseRegistration> implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
     *使用单位
     */
    private String useUnit;
    /**
     *使用单位联系人
     */
    private String userContact;
    /**
     *使用单位联系电话
     */
    private String userContactNumber;
    /**
     *产权单位
     */
    private String propertyUnit;
    /**
     *设备名称
     */
    private String deviceName;
    /**
     *规格型号
     */
    private String specificationModel;
    /**
     *出厂日期
     */
    private String factoryTime;
    /**
     *设备备案编号
     */
    private String deviceFilingCode;
    /**
     *工程名称
     */
    private String engineeringName;
    /**
     *项目经理
     */
    private String projectManager;
    /**
     *安装单位
     */
    private String installationUnit;
    /**
     *安装单位资质等级
     */
    private String installationUnitQualificationLevel;
    /**
     *现场安装负责人
     */
    private String siteInstallationLeader;
    /**
     *安装单位资质证号
     */
    private String installationUnitQualificationCertificateNumber;
    /**
     *安装单位安全许可证号
     */
    private String installationUnitLicenseSafetyPermitNumber;
    /**
     *安装开始时间
     */
    private Date installationStartTime;
    /**
     *安装终止时间
     */
    private Date installationEndTime;
    /**
     *检验检测单位
     */
    private String testingUnit;
    /**
     *检测日期
     */
    private Date testingDate;
    /**
     *检验检测负责人
     */
    private String testingLeadere;
    /**
     *联合验收日期
     */
    private Date jointInspectionDate;
    /**
     *首次安装高度，单位：米
     */
    private String firstInstallationHeight;
    /**
     *最终使用高度，单位：米
     */
    private String finalUseHeight;
    /**
     *设备类别，T塔式起重机，S施工升降机，W物料提升机，Q其他
     */
    private String equipmentType;
    /**
     *备案编号
     */
        private String useFilingCode;
    /**
     * 注销标识
     */
    private Integer cancellationStatus;
    /**
     * 登记时间
     */
    private String registrationDate;
}