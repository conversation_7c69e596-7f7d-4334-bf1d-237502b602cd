package com.tzstcl.filing.equipmentFiling.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年11月06日
 * 说明：起重机备案表Mapper
 */
@Mapper
public interface FilingEquipmentFilingMapper extends BaseMapper<FilingEquipmentFiling>  {

    /**
    *
    * 批量增加起重机备案表
    * @param filingEquipmentFilingList
    * <AUTHOR>
    * @date 2019年11月06日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<FilingEquipmentFiling> filingEquipmentFilingList);

    Integer insertApplication(FilingEquipmentFilingApplication filingEquipmentFilingApplication);

    FilingEquipmentFiling getByCode(String filingCode);

    Integer cancel(String filingCode);

    List<FilingEquipmentFiling> getList();
}