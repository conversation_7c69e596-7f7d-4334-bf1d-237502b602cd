package com.tzstcl.filing.equipmentFiling.model;

import com.tzstcl.base.model.BaseModel;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年11月06日
 * 说明：起重机备案表实体类
 */
@Data
public class FilingEquipmentFiling extends BaseModel<FilingEquipmentFiling> implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
     *产权单位名称
     */
    private String propertyUnit;
    /**
     *产权单位社会统一信用代码
     */
    private String propertyUnitCode;
    /**
     *设备名称
     */
    private String deviceName;
    /**
     *规格型号
     */
    private String specificationModel;
    /**
     *生产厂家
     */
    private String manufacturer;
    /**
     *出厂时间
     */
    private String factoryTime;
    /**
     *制造许可证编号
     */
    private String manufacturingLicenseNumber;
    /**
     *备案日期
     */
    private Date filingDate;
    /**
     *出厂编号
     */
    private String factoryLicenseNumber;
    /**
     *购买时间
     */
    private String purchaseTime;
    /**
     *产权单位地址
     */
    private String unitAddress;
    /**
     *法定代表人
     */
    private String legalRepresentative;
    /**
     *联系电话
     */
    private String legalRepresentativeContactNumber;
    /**
     *技术负责人
     */
    private String technicalDirector;
    /**
     *技术负责人联系方式
     */
    private String technicalDirectorContactNumber;
    /**
     *设备管理负责人
     */
    private String equipmentManager;
    /**
     *设备管理负责人联系方式
     */
    private String equipmentManagerContactNumber;
    /**
     * 设备类型
     */
    private String equipmentType;
    /**
     *起重重量
     */
    private String liftingWeight;
    /**
     *备案编号
     */
    private String filingCode;
    /**
     *注销状态，0未注销，1已注销
     */
    private Integer cancellationStatus;
    /**
     *产权单位所属区域编码
     */
    private Long unitAreaCode;
}
