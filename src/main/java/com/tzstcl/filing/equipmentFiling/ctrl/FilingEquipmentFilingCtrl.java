package com.tzstcl.filing.equipmentFiling.ctrl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling;
import com.tzstcl.filing.equipmentFiling.service.FilingEquipmentFilingService;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication;
import com.tzstcl.filing.equipmentFilingApplication.service.FilingEquipmentFilingApplicationService;
import com.tzstcl.filing.file.model.FilingFile;
import com.tzstcl.filing.file.service.FilingFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年11月06日
 * 说明：起重机备案表Controller
 */
@Controller
@RequestMapping("/admin/filingEquipmentFiling")
public class FilingEquipmentFilingCtrl extends BaseCtrl {

    @Autowired
    private FilingEquipmentFilingService filingEquipmentFilingService;
    @Autowired
    private FilingFileService filingFileService;
    @Autowired
    private FilingEquipmentFilingApplicationService filingEquipmentFilingApplicationService;

    /**
    * list页面导航
    * @return
    */
    @RequestMapping("/toList")
    public String toList() {
        return "admin/filing/equipmentFiling/filingEquipmentFilingList";
    }

    /**
    * 获取查询的分页数据
    * @param filingEquipmentFiling
    * @return
    */
    @RequestMapping("/list")
    @ResponseBody
    public PageInfo<FilingEquipmentFiling> list(FilingEquipmentFiling filingEquipmentFiling) {
        return  filingEquipmentFilingService.selectPage(filingEquipmentFiling);
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"filingEquipmentFiling:edite","filingEquipmentFiling:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentFiling" ,filingEquipmentFilingService.getOne(id));
        }
        return "admin/filing/equipmentFiling/filingEquipmentFilingForm";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("filingEquipmentFiling:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentFiling" ,filingEquipmentFilingService.getOne(id));
            model.addAttribute("filies",filingFileService.getFiles(id));
        }
        return "admin/filing/equipmentFiling/filingEquipmentFilingDetail";
    }

    /**
    * form页面导航 新增
    * @return
    */
    @RequiresPermissions("filingEquipmentFiling:add")
    @GetMapping("/add")
    public String toAdd() {
        return "admin/filing/equipmentFiling/filingEquipmentFilingAdd";
    }

    /**
     * 新增
     * @param filingEquipmentFiling
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"filingEquipmentFiling:add"})
    @ResponseBody
    public AjaxResult save(@Valid FilingEquipmentFiling filingEquipmentFiling) {
         return toAjax(filingEquipmentFilingService.add(filingEquipmentFiling));
    }
    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("filingEquipmentFiling:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentFiling" ,filingEquipmentFilingService.getOne(id));
        }
        return "admin/filing/equipmentFiling/filingEquipmentFilingEdit";
    }

    /**
    * 更新
    * @param filingEquipmentFiling
    * @return
    */
    @PostMapping("/update")
    @RequiresPermissions("filingEquipmentFiling:edit")
    @ResponseBody
    public AjaxResult update(@Valid FilingEquipmentFiling filingEquipmentFiling) {
        return toAjax(filingEquipmentFilingService.update(filingEquipmentFiling));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("filingEquipmentFiling:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(filingEquipmentFilingService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("filingEquipmentFiling:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",filingEquipmentFilingService.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param filingEquipmentFiling
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"filingEquipmentFiling:add", "filingEquipmentFiling:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(FilingEquipmentFiling filingEquipmentFiling) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = filingEquipmentFiling.getId();
        filingEquipmentFiling.setId(null);
        List<FilingEquipmentFiling> filingEquipmentFilingList = filingEquipmentFilingService.selectList(filingEquipmentFiling);
        if(StringUtils.isNotEmpty(filingEquipmentFilingList)){
            if(filingEquipmentFilingList.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(filingEquipmentFilingList.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }
    @ResponseBody
    @RequestMapping("/search")
    public AjaxResult search(String code){
        FilingEquipmentFiling filingEquipmentFiling = filingEquipmentFilingService.getByCode(code);
        if (filingEquipmentFiling!= null){
            FilingEquipmentFilingApplication filingApplication = filingEquipmentFilingApplicationService.getByFilingCode(code);
            if (null!=filingApplication&&null!=filingApplication.getUnitAreaCode()){
                filingEquipmentFiling.setUnitAreaCode(filingApplication.getUnitAreaCode());
            }
            return success("操作成功",filingEquipmentFiling);
        }else{
            return error("操作失败");
        }
    }

    @ResponseBody
    @RequestMapping("/indexList")
    public List<FilingEquipmentFiling> indexList(){
        return filingEquipmentFilingService.getList();
    }
}
