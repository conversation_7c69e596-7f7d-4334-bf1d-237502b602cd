package com.tzstcl.filing.equipmentFiling.service;

import com.tzstcl.base.service.BaseService;
import com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年11月06日
 * 说明：起重机备案表Servic
 */
public interface FilingEquipmentFilingService extends BaseService<FilingEquipmentFiling> {

    /**
    *
    * 批量增加起重机备案表
    * @param filingEquipmentFilingList
    * <AUTHOR>
    * @date 2019年11月06日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<FilingEquipmentFiling> filingEquipmentFilingList);

    Integer addFling(FilingEquipmentFilingApplication filingEquipmentFilingApplication);

    FilingEquipmentFiling getByCode(String code);

    int cancel(String filingCode);

    List<FilingEquipmentFiling> getList();
}