package com.tzstcl.filing.equipmentFiling.service.impl;

import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.filing.equipmentFiling.service.FilingEquipmentFilingService;
import com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling;
import com.tzstcl.filing.equipmentFiling.mapper.FilingEquipmentFilingMapper;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年11月06日
 * 说明：起重机备案表ServiceImpl
 */
@Service
public class FilingEquipmentFilingServiceImpl extends BaseServiceImpl<FilingEquipmentFilingMapper,FilingEquipmentFiling> implements FilingEquipmentFilingService{

    /**
    *
    * 批量增加起重机备案表
    * @param filingEquipmentFilingList
    * <AUTHOR>
    * @date 2019年11月06日
    * @return Integer 插入的记录数
    */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<FilingEquipmentFiling> filingEquipmentFilingList){return this.mapper.insertBatch(filingEquipmentFilingList);}

    @Override
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    public Integer addFling(FilingEquipmentFilingApplication filingEquipmentFilingApplication) {
        filingEquipmentFilingApplication.setUpdateTime(new Date());
        return this.mapper.insertApplication(filingEquipmentFilingApplication);
    }

    @Override
    public FilingEquipmentFiling getByCode(String code) {
        return this.mapper.getByCode(code);
    }

    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public int cancel(String filingCode) {
        return this.mapper.cancel(filingCode);
    }

    @Override
    public List<FilingEquipmentFiling> getList() {
        return mapper.getList();
    }

}