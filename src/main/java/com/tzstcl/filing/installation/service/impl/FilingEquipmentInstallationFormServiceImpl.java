package com.tzstcl.filing.installation.service.impl;

import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellation;
import com.tzstcl.filing.installation.service.FilingEquipmentInstallationFormService;
import com.tzstcl.filing.installation.model.FilingEquipmentInstallationForm;
import com.tzstcl.filing.installation.mapper.FilingEquipmentInstallationFormMapper;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTFonts;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年12月04日
 * 说明：设备安装登记流程表ServiceImpl
 */
@Service
public class FilingEquipmentInstallationFormServiceImpl extends BaseServiceImpl<FilingEquipmentInstallationFormMapper, FilingEquipmentInstallationForm> implements FilingEquipmentInstallationFormService {
    @Value("${project.uploadFileAbsolutePath}")
    private String filePath;

    /**
     * 批量增加设备安装登记流程表
     *
     * @param filingEquipmentInstallationFormList
     * @return Integer 插入的记录数
     * <AUTHOR>
     * @date 2019年12月04日
     */
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<FilingEquipmentInstallationForm> filingEquipmentInstallationFormList) {
        return this.mapper.insertBatch(filingEquipmentInstallationFormList);
    }

    @Override
    public File file(Long id) throws IOException {
        FilingEquipmentInstallationForm filingEquipmentInstallationForm = getOne(id);
        //编号
        String number = filingEquipmentInstallationForm.getEquipmentFilingCode();
        //回复时间
        String auditTime = new SimpleDateFormat("yyyy年MM月dd日").format(filingEquipmentInstallationForm.getAuditTime());
        //安装单位
        String installCompany = filingEquipmentInstallationForm.getInstallationUnit();
        //安装时间
        String installDate = new SimpleDateFormat("yyyy年MM月dd日").format(filingEquipmentInstallationForm.getInstallationTime());
        //申请日期
        String applicationDate = new SimpleDateFormat("yyyy年MM月dd日").format(filingEquipmentInstallationForm.getApplicationDate());
        //工地名
        String constructionSite = filingEquipmentInstallationForm.getConstructionSite();
        //设备名
        String deviceName = filingEquipmentInstallationForm.getDeviceName() + "  " + filingEquipmentInstallationForm.getSpecificationModel();
        XWPFDocument docxDocument = new XWPFDocument();
        //设置段落
        // TODO: 2019/12/13 抬头
        XWPFParagraph paragraphX1 = docxDocument.createParagraph();
        paragraphX1.setAlignment(ParagraphAlignment.CENTER);//段落整体居中
        //设置文本内容
        XWPFRun runX1 = paragraphX1.createRun();
        String value = "建筑起重机械设备安装告知书";
        //设置属性
        runX1.setText(value);
        runX1.setFontFamily("黑体");//字体
        runX1.setBold(true);//加粗
        runX1.setFontSize(18);//字体大小

        //设置段落
        // TODO: 2019/12/13 正文
        XWPFParagraph paragraphX2 = docxDocument.createParagraph();
        paragraphX2.setAlignment(ParagraphAlignment.LEFT);//段落整体居中
        //设置文本内容
        XWPFRun runX2 = paragraphX2.createRun();

        String value2 = "    我单位拟于" + installDate + "在" + constructionSite +
                "对一台" + deviceName + "进行安装，其安装相关资料已经施工总承包单位、监理单位审核，特此告知。";
        //设置属性
        runX2.setText(value2);
//        runX2.setFontFamily("仿宋_GB2312",FontCharRange.cs);//字体
        runX2.setFontFamily("仿宋");
        runX2.setBold(false);//加粗
        runX2.setFontSize(16);//字体大小
        // TODO: 2019/12/13 空段落
        XWPFParagraph null1 = docxDocument.createParagraph();
        null1.setAlignment(ParagraphAlignment.LEFT);//段落整体居中
        XWPFRun null1Run = null1.createRun();
        null1Run.setText("");
        XWPFParagraph null2 = docxDocument.createParagraph();
        null2.setAlignment(ParagraphAlignment.LEFT);//段落整体居中
        XWPFRun null1Run2 = null2.createRun();
        null1Run2.setText("");
        // 设置段落
        // TODO: 2019/12/13 盖章
        XWPFParagraph paragraphX3 = docxDocument.createParagraph();
        paragraphX3.setAlignment(ParagraphAlignment.CENTER);//段落整体居中
        //设置文本内容
        XWPFRun runX3 = paragraphX3.createRun();
        String value3 ="                    单位（公章)";
        //设置属性x
        runX3.setText(value3);
        runX3.setBold(false);//加粗
        runX3.setFontSize(16);//字体大小


        // 设置段落
        // TODO: 2019/12/13  申请日期
        XWPFParagraph paragraphX4 = docxDocument.createParagraph();
        paragraphX4.setAlignment(ParagraphAlignment.CENTER);//段落整体居中
        //设置文本内容
        XWPFRun runX4 = paragraphX4.createRun();
        String value4 =  "                    "+applicationDate;
        //设置属性
        runX4.setText(value4);
        runX4.setBold(false);//加粗
        runX4.setFontSize(16);//字体大小

        // TODO: 2019/12/13 空段落
        XWPFParagraph null3 = docxDocument.createParagraph();
        null3.setAlignment(ParagraphAlignment.LEFT);//段落整体居中
        XWPFRun null3Run = null3.createRun();
        null3Run.setText("");
        XWPFParagraph null4 = docxDocument.createParagraph();
        null4.setAlignment(ParagraphAlignment.LEFT);//段落整体居中
        XWPFRun null1Run4 = null4.createRun();
        null1Run4.setText("");


        XWPFParagraph paragraphX0 = docxDocument.createParagraph();
        paragraphX0.setAlignment(ParagraphAlignment.CENTER);//段落整体居中
        //设置文本内容
        XWPFRun runX0 = paragraphX0.createRun();
        String value0 = "------------------------------------------------------------------";
        //设置属性
        runX0.setText(value0);
        runX0.setBold(false);//加粗
        runX0.setFontSize(18);//字体大小

        XWPFParagraph paragraphX5 = docxDocument.createParagraph();
        paragraphX5.setAlignment(ParagraphAlignment.RIGHT);//段落整体居中
        //设置文本内容
        XWPFRun runX5 = paragraphX5.createRun();
        String value5 = "编号："+number;
        //设置属性
        runX5.setText(value5);
        runX5.setFontFamily("宋体");//字体
        runX5.setBold(true);//加粗
        runX5.setFontSize(14);//字体大小

        XWPFParagraph paragraphX6 = docxDocument.createParagraph();
        paragraphX6.setAlignment(ParagraphAlignment.CENTER);//段落整体居中
        //设置文本内容
        XWPFRun runX6 = paragraphX6.createRun();
        String value6 = "建筑起重机械设备安装告知回复书";
        //设置属性
        runX6.setText(value6);
        runX6.setFontFamily("宋体");//字体
        runX6.setBold(true);//加粗
        runX6.setFontSize(18);//字体大小

        XWPFParagraph paragraph7 = docxDocument.createParagraph();
        paragraph7.setAlignment(ParagraphAlignment.LEFT);
        XWPFRun run7 = paragraph7.createRun();
        String value7 = installCompany+":";
        run7.setText(value7);
        run7.setBold(false);
        run7.setFontFamily("宋体");//字体
        run7.setFontSize(14);//字体大小

        XWPFParagraph paragraph8 = docxDocument.createParagraph();
        paragraph8.setAlignment(ParagraphAlignment.LEFT);
        XWPFRun run8 = paragraph8.createRun();
        String value8 = "    你单位关于拟在"+constructionSite+"对一台"+deviceName+"进行安装的告知书及相关材料已收悉，所报资料内容齐全。请按照安装方案及有关安全要求进行安装。" +
                "此告知单自下达之日起1日内有效，过期作废。要求建设、施工、监理等相关单位对起重机械安拆作业期间进行严格管理，切实落实安全责任。";
        run8.setText(value8);
        run8.setBold(false);
        run8.setFontFamily("宋体");//字体
        run8.setFontSize(14);//字体大小

        // TODO: 2019/12/13 空段落
        XWPFParagraph null5 = docxDocument.createParagraph();
        null5.setAlignment(ParagraphAlignment.LEFT);//段落整体居中
        XWPFRun null5Run = null5.createRun();
        null5Run.setText("");
        XWPFParagraph null6 = docxDocument.createParagraph();
        null6.setAlignment(ParagraphAlignment.LEFT);//段落整体居中
        XWPFRun null6Run= null6.createRun();
        null6Run.setText("");

        XWPFParagraph paragraph9 = docxDocument.createParagraph();
        paragraph9.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun run9 = paragraph9.createRun();
        String value9 = "                    单位（公章)";
        run9.setText(value9);
        run9.setBold(false);
        run9.setFontFamily("宋体");//字体
        run9.setFontSize(14);//字体大小

        XWPFParagraph paragraph10 = docxDocument.createParagraph();
        paragraph10.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun run10 = paragraph10.createRun();
        String value10 ="                    "+auditTime;
        run10.setText(value10);
        run10.setBold(false);
        run10.setFontFamily("宋体");//字体
        run10.setFontSize(14);//字体大小

        String fileName = StringUtils.uuid() + ".docx";
        File importDir = new File(filePath + fileName);
        if (importDir.exists()) {
            importDir.delete();
        }
        FileOutputStream out = new FileOutputStream(filePath + fileName);
        docxDocument.write(out);
        out.close();
        return importDir;
    }
    @Override
    public File unistallfile(Long id) throws IOException {

        FilingEquipmentInstallationForm filingEquipmentInstallationForm = getOne(id);
        //编号
        String number = filingEquipmentInstallationForm.getEquipmentFilingCode();
        //回复时间
        String auditTime = new SimpleDateFormat("yyyy年MM月dd日").format(filingEquipmentInstallationForm.getAuditTime());
        //安装单位
        String installCompany = filingEquipmentInstallationForm.getInstallationUnit();
        //安装时间
        String installDate = new SimpleDateFormat("yyyy年MM月dd日").format(filingEquipmentInstallationForm.getInstallationTime());
        //申请日期
        String applicationDate = new SimpleDateFormat("yyyy年MM月dd日").format(filingEquipmentInstallationForm.getApplicationDate());
        //工地名
        String constructionSite = filingEquipmentInstallationForm.getConstructionSite();
        //设备名
        String deviceName = filingEquipmentInstallationForm.getDeviceName() + "  " + filingEquipmentInstallationForm.getSpecificationModel();
        XWPFDocument docxDocument = new XWPFDocument();
        //设置段落
        // TODO: 2019/12/13 抬头
        XWPFParagraph paragraphX1 = docxDocument.createParagraph();
        paragraphX1.setAlignment(ParagraphAlignment.CENTER);//段落整体居中
        //设置文本内容
        XWPFRun runX1 = paragraphX1.createRun();
        String value = "建筑起重机械设备拆卸告知书";
        //设置属性
        runX1.setText(value);
        runX1.setFontFamily("黑体");//字体
        runX1.setBold(true);//加粗
        runX1.setFontSize(18);//字体大小

        //设置段落
        // TODO: 2019/12/13 正文
        XWPFParagraph paragraphX2 = docxDocument.createParagraph();
        paragraphX2.setAlignment(ParagraphAlignment.LEFT);//段落整体居中
        //设置文本内容
        XWPFRun runX2 = paragraphX2.createRun();

        String value2 = "    我单位拟于" + installDate + "在" + constructionSite +
                "对一台" + deviceName + "进行拆卸，其拆卸相关资料已经施工总承包单位、监理单位审核，特此告知。";
        //设置属性
        runX2.setText(value2);
//        runX2.setFontFamily("仿宋_GB2312",FontCharRange.cs);//字体
        runX2.setFontFamily("仿宋");
        runX2.setBold(false);//加粗
        runX2.setFontSize(16);//字体大小
        // TODO: 2019/12/13 空段落
        XWPFParagraph null1 = docxDocument.createParagraph();
        null1.setAlignment(ParagraphAlignment.LEFT);//段落整体居中
        XWPFRun null1Run = null1.createRun();
        null1Run.setText("");
        XWPFParagraph null2 = docxDocument.createParagraph();
        null2.setAlignment(ParagraphAlignment.LEFT);//段落整体居中
        XWPFRun null1Run2 = null2.createRun();
        null1Run2.setText("");
        // 设置段落
        // TODO: 2019/12/13 盖章
        XWPFParagraph paragraphX3 = docxDocument.createParagraph();
        paragraphX3.setAlignment(ParagraphAlignment.CENTER);//段落整体居中
        //设置文本内容
        XWPFRun runX3 = paragraphX3.createRun();
        String value3 = "                    单位（公章)";
        //设置属性
        runX3.setText(value3);
        runX3.setBold(false);//加粗
        runX3.setFontSize(16);//字体大小

        // 设置段落
        // TODO: 2019/12/13  申请日期
        XWPFParagraph paragraphX4 = docxDocument.createParagraph();
        paragraphX4.setAlignment(ParagraphAlignment.CENTER);//段落整体居中
        //设置文本内容
        XWPFRun runX4 = paragraphX4.createRun();
        String value4 =  "                    "+applicationDate;
        //设置属性
        runX4.setText(value4);
        runX4.setBold(false);//加粗
        runX4.setFontSize(16);//字体大小

        // TODO: 2019/12/13 空段落
        XWPFParagraph null8 = docxDocument.createParagraph();
        null8.setAlignment(ParagraphAlignment.LEFT);//段落整体居中
        XWPFRun null8Run = null8.createRun();
        null8Run.setText("");
        XWPFParagraph null9 = docxDocument.createParagraph();
        null9.setAlignment(ParagraphAlignment.LEFT);//段落整体居中
        XWPFRun null9Run = null9.createRun();
        null9Run.setText("");


        XWPFParagraph paragraphX0 = docxDocument.createParagraph();
        paragraphX0.setAlignment(ParagraphAlignment.CENTER);//段落整体居中
        //设置文本内容
        XWPFRun runX0 = paragraphX0.createRun();
        String value0 = "------------------------------------------------------------------";
        //设置属性
        runX0.setText(value0);
        runX0.setBold(false);//加粗
        runX0.setFontSize(18);//字体大小

        XWPFParagraph paragraph5 = docxDocument.createParagraph();
        paragraph5.setAlignment(ParagraphAlignment.RIGHT);//段落整体居中
        //设置文本内容
        XWPFRun run5 = paragraph5.createRun();
        String valueX5 = "编号："+number;
        //设置属性
        run5.setText(valueX5);
        run5.setFontFamily("宋体");//字体
        run5.setBold(true);//加粗
        run5.setFontSize(14);//字体大小

        XWPFParagraph paragraph6 = docxDocument.createParagraph();
        paragraph6.setAlignment(ParagraphAlignment.CENTER);//段落整体居中
        //设置文本内容
        XWPFRun run6 = paragraph6.createRun();
        String valueX6 = "建筑起重机械设备拆卸告知回复书";
        //设置属性
        run6.setText(valueX6);
        run6.setFontFamily("宋体");//字体
        run6.setBold(true);//加粗
        run6.setFontSize(18);//字体大小

        XWPFParagraph paragraph7 = docxDocument.createParagraph();
        paragraph7.setAlignment(ParagraphAlignment.LEFT);
        XWPFRun run7 = paragraph7.createRun();
        String value7 = installCompany+":";
        run7.setText(value7);
        run7.setBold(false);
        run7.setFontFamily("宋体");//字体
        run7.setFontSize(14);//字体大小

        XWPFParagraph paragraph8 = docxDocument.createParagraph();
        paragraph8.setAlignment(ParagraphAlignment.LEFT);
        XWPFRun run8 = paragraph8.createRun();
        String value8 = "    你单位关于拟在"+constructionSite+"对一台"+deviceName+"进行拆卸的告知书及相关材料已收悉，所报资料内容齐全。请按照拆卸方案及有关安全要求进行拆卸。" +
                "此告知单自下达之日起1日内有效，过期作废。要求建设、施工、监理等相关单位对起重机械安拆作业期间进行严格管理，切实落实安全责任。";
        run8.setText(value8);
        run8.setBold(false);
        run8.setFontFamily("宋体");//字体
        run8.setFontSize(14);//字体大小

        // TODO: 2019/12/13 空段落
        XWPFParagraph null5 = docxDocument.createParagraph();
        null5.setAlignment(ParagraphAlignment.LEFT);//段落整体居中
        XWPFRun null5Run = null5.createRun();
        null5Run.setText("");
        XWPFParagraph null6 = docxDocument.createParagraph();
        null6.setAlignment(ParagraphAlignment.LEFT);//段落整体居中
        XWPFRun null6Run= null6.createRun();
        null6Run.setText("");

        XWPFParagraph paragraph9 = docxDocument.createParagraph();
        paragraph9.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun run9 = paragraph9.createRun();
        String value9 = "                    单位（公章)";
        run9.setText(value9);
        run9.setBold(false);
        run9.setFontFamily("宋体");//字体
        run9.setFontSize(14);//字体大小

        XWPFParagraph paragraph10 = docxDocument.createParagraph();
        paragraph10.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun run10 = paragraph10.createRun();
        String value10 ="                    "+auditTime;
        run10.setText(value10);
        run10.setBold(false);
        run10.setFontFamily("宋体");//字体
        run10.setFontSize(14);//字体大小

        String fileName = StringUtils.uuid() + ".docx";
        File importDir = new File(filePath + fileName);
        if (importDir.exists()) {
            importDir.delete();
        }
        FileOutputStream out = new FileOutputStream(filePath + fileName);
        docxDocument.write(out);
        out.close();
        return importDir;
    }

    @Override
    public List<FilingEquipmentInstallationForm> selectListByAreaCode(List<Long> areaList, String auditStatus,String workType) {
        return mapper.selectListByAreaCode(areaList,auditStatus,workType);
    }

    @Override
    public List<FilingEquipmentInstallationForm> selectListByCondition(String year, List<Long> areaList, String workType) {
        return mapper.selectListByCondition(year,areaList,workType);
    }

    @Override
    public FilingEquipmentInstallationForm getOneByFilingCode(String equipmentFilingCode,String workType) {
        return mapper.getOneByFilingCode(equipmentFilingCode,workType);
    }

    @Override
    public List<FilingEquipmentInstallationForm> selectListGroupByFilingCode(String year, List<Long> areaList, String workType) {
        return mapper.selectListGroupByFilingCode(year,areaList,workType);
    }

    @Override
    public FilingEquipmentInstallationForm selectByFilingCode(String equipmentFilingCode) {
        return mapper.selectByFilingCode(equipmentFilingCode);
    }
}
