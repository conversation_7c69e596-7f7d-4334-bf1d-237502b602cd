package com.tzstcl.filing.installation.service;

import com.tzstcl.base.service.BaseService;
import com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellation;
import com.tzstcl.filing.installation.model.FilingEquipmentInstallationForm;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年12月04日
 * 说明：设备安装登记流程表Servic
 */
public interface FilingEquipmentInstallationFormService extends BaseService<FilingEquipmentInstallationForm> {

    /**
    *
    * 批量增加设备安装登记流程表
    * @param filingEquipmentInstallationFormList
    * <AUTHOR>
    * @date 2019年12月04日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<FilingEquipmentInstallationForm> filingEquipmentInstallationFormList);

    File file(Long id) throws IOException;
    File unistallfile(Long id) throws IOException;

    List<FilingEquipmentInstallationForm> selectListByAreaCode(List<Long> areaList, String auditStatus,String workType);

    List<FilingEquipmentInstallationForm> selectListByCondition(String year, List<Long> areaList, String workType);

    FilingEquipmentInstallationForm getOneByFilingCode(String equipmentFilingCode,String workType);

    List<FilingEquipmentInstallationForm> selectListGroupByFilingCode(String year, List<Long> areaList, String s);

    FilingEquipmentInstallationForm selectByFilingCode(String equipmentFilingCode);
}
