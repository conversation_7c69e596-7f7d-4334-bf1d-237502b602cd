package com.tzstcl.filing.installation.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import com.tzstcl.filing.installation.model.FilingEquipmentInstallationForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年12月04日
 * 说明：设备安装登记流程表Mapper
 */
@Mapper
public interface FilingEquipmentInstallationFormMapper extends BaseMapper<FilingEquipmentInstallationForm>  {

    /**
    *
    * 批量增加设备安装登记流程表
    * @param filingEquipmentInstallationFormList
    * <AUTHOR>
    * @date 2019年12月04日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<FilingEquipmentInstallationForm> filingEquipmentInstallationFormList);

    List<FilingEquipmentInstallationForm> selectListByAreaCode(List<Long> areaList, String auditStatus,String workType);

    List<FilingEquipmentInstallationForm> selectListByCondition(String year, List<Long> areaList, String workType);

    FilingEquipmentInstallationForm getOneByFilingCode(@Param("equipmentFilingCode") String equipmentFilingCode, @Param("workType") String workType);

    List<FilingEquipmentInstallationForm> selectListGroupByFilingCode(String year, List<Long> areaList, String workType);

    FilingEquipmentInstallationForm selectByFilingCode(String equipmentFilingCode);
}
