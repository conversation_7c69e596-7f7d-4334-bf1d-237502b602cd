package com.tzstcl.filing.installation.ctrl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.tzstcl.archetype.detail.model.SysNewsDetails;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.*;
import com.tzstcl.filing.access.model.AccessKey;
import com.tzstcl.filing.access.service.IAccessKeyService;
import com.tzstcl.filing.annotation.VerifySign;
import com.tzstcl.filing.area.mapper.AreaMapper;
import com.tzstcl.filing.area.model.Area;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication;
import com.tzstcl.filing.equipmentFilingApplication.service.FilingEquipmentFilingApplicationService;
import com.tzstcl.filing.equipmentUseCancellation.model.FilingEquipmentUseCancellation;
import com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellation;
import com.tzstcl.filing.equipmentfilingcancellation.service.FilingEquipmentFilingCancellationService;
import com.tzstcl.filing.file.service.FilingFileService;
import com.tzstcl.filing.installation.model.FilingEquipmentInstallationForm;
import com.tzstcl.filing.installation.model.FilingEquipmentInstallationFormVo;
import com.tzstcl.filing.installation.service.FilingEquipmentInstallationFormService;
import com.tzstcl.framework.shiro.ShiroUtils;
import com.tzstcl.sys.user.model.SysUser;
import com.tzstcl.sys.user.service.impl.SysDictServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.sound.midi.Instrument;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年12月04日
 * 说明：设备安装登记流程表Controller
 */
@Controller
@RequestMapping("/admin/filingEquipmentInstallationForm")
public class FilingEquipmentInstallationFormCtrl extends BaseCtrl {

    @Autowired
    private FilingEquipmentInstallationFormService filingEquipmentInstallationFormService;
    @Autowired
    private FilingFileService filingFileService;
    @Autowired
    private IAccessKeyService accessKeyService;
    @Autowired
    private SysDictServiceImpl sysDictService;
    @Autowired
    private FilingEquipmentFilingApplicationService filingEquipmentFilingApplicationService;
    @Autowired
    private FilingEquipmentFilingCancellationService filingEquipmentFilingCancellationService;
    @Autowired
    private AreaMapper areaMapper;
    /**
    * list页面导航
    * @return
    */
    @RequiresPermissions("install:view")
    @RequestMapping("/toList")
    public String toList(Model model) {
        model.addAttribute("dict",sysDictService.findByType("city"));
        model.addAttribute("userId", ShiroUtils.getUser().getMobile());
        return "admin/filing/installation/filingEquipmentInstallationFormList";
    }

    /**
    * 获取查询的分页数据
    * @param filingEquipmentInstallationForm
    * @return
    */
    @RequestMapping("/list")
    @RequiresPermissions("install:view")
    @ResponseBody
    public PageInfo<FilingEquipmentInstallationForm> list(FilingEquipmentInstallationForm filingEquipmentInstallationForm) {
//        String userType = ShiroUtils.getUser().getUserType();
//        //企业用户只查询本企业申请的备案信息
//        if (StringUtils.isNotEmpty(userType)&&userType.equals("0")){
//            filingEquipmentInstallationForm.setUserId(ShiroUtils.getUser().getMobile());
//        }
        filingEquipmentInstallationForm.setWorkType("0");
        return  filingEquipmentInstallationFormService.selectPage(filingEquipmentInstallationForm);
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"install:edite","install:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        SysUser user = ShiroUtils.getUser();
        model.addAttribute("user",user);
        if(null != id){
            model.addAttribute("filingEquipmentInstallationForm" ,filingEquipmentInstallationFormService.getOne(id));
        }
        model.addAttribute("id", SnowflakeIdWorker.getInstance().nextId());
        model.addAttribute("userId", ShiroUtils.getUser().getMobile());
        model.addAttribute("dict",sysDictService.findByType("city"));
        return "admin/filing/installation/filingEquipmentInstallationFormForm";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("install:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            SysUser user = ShiroUtils.getUser();
            model.addAttribute("user",user);
            model.addAttribute("filingEquipmentInstallationForm" ,filingEquipmentInstallationFormService.getOne(id));
            model.addAttribute("files",filingFileService.getFiles(id));
            model.addAttribute("dict",sysDictService.findByType("city"));
        }
        return "admin/filing/installation/filingEquipmentInstallationFormDetail";
    }

    /**
     * 新增
     * @param filingEquipmentInstallationForm
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"install:add"})
    @ResponseBody
    public AjaxResult save(@Valid FilingEquipmentInstallationForm filingEquipmentInstallationForm) {
        FilingEquipmentFilingApplication application = filingEquipmentFilingApplicationService.selectByFilingCode(filingEquipmentInstallationForm.getEquipmentFilingCode());
        if (null==application){
            return error("该设备不存在，无法安装！");
        }
        FilingEquipmentFilingCancellation cancellation = filingEquipmentFilingCancellationService.getOneByCode(filingEquipmentInstallationForm.getEquipmentFilingCode());
        if (null!=cancellation&&"1".equals(cancellation.getAuditStatus())){
            return error("该设备已注销，添加失败！");
        }
        FilingEquipmentInstallationForm installationForm = filingEquipmentInstallationFormService.getOneByFilingCode(filingEquipmentInstallationForm.getEquipmentFilingCode(),"0");
        if (null!=installationForm){
            return error("该设备已申请安装，请勿重复添加！");
        }
        filingEquipmentInstallationForm.setApplicationDate(new Date());
        filingEquipmentInstallationForm.setCreateTime(new Date());
        filingEquipmentInstallationForm.setCreateBy(ShiroUtils.getUserId()+"");
        filingEquipmentInstallationForm.setWorkType("0");
         return toAjax(filingEquipmentInstallationFormService.add(filingEquipmentInstallationForm));
    }

    /**
     * 设备安装数据推送
     * @return
     */
    @CrossOrigin
    @RequestMapping("/addInstallationForm")
    @ResponseBody
    public AjaxResult addInstallationForm(@RequestParam("appId") String appId,
                                          @RequestParam("appKey") String appKey,
                                          @RequestBody FilingEquipmentInstallationFormVo equipmentInstallationForm

    ) {
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key){
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())){
            return AjaxResult.error("appKey输入有误");
        }
        //判断该数据是否重复
        FilingEquipmentInstallationForm installationForm1 = new FilingEquipmentInstallationForm();
        installationForm1.setEquipmentFilingCode(equipmentInstallationForm.getEquipmentFilingCode());
        List<FilingEquipmentInstallationForm> list = filingEquipmentInstallationFormService.selectList(installationForm1);
        if (list.size()>0){
            return AjaxResult.error("ID为"+equipmentInstallationForm.getBusinessId()+"的数据已存在,请勿重复推送");
        }

        FilingEquipmentInstallationForm installationForm = new FilingEquipmentInstallationForm();
        installationForm.setBusinessId(equipmentInstallationForm.getBusinessId());
        installationForm.setWorkType(equipmentInstallationForm.getWorkType());
        installationForm.setAuditStatus(equipmentInstallationForm.getAuditStatus());
        installationForm.setEquipmentFilingCode(equipmentInstallationForm.getEquipmentFilingCode());
        if (null!=equipmentInstallationForm.getDeviceName()){
            installationForm.setDeviceName(equipmentInstallationForm.getDeviceName());
        }
        installationForm.setSpecificationModel(equipmentInstallationForm.getSpecificationModel());
        installationForm.setEngineeringName(equipmentInstallationForm.getEngineeringName());
        installationForm.setConstructionSite(equipmentInstallationForm.getConstructionSite());
        installationForm.setJobContent(equipmentInstallationForm.getJobContent());
        installationForm.setInstallationUnit(equipmentInstallationForm.getInstallationUnit());
        installationForm.setInstallationTime(DateUtils.parseDate(equipmentInstallationForm.getInstallationTime()));
        try {
            installationForm.setInstallationTime(new SimpleDateFormat("yyyy-MM-dd").parse(equipmentInstallationForm.getInstallationTime()));
            installationForm.setApplicationDate(new SimpleDateFormat("yyyy-MM-dd").parse(equipmentInstallationForm.getApplicationDate()));
        }catch (Exception e){
            e.printStackTrace();
            return AjaxResult.error("请输入正确的日期格式");
        }
        installationForm.setInstallationHeight(equipmentInstallationForm.getInstallationHeight());
        installationForm.setInstallationLeader(equipmentInstallationForm.getInstallationLeader());
        installationForm.setInstallationLeaderContactNumber(equipmentInstallationForm.getInstallationLeaderContactNumber());
        installationForm.setTechLeader(equipmentInstallationForm.getTechLeader());
        installationForm.setTechLeaderContactNumber(equipmentInstallationForm.getTechLeaderContactNumber());
        installationForm.setSiteLeader(equipmentInstallationForm.getSiteLeader());
        installationForm.setSiteLeaderContactNumber(equipmentInstallationForm.getSiteLeaderContactNumber());
        installationForm.setNotificationBookFilePath(equipmentInstallationForm.getNotificationBookFilePath());
        installationForm.setEquipmentFilingLicenseFilePath(equipmentInstallationForm.getEquipmentFilingLicenseFilePath());
        installationForm.setAuditFormFilePath(equipmentInstallationForm.getAuditFormFilePath());
        installationForm.setQualifiLicenseFilePath(equipmentInstallationForm.getQualifiLicenseFilePath());
        installationForm.setLicenseSafetyPermitFilePath(equipmentInstallationForm.getLicenseSafetyPermitFilePath());
        installationForm.setOperatorsListFilePath(equipmentInstallationForm.getOperatorsListFilePath());
        installationForm.setOperatorsLicenseFilePath(equipmentInstallationForm.getOperatorsLicenseFilePath());
        installationForm.setSpecialConstructionSchemeFilePath(equipmentInstallationForm.getSpecialConstructionSchemeFilePath());
        installationForm.setInstallationContractFilePath(equipmentInstallationForm.getInstallationContractFilePath());
        installationForm.setSecurityProtocolFilePath(equipmentInstallationForm.getSecurityProtocolFilePath());
        installationForm.setEmergencyRescuePlanFilePath(equipmentInstallationForm.getEmergencyRescuePlanFilePath());
        installationForm.setEquipmentQualificationCertificateFilePath(equipmentInstallationForm.getEquipmentQualificationCertificateFilePath());
        installationForm.setOperatorQualificationCertificateFilePath(equipmentInstallationForm.getOperatorQualificationCertificateFilePath());
        installationForm.setEngineeringAddressDetail(equipmentInstallationForm.getEngineeringAddressDetail());
        installationForm.setEngineeringAreaCode(equipmentInstallationForm.getEngineeringAreaCode());
        Area area = areaMapper.selectAreaById(equipmentInstallationForm.getEngineeringAreaCode());
        installationForm.setRemarks(area.getName());
        if (3==area.getLevel()){
            Area area1 = areaMapper.selectAreaById(area.getParentId());
            installationForm.setRemarks(area1.getName()+"/"+area.getName());
        }

        installationForm.setUserId(appId);
        installationForm.setCreateBy(appId);
        installationForm.setCreateTime(new Date());
        Long id  = SnowflakeIdWorker.getInstance().nextId();
        installationForm.setId(id);
        int flag = filingEquipmentInstallationFormService.add(installationForm);
        if (flag ==1){
            return success("设备安装数据推送成功",installationForm);
        }else{
            return error("ID为"+equipmentInstallationForm.getBusinessId()+"的设备安装数据推送失败");
        }
    }

    /**
     * 设备安装/拆卸数据修改
     * @return
     */
    @CrossOrigin
    @RequestMapping("/updateInstallationForm")
    @ResponseBody
    public AjaxResult updateInstallationForm(@RequestParam("appId") String appId,
                                          @RequestParam("appKey") String appKey,
//                                          @RequestParam("auditStatus") String auditStatus,   //审核状态 0未审核，1审核通过，2驳回
//                                          @RequestParam("applicationDate") String applicationDate,   //申请日期
//                                          @RequestParam("workType") String workType, //申请类型 0安装 1拆卸
//                                          @RequestParam("equipmentFilingCode") String equipmentFilingCode, //设备备案编号
//                                          @RequestParam("deviceName") String deviceName, //设备名称
//                                          @RequestParam("specificationModel") String specificationModel, //规格型号
//                                          @RequestParam("engineeringName") String engineeringName, //工程名称
//                                          @RequestParam("constructionSite") String constructionSite, //工地名称
//                                          @RequestParam("jobContent") String jobContent, //工作内容
//                                          @RequestParam("installationUnit") String installationUnit, //安装单位
//                                          @RequestParam("installationTime") String installationTime, //安装时间
//                                          @RequestParam("installationHeight") String installationHeight, //安装高度，单位：米
//                                          @RequestParam("installationLeader") String installationLeader, //安装负责人
//                                          @RequestParam("installationLeaderContactNumber") String installationLeaderContactNumber, //安装负责人联系电话
//                                          @RequestParam("techLeader") String techLeader, //技术负责人
//                                          @RequestParam("techLeaderContactNumber") String techLeaderContactNumber, //技术负责人联系电话
//                                          @RequestParam("siteLeader") String siteLeader, //现场负责人
//                                          @RequestParam("siteLeaderContactNumber") String siteLeaderContactNumber, //现场负责人联系方式
//                                          @RequestParam("notificationBookFilePath") String notificationBookFilePath, //起重机安装告知书
//                                          @RequestParam("equipmentFilingLicenseFilePath") String equipmentFilingLicenseFilePath, //起重机备案证
//                                          @RequestParam("auditFormFilePath") String auditFormFilePath, //起重机安装审核表
//                                          @RequestParam("qualifiLicenseFilePath") String qualifiLicenseFilePath, //企业资质证书
//                                          @RequestParam("licenseSafetyPermitFilePath") String licenseSafetyPermitFilePath, //安全生产许可证副本
//                                          @RequestParam("operatorsListFilePath") String operatorsListFilePath, //安装单位  特种作业人员名单
//                                          @RequestParam("operatorsLicenseFilePath") String operatorsLicenseFilePath, //安装单位特种作业人员证书
//                                          @RequestParam("specialConstructionSchemeFilePath") String specialConstructionSchemeFilePath, //设备安装专项施工方案
//                                          @RequestParam("installationContractFilePath") String installationContractFilePath, //安装合同
//                                          @RequestParam("securityProtocolFilePath") String securityProtocolFilePath, //安全协议
//                                          @RequestParam("emergencyRescuePlanFilePath") String emergencyRescuePlanFilePath, //应急救援预案
//                                          @RequestParam("equipmentQualificationCertificateFilePath") String equipmentQualificationCertificateFilePath, //设备产品合格证明
//                                          @RequestParam("operatorQualificationCertificateFilePath") String operatorQualificationCertificateFilePath, //操作人员资格证书
//                                          @RequestParam("businessId") String businessId, //业务ID
                                          @RequestBody FilingEquipmentInstallationFormVo equipmentInstallationForm

    ) {
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key){
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())){
            return AjaxResult.error("appKey输入有误");
        }
        //判断该数据是否重复
        FilingEquipmentInstallationForm installationForm1 = new FilingEquipmentInstallationForm();
        installationForm1.setEquipmentFilingCode(equipmentInstallationForm.getEquipmentFilingCode());
        List<FilingEquipmentInstallationForm> list = filingEquipmentInstallationFormService.selectList(installationForm1);
        if (list.size()==0){
            return AjaxResult.error("ID为"+equipmentInstallationForm.getBusinessId()+"的数据不存在,请先推送数据");
        }

        FilingEquipmentInstallationForm installationForm = new FilingEquipmentInstallationForm();
        installationForm.setId(list.get(0).getId());
        installationForm.setBusinessId(equipmentInstallationForm.getBusinessId());
        installationForm.setWorkType(equipmentInstallationForm.getWorkType());
        installationForm.setAuditStatus(equipmentInstallationForm.getAuditStatus());
        installationForm.setEquipmentFilingCode(equipmentInstallationForm.getEquipmentFilingCode());
        if (null!=equipmentInstallationForm.getDeviceName()){
            installationForm.setDeviceName(equipmentInstallationForm.getDeviceName());
        }
        installationForm.setSpecificationModel(equipmentInstallationForm.getSpecificationModel());
        installationForm.setEngineeringName(equipmentInstallationForm.getEngineeringName());
        installationForm.setConstructionSite(equipmentInstallationForm.getConstructionSite());
        installationForm.setJobContent(equipmentInstallationForm.getJobContent());
        installationForm.setInstallationUnit(equipmentInstallationForm.getInstallationUnit());
        installationForm.setInstallationTime(DateUtils.parseDate(equipmentInstallationForm.getInstallationTime()));
        try {
            installationForm.setInstallationTime(new SimpleDateFormat("yyyy-MM-dd").parse(equipmentInstallationForm.getInstallationTime()));
            installationForm.setApplicationDate(new SimpleDateFormat("yyyy-MM-dd").parse(equipmentInstallationForm.getApplicationDate()));
        }catch (Exception e){
            e.printStackTrace();
            return AjaxResult.error("请输入正确的日期格式");
        }
        installationForm.setInstallationHeight(equipmentInstallationForm.getInstallationHeight());
        installationForm.setInstallationLeader(equipmentInstallationForm.getInstallationLeader());
        installationForm.setInstallationLeaderContactNumber(equipmentInstallationForm.getInstallationLeaderContactNumber());
        installationForm.setTechLeader(equipmentInstallationForm.getTechLeader());
        installationForm.setTechLeaderContactNumber(equipmentInstallationForm.getTechLeaderContactNumber());
        installationForm.setSiteLeader(equipmentInstallationForm.getSiteLeader());
        installationForm.setSiteLeaderContactNumber(equipmentInstallationForm.getSiteLeaderContactNumber());
        installationForm.setNotificationBookFilePath(equipmentInstallationForm.getNotificationBookFilePath());
        installationForm.setEquipmentFilingLicenseFilePath(equipmentInstallationForm.getEquipmentFilingLicenseFilePath());
        installationForm.setAuditFormFilePath(equipmentInstallationForm.getAuditFormFilePath());
        installationForm.setQualifiLicenseFilePath(equipmentInstallationForm.getQualifiLicenseFilePath());
        installationForm.setLicenseSafetyPermitFilePath(equipmentInstallationForm.getLicenseSafetyPermitFilePath());
        installationForm.setOperatorsListFilePath(equipmentInstallationForm.getOperatorsListFilePath());
        installationForm.setOperatorsLicenseFilePath(equipmentInstallationForm.getOperatorsLicenseFilePath());
        installationForm.setSpecialConstructionSchemeFilePath(equipmentInstallationForm.getSpecialConstructionSchemeFilePath());
        installationForm.setInstallationContractFilePath(equipmentInstallationForm.getInstallationContractFilePath());
        installationForm.setSecurityProtocolFilePath(equipmentInstallationForm.getSecurityProtocolFilePath());
        installationForm.setEmergencyRescuePlanFilePath(equipmentInstallationForm.getEmergencyRescuePlanFilePath());
        installationForm.setEquipmentQualificationCertificateFilePath(equipmentInstallationForm.getEquipmentQualificationCertificateFilePath());
        installationForm.setOperatorQualificationCertificateFilePath(equipmentInstallationForm.getOperatorQualificationCertificateFilePath());
        installationForm.setEngineeringAddressDetail(equipmentInstallationForm.getEngineeringAddressDetail());
        installationForm.setEngineeringAreaCode(equipmentInstallationForm.getEngineeringAreaCode());
        Area area = areaMapper.selectAreaById(equipmentInstallationForm.getEngineeringAreaCode());
        installationForm.setRemarks(area.getName());
        if (3==area.getLevel()){
            Area area1 = areaMapper.selectAreaById(area.getParentId());
            installationForm.setRemarks(area1.getName()+"/"+area.getName());
        }

        installationForm.setUpdateBy(appId);
        installationForm.setUpdateTime(new Date());
        int flag = filingEquipmentInstallationFormService.update(installationForm);
        if (flag ==1){
            return success("设备安装数据修改成功",installationForm);
        }else{
            return error("ID为"+equipmentInstallationForm.getBusinessId()+"的设备安装数据修改失败");
        }
    }

    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("install:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentInstallationForm" ,filingEquipmentInstallationFormService.getOne(id));
            model.addAttribute("files",filingFileService.getFiles(id));
            model.addAttribute("dict",sysDictService.findByType("city"));
        }
        return "admin/filing/installation/filingEquipmentInstallationFormEdit";
    }

    /**
    * 更新
    * @param filingEquipmentInstallationForm
    * @return
    */
    @PostMapping("/update")
    @RequiresPermissions("install:edit")
    @ResponseBody
    public AjaxResult update(@Valid FilingEquipmentInstallationForm filingEquipmentInstallationForm) {
        filingEquipmentInstallationForm.setAuditStatus(0);
        filingEquipmentInstallationForm.setWorkType("0");
        return toAjax(filingEquipmentInstallationFormService.update(filingEquipmentInstallationForm));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("install:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(filingEquipmentInstallationFormService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("install:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",filingEquipmentInstallationFormService.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param filingEquipmentInstallationForm
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"install:add", "install:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(FilingEquipmentInstallationForm filingEquipmentInstallationForm) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = filingEquipmentInstallationForm.getId();
        filingEquipmentInstallationForm.setId(null);
        List<FilingEquipmentInstallationForm> filingEquipmentInstallationFormList = filingEquipmentInstallationFormService.selectList(filingEquipmentInstallationForm);
        if(StringUtils.isNotEmpty(filingEquipmentInstallationFormList)){
            if(filingEquipmentInstallationFormList.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(filingEquipmentInstallationFormList.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }
    /**
    * @Anthor: juziqiang
    * @Description: 审核通过
    * @Date: 14:36 2019/12/10
    */
    @RequestMapping("/examine")
    @RequiresPermissions("install:examine")
    @ResponseBody
    public AjaxResult examine(Long id) {
        FilingEquipmentInstallationForm installationForm = filingEquipmentInstallationFormService.getOne(id);
        if (installationForm.getAuditStatus()!=0){
            return error("已审核的申请，不可重复审核");
        }
        installationForm.setAuditStatus(1);
        installationForm.setAuditRejectReason("");
        installationForm.setAuditTime(new Date());
        return  toAjax(filingEquipmentInstallationFormService.update(installationForm));
    }
    /**
    * @Anthor: juziqiang
    * @Description: 审核驳回
    * @Date: 14:36 2019/12/10
    */
    @RequiresPermissions("install:examine")
    @RequestMapping("/reject")
    @ResponseBody
    public AjaxResult reject(Long id, String rejectReason) {
        FilingEquipmentInstallationForm filingEquipmentInstallationForm = filingEquipmentInstallationFormService.getOne(id);
        if (filingEquipmentInstallationForm.getAuditStatus()!=0){
            return error("已审核的申请，不可重复审核");
        }
        filingEquipmentInstallationForm.setAuditStatus(2);
        filingEquipmentInstallationForm.setAuditRejectReason(rejectReason);
        filingEquipmentInstallationForm.setAuditTime(new Date());
        return toAjax(filingEquipmentInstallationFormService.update(filingEquipmentInstallationForm));
    }

    /**
    * @Anthor: juziqiang
    * @Description: 外部查询
    * @Date: 9:58 2019/12/11
    */
    @RequestMapping("/allList")
    @ResponseBody
    public PageInfo<FilingEquipmentInstallationForm> allList(FilingEquipmentInstallationForm filingEquipmentInstallationForm){
        filingEquipmentInstallationForm.setAuditStatus(1);
        return filingEquipmentInstallationFormService.selectPage(filingEquipmentInstallationForm);
    }

    @RequestMapping("/license")
    public void license(Long id,  HttpServletResponse response){
        File file = null;
        try {
            file = filingEquipmentInstallationFormService.file(id);
        } catch (IOException e) {
            e.printStackTrace();
        }
        ResponseUtils.response(file, file.getName(), response);
    }
    /**
     *  文件上传
     */
    @RequestMapping("/uploadFile")
    @ResponseBody
    public AjaxResult uploadFile(MultipartFile uploadFile) throws IOException {
        String type = "filing";
        Map<String, Object> result = UploadFileUtil.saveFile(uploadFile, type);
        return AjaxResult.success("操作成功", result);
    }

    /**
     * 设备使用登记注销申请推送接口
     *
     * @return
     */
    @PostMapping("/syncInstall")
    @ResponseBody
    @VerifySign
    public AjaxResult syncInstall(
            @RequestParam("appId") String appId,
            @RequestParam("appKey") String appKey,
            @RequestBody() String  data
    ){
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key){
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())){
            return AjaxResult.error("appKey输入有误");
        }
        String str1 = data.replaceAll("\\[","");
        String str2 = str1.replaceAll("\\]","");
        JSONObject jsonObject = JSONObject.parseObject(str2);
        FilingEquipmentInstallationForm installationForm = jsonObject.toJavaObject(FilingEquipmentInstallationForm.class);
        //判断该数据是否重复
        FilingEquipmentInstallationForm installationForm1 = filingEquipmentInstallationFormService.selectByFilingCode(installationForm.getEquipmentFilingCode());
        if (null==installationForm1){
            //新增
            if (null==installationForm.getUserId()){
                installationForm.setUserId(appId);
            }
            installationForm.setCreateBy(appId);
            installationForm.setCreateTime(new Date());
            Long id  = SnowflakeIdWorker.getInstance().nextId();
            installationForm.setId(id);
            int flag = filingEquipmentInstallationFormService.add(installationForm);
            if (flag ==1){
                return success("安装/拆卸申请推送成功",installationForm);
            }else{
                return error("设备"+installationForm.getEquipmentFilingCode()+"安装/拆卸申请推送失败");
            }
        }else {
            //修改
            installationForm.setUpdateTime(new Date());
            installationForm.setUpdateBy(appId);
            installationForm.setId(installationForm1.getId());
            int flag = filingEquipmentInstallationFormService.update(installationForm);
            if (flag ==1){
                return success("安装/拆卸申请修改成功",installationForm);
            }else{
                return error("设备"+installationForm.getEquipmentFilingCode()+"安装/拆卸申请修改失败");
            }
        }
    }

}
