package com.tzstcl.filing.installation.ctrl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.ResponseUtils;
import com.tzstcl.commons.utils.SnowflakeIdWorker;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.commons.utils.UploadFileUtil;
import com.tzstcl.filing.access.service.IAccessKeyService;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication;
import com.tzstcl.filing.equipmentFilingApplication.service.FilingEquipmentFilingApplicationService;
import com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellation;
import com.tzstcl.filing.equipmentfilingcancellation.service.FilingEquipmentFilingCancellationService;
import com.tzstcl.filing.file.service.FilingFileService;
import com.tzstcl.filing.installation.model.FilingEquipmentInstallationForm;
import com.tzstcl.filing.installation.service.FilingEquipmentInstallationFormService;
import com.tzstcl.framework.shiro.ShiroUtils;
import com.tzstcl.sys.user.model.SysUser;
import com.tzstcl.sys.user.service.impl.SysDictServiceImpl;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年12月04日
 * 说明：设备安装登记流程表Controller
 */
@Controller
@RequestMapping("/admin/filingEquipmentUninstallationForm")
public class FilingEquipmentUninstallationFormCtrl extends BaseCtrl {

    @Autowired
    private FilingEquipmentInstallationFormService filingEquipmentInstallationFormService;
    @Autowired
    private FilingFileService filingFileService;
    @Autowired
    private IAccessKeyService accessKeyService;
    @Autowired
    private SysDictServiceImpl sysDictService;
    @Autowired
    private FilingEquipmentFilingApplicationService filingEquipmentFilingApplicationService;
    @Autowired
    private FilingEquipmentFilingCancellationService filingEquipmentFilingCancellationService;
    /**
    * list页面导航
    * @return
    */
    @RequiresPermissions("install:view")
    @RequestMapping("/toList")
    public String toList(Model model) {
        model.addAttribute("userId", ShiroUtils.getUser().getMobile());
        model.addAttribute("dict",sysDictService.findByType("city"));
        return "admin/filing/uninstall/filingEquipmentInstallationFormList";
    }

    /**
    * 获取查询的分页数据
    * @param filingEquipmentInstallationForm
    * @return
    */
    @RequestMapping("/list")
    @RequiresPermissions("install:view")
    @ResponseBody
    public PageInfo<FilingEquipmentInstallationForm> list(FilingEquipmentInstallationForm filingEquipmentInstallationForm) {
//        String userType = ShiroUtils.getUser().getUserType();
//        if (StringUtils.isNotEmpty(userType)&&userType.equals("0")){//企业用户只查询本企业申请的备案信息
//            filingEquipmentInstallationForm.setUserId(ShiroUtils.getUser().getMobile());
//        }
        filingEquipmentInstallationForm.setWorkType("1");
        return  filingEquipmentInstallationFormService.selectPage(filingEquipmentInstallationForm);
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"install:edite","install:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        SysUser user = ShiroUtils.getUser();
        model.addAttribute("user",user);
        if(null != id){
            model.addAttribute("filingEquipmentInstallationForm" ,filingEquipmentInstallationFormService.getOne(id));
        }
        model.addAttribute("id", SnowflakeIdWorker.getInstance().nextId());
        model.addAttribute("userId", ShiroUtils.getUser().getMobile());
        model.addAttribute("dict",sysDictService.findByType("city"));
        return "admin/filing/uninstall/filingEquipmentInstallationFormForm";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("install:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentUninstallationForm" ,filingEquipmentInstallationFormService.getOne(id));
            model.addAttribute("files",filingFileService.getFiles(id));
            model.addAttribute("dict",sysDictService.findByType("city"));
        }
        return "admin/filing/uninstall/filingEquipmentInstallationFormDetail";
    }

    /**
     * 新增
     * @param filingEquipmentInstallationForm
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"install:add"})
    @ResponseBody
    public AjaxResult save(@Valid FilingEquipmentInstallationForm filingEquipmentInstallationForm) {
        FilingEquipmentFilingApplication filingApplication = filingEquipmentFilingApplicationService.selectByFilingCode(filingEquipmentInstallationForm.getEquipmentFilingCode());
        if (null==filingApplication){
            return error("该设备不存在，添加失败！");
        }
        FilingEquipmentFilingCancellation cancellation = filingEquipmentFilingCancellationService.getOneByCode(filingEquipmentInstallationForm.getEquipmentFilingCode());
        if (null!=cancellation&&"1".equals(cancellation.getAuditStatus())){
            return error("该设备已注销，添加失败！");
        }
        FilingEquipmentInstallationForm installationForm1 = filingEquipmentInstallationFormService.getOneByFilingCode(filingEquipmentInstallationForm.getEquipmentFilingCode(), "0");
        if (null==installationForm1){
            return error("该设备还未安装，添加失败！");
        }
        FilingEquipmentInstallationForm installationForm = filingEquipmentInstallationFormService.getOneByFilingCode(filingEquipmentInstallationForm.getEquipmentFilingCode(),"1");
        if (null!=installationForm){
            return error("该设备已申请拆卸，请勿重复添加！");
        }
        filingEquipmentInstallationForm.setApplicationDate(new Date());
        filingEquipmentInstallationForm.setCreateTime(new Date());
        filingEquipmentInstallationForm.setCreateBy(ShiroUtils.getUserId()+"");
        filingEquipmentInstallationForm.setWorkType("1");
         return toAjax(filingEquipmentInstallationFormService.add(filingEquipmentInstallationForm));
    }

    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("install:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentUninstallationForm" ,filingEquipmentInstallationFormService.getOne(id));
            model.addAttribute("files",filingFileService.getFiles(id));
            model.addAttribute("dict",sysDictService.findByType("city"));
        }
        return "admin/filing/uninstall/filingEquipmentInstallationFormEdit";
    }

    /**
    * 更新
    * @param filingEquipmentInstallationForm
    * @return
    */
    @PostMapping("/update")
    @RequiresPermissions("install:edit")
    @ResponseBody
    public AjaxResult update(@Valid FilingEquipmentInstallationForm filingEquipmentInstallationForm) {
        filingEquipmentInstallationForm.setAuditStatus(0);
        filingEquipmentInstallationForm.setWorkType("1");
        return toAjax(filingEquipmentInstallationFormService.update(filingEquipmentInstallationForm));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("install:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(filingEquipmentInstallationFormService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("install:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",filingEquipmentInstallationFormService.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param filingEquipmentInstallationForm
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"install:add", "install:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(FilingEquipmentInstallationForm filingEquipmentInstallationForm) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = filingEquipmentInstallationForm.getId();
        filingEquipmentInstallationForm.setId(null);
        List<FilingEquipmentInstallationForm> filingEquipmentInstallationFormList = filingEquipmentInstallationFormService.selectList(filingEquipmentInstallationForm);
        if(StringUtils.isNotEmpty(filingEquipmentInstallationFormList)){
            if(filingEquipmentInstallationFormList.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(filingEquipmentInstallationFormList.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }
    /**
    * @Anthor: juziqiang
    * @Description: 审核通过
    * @Date: 14:36 2019/12/10
    */
    @RequestMapping("/examine")
    @RequiresPermissions("install:examine")
    @ResponseBody
    public AjaxResult examine(Long id) {
        FilingEquipmentInstallationForm installationForm = filingEquipmentInstallationFormService.getOne(id);
        if (installationForm.getAuditStatus()!=0){
            return error("已审核的申请，不可重复审核");
        }
        installationForm.setAuditStatus(1);
        installationForm.setAuditRejectReason("");
        installationForm.setAuditTime(new Date());
        return  toAjax(filingEquipmentInstallationFormService.update(installationForm));
    }
    /**
    * @Anthor: juziqiang
    * @Description: 审核驳回
    * @Date: 14:36 2019/12/10
    */
    @RequiresPermissions("install:examine")
    @RequestMapping("/reject")
    @ResponseBody
    public AjaxResult reject(Long id, String rejectReason) {

        FilingEquipmentInstallationForm filingEquipmentInstallationForm = filingEquipmentInstallationFormService.getOne(id);
        if (filingEquipmentInstallationForm.getAuditStatus()!=0){
            return error("已审核的申请，不可重复审核");
        }
        filingEquipmentInstallationForm.setAuditStatus(2);
        filingEquipmentInstallationForm.setAuditRejectReason(rejectReason);
        filingEquipmentInstallationForm.setAuditTime(new Date());
        return toAjax(filingEquipmentInstallationFormService.update(filingEquipmentInstallationForm));
    }

    @RequestMapping("/license")
    public void license(Long id,  HttpServletResponse response){
        File file = null;
        try {
            file = filingEquipmentInstallationFormService.unistallfile(id);
        } catch (IOException e) {
            e.printStackTrace();
        }
        ResponseUtils.response(file, file.getName(), response);
    }
    /**
     *  文件上传
     */
    @RequestMapping("/uploadFile")
    @ResponseBody
    public AjaxResult uploadFile(MultipartFile uploadFile) throws IOException {
        String type = "filing";
        Map<String, Object> result = UploadFileUtil.saveFile(uploadFile, type);
        return AjaxResult.success("操作成功", result);
    }
}
