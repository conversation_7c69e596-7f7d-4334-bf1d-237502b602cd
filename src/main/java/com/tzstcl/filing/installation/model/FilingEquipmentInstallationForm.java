package com.tzstcl.filing.installation.model;

import com.tzstcl.base.model.BaseModel;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年12月04日
 * 说明：设备安装登记流程表实体类
 */
@Data
public class FilingEquipmentInstallationForm extends BaseModel<FilingEquipmentInstallationForm> implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
     *申请人id
     */
    private String userId;
    /**
     *申请日期
     */
    private Date applicationDate;
    /**
     *设备备案编号
     */
    private String equipmentFilingCode;
    /**
     *设备名称
     */
    private String deviceName;
    /**
     *规格型号
     */
    private String specificationModel;
    /**
     *工程名称
     */
    private String engineeringName;
    /**
     *安装单位
     */
    private String installationUnit;
    /**
     *现场负责人
     */
    private String siteLeader;
    /**
     *现场负责人联系方式
     */
    private String siteLeaderContactNumber;
    /**
     *安装时间
     */
    private Date installationTime;
    /**
     *安装高度，单位：米
     */
    private String installationHeight;
    /**
     *工地名称
     */
    private String constructionSite;
    /**
     *工作内容
     */
    private String jobContent;
    /**
     *安装负责人
     */
    private String installationLeader;
    /**
     *安装负责人联系电话
     */
    private String installationLeaderContactNumber;
    /**
     *技术负责人
     */
    private String techLeader;
    /**
     *技术负责人联系电话
     */
    private String techLeaderContactNumber;
    /**
     *起重机安装告知书
     */
    private String notificationBookFilePath;
    /**
     *起重机备案证
     */
    private String equipmentFilingLicenseFilePath;
    /**
     *起重机安装审核表
     */
    private String auditFormFilePath;
    /**
     *企业资质证书
     */
    private String qualifiLicenseFilePath;
    /**
     *安全生产许可证副本
     */
    private String licenseSafetyPermitFilePath;
    /**
     *安装单位  特种作业人员名单
     */
    private String operatorsListFilePath;
    /**
     *安装单位特种作业人员证书
     */
    private String operatorsLicenseFilePath;
    /**
     *设备安装专项施工方案
     */
    private String specialConstructionSchemeFilePath;
    /**
     *安装合同
     */
    private String installationContractFilePath;
    /**
     *安全协议
     */
    private String securityProtocolFilePath;
    /**
     *应急救援预案
     */
    private String emergencyRescuePlanFilePath;
    /**
     *设备产品合格证明
     */
    private String equipmentQualificationCertificateFilePath;
    /**
     *操作人员资格证书
     */
    private String operatorQualificationCertificateFilePath;
    /**
     *审核状态，0未审核，1审核通过，2审核驳回
     */
    private Integer auditStatus;
    /**
     *审核时间，yyyy-MM-dd HH:mm:ss
     */
    private Date auditTime;
    /**
     *审核驳回意见
     */
    private String auditRejectReason;
    /**
    *申请类型 0安装  1拆卸
    **/
    private String workType;
    /**
     *业务ID
     */
    private String businessId;
    /**
     * 项目工程详细地址
     */
    private String engineeringAddressDetail;
    /**
     * 工程所属地区域编码
     */
    private Long engineeringAreaCode;
    /**
     * 机械类别，T塔式起重机，S施工升降机，W物料提升机，Q其他
     */
    private String equipmentType;
}
