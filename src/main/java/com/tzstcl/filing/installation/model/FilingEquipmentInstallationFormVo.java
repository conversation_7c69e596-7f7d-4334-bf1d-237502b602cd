package com.tzstcl.filing.installation.model;

import com.tzstcl.base.model.BaseModel;
import lombok.Data;
import lombok.NonNull;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/23
 */
@Data
public class FilingEquipmentInstallationFormVo extends BaseModel<FilingEquipmentInstallationFormVo> implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *申请人id
     */
    private String userId;
    /**
     *申请日期
     */
    @NonNull
    private String applicationDate;
    /**
     *设备备案编号
     */
    @NonNull
    private String equipmentFilingCode;
    /**
     *设备名称
     */
    //@NonNull
    private String deviceName;
    /**
     *规格型号
     */
    @NonNull
    private String specificationModel;
    /**
     *工程名称
     */
    @NonNull
    private String engineeringName;
    /**
     *安装单位
     */
    @NonNull
    private String installationUnit;
    /**
     *现场负责人
     */
    @NonNull
    private String siteLeader;
    /**
     *现场负责人联系方式
     */
    @NonNull
    private String siteLeaderContactNumber;
    /**
     *安装时间
     */
    @NonNull
    private String installationTime;
    /**
     *安装高度，单位：米
     */
    @NonNull
    private String installationHeight;
    /**
     *工地名称
     */
    @NonNull
    private String constructionSite;
    /**
     *工作内容
     */
    @NonNull
    private String jobContent;
    /**
     *安装负责人
     */
    @NonNull
    private String installationLeader;
    /**
     *安装负责人联系电话
     */
    @NonNull
    private String installationLeaderContactNumber;
    /**
     *技术负责人
     */
    @NonNull
    private String techLeader;
    /**
     *技术负责人联系电话
     */
    @NonNull
    private String techLeaderContactNumber;
    /**
     *起重机安装告知书
     */
    @NonNull
    private String notificationBookFilePath;
    /**
     *起重机备案证
     */
    @NonNull
    private String equipmentFilingLicenseFilePath;
    /**
     *起重机安装审核表
     */
    @NonNull
    private String auditFormFilePath;
    /**
     *企业资质证书
     */
    @NonNull
    private String qualifiLicenseFilePath;
    /**
     *安全生产许可证副本
     */
    @NonNull
    private String licenseSafetyPermitFilePath;
    /**
     *安装单位  特种作业人员名单
     */
    @NonNull
    private String operatorsListFilePath;
    /**
     *安装单位特种作业人员证书
     */
    @NonNull
    private String operatorsLicenseFilePath;
    /**
     *设备安装专项施工方案
     */
    @NonNull
    private String specialConstructionSchemeFilePath;
    /**
     *安装合同
     */
    @NonNull
    private String installationContractFilePath;
    /**
     *安全协议
     */
    @NonNull
    private String securityProtocolFilePath;
    /**
     *应急救援预案
     */
    @NonNull
    private String emergencyRescuePlanFilePath;
    /**
     *设备产品合格证明
     */
    @NonNull
    private String equipmentQualificationCertificateFilePath;
    /**
     *操作人员资格证书
     */
    @NonNull
    private String operatorQualificationCertificateFilePath;
    /**
     *审核状态，0未审核，1审核通过，2审核驳回
     */
    @NonNull
    private Integer auditStatus;
    /**
     *审核时间，yyyy-MM-dd HH:mm:ss
     */
    private Date auditTime;
    /**
     *审核驳回意见
     */
    private String auditRejectReason;
    /**
     *申请类型
     **/
    @NonNull
    private String workType;
    /**
     *业务ID
     */
    @NonNull
    private String businessId;
    /**
     * 项目工程详细地址
     */
    @NonNull
    private String engineeringAddressDetail;
    /**
     * 工程所属地区域编码
     */
    @NonNull
    private Long engineeringAreaCode;
    /**
     *appId
     */
    private String appId;
    /**
     *appKey
     */
    private String appKey;
}
