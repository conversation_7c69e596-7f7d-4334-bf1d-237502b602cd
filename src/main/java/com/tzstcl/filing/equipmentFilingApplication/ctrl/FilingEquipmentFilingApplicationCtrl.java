package com.tzstcl.filing.equipmentFilingApplication.ctrl;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.google.gson.JsonObject;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.JsonUtil;
import com.tzstcl.commons.utils.SnowflakeIdWorker;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.commons.utils.UploadFileUtil;
import com.tzstcl.filing.access.model.AccessKey;
import com.tzstcl.filing.access.service.IAccessKeyService;
import com.tzstcl.filing.annotation.VerifySign;
import com.tzstcl.filing.area.mapper.AreaMapper;
import com.tzstcl.filing.area.model.Area;
import com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling;
import com.tzstcl.filing.equipmentFiling.service.FilingEquipmentFilingService;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplicationVo;
import com.tzstcl.filing.equipmentFilingApplication.service.FilingEquipmentFilingApplicationService;
import com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationForm;
import com.tzstcl.filing.file.service.FilingFileService;
import com.tzstcl.framework.shiro.ShiroUtils;
import com.tzstcl.sys.user.model.SysDict;
import com.tzstcl.sys.user.model.SysUser;
import com.tzstcl.sys.user.service.impl.SysDictServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年11月05日
 * 说明：起重机设备备案申请Controller
 */
@Controller
@RequestMapping("/admin/filingEquipmentFilingApplication")
public class FilingEquipmentFilingApplicationCtrl extends BaseCtrl {

    @Autowired
    private FilingEquipmentFilingApplicationService filingEquipmentFilingApplicationService;
    @Autowired
    private FilingEquipmentFilingService filingEquipmentFilingService;
    @Autowired
    private SysDictServiceImpl sysDictService;
    @Autowired
    private FilingFileService filingFileService;
    @Autowired
    private IAccessKeyService accessKeyService;
    @Autowired
    private AreaMapper areaMapper;

    /**
     * list页面导航
     *
     * @return
     */
    @RequiresPermissions("equipment:view")
    @GetMapping("/toList")
    public String toList(Model model) {
        model.addAttribute("dict",sysDictService.findByType("city"));
        return "admin/filing/equipmentFilingApplication/filingEquipmentFilingApplicationList";
    }

    /**
     * 获取查询的分页数据
     *
     * @param filingEquipmentFilingApplication
     * @return
     */
    @RequestMapping("/list")
    @RequiresPermissions("equipment:view")
    @ResponseBody
    public PageInfo<FilingEquipmentFilingApplication> list(FilingEquipmentFilingApplication filingEquipmentFilingApplication) {
//        SysUser sysUser = ShiroUtils.getUser();
//        if (!sysUser.getId().equals(1L)){
//            String userType = sysUser.getUserType();
//            if (userType.equals("0")){//企业用户只查询本企业申请的备案信息
//                filingEquipmentFilingApplication.setUserId(sysUser.getMobile());
//            }
//        }
        return filingEquipmentFilingApplicationService.selectPage(filingEquipmentFilingApplication);
    }


    /**
     * 公示获取查询的分页数据
     *
     * @param filingEquipmentFilingApplication
     * @return
     */
    @RequestMapping("/seeList")
    @ResponseBody
    public PageInfo<FilingEquipmentFilingApplication> seeList(FilingEquipmentFilingApplication filingEquipmentFilingApplication) {
        return filingEquipmentFilingApplicationService.selectPage(filingEquipmentFilingApplication);
    }

    /**
     * form页面导航
     *
     * @return
     */
    @RequiresPermissions(value = {"equipment:edit", "equipment:add"}, logical = Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value = "id", required = false) Long id, Model model) {
        SysUser user = ShiroUtils.getUser();
        model.addAttribute("user",user);
        model.addAttribute("userId", ShiroUtils.getUser().getMobile());
        model.addAttribute("id",SnowflakeIdWorker.getInstance().nextId());
        model.addAttribute("dict",sysDictService.findByType("city"));
        if (null != id) {
            model.addAttribute("filingEquipmentFilingApplication", filingEquipmentFilingApplicationService.getOne(id));
        }
        return "admin/filing/equipmentFilingApplication/filingEquipmentFilingApplicationForm";
    }

    /**
     * form页面导航 详情
     *
     * @return
     */
    @RequiresPermissions("equipment:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id") Long id, Model model) {
        if (null != id) {
            FilingEquipmentFilingApplication filingEquipmentFilingApplication = filingEquipmentFilingApplicationService.getOne(id);
            if(StringUtils.isNotEmpty(filingEquipmentFilingApplication.getRemarks())){
                SysDict city = sysDictService.getByValue("city", filingEquipmentFilingApplication.getRemarks());
                if (null!=city){
                    filingEquipmentFilingApplication.setRemarks(city.getDictLabel());
                }
            }
            model.addAttribute("filingEquipmentFilingApplication", filingEquipmentFilingApplication);
            model.addAttribute("files",filingFileService.getFiles(id));
            model.addAttribute("dict",sysDictService.findByType("city"));
        }
        return "admin/filing/equipmentFilingApplication/filingEquipmentFilingApplicationDetail";
    }



    @GetMapping("/seeDetail/{id}")
    public String seeDetail(@PathVariable("id") Long id, Model model) {
        if (null != id) {
            SysUser user = ShiroUtils.getUser();
            model.addAttribute("user",user);
            model.addAttribute("filingEquipmentFilingApplication", filingEquipmentFilingApplicationService.getOne(id));
            model.addAttribute("files",filingFileService.getFiles(id));
            model.addAttribute("dict",sysDictService.findByType("city"));
        }
        return "admin/filing/equipmentFilingApplication/filingEquipmentFilingApplicationDetail";
    }
    /**
     * form页面导航 新增
     *
     * @return
     */
    @RequiresPermissions("equipment:add")
    @GetMapping("/add")
    public String toAdd(Model model) {
        Long id  = SnowflakeIdWorker.getInstance().nextId();
        model.addAttribute("id",id);
        model.addAttribute("dict",sysDictService.findByType("city"));
        SysUser user = ShiroUtils.getUser();
        model.addAttribute("user",user);
        return "admin/filing/equipmentFilingApplication/filingEquipmentFilingApplicationForm";
    }

    /**
     * 新增
     *
     * @param filingEquipmentFilingApplication
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions("equipment:add")
    @ResponseBody
    public AjaxResult save(@Valid FilingEquipmentFilingApplication filingEquipmentFilingApplication) {
        FilingEquipmentFilingApplication filingApplication = filingEquipmentFilingApplicationService.getOneByFactoryNum(filingEquipmentFilingApplication.getManufacturer(),filingEquipmentFilingApplication.getFactoryLicenseNumber());
        if (filingApplication!=null){
            return error("该设备已申请备案，请勿重复添加！");
        }
        filingEquipmentFilingApplication.setApplicationDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        int flag = filingEquipmentFilingApplicationService.insert(filingEquipmentFilingApplication);
        if (flag ==1){
            return success("添加成功",filingEquipmentFilingApplication);
        }else{
            return error("添加失败");
        }
    }

    /**
     * 新增备案申请推送接口
     *
     * @return
     */
    @CrossOrigin
    @PostMapping("/addFilingApplication")
    @ResponseBody
    public AjaxResult addFilingApplication(
            @RequestParam("appId") String appId,
            @RequestParam("appKey") String appKey,
            @RequestBody FilingEquipmentFilingApplicationVo equipmentFilingApplication
    ){
        //System.out.println("开始添加数据");
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key){
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())){
            return AjaxResult.error("appKey输入有误");
        }
        //根据备案编号判断该数据是否重复
        FilingEquipmentFilingApplication filingApplication1 = new FilingEquipmentFilingApplication();
        filingApplication1.setFilingCode(equipmentFilingApplication.getFilingCode());
        List<FilingEquipmentFilingApplication> list = filingEquipmentFilingApplicationService.selectList(filingApplication1);
        if (list.size()>0){
            //return AjaxResult.error("ID为"+equipmentFilingApplication.getBusinessId()+"的数据已存在,请勿重复推送");
            return AjaxResult.error("该设备已存在,请勿重复推送");
        }
        FilingEquipmentFilingApplication filingApplication = new FilingEquipmentFilingApplication();
        filingApplication.setFilingCode(equipmentFilingApplication.getFilingCode());
        filingApplication.setBusinessDataId(equipmentFilingApplication.getBusinessId());
        filingApplication.setAuditStatus(equipmentFilingApplication.getAuditStatus());
        filingApplication.setApplicationDate(equipmentFilingApplication.getApplicationDate());
        filingApplication.setEquipmentType(equipmentFilingApplication.getEquipmentType());
        if (null!=equipmentFilingApplication.getDeviceName()){
            filingApplication.setDeviceName(equipmentFilingApplication.getDeviceName());
        }
        filingApplication.setSpecificationModel(equipmentFilingApplication.getSpecificationModel());
        filingApplication.setPurchaseTime(equipmentFilingApplication.getPurchaseTime());
        if (null!=equipmentFilingApplication.getLiftingWeight()){
            filingApplication.setLiftingWeight(equipmentFilingApplication.getLiftingWeight());
        }
        filingApplication.setPropertyUnit(equipmentFilingApplication.getPropertyUnit());
        filingApplication.setUnitAddress(equipmentFilingApplication.getUnitAddress());
        filingApplication.setUnitAreaCode(equipmentFilingApplication.getUnitAreaCode());
        Area area = areaMapper.selectAreaById(equipmentFilingApplication.getUnitAreaCode());
        filingApplication.setRemarks(area.getName());
        if (3==area.getLevel()){
            Area area1 = areaMapper.selectAreaById(area.getParentId());
            filingApplication.setRemarks(area1.getName()+"/"+area.getName());
        }
        filingApplication.setPropertyUnitCode(equipmentFilingApplication.getPropertyUnitCode());
        filingApplication.setLegalRepresentative(equipmentFilingApplication.getLegalRepresentative());
        filingApplication.setLegalRepresentativeContactNumber(equipmentFilingApplication.getLegalRepresentativeContactNumber());
        filingApplication.setTechnicalDirector(equipmentFilingApplication.getTechnicalDirector());
        filingApplication.setTechnicalDirectorContactNumber(equipmentFilingApplication.getTechnicalDirectorContactNumber());
        filingApplication.setEquipmentManager(equipmentFilingApplication.getEquipmentManager());
        filingApplication.setEquipmentManagerContactNumber(equipmentFilingApplication.getEquipmentManagerContactNumber());
        filingApplication.setManufacturer(equipmentFilingApplication.getManufacturer());
        filingApplication.setFactoryTime(equipmentFilingApplication.getFactoryTime());
        filingApplication.setFactoryLicenseNumber(equipmentFilingApplication.getFactoryLicenseNumber());
        filingApplication.setManufacturingLicenseNumber(equipmentFilingApplication.getManufacturingLicenseNumber());
        filingApplication.setFilingApplicationFormPath(equipmentFilingApplication.getFilingApplicationFormPath());
        filingApplication.setBusinessLicensePath(equipmentFilingApplication.getBusinessLicensePath());
        filingApplication.setEquipmentManufacturingLicensePath(equipmentFilingApplication.getEquipmentManufacturingLicensePath());
        filingApplication.setProductCertificationPath(equipmentFilingApplication.getProductCertificationPath());
        filingApplication.setPurchaseSaleCertificatePath(equipmentFilingApplication.getPurchaseSaleCertificatePath());
        filingApplication.setOldNumberFlag(equipmentFilingApplication.getOldNumberFlag());
        if ("1".equals(equipmentFilingApplication.getOldNumberFlag())){
            if (null==equipmentFilingApplication.getOldNumber()){
                return AjaxResult.error("缺少旧备案编号");
            }else {
                filingApplication.setOldNumber(equipmentFilingApplication.getOldNumber());
            }
        }

        filingApplication.setUserId(appId);
        filingApplication.setCreateBy(appId);
        filingApplication.setCreateTime(new Date());
        Long id  = SnowflakeIdWorker.getInstance().nextId();
        filingApplication.setId(id);
        int flag = filingEquipmentFilingApplicationService.insert(filingApplication);
        if (flag ==1){
            return success("备案申请推送成功",filingApplication);
        }else{
            return error("ID为"+equipmentFilingApplication.getBusinessId()+"的备案申请推送失败");
        }
    }

    /**
     * 新增备案申请推送接口
     *
     * @return
     */
    @PostMapping("/syncFilingApplication")
    @ResponseBody
    @VerifySign
    public AjaxResult syncFilingApplication(
            @RequestParam("appId") String appId,
            @RequestParam("appKey") String appKey,
            @RequestBody() String  data
    ){
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key){
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())){
            return AjaxResult.error("appKey输入有误");
        }
        String str1 = data.replaceAll("\\[","");
        String str2 = str1.replaceAll("\\]","");
        JSONObject jsonObject = JSONObject.parseObject(str2);
        FilingEquipmentFilingApplication filingApplication = jsonObject.toJavaObject(FilingEquipmentFilingApplication.class);
        //判断该数据是否重复
        FilingEquipmentFilingApplication application = filingEquipmentFilingApplicationService.selectByFilingCode(filingApplication.getFilingCode());
        if (null==application){
            //新增
            if (null==filingApplication.getUserId()){
                filingApplication.setUserId(appId);
            }
            filingApplication.setCreateBy(appId);
            filingApplication.setCreateTime(new Date());
            Long id  = SnowflakeIdWorker.getInstance().nextId();
            filingApplication.setId(id);
            int flag = filingEquipmentFilingApplicationService.insert(filingApplication);
            if (flag ==1){
                return success("备案申请推送成功",filingApplication);
            }else{
                return error("设备"+filingApplication.getFilingCode()+"备案申请推送失败");
            }
        }else {
            //修改
            filingApplication.setUpdateTime(new Date());
            filingApplication.setUpdateBy(appId);
            filingApplication.setId(application.getId());
            int flag = filingEquipmentFilingApplicationService.update(filingApplication);
            if (flag ==1){
                return success("备案申请修改成功",filingApplication);
            }else{
                return error("设备"+filingApplication.getFilingCode()+"备案申请修改失败");
            }
        }
    }

    /**
     * 修改备案申请推送接口
     *
     * @return
     */
    @CrossOrigin
    @PostMapping("/updateFilingApplication")
    @ResponseBody
    public AjaxResult updateFilingApplication(@RequestParam("appId") String appId,
                                              @RequestParam("appKey") String appKey,
                                              @RequestBody FilingEquipmentFilingApplicationVo equipmentFilingApplication

    ) {
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key){
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())){
            return AjaxResult.error("appKey输入有误");
        }
        //判断该数据是否重复
        FilingEquipmentFilingApplication filingApplication1 = new FilingEquipmentFilingApplication();
        filingApplication1.setFilingCode(equipmentFilingApplication.getFilingCode());
        List<FilingEquipmentFilingApplication> list = filingEquipmentFilingApplicationService.selectList(filingApplication1);
        if (list.size()==0){
            return AjaxResult.error("ID为"+equipmentFilingApplication.getBusinessId()+"的数据不存在,请先推送数据");
        }
        FilingEquipmentFilingApplication filingApplication = new FilingEquipmentFilingApplication();
        filingApplication.setId(list.get(0).getId());
        filingApplication.setFilingCode(equipmentFilingApplication.getFilingCode());
        filingApplication.setBusinessDataId(equipmentFilingApplication.getBusinessId());
        filingApplication.setAuditStatus(equipmentFilingApplication.getAuditStatus());
        filingApplication.setApplicationDate(equipmentFilingApplication.getApplicationDate());
        filingApplication.setEquipmentType(equipmentFilingApplication.getEquipmentType());
        if (null!=equipmentFilingApplication.getDeviceName()){
            filingApplication.setDeviceName(equipmentFilingApplication.getDeviceName());
        }
        filingApplication.setSpecificationModel(equipmentFilingApplication.getSpecificationModel());
        filingApplication.setPurchaseTime(equipmentFilingApplication.getPurchaseTime());
        if (null!=equipmentFilingApplication.getLiftingWeight()){
            filingApplication.setLiftingWeight(equipmentFilingApplication.getLiftingWeight());
        }
        filingApplication.setPropertyUnit(equipmentFilingApplication.getPropertyUnit());
        filingApplication.setUnitAddress(equipmentFilingApplication.getUnitAddress());
        filingApplication.setUnitAreaCode(equipmentFilingApplication.getUnitAreaCode());
        Area area = areaMapper.selectAreaById(equipmentFilingApplication.getUnitAreaCode());
        filingApplication.setRemarks(area.getName());
        if (3==area.getLevel()){
            Area area1 = areaMapper.selectAreaById(area.getParentId());
            filingApplication.setRemarks(area1.getName()+"/"+area.getName());
        }
        filingApplication.setPropertyUnitCode(equipmentFilingApplication.getPropertyUnitCode());
        filingApplication.setLegalRepresentative(equipmentFilingApplication.getLegalRepresentative());
        filingApplication.setLegalRepresentativeContactNumber(equipmentFilingApplication.getLegalRepresentativeContactNumber());
        filingApplication.setTechnicalDirector(equipmentFilingApplication.getTechnicalDirector());
        filingApplication.setTechnicalDirectorContactNumber(equipmentFilingApplication.getTechnicalDirectorContactNumber());
        filingApplication.setEquipmentManager(equipmentFilingApplication.getEquipmentManager());
        filingApplication.setEquipmentManagerContactNumber(equipmentFilingApplication.getEquipmentManagerContactNumber());
        filingApplication.setManufacturer(equipmentFilingApplication.getManufacturer());
        filingApplication.setFactoryTime(equipmentFilingApplication.getFactoryTime());
        filingApplication.setFactoryLicenseNumber(equipmentFilingApplication.getFactoryLicenseNumber());
        filingApplication.setManufacturingLicenseNumber(equipmentFilingApplication.getManufacturingLicenseNumber());
        filingApplication.setFilingApplicationFormPath(equipmentFilingApplication.getFilingApplicationFormPath());
        filingApplication.setBusinessLicensePath(equipmentFilingApplication.getBusinessLicensePath());
        filingApplication.setEquipmentManufacturingLicensePath(equipmentFilingApplication.getEquipmentManufacturingLicensePath());
        filingApplication.setProductCertificationPath(equipmentFilingApplication.getProductCertificationPath());
        filingApplication.setPurchaseSaleCertificatePath(equipmentFilingApplication.getPurchaseSaleCertificatePath());
        filingApplication.setOldNumberFlag(equipmentFilingApplication.getOldNumberFlag());
        if ("1".equals(equipmentFilingApplication.getOldNumberFlag())){
            if (null==equipmentFilingApplication.getOldNumber()){
                return AjaxResult.error("缺少旧备案编号");
            }else {
                filingApplication.setOldNumber(equipmentFilingApplication.getOldNumber());
            }
        }

        filingApplication.setUpdateBy(appId);
        filingApplication.setUpdateTime(new Date());
        int flag = filingEquipmentFilingApplicationService.update(filingApplication);
        if (flag ==1){
            return success("备案申请数据修改成功",filingApplication);
        }else{
            return error("ID为"+equipmentFilingApplication.getBusinessId()+"的备案申请修改失败");
        }
    }

    /**
     * 根据备案编号查询
     *
     * @param deviceFilingCode
     * @return
     */
    @CrossOrigin
    @GetMapping("/getByDeviceFilingCode")
    @ResponseBody
    public AjaxResult getByDeviceFilingCode(@RequestParam("appId") String appId,
                                             @RequestParam("appKey") String appKey,
                                             @RequestParam("deviceFilingCode") String deviceFilingCode
    ) {
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key) {
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())) {
            return AjaxResult.error("appKey输入有误");
        }
        FilingEquipmentFilingApplication filingApplication = filingEquipmentFilingApplicationService.getByFilingCode(deviceFilingCode);
        if (null==filingApplication){
            return AjaxResult.error("该设备不存在");
        }
        ObjectMapper objectMapper = new ObjectMapper();
        String str = null;
        try {
            str = objectMapper.writeValueAsString(filingApplication);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return AjaxResult.success(str);
    }

    /**
     * form页面导航 更新
     *
     * @return
     */
    @RequiresPermissions("equipment:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id") Long id, Model model) {
        if (null != id) {
            model.addAttribute("filingEquipmentFilingApplication", filingEquipmentFilingApplicationService.getOne(id));
            model.addAttribute("files",filingFileService.getFiles(id));
            model.addAttribute("dict",sysDictService.findByType("city"));
        }
        return "admin/filing/equipmentFilingApplication/filingEquipmentFilingApplicationEdit";
    }

    /**
     * 更新
     *
     * @param filingEquipmentFilingApplication
     * @return
     */
    @PostMapping("/update")
    @RequiresPermissions("equipment:edit")
    @ResponseBody
    public AjaxResult update(@Valid FilingEquipmentFilingApplication filingEquipmentFilingApplication) {
        filingEquipmentFilingApplication.setAuditStatus("0");
        return toAjax(filingEquipmentFilingApplicationService.update(filingEquipmentFilingApplication));
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("equipment:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(filingEquipmentFilingApplicationService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     *
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("equipment:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return success("获取信息成功", filingEquipmentFilingApplicationService.getOne(id));
    }

    /**
     * 校验唯一性
     * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
     *
     * @param filingEquipmentFilingApplication
     * @return
     */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value = {"equipment:add", "equipment:edit"}, logical = Logical.OR)
    @ResponseBody
    public Map<String, Boolean> checkUnique(FilingEquipmentFilingApplication filingEquipmentFilingApplication) {
        Map<String, Boolean> result = new HashMap<String, Boolean>(4);
        result.put("valid", true);
        Long id = filingEquipmentFilingApplication.getId();
        FilingEquipmentFiling filingEquipmentFiling = new FilingEquipmentFiling();
        filingEquipmentFiling.setCancellationStatus(0);
        filingEquipmentFiling.setManufacturingLicenseNumber(filingEquipmentFilingApplication.getManufacturingLicenseNumber());
        List<FilingEquipmentFiling> filingEquipmentFilings = filingEquipmentFilingService.selectList(filingEquipmentFiling);
        if (filingEquipmentFilings.size() > 0) {
            result.put("valid", false);
        }
        return result;
    }

    /**
     * @Anthor: juziqiang
     * @Description: 文件上传
     * @Date: 16:23 2019/11/5
     */
    @RequestMapping("/uploadFile")
    @ResponseBody
    public AjaxResult uploadFile(MultipartFile uploadFile) throws IOException {
        String type = "filing";
        return success("操作成功",UploadFileUtil.saveFile(uploadFile, type));
    }

    /**
     * @Anthor: juziqiang
     * @Description: 备案申请
     * @Date: 10:29 2019/11/6
     */
    @RequestMapping("/application")
    @RequiresPermissions("equipment:add")
    @ResponseBody
    public AjaxResult application(FilingEquipmentFilingApplication filingEquipmentFilingApplication){
        filingEquipmentFilingApplication.setAuditStatus("0");
        return toAjax(filingEquipmentFilingApplicationService.add(filingEquipmentFilingApplication));
    }

    /**
     * @Anthor: juziqiang
     * @Description: 备案审查通过
     * @Date: 10:32 2019/11/6
     */
    @RequestMapping("/examine")
    @RequiresPermissions("equipment:examine")
    @ResponseBody
    public AjaxResult examine(Long id) {
        FilingEquipmentFilingApplication filingEquipmentFilingApplication = filingEquipmentFilingApplicationService.getOne(id);
        if(!filingEquipmentFilingApplication.getAuditStatus().equals("0")){
            return error("已审核的备案信息，不可重新审核");
        }
        filingEquipmentFilingApplication.setAuditStatus("1");
        filingEquipmentFilingApplication.setAuditTime(new Date());
        filingEquipmentFilingApplication.setAuditReject("");
        //根据产权单位区域编码去查区域字典
        Area area = areaMapper.selectAreaById(filingEquipmentFilingApplication.getUnitAreaCode());
        String filingCode = "FA";
        if (null!=area){
            filingCode = filingEquipmentFilingApplicationService.filingCode(area.getAreaDict(),filingEquipmentFilingApplication.getEquipmentType());
        }
        if(StringUtils.isEmpty(filingCode)){
            return error("生成备案编号错误，请联系系统管理员");
        }
        filingEquipmentFilingApplication.setFilingCode(filingCode);
        Integer flag = filingEquipmentFilingApplicationService.insertOrUpdate(filingEquipmentFilingApplication);
        if (flag == 1) {
            return toAjax(filingEquipmentFilingService.addFling(filingEquipmentFilingApplication));
        } else {
            return error("操作失败");
        }
    }

    /**
     * @Anthor: juziqiang
     * @Description: 备案审查驳回
     * @Date: 10:34 2019/11/6
     */
    @RequiresPermissions("equipment:examine")
    @RequestMapping("/reject")
    @ResponseBody
    public AjaxResult reject(Long id, String rejectReason) {
        FilingEquipmentFilingApplication filingEquipmentFilingApplication = filingEquipmentFilingApplicationService.getOne(id);
        filingEquipmentFilingApplication.setAuditStatus("2");
        filingEquipmentFilingApplication.setAuditReject(rejectReason);
        filingEquipmentFilingApplication.setAuditTime(new Date());
        return toAjax(filingEquipmentFilingApplicationService.update(filingEquipmentFilingApplication));
    }

    @RequestMapping("/license")
    public String license(String code, Model model){
        model.addAttribute("filingEquipmentRegistration",filingEquipmentFilingService.getByCode(code));
        return "admin/filing/publicInquiry/rework";
    }
    @RequestMapping("/card")
    public String card(String code, Model model){
        model.addAttribute("filingEquipmentRegistration",filingEquipmentFilingService.getByCode(code));
        return "admin/filing/publicInquiry/FilingCard";
    }

    /**
     * 根据appid获取地市名称
     *
     * @param appId
     * @return
     */
    @CrossOrigin
    @GetMapping("/getAreaByApp")
    @ResponseBody
    public AjaxResult getAreaByApp(@RequestParam("appId") String appId,
                                   @RequestParam("appKey") String appKey
    ) {
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key) {
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())) {
            return AjaxResult.error("appKey输入有误");
        }
        ObjectMapper objectMapper = new ObjectMapper();
        String str = null;
        try {
            str = objectMapper.writeValueAsString(key);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return AjaxResult.success(str);
    }

}
