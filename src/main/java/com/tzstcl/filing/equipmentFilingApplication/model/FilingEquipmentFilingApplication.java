package com.tzstcl.filing.equipmentFilingApplication.model;

import com.tzstcl.base.model.BaseModel;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年11月05日
 * 说明：起重机设备备案申请实体类
 */
@Data
public class FilingEquipmentFilingApplication extends BaseModel<FilingEquipmentFilingApplication> implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *审核发起人id
     */
    private String userId;
    /**
     *产权单位名称
     */
    private String propertyUnit;
    /**
     *产权单位社会统一信用代码
     */
    private String propertyUnitCode;
    /**
     *设备名称
     */
    private String deviceName;
    /**
     *规格型号
     */
    private String specificationModel;
    /**
     *生产厂家
     */
    private String manufacturer;

    /**
     *生产厂家信用代码
     */
    private String manufactureCorpCode;


    /**
     * 设备类型 T塔式起重机，S施工升降机（不含物料提升机），W物料提升机，Q其他起重机械
     */
    private String equipmentType;

    /**
     *出厂时间
     */
    private String factoryTime;
    /**
     *制造许可证编号
     */
    private String manufacturingLicenseNumber;
    /**
     *申请日期，年
     */
    private String applicationDate;
    /**
     *出厂编号
     */
    private String factoryLicenseNumber;
    /**
     *购买时间
     */
    private String purchaseTime;
    /**
     *产权单位详细地址
     */
    private String unitAddress;
    /**
     *法定代表人
     */
    private String legalRepresentative;
    /**
     *法定代表人联系电话
     */
    private String legalRepresentativeContactNumber;
    /**
     *技术负责人
     */
    private String technicalDirector;
    /**
     *技术负责人联系电话
     */
    private String technicalDirectorContactNumber;
    /**
     *设备管理负责人
     */
    private String equipmentManager;
    /**
     *设备管理负责人联系电话
     */
    private String equipmentManagerContactNumber;
    /**
     *起重重量
     */
    private String liftingWeight;
    /**
     *产品合格证文件路径
     */
    private String productCertificationPath;
    /**
     *设备制造许可证文件路径
     */
    private String equipmentManufacturingLicensePath;
    /**
     *起重机备案申请表文件路径
     */
    private String filingApplicationFormPath;
    /**
     *购销凭证文件路径
     */
    private String purchaseSaleCertificatePath;
    /**
     *营业执照文件路径
     */
    private String businessLicensePath;
    /**
     *审核状态，0未审核，1审核通过，2驳回
     */
    private String auditStatus;
    /**
     *审核退回原因
     */
    private String auditReject;
    /**
     *审核时间
     */
    private Date auditTime;
    /**
     *备案编号
     */
    private String filingCode;
    /**
     *产权单位所属区域编码
     */
    private Long unitAreaCode;
    /**
     * 营业执照号
     */
    private String businessLicenseNumber;
    /**
     * 是否有旧的设备备案编号
     */
    private String oldNumberFlag;
    /**
     * 旧备案编号
     */
    private String oldNumber;
    /**
     *是否申请备案注销
     */
    private String isFilingCancel;

    /**
     * 出厂价格
     */
    private Double exFactoryPrice;

    /**
     * 设计使用年限
     */
    private Integer designServiceLife;

    /**
     * 下次安全评估时间
     */
    private String nextSaftyEvalTime;


    /**
     * 产权单位法定代表人身份证件类型代码
     */
    private String ownerLPCodeType;

    /**
     * 产权单位法定代表人身份证件号
     */
    private String ownerLPCode;

    /**
     * 额定起重力矩，塔式起重机基本臂长时最大幅度与相应额定起重量的乘积，单位为t·m。
     */
    private Double ratedLoadMoment;

    /**
     * 起重臂长度，塔式起重机起重臂结构外形长度，单位为m
     */
    private Double lengthOfCraneJib;

    /**
     * 最大工作幅度，塔式起重机回转中心线至吊钩（空载时）垂直中心线的最大水平距离，单位为m。
     */
    private Double maxWorkingRange;

    /**
     * 最大幅度额定起重量，塔式起重机最大工作幅度处的额定起重量，单位为t。
     */
    private Double ratedLiftingCATWR;

    /**
     * 独立起升高度，塔式起重机最大独立起升高度，指塔式起重机运行或固定独立状态时，空载、塔身处于最大高度、吊钩处于最小幅度的最大允许高度处，吊钩支撑面对塔式起重机基准面的最大垂直距离，单位为m。
     */
    private Double nonTieInLoadLH;

    /**
     * 最大起升高度，根据说明书规定，塔式起重机使用附着时所能达到的最大起升高度，单位为m。
     */
    private Double maxLiftingHeight;

    /**
     * 塔式起重机标准节参数，塔式起重机标准节外形结构参数，（长×宽×高）单位为mm。
     */
    private String standardSectionPOfTC;

    /**
     * 塔式起重机标准节主要结构件规格，标准节主弦杆的材料规格和材质。
     */
    private String stdSectionMainSSOfTC;

    /**
     * 塔式起重机加强节参数，塔式起重机加强节外形结构参数，（长×宽×高）单位为mm。
     */
    private String reinforceSectionPOfTC;

    /**
     * 主要结构件唯一编号，由制造单位编制的起重机械主要结构件唯一编号，塔式起重机主要结构件指回转支承，施工升降机和物料提升机主要结构件指吊笼。
     */
    private String mainStrUniqueCode;

    /**
     * 施工升降机用途类型，依据可运载货物类型对施工升降机的用途类型进行划分（代码集详见附录A.3施工升降机用途类型代码）。
     */
    private String constructionHUTypes;

    /**
     * 最大提升高度，吊笼运行至最高上限位置时，吊笼内底平面与底座底平面间的垂直距离，单位为m。
     */
    private Double maxHoistingHeight;

    /**
     * 额定提升速度，指装载额定载荷，在额定功率下稳定上升的速度，单位为m/min。
     */
    private Double ratedLiftingSpeed;

    /**
     * 电动机总功率，建筑起重机械正常工作条件下的所搭载电动机的额定总功率，单位为kW。
     */
    private Double totalElectricMotorPower;

    /**
     * 防坠安全器型号，防坠安全器（或超速安全装置）的型号。
     */
    private String antifallSafetyDeviceType;

    /**
     * 运载装置（吊笼）净空尺寸，吊笼不计算侧壁厚度的内空间净尺寸，（长×宽×高）单位为m。
     */
    private String carrierUnitHD;

    /**
     * 其他起重机械类型，用于区分其他起重机械里的门式起重机和履带式起重机（代码集详见附录A.4其他起重机械类型代码）。
     */
    private String otherCraneType;

    /**
     * 门式起重机跨度，门式起重机两个支承中心线之间的水平距离，单位m。
     */
    private Double portalCraneSpan;

    /**
     * 首次备案日期，该建筑起重机械首次办理备案的日期，按照公元纪年精确至日。用阿拉伯数字将年、用于照面展示时，月、日标全，月、日不标虚位。
     */
    private Date firstFilingDate;

    /**
     * 证书状态代码
     */
    private String certState;

    /**
     * 证书状态描述
     */
    private String certStateDesc;

    /**
     * 关联证照标识
     */
    private String associatedCertId;

    /**
     * 操作类型。
     */
    private String operateType;

    /**
     * eid
     */
    private String eid;
    /**
     * imageBase
     */
    private String imageBase;

    /**
     * 电子证照唯一标识
     *
     */
    private  String fileNumber;
    /**
     * 预览地址
     */
    private String fileUrl;


    private String equipmentUniqueCode;
    /**
     *业务id
     */
    private String businessDataId;


    public String getFilingCode() {
        return filingCode;
    }

    public void setFilingCode(String filingCode) {
        this.filingCode = filingCode.trim();
    }


}
