package com.tzstcl.filing.equipmentFilingApplication.model;

import com.tzstcl.base.model.BaseModel;
import lombok.Data;
import lombok.NonNull;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
@Data
public class FilingEquipmentFilingApplicationVo extends BaseModel<FilingEquipmentFilingApplicationVo> implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *审核发起人id
     */
    private String userId;
    /**
     *产权单位名称
     */
    @NonNull
    private String propertyUnit;
    /**
     *产权单位社会统一信用代码
     */
    @NonNull
    private String propertyUnitCode;
    /**
     *设备名称
     */
    //@NonNull
    private String deviceName;
    /**
     *规格型号
     */
    @NonNull
    private String specificationModel;
    /**
     *生产厂家
     */
    @NonNull
    private String manufacturer;

    /**
     * 设备类型 T塔式起重机，S施工升降机（不含物料提升机），W物料提升机，Q其他起重机械
     */
    @NonNull
    private String equipmentType;

    /**
     *出厂时间
     */
    @NonNull
    private String factoryTime;
    /**
     *制造许可证编号
     */
    @NonNull
    private String manufacturingLicenseNumber;
    /**
     *申请日期，年
     */
    @NonNull
    private String applicationDate;
    /**
     *出厂编号
     */
    @NonNull
    private String factoryLicenseNumber;
    /**
     *购买时间
     */
    private String purchaseTime;
    /**
     *产权单位详细地址
     */
    @NonNull
    private String unitAddress;
    /**
     *法定代表人
     */
    @NonNull
    private String legalRepresentative;
    /**
     *法定代表人联系电话
     */
    @NonNull
    private String legalRepresentativeContactNumber;
    /**
     *技术负责人
     */
    @NonNull
    private String technicalDirector;
    /**
     *技术负责人联系电话
     */
    @NonNull
    private String technicalDirectorContactNumber;
    /**
     *设备管理负责人
     */
    @NonNull
    private String equipmentManager;
    /**
     *设备管理负责人联系电话
     */
    @NonNull
    private String equipmentManagerContactNumber;
    /**
     *起重重量
     */
    private String liftingWeight;
    /**
     *产品合格证文件路径
     */
    @NonNull
    private String productCertificationPath;
    /**
     *设备制造许可证文件路径
     */
    @NonNull
    private String equipmentManufacturingLicensePath;
    /**
     *起重机备案申请表文件路径
     */
    @NonNull
    private String filingApplicationFormPath;
    /**
     *购销凭证文件路径
     */
    @NonNull
    private String purchaseSaleCertificatePath;
    /**
     *营业执照文件路径
     */
    @NonNull
    private String businessLicensePath;
    /**
     *审核状态，0未审核，1审核通过，2驳回
     */
    @NonNull
    private String auditStatus;
    /**
     *审核退回原因
     */
    private String auditReject;
    /**
     *审核时间
     */
    private Date auditTime;
    /**
     *备案编号
     */
    @NonNull
    private String filingCode;
    /**
     *业务ID
     */
    @NonNull
    private String businessId;
    /**
     *产权单位所属区域编码
     */
    @NonNull
    private Long unitAreaCode;
    /**
     * 营业执照号
     */
    private String businessLicenseNumber;
    /**
     * 是否有旧的设备备案编号
     */
    @NonNull
    private String oldNumberFlag;
    /**
     * 旧备案编号
     */
    private String oldNumber;
    /**
     *是否申请过备案注销
     */
    private String isFilingCancel;
    /**
     *appId
     */
    private String appId;
    /**
     *appKey
     */
    private String appKey;

}
