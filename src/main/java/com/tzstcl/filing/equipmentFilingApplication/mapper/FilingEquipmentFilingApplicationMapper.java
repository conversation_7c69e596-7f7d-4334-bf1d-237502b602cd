package com.tzstcl.filing.equipmentFilingApplication.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import com.tzstcl.filing.area.model.Area;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年11月05日
 * 说明：起重机设备备案申请Mapper
 */
@Mapper
public interface FilingEquipmentFilingApplicationMapper extends BaseMapper<FilingEquipmentFilingApplication>  {

    /**
    *
    * 批量增加起重机设备备案申请
    * @param filingEquipmentFilingApplicationList
    * <AUTHOR>
    * @date 2019年11月05日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<FilingEquipmentFilingApplication> filingEquipmentFilingApplicationList);

    Integer getCode();

    Integer addCode(Integer code);

    List<FilingEquipmentFilingApplication> selectListByAreaCode(List<Long> areaList, String auditStatus);

    List<FilingEquipmentFilingApplication> selectListByCondition(String year, List<Long> areaList);

    FilingEquipmentFilingApplication getByFilingCode(String code);

    FilingEquipmentFilingApplication getOneByFactoryNum(String manufacturer,String factoryLicenseNumber);

    FilingEquipmentFilingApplication selectByFilingCode(String filingCode);

    List<FilingEquipmentFilingApplication> selectListGroupByFilingCode(String year, List<Long> areaList);
}
