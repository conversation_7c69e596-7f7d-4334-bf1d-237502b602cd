package com.tzstcl.filing.equipmentFilingApplication.service.impl;

import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.filing.area.model.Area;
import com.tzstcl.filing.code.model.Code;
import com.tzstcl.filing.code.service.CodeService;
import com.tzstcl.filing.equipmentFiling.mapper.FilingEquipmentFilingMapper;
import com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling;
import com.tzstcl.filing.equipmentFilingApplication.service.FilingEquipmentFilingApplicationService;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication;
import com.tzstcl.filing.equipmentFilingApplication.mapper.FilingEquipmentFilingApplicationMapper;
import com.tzstcl.framework.shiro.ShiroUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年11月05日
 * 说明：起重机设备备案申请ServiceImpl
 */
@Service
@Slf4j
public class FilingEquipmentFilingApplicationServiceImpl extends BaseServiceImpl<FilingEquipmentFilingApplicationMapper, FilingEquipmentFilingApplication> implements FilingEquipmentFilingApplicationService {

    @Autowired
    private CodeService codeService;

    private static final String codeType = "device";
    @Resource
    private FilingEquipmentFilingMapper filingEquipmentFilingMapper;

    /**
     * 批量增加起重机设备备案申请
     *
     * @param filingEquipmentFilingApplicationList
     * @return Integer 插入的记录数
     * <AUTHOR>
     * @date 2019年11月05日
     */
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<FilingEquipmentFilingApplication> filingEquipmentFilingApplicationList) {
        return this.mapper.insertBatch(filingEquipmentFilingApplicationList);
    }

    @Transactional(readOnly = false, rollbackFor = Exception.class)
    @Override
    public String filingCode(String cityCode, String deviceType) {
        //获取当前日期
        Calendar cal = Calendar.getInstance();
        //编号第四五位年份
        int year = cal.get(Calendar.YEAR) % 100;
        //编号第六七位月份
        int month = cal.get(Calendar.MONTH) + 1;
        //月份数字 字符串化
        String monthStr = new DecimalFormat("00").format(month);
        Code code = codeService.getCode(codeType, deviceType);
        try {
            //编号序列化
            String codeStr = new DecimalFormat("0000").format(code.getValue());
            //更新编号，+1顺延
            code.setValue(code.getValue()+1);
            codeService.update(code);
            //拼接编号
            String deviceCode = "豫备" + cityCode + deviceType + year + monthStr + codeStr;
            FilingEquipmentFiling filingEquipmentFiling = new FilingEquipmentFiling();
            filingEquipmentFiling.setFilingCode(deviceCode);
            List<FilingEquipmentFiling> list = filingEquipmentFilingMapper.select(filingEquipmentFiling);
            for (int i=1;list.size()>0;i++){
                codeStr = new DecimalFormat("0000").format(code.getValue());
                code.setValue(code.getValue()+1);
                codeService.update(code);
                deviceCode = "豫备" + cityCode + deviceType + year + monthStr + codeStr;
                filingEquipmentFiling = new FilingEquipmentFiling();
                filingEquipmentFiling.setFilingCode(deviceCode);
                list = filingEquipmentFilingMapper.select(filingEquipmentFiling);
            }
            return deviceCode;
        } catch (Exception e) {
            log.debug(e.getMessage());
            //系统报错，返回空字符串，controller层根据编号判断生成编号是否出现问题
            return "";
        }
    }

    @Transactional(readOnly = false, rollbackFor = Exception.class)
    @Override
    public int insert(FilingEquipmentFilingApplication filingEquipmentFilingApplication) {
        try {
            if(StringUtils.isEmpty(filingEquipmentFilingApplication.getCreateBy())){
                Long createBy = ShiroUtils.getUserId();
                filingEquipmentFilingApplication.setCreateBy(createBy + "");
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        filingEquipmentFilingApplication.setCreateTime(new Date());
        return this.mapper.insert(filingEquipmentFilingApplication);
    }

    @Override
    public List<FilingEquipmentFilingApplication> selectListByAreaCode(List<Long> areaList, String auditStatus) {
        return mapper.selectListByAreaCode(areaList,auditStatus);
    }

    @Override
    public List<FilingEquipmentFilingApplication> selectListByAreaCode(List<Long> areaList, String auditStatus, String year) {
        return null;
    }

    @Override
    public List<FilingEquipmentFilingApplication> selectListByCondition(String year, List<Long> areaList) {
        return mapper.selectListByCondition(year,areaList);
    }

    @Override
    public FilingEquipmentFilingApplication getByFilingCode(String code) {
        return mapper.getByFilingCode(code);
    }

    @Override
    public FilingEquipmentFilingApplication getOneByFactoryNum(String manufacturer, String factoryLicenseNumber) {
        return mapper.getOneByFactoryNum(manufacturer,factoryLicenseNumber);
    }

    @Override
    public FilingEquipmentFilingApplication selectByFilingCode(String filingCode) {
        return mapper.selectByFilingCode(filingCode);
    }

    @Override
    public List<FilingEquipmentFilingApplication> selectListGroupByFilingCode(String year, List<Long> areaList) {
        return mapper.selectListGroupByFilingCode(year,areaList);
    }
}
