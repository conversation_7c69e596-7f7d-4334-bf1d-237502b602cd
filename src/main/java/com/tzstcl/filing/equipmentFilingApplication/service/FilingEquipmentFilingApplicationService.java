package com.tzstcl.filing.equipmentFilingApplication.service;

import com.tzstcl.base.service.BaseService;
import com.tzstcl.filing.area.model.Area;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年11月05日
 * 说明：起重机设备备案申请Servic
 */
public interface FilingEquipmentFilingApplicationService extends BaseService<FilingEquipmentFilingApplication> {

    /**
     * 批量增加起重机设备备案申请
     *
     * @param filingEquipmentFilingApplicationList
     * @return Integer 插入的记录数
     * <AUTHOR>
     * @date 2019年11月05日
     */
    Integer insertBatch(List<FilingEquipmentFilingApplication> filingEquipmentFilingApplicationList);

    /**
     * 获取最新完整备案编号
     * @param code       地市编号
     * @param deviceType 设备类型
     * @return
     * @Anthor juziqiang
     * @Date 10:05 2020/4/22
     */
    String filingCode(String code, String deviceType);

    int insert(FilingEquipmentFilingApplication filingEquipmentFilingApplication);

    List<FilingEquipmentFilingApplication> selectListByAreaCode(List<Long> areaList, String auditStatus);

    List<FilingEquipmentFilingApplication> selectListByAreaCode(List<Long> areaList, String auditStatus,String year);

    List<FilingEquipmentFilingApplication> selectListByCondition(String year, List<Long> areaList);

    FilingEquipmentFilingApplication getByFilingCode(String code);

    FilingEquipmentFilingApplication getOneByFactoryNum(String manufacturer, String factoryLicenseNumber);

    FilingEquipmentFilingApplication selectByFilingCode(String filingCode);

    List<FilingEquipmentFilingApplication> selectListGroupByFilingCode(String year, List<Long> areaList);
}
