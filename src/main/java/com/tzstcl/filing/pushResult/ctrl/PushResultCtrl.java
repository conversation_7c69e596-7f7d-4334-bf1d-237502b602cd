package com.tzstcl.filing.pushResult.ctrl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.filing.pushResult.model.PushResult;
import com.tzstcl.filing.pushResult.service.PushResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：推送结果表Controller
 */
@Controller
@RequestMapping("/admin/pushResult")
public class PushResultCtrl extends BaseCtrl {

    @Autowired
    private PushResultService pushResultService;

    /**
    * list页面导航
    * @return
    */
    @RequiresPermissions("pushResult:view")
    @RequestMapping("/toList")
    public String toList() {
        return "admin/filing/pushResult/pushResultList";
    }

    /**
    * 获取查询的分页数据
    * @param pushResult
    * @return
    */
    @RequestMapping("/list")
    @RequiresPermissions("pushResult:view")
    @ResponseBody
    public PageInfo<PushResult> list(PushResult pushResult) {
        return  pushResultService.selectPage(pushResult);
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"pushResult:edite","pushResult:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        if(null != id){
            model.addAttribute("pushResult" ,pushResultService.getOne(id));
        }
        return "admin/filing/pushResult/pushResultForm";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("pushResult:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("pushResult" ,pushResultService.getOne(id));
        }
        return "admin/filing/pushResult/pushResultDetail";
    }

    /**
     * 新增
     * @param pushResult
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"pushResult:add"})
    @ResponseBody
    public AjaxResult save(@Valid PushResult pushResult) {
         return toAjax(pushResultService.add(pushResult));
    }

    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("pushResult:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("pushResult" ,pushResultService.getOne(id));
        }
        return "admin/filing/pushResult/pushResultEdit";
    }

    /**
    * 更新
    * @param pushResult
    * @return
    */
    @PostMapping("/update")
    @RequiresPermissions("pushResult:edit")
    @ResponseBody
    public AjaxResult update(@Valid PushResult pushResult) {
        return toAjax(pushResultService.update(pushResult));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("pushResult:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(pushResultService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("pushResult:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",pushResultService.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param pushResult
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"pushResult:add", "pushResult:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(PushResult pushResult) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = pushResult.getId();
        pushResult.setId(null);
        List<PushResult> pushResultList = pushResultService.selectList(pushResult);
        if(StringUtils.isNotEmpty(pushResultList)){
            if(pushResultList.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(pushResultList.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }

}
