package com.tzstcl.filing.pushResult.model;

import com.tzstcl.base.model.BaseModel;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：推送结果表实体类
 */
@Data
public class PushResult extends BaseModel<PushResult> implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
     *关联id
     */
    private String associationId;
    /**
     *返回代码
     */
    private String returnCode;
    /**
     *返回信息
     */
    private String returnMsg;
    /**
     *返回数据结果集
     */
    private String returnData;
    /**
     *创建时间
     */
    private String createDate;
    /**
     *创建人
     */
    private String creatAt;
    /**
     * B 备案 S 使用登记
     */
    private String type;

}
