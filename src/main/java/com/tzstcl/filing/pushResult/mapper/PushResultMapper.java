package com.tzstcl.filing.pushResult.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import com.tzstcl.filing.pushResult.model.PushResult;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：推送结果表Mapper
 */
@Mapper
public interface PushResultMapper extends BaseMapper<PushResult>  {

    /**
    *
    * 批量增加推送结果表
    * @param pushResultList
    * <AUTHOR>
    * @date 2024年05月23日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<PushResult> pushResultList);

}