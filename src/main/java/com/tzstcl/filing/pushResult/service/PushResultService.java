package com.tzstcl.filing.pushResult.service;

import com.tzstcl.base.service.BaseService;
import com.tzstcl.filing.pushResult.model.PushResult;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：推送结果表Servic
 */
public interface PushResultService extends BaseService<PushResult> {

    /**
    *
    * 批量增加推送结果表
    * @param pushResultList
    * <AUTHOR>
    * @date 2024年05月23日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<PushResult> pushResultList);

}