package com.tzstcl.filing.pushResult.service.impl;

import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.filing.pushResult.service.PushResultService;
import com.tzstcl.filing.pushResult.model.PushResult;
import com.tzstcl.filing.pushResult.mapper.PushResultMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：推送结果表ServiceImpl
 */
@Service
public class PushResultServiceImpl extends BaseServiceImpl<PushResultMapper,PushResult> implements PushResultService{

    /**
    *
    * 批量增加推送结果表
    * @param pushResultList
    * <AUTHOR>
    * @date 2024年05月23日
    * @return Integer 插入的记录数
    */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<PushResult> pushResultList){return this.mapper.insertBatch(pushResultList);}

}