package com.tzstcl.filing.access.service.impl;

import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.commons.utils.AppUtils;
import com.tzstcl.commons.utils.Convert;
import com.tzstcl.filing.access.mapper.AccessKeyMapper;
import com.tzstcl.filing.access.model.AccessKey;
import com.tzstcl.filing.access.service.IAccessKeyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 推送用户唯一标识Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-09
 */
@Service
public class AccessKeyServiceImpl extends BaseServiceImpl<AccessKeyMapper, AccessKey> implements IAccessKeyService
{
    @Autowired
    private AccessKeyMapper accessKeyMapper;

    /**
     * 查询推送用户唯一标识
     *
     * @param accessId 推送用户唯一标识ID
     * @return 推送用户唯一标识
     */
    @Override
    public AccessKey selectAccessKeyById(Long accessId)
    {
        return accessKeyMapper.selectAccessKeyById(accessId);
    }

    /**
     * 查询推送用户唯一标识列表
     *
     * @param accessKey 推送用户唯一标识
     * @return 推送用户唯一标识
     */
    @Override
    public List<AccessKey> selectAccessKeyList(AccessKey accessKey)
    {
        return this.mapper.select(accessKey);
    }

    /**
     * 新增推送用户唯一标识
     *
     * @param accessKey 推送用户唯一标识
     * @return 结果
     */
    @Transactional(readOnly = false)
    @Override
    public int insertAccessKey(AccessKey accessKey)
    {
        String appId = AppUtils.getAppId();
        String appKey = AppUtils.getAppSecret(appId);
        accessKey.setAppId(appId);
        accessKey.setAppKey(appKey);
        accessKey.setPushTime(new Date());
        return this.mapper.insert(accessKey);
    }

    /**
     * 修改推送用户唯一标识
     *
     * @param accessKey 推送用户唯一标识
     * @return 结果
     */
    @Transactional(readOnly = false)
    @Override
    public int updateAccessKey(AccessKey accessKey)
    {
        accessKey.setPushTime(new Date());
        return this.mapper.update(accessKey);
    }

    /**
     * 删除推送用户唯一标识对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Transactional(readOnly = false)
    @Override
    public int deleteAccessKeyByIds(String ids)
    {
        return accessKeyMapper.deleteAccessKeyByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除推送用户唯一标识信息
     *
     * @param accessId 推送用户唯一标识ID
     * @return 结果
     */
    @Transactional(readOnly = false)
    @Override
    public int deleteAccessKeyById(Long accessId)
    {
        return this.mapper.delete(accessId);
    }

    @Override
    public AccessKey selectAccessKeyByAppId(String appId) {
        return accessKeyMapper.selectAccessKeyByAppId(appId);
    }
}
