package com.tzstcl.filing.access.service;

import com.tzstcl.base.service.BaseService;
import com.tzstcl.filing.access.model.AccessKey;

import java.util.List;

/**
 * 推送用户唯一标识Service接口
 *
 * <AUTHOR>
 * @date 2024-05-09
 */
public interface IAccessKeyService extends BaseService<AccessKey>
{
    /**
     * 查询推送用户唯一标识
     *
     * @param accessId 推送用户唯一标识ID
     * @return 推送用户唯一标识
     */
    public AccessKey selectAccessKeyById(Long accessId);

    /**
     * 查询推送用户唯一标识列表
     *
     * @param accessKey 推送用户唯一标识
     * @return 推送用户唯一标识集合
     */
    public List<AccessKey> selectAccessKeyList(AccessKey accessKey);

    /**
     * 新增推送用户唯一标识
     *
     * @param accessKey 推送用户唯一标识
     * @return 结果
     */
    public int insertAccessKey(AccessKey accessKey);

    /**
     * 修改推送用户唯一标识
     *
     * @param accessKey 推送用户唯一标识
     * @return 结果
     */
    public int updateAccessKey(AccessKey accessKey);

    /**
     * 批量删除推送用户唯一标识
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteAccessKeyByIds(String ids);

    /**
     * 删除推送用户唯一标识信息
     *
     * @param accessId 推送用户唯一标识ID
     * @return 结果
     */
    public int deleteAccessKeyById(Long accessId);

    AccessKey selectAccessKeyByAppId(String appId);
}
