package com.tzstcl.filing.equipmentfilingcancellation.service.impl;

import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.filing.equipmentfilingcancellation.mapper.FilingEquipmentFilingCancellationMapper;
import com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellation;
import com.tzstcl.filing.equipmentfilingcancellation.service.FilingEquipmentFilingCancellationService;
import com.tzstcl.framework.shiro.ShiroUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：huminghui
 * 日期：2019年11月06日
 * 说明：起重机设备注销申请ServiceImpl
 */
@Service
public class FilingEquipmentFilingCancellationServiceImpl extends BaseServiceImpl<FilingEquipmentFilingCancellationMapper, FilingEquipmentFilingCancellation> implements FilingEquipmentFilingCancellationService {

    /**
    *
    * 批量增加起重机设备注销申请
    * @param filingEquipmentFilingCancellationList
    * <AUTHOR>
    * @date 2019年11月06日
    * @return Integer 插入的记录数
    */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<FilingEquipmentFilingCancellation> filingEquipmentFilingCancellationList){return this.mapper.insertBatch(filingEquipmentFilingCancellationList);}

    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public int insert(FilingEquipmentFilingCancellation filingEquipmentFilingCancellation) {
        try {
            Long createBy = ShiroUtils.getUserId();
            filingEquipmentFilingCancellation.setCreateBy(createBy + "");
        }catch (Exception e){
            e.printStackTrace();
        }
        filingEquipmentFilingCancellation.setCreateTime(new Date());
        return this.mapper.insert(filingEquipmentFilingCancellation);
    }

    @Override
    public List<FilingEquipmentFilingCancellation> selectListByAreaCode(List<Long> areaList, String auditStatus) {
        return mapper.selectListByAreaCode(areaList,auditStatus);
    }

    @Override
    public List<FilingEquipmentFilingCancellation> selectListByCondition(String year, List<Long> areaList) {
        return mapper.selectListByCondition(year,areaList);
    }

    @Override
    public FilingEquipmentFilingCancellation getOneByCode(String filingCode) {
        return mapper.getOneByCode(filingCode);
    }

    @Override
    public List<FilingEquipmentFilingCancellation> selectListGroupByFilingCode(String year, List<Long> areaList) {
        return mapper.selectListGroupByFilingCode(year,areaList);
    }
}
