package com.tzstcl.filing.equipmentfilingcancellation.ctrl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.DateUtils;
import com.tzstcl.commons.utils.SnowflakeIdWorker;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.commons.utils.UploadFileUtil;
import com.tzstcl.filing.access.model.AccessKey;
import com.tzstcl.filing.access.service.IAccessKeyService;
import com.tzstcl.filing.annotation.VerifySign;
import com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling;
import com.tzstcl.filing.equipmentFiling.service.FilingEquipmentFilingService;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplicationVo;
import com.tzstcl.filing.equipmentFilingApplication.service.FilingEquipmentFilingApplicationService;
import com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellation;
import com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellationVo;
import com.tzstcl.filing.equipmentfilingcancellation.service.FilingEquipmentFilingCancellationService;
import com.tzstcl.filing.file.model.FilingFile;
import com.tzstcl.filing.file.service.FilingFileService;
import com.tzstcl.framework.shiro.ShiroUtils;
import com.tzstcl.sys.user.model.SysUser;
import com.tzstcl.sys.user.service.impl.SysDictServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：huminghui
 * 日期：2019年11月06日
 * 说明：起重机设备注销申请Controller
 */
@Controller
@RequestMapping("/admin/equipmentFilingCancellation")
public class FilingEquipmentFilingCancellationCtrl extends BaseCtrl {

    @Autowired
    private FilingEquipmentFilingCancellationService filingEquipmentFilingCancellationService;
    @Autowired
    private FilingFileService filingFileService;
    @Autowired
    private FilingEquipmentFilingService filingEquipmentFilingService;
    @Autowired
    private IAccessKeyService accessKeyService;
    @Autowired
    private SysDictServiceImpl sysDictService;
    @Autowired
    private FilingEquipmentFilingApplicationService filingEquipmentFilingApplicationService;
    /**
     * list页面导航
     * @return
     */
    @RequiresPermissions("cancel:view")
    @RequestMapping("/toList")
    public String toList(Model model) {
        model.addAttribute("dict",sysDictService.findByType("city"));
        return "admin/filing/equipmentfilingcancellation/cancellationList";
    }

    /**
     * 获取查询的分页数据
     * @param filingEquipmentFilingCancellation
     * @return
     */
    @RequestMapping("/list")
    @RequiresPermissions("cancel:view")
    @ResponseBody
    public PageInfo<FilingEquipmentFilingCancellation> list(FilingEquipmentFilingCancellation filingEquipmentFilingCancellation) {
//        SysUser sysUser = ShiroUtils.getUser();
//        if (!sysUser.getId().equals(1L)){
//            if (sysUser.getUserType().equals("0")){
//                filingEquipmentFilingCancellation.setUserId(sysUser.getMobile());
//            }
//        }
        return  filingEquipmentFilingCancellationService.selectPage(filingEquipmentFilingCancellation);
    }

    /**
     * 公示获取查询的分页数据
     * @param filingEquipmentFilingCancellation
     * @return
     */
    @RequestMapping("/seeList")
    @ResponseBody
    public PageInfo<FilingEquipmentFilingCancellation> seeList(FilingEquipmentFilingCancellation filingEquipmentFilingCancellation) {
        return  filingEquipmentFilingCancellationService.selectPage(filingEquipmentFilingCancellation);
    }


    /**
     * 点击新增申请，把id和userId返回到页面
     * @return
     */
    @RequiresPermissions("cancel:add")
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        SysUser user = ShiroUtils.getUser();
        model.addAttribute("user",user);
        model.addAttribute("userId",ShiroUtils.getUser().getMobile());
        model.addAttribute("id", SnowflakeIdWorker.getInstance().nextId());
        model.addAttribute("dict",sysDictService.findByType("city"));

        return "admin/filing/equipmentfilingcancellation/cancellationForm";
    }
    /**
     *  文件上传
     */
    @RequestMapping("/uploadFile")
    @ResponseBody
    public AjaxResult uploadFile(MultipartFile uploadFile) throws IOException {
        String type = "filing";
        Map<String, Object> result = UploadFileUtil.saveFile(uploadFile, type);
        return AjaxResult.success("操作成功", result);
    }
    /**
     * 新增，保存数据
     * @param
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"cancel:add"})
    @ResponseBody
    public AjaxResult save(@Valid FilingEquipmentFilingCancellation filingEquipmentFilingCancellation) throws IOException {
        if (null!=filingEquipmentFilingCancellation.getId()){  //判断是否为重新提交，是的话将状态改为 3重新提交
            FilingEquipmentFilingCancellation cancellation = new FilingEquipmentFilingCancellation();
            cancellation.setId(filingEquipmentFilingCancellation.getId());
            cancellation.setAuditStatus("3");
            filingEquipmentFilingCancellationService.update(cancellation);
        }
        filingEquipmentFilingCancellation.setApplicationDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        int i = filingEquipmentFilingCancellationService.insert(filingEquipmentFilingCancellation);
        if (i>0){
            //需要根据备案编号将备案申请表设备注销状态改为1
            FilingEquipmentFilingApplication application = filingEquipmentFilingApplicationService.selectByFilingCode(filingEquipmentFilingCancellation.getFilingCode());
            application.setIsFilingCancel("1");
            filingEquipmentFilingApplicationService.update(application);
        }
        return toAjax(i);
    }

    /**
     * 推送备案注销数据
     * @param
     * @return
     */
    @CrossOrigin
    @PostMapping("/addFilingCancellation")
    @ResponseBody
    public AjaxResult addFilingCancellation(@RequestParam("appId") String appId,
                                            @RequestParam("appKey") String appKey,
//                                            @RequestParam("auditStatus") String auditStatus,   //审核状态 0未审核，1审核通过，2驳回
//                                            @RequestParam("applicationDate") String applicationDate,   //申请日期
//                                            //@RequestParam("equipmentType") String equipmentType,   //设备类型
//                                            @RequestParam("deviceName") String deviceName,   //设备名称
//                                            @RequestParam("specificationModel") String specificationModel, //规格型号
//                                            @RequestParam("purchaseTime") String purchaseTime,  //购买时间
//                                            //@RequestParam(name = "liftingWeight", required = false) String liftingWeight,  //起重重量
//                                            @RequestParam("propertyUnit") String propertyUnit, //产权单位名称
//                                            @RequestParam("unitAddress") String unitAddress, //产权单位详细地址
//                                            //@RequestParam("remarks") String remarks, //产权单位属地
//                                            //@RequestParam("propertyUnitCode") String propertyUnitCode, //产权单位社会统一信用代码
//                                            @RequestParam("legalRepresentative") String legalRepresentative, //法定代表人
//                                            @RequestParam("legalRepresentativeContactNumber") String legalRepresentativeContactNumber, //法定代表人联系电话
//                                            @RequestParam("technicalDirector") String technicalDirector, //技术负责人
//                                            @RequestParam("technicalDirectorContactNumber") String technicalDirectorContactNumber, //技术负责人联系电话
//                                            @RequestParam("equipmentManager") String equipmentManager, //设备管理负责人
//                                            @RequestParam("equipmentManagerContactNumber") String equipmentManagerContactNumber, //设备管理负责人联系电话
//                                            @RequestParam("manufacturer") String manufacturer, //生产厂家
//                                            @RequestParam("factoryTime") String factoryTime, //出厂时间
//                                            @RequestParam("factoryLicenseNumber") String factoryLicenseNumber, //出厂编号
//                                            @RequestParam("manufacturingLicenseNumber") String manufacturingLicenseNumber, //制造许可证编号
//                                            @RequestParam("filingPaper") String filingPaper, //备案证文件路径
//                                            @RequestParam("cancellationApplicationFormPath") String cancellationApplicationFormPath, //注销申请表文件路径
//                                            @RequestParam("filingCard") String filingCard, //备案牌文件路径
//                                            @RequestParam("cancellationReason") String cancellationReason, //注销原因
//                                            @RequestParam("businessId") String businessId //业务ID
                                            @RequestBody FilingEquipmentFilingCancellationVo filingCancellation

    ){
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key){
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())){
            return AjaxResult.error("appKey输入有误");
        }
        //判断该数据是否重复
        FilingEquipmentFilingCancellation filingApplication1 = new FilingEquipmentFilingCancellation();
        filingApplication1.setFilingCode(filingCancellation.getFilingCode());
        List<FilingEquipmentFilingCancellation> list = filingEquipmentFilingCancellationService.selectList(filingApplication1);
        if (list.size()>0){
            return AjaxResult.error("ID为"+filingCancellation.getBusinessId()+"的数据已存在,请勿重复推送");
        }
        FilingEquipmentFilingCancellation filingApplication = new FilingEquipmentFilingCancellation();
        filingApplication.setFilingCode(filingCancellation.getFilingCode());
        filingApplication.setBusinessId(filingCancellation.getBusinessId());
//        filingApplication.setEquipmentType(equipmentType);
        filingApplication.setApplicationDate(filingCancellation.getApplicationDate());
        filingApplication.setAuditStatus(filingCancellation.getAuditStatus());
        if(null!=filingCancellation.getDeviceName()){
            filingApplication.setDeviceName(filingCancellation.getDeviceName());
        }
        filingApplication.setSpecificationModel(filingCancellation.getSpecificationModel());
        filingApplication.setPurchaseTime(filingCancellation.getPurchaseTime());
//        if (null!=liftingWeight){
//            filingApplication.setLiftingWeight(liftingWeight);
//        }
        filingApplication.setPropertyUnit(filingCancellation.getPropertyUnit());
        filingApplication.setUnitAddress(filingCancellation.getUnitAddress());
//        filingApplication.setRemarks(remarks);
//        filingApplication.setPropertyUnitCode(propertyUnitCode);
        filingApplication.setLegalRepresentative(filingCancellation.getLegalRepresentative());
        filingApplication.setLegalRepresentativeContactNumber(filingCancellation.getLegalRepresentativeContactNumber());
        filingApplication.setTechnicalDirector(filingCancellation.getTechnicalDirector());
        filingApplication.setTechnicalDirectorContactNumber(filingCancellation.getTechnicalDirectorContactNumber());
        filingApplication.setEquipmentManager(filingCancellation.getEquipmentManager());
        filingApplication.setEquipmentManagerContactNumber(filingCancellation.getEquipmentManagerContactNumber());
        filingApplication.setManufacturer(filingCancellation.getManufacturer());
        filingApplication.setFactoryTime(filingCancellation.getFactoryTime());
        filingApplication.setFactoryLicenseNumber(filingCancellation.getFactoryLicenseNumber());
        filingApplication.setManufacturingLicenseNumber(filingCancellation.getManufacturingLicenseNumber());
        filingApplication.setCancellationReason(filingCancellation.getCancellationReason());
        filingApplication.setFilingPaper(filingCancellation.getFilingPaper());
        filingApplication.setCancellationApplicationFormPath(filingCancellation.getCancellationApplicationFormPath());
        filingApplication.setFilingCard(filingCancellation.getFilingCard());

        filingApplication.setUserId(appId);
        filingApplication.setCreateBy(appId);
        filingApplication.setCreateTime(new Date());
        Long id  = SnowflakeIdWorker.getInstance().nextId();
        filingApplication.setId(id);
        int flag = filingEquipmentFilingCancellationService.insert(filingApplication);
        if (flag ==1){
            return success("备案注销推送成功",filingApplication);
        }else{
            return error("ID为"+filingCancellation.getBusinessId()+"的备案注销推送失败");
        }
    }

    /**
     * 更新备案注销数据
     * @param
     * @return
     */
    @CrossOrigin
    @PostMapping("/updateFilingCancellation")
    @ResponseBody
    public AjaxResult updateFilingCancellation(@RequestParam("appId") String appId,
                                               @RequestParam("appKey") String appKey,
                                               @RequestBody FilingEquipmentFilingCancellationVo filingCancellation

    ){
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key){
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())){
            return AjaxResult.error("appKey输入有误");
        }
        //判断该数据是否重复
        FilingEquipmentFilingCancellation filingApplication1 = new FilingEquipmentFilingCancellation();
        filingApplication1.setFilingCode(filingCancellation.getFilingCode());
        List<FilingEquipmentFilingCancellation> list = filingEquipmentFilingCancellationService.selectList(filingApplication1);
        if (list.size()==0){
            return AjaxResult.error("ID为"+filingCancellation.getBusinessId()+"的数据不存在,请先推送数据");
        }
        FilingEquipmentFilingCancellation filingApplication = new FilingEquipmentFilingCancellation();
        filingApplication.setId(list.get(0).getId());
        filingApplication.setFilingCode(filingCancellation.getFilingCode());
        filingApplication.setBusinessId(filingCancellation.getBusinessId());
//        filingApplication.setEquipmentType(equipmentType);
        filingApplication.setApplicationDate(filingCancellation.getApplicationDate());
        filingApplication.setAuditStatus(filingCancellation.getAuditStatus());
        if(null!=filingCancellation.getDeviceName()){
            filingApplication.setDeviceName(filingCancellation.getDeviceName());
        }
        filingApplication.setSpecificationModel(filingCancellation.getSpecificationModel());
        filingApplication.setPurchaseTime(filingCancellation.getPurchaseTime());
//        if (null!=liftingWeight){
//            filingApplication.setLiftingWeight(liftingWeight);
//        }
        filingApplication.setPropertyUnit(filingCancellation.getPropertyUnit());
        filingApplication.setUnitAddress(filingCancellation.getUnitAddress());
//        filingApplication.setRemarks(remarks);
//        filingApplication.setPropertyUnitCode(propertyUnitCode);
        filingApplication.setLegalRepresentative(filingCancellation.getLegalRepresentative());
        filingApplication.setLegalRepresentativeContactNumber(filingCancellation.getLegalRepresentativeContactNumber());
        filingApplication.setTechnicalDirector(filingCancellation.getTechnicalDirector());
        filingApplication.setTechnicalDirectorContactNumber(filingCancellation.getTechnicalDirectorContactNumber());
        filingApplication.setEquipmentManager(filingCancellation.getEquipmentManager());
        filingApplication.setEquipmentManagerContactNumber(filingCancellation.getEquipmentManagerContactNumber());
        filingApplication.setManufacturer(filingCancellation.getManufacturer());
        filingApplication.setFactoryTime(filingCancellation.getFactoryTime());
        filingApplication.setFactoryLicenseNumber(filingCancellation.getFactoryLicenseNumber());
        filingApplication.setManufacturingLicenseNumber(filingCancellation.getManufacturingLicenseNumber());
        filingApplication.setCancellationReason(filingCancellation.getCancellationReason());
        filingApplication.setFilingPaper(filingCancellation.getFilingPaper());
        filingApplication.setCancellationApplicationFormPath(filingCancellation.getCancellationApplicationFormPath());
        filingApplication.setFilingCard(filingCancellation.getFilingCard());

        filingApplication.setUpdateBy(appId);
        filingApplication.setUpdateTime(new Date());
        int flag = filingEquipmentFilingCancellationService.update(filingApplication);
        if (flag ==1){
            return success("备案注销修改成功",filingApplication);
        }else{
            return error("ID为"+filingCancellation.getBusinessId()+"的备案注销修改失败");
        }
    }

    /**
     * form页面导航 详情
     * @return
     */
    @RequiresPermissions("cancel:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentFilingCancellation" ,filingEquipmentFilingCancellationService.getOne(id));
            model.addAttribute("filepath",filingFileService.getFiles(id));
            model.addAttribute("dict",sysDictService.findByType("city"));
        }
        return "admin/filing/equipmentfilingcancellation/cancellationDetail";
    }
    /**
     * 审核页详情
     * @return
     */
    @RequiresPermissions("cancel:examine")
    @GetMapping("/audit/{id}")
    public String audit(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentFilingCancellation" ,filingEquipmentFilingCancellationService.getOne(id));
            model.addAttribute("filepath",filingFileService.getFiles(id));
            model.addAttribute("dict",sysDictService.findByType("city"));
        }

        return "admin/filing/equipmentfilingcancellation/cancellationAudit";
    }
    /**
     *  审核通过
     * @param
     * @return
     */
    @PostMapping("/auditpass")
    @RequiresPermissions("cancel:examine")
    @ResponseBody
    public AjaxResult auditpass(@Valid FilingEquipmentFilingCancellation filingEquipmentFilingCancellation) {
        Long id = filingEquipmentFilingCancellation.getId();
        FilingEquipmentFilingCancellation filingEquipmentFilingCancellation1 = filingEquipmentFilingCancellationService.getOne(id);
        if (!filingEquipmentFilingCancellation1.getAuditStatus().equals("0")){
            return error("已审核的申请，不可重复审核");
        }
        filingEquipmentFilingCancellation.setAuditStatus("1");
        filingEquipmentFilingCancellation.setAuditReject("");
        filingEquipmentFilingCancellation.setAuditTime(new Date());
        int flag = filingEquipmentFilingService.cancel(filingEquipmentFilingCancellation.getFilingCode());
        if (flag ==1){
            return toAjax(filingEquipmentFilingCancellationService.update(filingEquipmentFilingCancellation));
        }else{
            return error("操作失败");
        }
    }
    /**
     *  审核驳回
     * @param
     * @return
     */
    @PostMapping("/auditupdate")
    @RequiresPermissions("cancel:examine")
    @ResponseBody
    public AjaxResult auditupdate(@Valid FilingEquipmentFilingCancellation filingEquipmentFilingCancellation) {
        filingEquipmentFilingCancellation.setAuditStatus("2");
        filingEquipmentFilingCancellation.setAuditTime(new Date());
        Integer i = filingEquipmentFilingCancellationService.update(filingEquipmentFilingCancellation);
        FilingEquipmentFilingCancellation cancellation = filingEquipmentFilingCancellationService.getOne(filingEquipmentFilingCancellation.getId());
        if (i>0){
            //需要根据备案编号将备案申请表设备注销状态改为0
            FilingEquipmentFilingApplication application = filingEquipmentFilingApplicationService.selectByFilingCode(cancellation.getFilingCode());
            application.setIsFilingCancel("0");
            filingEquipmentFilingApplicationService.update(application);
        }
        return toAjax(i);
    }

    /**
     * form页面导航 新增
     * @return
     */
    @RequiresPermissions("cancel:add")
    @GetMapping("/add")
    public String toAdd(Model model) {
        model.addAttribute("dict",sysDictService.findByType("city"));
        return "admin/filing/equipmentfilingcancellation/filingEquipmentFilingCancellationAdd";
    }



    /**
     * form页面导航 更新
     * @return
     */
    @RequiresPermissions("cancel:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentFilingCancellation" ,filingEquipmentFilingCancellationService.getOne(id));
            model.addAttribute("filepath",filingFileService.getFiles(id));
            model.addAttribute("dict",sysDictService.findByType("city"));
        }
        return "admin/filing/equipmentfilingcancellation/cancellationEdit";
    }

    /**
     * 更新,再次提交申请的时候，审核状态重置为：0审核，驳回原因清空；
     * @param filingEquipmentFilingCancellation
     * @return
     */
    @PostMapping("/update")
    @RequiresPermissions("cancel:edit")
    @ResponseBody
    public AjaxResult update(@Valid FilingEquipmentFilingCancellation filingEquipmentFilingCancellation) {
        filingEquipmentFilingCancellation.setAuditStatus("0");
        filingEquipmentFilingCancellation.setAuditReject("");
        return toAjax(filingEquipmentFilingCancellationService.update(filingEquipmentFilingCancellation));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cancel:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        String[] split = ids.split(",");
        for (String s : split) {
            FilingEquipmentFilingCancellation cancellation = filingEquipmentFilingCancellationService.getOne(Long.parseLong(s));
            //需要根据备案编号将备案申请表设备注销状态改为0
            FilingEquipmentFilingApplication application = filingEquipmentFilingApplicationService.selectByFilingCode(cancellation.getFilingCode());
            application.setIsFilingCancel("0");
            filingEquipmentFilingApplicationService.update(application);
        }
        return toAjax(filingEquipmentFilingCancellationService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("cancel:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",filingEquipmentFilingCancellationService.getOne(id));
    }

    /**
     * 校验唯一性
     * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
     * @param filingEquipmentFilingCancellation
     * @return
     */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"cancel:add", "cancel:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(FilingEquipmentFilingCancellation filingEquipmentFilingCancellation) {

        Map<String, Boolean> result = new HashMap<String, Boolean>(4);
        result.put("valid", true);
        Long id = filingEquipmentFilingCancellation.getId();
        filingEquipmentFilingCancellation.setId(null);
        List<FilingEquipmentFilingCancellation> filingEquipmentFilingCancellationList = filingEquipmentFilingCancellationService.selectList(filingEquipmentFilingCancellation);
        if (StringUtils.isNotEmpty(filingEquipmentFilingCancellationList)) {
            if (filingEquipmentFilingCancellationList.size() > 1) {
                result.put("valid", false);
            } else {
                if (id != null) {
                    // 修改
                    if (!id.equals(filingEquipmentFilingCancellationList.get(0).getId())) {
                        result.put("valid", false);
                    }
                } else {
                    // 新增
                    result.put("valid", false);
                }
            }
        }
        return result;

    }

    /**
     * 新增备案注销申请推送接口
     *
     * @return
     */
    @PostMapping("/syncFilingCancel")
    @ResponseBody
    @VerifySign
    public AjaxResult syncFilingCancel(
            @RequestParam("appId") String appId,
            @RequestParam("appKey") String appKey,
            @RequestBody() String  data
    ){
        //先进行Appid校验
        AccessKey key = accessKeyService.selectAccessKeyByAppId(appId);
        if (null == key){
            return AjaxResult.error("该appId不存在");
        }
        if (!appKey.equals(key.getAppKey())){
            return AjaxResult.error("appKey输入有误");
        }
        String str1 = data.replaceAll("\\[","");
        String str2 = str1.replaceAll("\\]","");
        JSONObject jsonObject = JSONObject.parseObject(str2);
        FilingEquipmentFilingCancellation filingCancellation = jsonObject.toJavaObject(FilingEquipmentFilingCancellation.class);
        //判断该数据是否重复
        FilingEquipmentFilingCancellation application = filingEquipmentFilingCancellationService.getOneByCode(filingCancellation.getFilingCode());
        if (null==application){
            //新增
            if (null==filingCancellation.getUserId()){
                filingCancellation.setUserId(appId);
            }
            filingCancellation.setCreateBy(appId);
            filingCancellation.setCreateTime(new Date());
            Long id  = SnowflakeIdWorker.getInstance().nextId();
            filingCancellation.setId(id);
            int flag = filingEquipmentFilingCancellationService.insert(filingCancellation);
            if (flag ==1){
                return success("备案注销申请推送成功",filingCancellation);
            }else{
                return error("设备"+filingCancellation.getFilingCode()+"备案注销申请推送失败");
            }
        }else {
            //修改
            filingCancellation.setUpdateTime(new Date());
            filingCancellation.setUpdateBy(appId);
            filingCancellation.setId(application.getId());
            int flag = filingEquipmentFilingCancellationService.update(filingCancellation);
            if (flag ==1){
                return success("备案注销申请修改成功",filingCancellation);
            }else{
                return error("设备"+filingCancellation.getFilingCode()+"备案注销申请修改失败");
            }
        }
    }

}
