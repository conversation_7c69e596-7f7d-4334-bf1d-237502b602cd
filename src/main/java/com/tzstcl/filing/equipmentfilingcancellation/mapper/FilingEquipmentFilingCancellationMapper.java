package com.tzstcl.filing.equipmentfilingcancellation.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：huminghui
 * 日期：2019年11月06日
 * 说明：起重机设备注销申请Mapper
 */
@Mapper
public interface FilingEquipmentFilingCancellationMapper extends BaseMapper<FilingEquipmentFilingCancellation>  {

    /**
    *
    * 批量增加起重机设备注销申请
    * @param filingEquipmentFilingCancellationList
    * <AUTHOR>
    * @date 2019年11月06日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<FilingEquipmentFilingCancellation> filingEquipmentFilingCancellationList);

    List<FilingEquipmentFilingCancellation> selectListByAreaCode(List<Long> areaList, String auditStatus);

    List<FilingEquipmentFilingCancellation> selectListByCondition(String year, List<Long> areaList);

    FilingEquipmentFilingCancellation getOneByCode(@Param("filingCode") String filingCode);

    List<FilingEquipmentFilingCancellation> selectListGroupByFilingCode(String year, List<Long> areaList);
}
