package com.tzstcl.filing.equipmentfilingcancellation.model;

import com.tzstcl.base.model.BaseModel;
import lombok.Data;
import lombok.NonNull;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
@Data
public class FilingEquipmentFilingCancellationVo extends BaseModel<FilingEquipmentFilingCancellationVo> implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 审核发起人id
     */
    private String userId;
    /**
     * 产权单位名称
     */
    @NonNull
    private String propertyUnit;
    /**
     *产权单位社会统一信用代码
     */
    private String propertyUnitCode;
    /**
     * 设备名称
     */
    //@NonNull
    private String deviceName;
    /**
     * 规格型号
     */
    @NonNull
    private String specificationModel;
    /**
     * 生产厂家
     */
    @NonNull
    private String manufacturer;
    /**
     * 出厂时间
     */
    @NonNull
    private String factoryTime;
    /**
     * 制造许可证编号
     */
    @NonNull
    private String manufacturingLicenseNumber;
    /**
     * 申请日期，年
     */
    @NonNull
    private String applicationDate;
    /**
     * 出厂编号
     */
    @NonNull
    private String factoryLicenseNumber;
    /**
     * 购买时间
     */
    @NonNull
    private String purchaseTime;
    /**
     * 产权单位地址
     */
    @NonNull
    private String unitAddress;
    /**
     * 法定代表人
     */
    @NonNull
    private String legalRepresentative;
    /**
     * 联系电话
     */
    @NonNull
    private String legalRepresentativeContactNumber;
    /**
     * 技术负责人
     */
    @NonNull
    private String technicalDirector;
    /**
     * 技术负责人联系方式
     */
    @NonNull
    private String technicalDirectorContactNumber;
    /**
     * 设备管理负责人
     */
    @NonNull
    private String equipmentManager;
    /**
     * 设备管理负责人联系方式
     */
    @NonNull
    private String equipmentManagerContactNumber;
    /**
     * 起重重量
     */
    private String liftingWeight;
    /**
     * 设备类型
     */
    private String equipmentType;
    /**
     * 注销原因
     */
    @NonNull
    private String cancellationReason;
    /**
     * 注销申请表文件路径
     */
    @NonNull
    private String cancellationApplicationFormPath;
    /**
     * 备案编号
     */
    @NonNull
    private String filingCode;
    /**
     * 备案证文件路径
     */
    @NonNull
    private String filingPaper;
    /**
     * 备案牌文件路径
     */
    @NonNull
    private String filingCard;
    /**
     * 审核状态，0未审核，1审核通过，2驳回
     */
    @NonNull
    private String auditStatus;
    /**
     * 审核退回原因
     */
    private String auditReject;
    /**
     * 审核时间
     */
    private Date auditTime;
    /**
     *业务ID
     */
    @NonNull
    private String businessId;
    /**
     *产权单位所属区域编码
     */
    @NonNull
    private Long unitAreaCode;
    /**
     *appId
     */
    private String appId;
    /**
     *appKey
     */
    private String appKey;
}
