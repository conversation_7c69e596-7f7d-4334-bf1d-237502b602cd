package com.tzstcl.filing.equipmentUser.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import com.tzstcl.filing.equipmentUser.model.FilingEquipmentUser;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年11月12日
 * 说明：设备使用人员表Mapper
 */
@Mapper
public interface FilingEquipmentUserMapper extends BaseMapper<FilingEquipmentUser>  {

    /**
    *
    * 批量增加设备使用人员表
    * @param filingEquipmentUserList
    * <AUTHOR>
    * @date 2019年11月12日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<FilingEquipmentUser> filingEquipmentUserList);

}