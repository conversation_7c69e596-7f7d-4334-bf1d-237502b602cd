package com.tzstcl.filing.equipmentUser.model;

import com.tzstcl.base.model.BaseModel;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年11月12日
 * 说明：设备使用人员表实体类
 */
@Data
public class FilingEquipmentUser extends BaseModel<FilingEquipmentUser> implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
     *设备使用备案id
     */
    private String useRegistrationId;
    /**
     *姓名
     */
    private String name;
    /**
     *工种
     */
    private String workerType;
    /**
     *上岗证号
     */
    private String appointmentCertificateCode;

}