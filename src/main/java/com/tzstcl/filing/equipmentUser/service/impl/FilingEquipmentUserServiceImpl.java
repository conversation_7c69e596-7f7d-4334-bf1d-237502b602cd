package com.tzstcl.filing.equipmentUser.service.impl;

import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.filing.equipmentUser.service.FilingEquipmentUserService;
import com.tzstcl.filing.equipmentUser.model.FilingEquipmentUser;
import com.tzstcl.filing.equipmentUser.mapper.FilingEquipmentUserMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年11月12日
 * 说明：设备使用人员表ServiceImpl
 */
@Service
public class FilingEquipmentUserServiceImpl extends BaseServiceImpl<FilingEquipmentUserMapper,FilingEquipmentUser> implements FilingEquipmentUserService{

    /**
    *
    * 批量增加设备使用人员表
    * @param filingEquipmentUserList
    * <AUTHOR>
    * @date 2019年11月12日
    * @return Integer 插入的记录数
    */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<FilingEquipmentUser> filingEquipmentUserList){return this.mapper.insertBatch(filingEquipmentUserList);}

}