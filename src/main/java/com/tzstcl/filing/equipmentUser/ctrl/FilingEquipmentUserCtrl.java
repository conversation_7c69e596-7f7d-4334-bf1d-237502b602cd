package com.tzstcl.filing.equipmentUser.ctrl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.filing.equipmentUser.model.FilingEquipmentUser;
import com.tzstcl.filing.equipmentUser.service.FilingEquipmentUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：juziqiang
 * 日期：2019年11月12日
 * 说明：设备使用人员表Controller
 */
@Controller
@RequestMapping("/admin/filingEquipmentUser")
public class FilingEquipmentUserCtrl extends BaseCtrl {

    @Autowired
    private FilingEquipmentUserService filingEquipmentUserService;

    /**
    * list页面导航
    * @return
    */
    @RequiresPermissions("filingEquipmentUser:view")
    @RequestMapping("/toList/{id}")
    public String toList(@PathVariable("id")Long id,Model model) {
        model.addAttribute("useRegistrationId",id);
        return "admin/filing/equipmentUser/filingEquipmentUserList";
    }

    /**
    * 获取查询的分页数据
    * @param filingEquipmentUser
    * @return
    */
    @RequestMapping("/list")
    @ResponseBody
    public PageInfo<FilingEquipmentUser> list(FilingEquipmentUser filingEquipmentUser) {
        return  filingEquipmentUserService.selectPage(filingEquipmentUser);
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"filingEquipmentUser:edite","filingEquipmentUser:add"},logical=Logical.OR)
    @RequestMapping("/toForm/{id}")
    public String toForm(@PathVariable("id")Long id, Model model) {
            model.addAttribute("useRegistrationId" ,id);
        return "admin/filing/equipmentUser/filingEquipmentUserForm";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("filingEquipmentUser:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentUser" ,filingEquipmentUserService.getOne(id));
        }
        return "admin/filing/equipmentUser/filingEquipmentUserDetail";
    }

    /**
     * 新增
     * @param filingEquipmentUser
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions("filingEquipmentUser:add")
    @ResponseBody
    public AjaxResult save( FilingEquipmentUser filingEquipmentUser) {
         int flag = filingEquipmentUserService.add(filingEquipmentUser);
         if (flag ==1){
             return success("操作成功",filingEquipmentUser);
         }else{
             return error("操作失败");
         }
    }

    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("filingEquipmentUser:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("filingEquipmentUser" ,filingEquipmentUserService.getOne(id));
        }
        return "admin/filing/equipmentUser/filingEquipmentUserEdit";
    }

    /**
    * 更新
    * @param filingEquipmentUser
    * @return
    */
    @PostMapping("/update")
    @RequiresPermissions("filingEquipmentUser:edit")
    @ResponseBody
    public AjaxResult update(@Valid FilingEquipmentUser filingEquipmentUser) {
        return toAjax(filingEquipmentUserService.update(filingEquipmentUser));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("filingEquipmentUser:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(filingEquipmentUserService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("filingEquipmentUser:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",filingEquipmentUserService.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param filingEquipmentUser
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"filingEquipmentUser:add", "filingEquipmentUser:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(FilingEquipmentUser filingEquipmentUser) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = filingEquipmentUser.getId();
        filingEquipmentUser.setId(null);
        List<FilingEquipmentUser> filingEquipmentUserList = filingEquipmentUserService.selectList(filingEquipmentUser);
        if(StringUtils.isNotEmpty(filingEquipmentUserList)){
            if(filingEquipmentUserList.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(filingEquipmentUserList.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }
    /**
     * 删除
     * @param id
     * @return
     */
    @RequestMapping("/del")
    @RequiresPermissions("filingEquipmentUser:delete")
    @ResponseBody
    public AjaxResult del(Long id) {
        return toAjax(filingEquipmentUserService.delete(id));
    }
}