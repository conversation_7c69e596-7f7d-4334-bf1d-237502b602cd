package com.tzstcl.filing.constructionMachineryCert.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import com.tzstcl.filing.constructionMachineryCert.model.ConstructionMachineryCert;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：
 * 日期：2025年03月21日
 * 说明：建筑起重机械备案证数据变更表Mapper
 */
@Mapper
public interface ConstructionMachineryCertMapper extends BaseMapper<ConstructionMachineryCert>  {

    /**
    *
    * 批量增加建筑起重机械备案证数据变更表
    * @param constructionMachineryCertList
    * <AUTHOR> @date 2025年03月21日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<ConstructionMachineryCert> constructionMachineryCertList);

}