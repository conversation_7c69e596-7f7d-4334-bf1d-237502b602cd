package com.tzstcl.filing.constructionMachineryCert.model;

import com.tzstcl.base.model.BaseModel;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 公司：天筑科技股份有限公司
 * 作者：
 * 日期：2025年03月21日
 * 说明：建筑起重机械备案证数据变更表实体类
 */
@Data
public class ConstructionMachineryCert extends BaseModel<ConstructionMachineryCert> implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
     *业务数据ID，主键
     */
    private String businessdataid;
    /**
     *证照标识
     */
    private String certid;
    /**
     *行政区划代码
     */
    private String areacode;
    /**
     *证照编号
     */
    private String certnum;
    /**
     *设备唯一编号
     */
    private String equipmentuniquecode;
    /**
     *设备类别代码
     */
    private String equipmentcategorycode;
    /**
     *规格型号
     */
    private String equipmentspecifications;
    /**
     *出厂编号
     */
    private String exfactorynumber;
    /**
     *制造单位统一社会信用代码
     */
    private String manufacturecorpcode;
    /**
     *产权单位登记地址（条件必填）
     */
    private String ownerregaddress;
    /**
     *产权单位法定代表人（条件必填）
     */
    private String ownerlegalperson;
    /**
     *法定代表人证件类型代码（条件必填）
     */
    private String ownerlpcodetype;
    /**
     *法定代表人证件号（条件必填）
     */
    private String ownerlpcode;
    /**
     *下次安全评估时间（条件必填）
     */
    private String nextsaftyevaltime;
    /**
     *起重臂长度（条件必填）
     */
    private String lengthofcranejib;
    /**
     *最大工作幅度（条件必填）
     */
    private String maxworkingrange;
    /**
     *最大幅度额定起重量（条件必填）
     */
    private String ratedliftingcatwr;
    /**
     *独立起升高度（条件必填）
     */
    private String nontieinloadlh;
    /**
     *最大起升高度（条件必填）
     */
    private String maxliftingheight;
    /**
     *最大提升高度（条件必填）
     */
    private String maxhoistingheight;
    /**
     *操作类型（固定值）
     */
    private String operatetype;

    private String  pushFlag;

    private Date pushTime;

}
