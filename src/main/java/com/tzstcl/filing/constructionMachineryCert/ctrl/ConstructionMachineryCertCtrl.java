package com.tzstcl.filing.constructionMachineryCert.ctrl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.filing.constructionMachineryCert.model.ConstructionMachineryCert;
import com.tzstcl.filing.constructionMachineryCert.service.ConstructionMachineryCertService;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：
 * 日期：2025年03月21日
 * 说明：建筑起重机械备案证数据变更表Controller
 */
@Controller
@RequestMapping("/admin/constructionMachineryCert")
public class ConstructionMachineryCertCtrl extends BaseCtrl {

    @Autowired
    private ConstructionMachineryCertService constructionMachineryCertService;

    /**
    * list页面导航
    * @return
    */
    @RequiresPermissions("constructionMachineryCert:view")
    @RequestMapping("/toList")
    public String toList() {
        return "admin/filing/constructionMachineryCert/constructionMachineryCertList";
    }

    /**
    * 获取查询的分页数据
    * @param constructionMachineryCert
    * @return
    */
    @RequestMapping("/list")
    @RequiresPermissions("constructionMachineryCert:view")
    @ResponseBody
    public PageInfo<ConstructionMachineryCert> list(ConstructionMachineryCert constructionMachineryCert) {
        return  constructionMachineryCertService.selectPage(constructionMachineryCert);
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"constructionMachineryCert:edite","constructionMachineryCert:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        if(null != id){
            model.addAttribute("constructionMachineryCert" ,constructionMachineryCertService.getOne(id));
        }
        return "admin/filing/constructionMachineryCert/constructionMachineryCertForm";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("constructionMachineryCert:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("constructionMachineryCert" ,constructionMachineryCertService.getOne(id));
        }
        return "admin/filing/constructionMachineryCert/constructionMachineryCertDetail";
    }

    /**
     * 新增
     * @param constructionMachineryCert
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"constructionMachineryCert:add"})
    @ResponseBody
    public AjaxResult save(@Valid ConstructionMachineryCert constructionMachineryCert) {
         return toAjax(constructionMachineryCertService.add(constructionMachineryCert));
    }

    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("constructionMachineryCert:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("constructionMachineryCert" ,constructionMachineryCertService.getOne(id));
        }
        return "admin/filing/constructionMachineryCert/constructionMachineryCertEdit";
    }

    /**
    * 更新
    * @param constructionMachineryCert
    * @return
    */
    @PostMapping("/update")
    @RequiresPermissions("constructionMachineryCert:edit")
    @ResponseBody
    public AjaxResult update(@Valid ConstructionMachineryCert constructionMachineryCert) {
        return toAjax(constructionMachineryCertService.update(constructionMachineryCert));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("constructionMachineryCert:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(constructionMachineryCertService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("constructionMachineryCert:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",constructionMachineryCertService.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param constructionMachineryCert
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"constructionMachineryCert:add", "constructionMachineryCert:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(ConstructionMachineryCert constructionMachineryCert) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = constructionMachineryCert.getId();
        constructionMachineryCert.setId(null);
        List<ConstructionMachineryCert> constructionMachineryCertList = constructionMachineryCertService.selectList(constructionMachineryCert);
        if(StringUtils.isNotEmpty(constructionMachineryCertList)){
            if(constructionMachineryCertList.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(constructionMachineryCertList.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }

}
