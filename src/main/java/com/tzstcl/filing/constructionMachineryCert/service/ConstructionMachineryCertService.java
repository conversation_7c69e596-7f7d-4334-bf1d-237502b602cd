package com.tzstcl.filing.constructionMachineryCert.service;

import com.alibaba.fastjson.JSONObject;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.service.BaseService;
import com.tzstcl.filing.constructionMachineryCert.model.ConstructionMachineryCert;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：
 * 日期：2025年03月21日
 * 说明：建筑起重机械备案证数据变更表Servic
 */
public interface ConstructionMachineryCertService extends BaseService<ConstructionMachineryCert> {

    /**
    *
    * 批量增加建筑起重机械备案证数据变更表
    * @param constructionMachineryCertList
    * <AUTHOR> @date 2025年03月21日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<ConstructionMachineryCert> constructionMachineryCertList);

    AjaxResult changeInfo(List<JSONObject> list, String constructionDataList);

    AjaxResult changeStatus(List<JSONObject> list, String constructionDataList);

    AjaxResult amend(List<JSONObject> list, String constructionDataList);
}
