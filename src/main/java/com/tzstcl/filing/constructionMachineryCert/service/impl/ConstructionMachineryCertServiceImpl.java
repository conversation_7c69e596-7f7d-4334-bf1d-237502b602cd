package com.tzstcl.filing.constructionMachineryCert.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.commons.utils.DateUtils;
import com.tzstcl.filing.constructionMachineryCert.service.ConstructionMachineryCertService;
import com.tzstcl.filing.constructionMachineryCert.model.ConstructionMachineryCert;
import com.tzstcl.filing.constructionMachineryCert.mapper.ConstructionMachineryCertMapper;
import com.tzstcl.filing.pushResult.mapper.PushResultMapper;
import com.tzstcl.filing.pushResult.model.PushResult;
import com.tzstcl.filing.tripartitePush.utils.CallAPI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.tzstcl.filing.tripartitePush.utils.CallAPI.APIInvoke;

/**
 * 公司：天筑科技股份有限公司
 * 作者：
 * 日期：2025年03月21日
 * 说明：建筑起重机械备案证数据变更表ServiceImpl
 */
@Service
public class ConstructionMachineryCertServiceImpl extends BaseServiceImpl<ConstructionMachineryCertMapper,ConstructionMachineryCert> implements ConstructionMachineryCertService{
    @Value("${nation.building.changeInfo.url}")
    private String changeInfoUrl;

    @Value("${nation.building.changeStatus.url}")
    private String changeStatusUrl;

    @Value("${nation.building.amend.url}")
    private String amendUrl;
    @Autowired
    PushResultMapper pushResultMapper;
    /**
    /**
    *
    * 批量增加建筑起重机械备案证数据变更表
    * @param constructionMachineryCertList
    * <AUTHOR> @date 2025年03月21日
    * @return Integer 插入的记录数
    */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<ConstructionMachineryCert> constructionMachineryCertList){return this.mapper.insertBatch(constructionMachineryCertList);}
   /**
    * [5.7业务信息变更接口]
    * @param constructionDataList
    * @param constructionList
    * @return com.tzstcl.base.model.AjaxResult
    * <AUTHOR>
    * @date 2025/3/21 15:56
    */
   @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public AjaxResult changeInfo(List<JSONObject> constructionDataList, String constructionList) {
        String accessToken = CallAPI.getToken2(true).getAccessToken();
        String s = APIInvoke(changeInfoUrl, accessToken, null, constructionDataList);
        JSONObject jsonObject =new JSONObject();
        try{
            jsonObject=JSONObject.parseObject(s);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.put("ReturnCode",jsonObject.get("ReturnCode"))
                    .put("ReturnMsg",jsonObject.get("ReturnMsg"))
                    .put("ReturnData",jsonObject.get("ReturnData"));
            PushResult pushResult = JSONObject.parseObject(s, PushResult.class);
            List<ConstructionMachineryCert> list = JSONObject.parseArray(constructionList, ConstructionMachineryCert.class);
            list.forEach(item->{
                item.setPushFlag("S");
                item.setPushTime(DateUtils.getNowDate());
                pushResult.setAssociationId(item.getBusinessdataid());
                pushResult.setType("B");
            });
            this.mapper.insertBatch(list);
            pushResultMapper.insert(pushResult);
            return ajaxResult;
        }catch (Exception e){
            return AjaxResult.error().put("接口调用异常",s);
        }
    }
    /**
     * [5.8证书状态变更接口]
     * @param constructionDataList
     * @param constructionList
     * @return com.tzstcl.base.model.AjaxResult
     * <AUTHOR>
     * @date 2025/3/21 16:02
     */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public AjaxResult changeStatus(List<JSONObject> constructionDataList, String constructionList) {
        String accessToken = CallAPI.getToken2(true).getAccessToken();
        String s = APIInvoke(changeStatusUrl, accessToken, null, constructionDataList);
        JSONObject jsonObject =new JSONObject();
        try{
            jsonObject=JSONObject.parseObject(s);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.put("ReturnCode",jsonObject.get("ReturnCode"))
                    .put("ReturnMsg",jsonObject.get("ReturnMsg"))
                    .put("ReturnData",jsonObject.get("ReturnData"));
            PushResult pushResult = JSONObject.parseObject(s, PushResult.class);
            List<ConstructionMachineryCert> list = JSONObject.parseArray(constructionList, ConstructionMachineryCert.class);
            list.forEach(item->{
                item.setPushFlag("S");
                item.setPushTime(DateUtils.getNowDate());
                pushResult.setAssociationId(item.getBusinessdataid());
                pushResult.setType("B");
            });
            this.mapper.insertBatch(list);
            pushResultMapper.insert(pushResult);
            return ajaxResult;
        }catch (Exception e){
            return AjaxResult.error(401,s);
        }
    }
    /**
     * [5.9业务数据更正接口]
     * @param constructionList
     * @param constructionDataList
     * @return com.tzstcl.base.model.AjaxResult
     * <AUTHOR>
     * @date 2025/3/21 16:05
     */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public AjaxResult amend(List<JSONObject> constructionDataList, String constructionList) {
        String accessToken = CallAPI.getToken2(true).getAccessToken();
        String s = APIInvoke(amendUrl, accessToken, null, constructionDataList);
        JSONObject jsonObject =new JSONObject();
        try{
            jsonObject=JSONObject.parseObject(s);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.put("ReturnCode",jsonObject.get("ReturnCode"))
                    .put("ReturnMsg",jsonObject.get("ReturnMsg"))
                    .put("ReturnData",jsonObject.get("ReturnData"));
            PushResult pushResult = JSONObject.parseObject(s, PushResult.class);
            List<ConstructionMachineryCert> list = JSONObject.parseArray(constructionList, ConstructionMachineryCert.class);
            list.forEach(item->{
                item.setPushFlag("S");
                item.setPushTime(DateUtils.getNowDate());
                pushResult.setAssociationId(item.getBusinessdataid());
                pushResult.setType("B");
            });
            this.mapper.insertBatch(list);
            pushResultMapper.insert(pushResult);
            return ajaxResult;
        }catch (Exception e){
            return AjaxResult.error().put("接口调用异常",s);
        }
    }
}
