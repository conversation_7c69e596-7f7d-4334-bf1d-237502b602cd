package com.tzstcl.filing.buildingCraneCert.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tzstcl.base.model.BaseModel;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 公司：天筑科技股份有限公司
 * 作者：
 * 日期：2025年03月20日
 * 说明：建筑起重机械备案证数据表实体类
 */
@Data
public class BuildingCraneCert extends BaseModel<BuildingCraneCert> implements Serializable {

	private static final long serialVersionUID = 1L;


    /** 主键 */
    private Long id;

    /** 业务数据ID，地方电子证照系统业务主键，长度要求固定为36位 */
    private String businessDataId;

    /** 备案地省(自治区、直辖市)、地(市、州、盟)、县(区、市、旗)行政区区划 */
    private String areaCode;

    /** 证照编号，建筑起重机械备案证书的唯一编号 */
    private String certNum;

    /** 原存量证书编号（条件必填） */
    private String oldCertNum;

    /** 发证机关名称 */
    private String issuAuthName;

    /** 发证机关统一社会信用代码 */
    private String issuAuthCode;

    /** 发证日期 */
    private String issuDate;

    /** 设备名称 */
    private String equipmentName;

    /** 设备类别代码 */
    private String equipmentCategoryCode;

    /** 规格型号 */
    private String equipmentSpecifications;

    /** 出厂编号 */
    private String exFactoryNumber;

    /** 出厂日期 */
    private String exFactoryDate;

    /** 出厂价格（万元） */
    private String exFactoryPrice;

    /** 设计使用年限（条件必填） */
    private String designServiceLife;

    /** 下次安全评估时间（条件必填） */
    private String nextSaftyEvalTime;

    /** 制造单位名称 */
    private String manufactureCorpName;

    /** 制造单位统一社会信用代码 */
    private String manufactureCorpCode;

    /** 特种设备生产许可证编号 */
    private String manufactureLicCode;

    /** 出厂合格证照片（Base64字符串） */
    private String exFactoryCertPhoto;

    /** 产权单位名称 */
    private String ownerCorpName;

    /** 产权单位统一社会信用代码 */
    private String ownerCorpCode;

    /** 产权单位登记地址 */
    private String ownerRegAddress;

    /** 产权单位法定代表人 */
    private String ownerLegalPerson;

    /** 法定代表人身份证件类型代码 */
    private String ownerLPCodeType;

    /** 法定代表人身份证件号 */
    private String ownerLPCode;

    /** 额定起重量（条件必填） */
    private String ratedCapacity;

    /** 额定起重力矩（条件必填） */
    private String ratedLoadMoment;

    /** 起重臂长度（条件必填） */
    private String lengthOfCraneJib;

    /** 最大工作幅度（条件必填） */
    private String maxWorkingRange;

    /** 最大幅度额定起重量（条件必填） */
    private String ratedLiftingCATWR;

    /** 独立起升高度（条件必填） */
    private String nonTieInLoadLH;

    /** 最大起升高度（条件必填） */
    private String maxLiftingHeight;

    /** 塔式起重机标准节参数（条件必填） */
    private String standardSectionPOfTC;

    /** 标准节主要结构件规格（条件必填） */
    private String stdSectionMainSSOfTC;

    /** 加强节参数（条件必填） */
    private String reinforceSectionPOfTC;

    /** 主要结构件唯一编号（条件必填） */
    private String mainStrUniqueCode;

    /** 施工升降机用途类型（条件必填） */
    private String constructionHUTypes;

    /** 额定提升速度（条件必填） */
    private String ratedLiftingSpeed;

    /** 最大提升高度（条件必填） */
    private String maxHoistingHeight;

    /** 电动机总功率（条件必填） */
    private String totalElectricMotorPower;

    /** 防坠安全器型号（条件必填） */
    private String antifallSafetyDeviceType;

    /** 运载装置净空尺寸（条件必填） */
    private String carrierUnitHD;

    /** 其他起重机械类型（条件必填） */
    private String otherCraneType;

    /** 门式起重机跨度（条件必填） */
    private String portalCraneSpan;

    /** 是否首次备案标识 */
    private String isFirstFiling;

    /** 设备唯一编号（条件必填） */
    private String equipmentUniqueCode;

    /** 首次备案日期 */
    private String firstFilingDate;

    /** 证书状态代码 */
    private String certState;

    /** 证书状态描述（条件必填） */
    private String certStateDesc;

    /** 关联证照标识（条件必填） */
    private String associatedCertId;

    /** 业务信息（可选） */
    private String businessInformation;

    /** 操作类型代码 */
    private String operateType;

    /** 推送标识 */
    private String pushFlag;

    /** 推送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date pushTime;

}
