package com.tzstcl.filing.buildingCraneCert.ctrl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.filing.buildingCraneCert.model.BuildingCraneCert;
import com.tzstcl.filing.buildingCraneCert.service.BuildingCraneCertService;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：
 * 日期：2025年03月20日
 * 说明：建筑起重机械备案证数据表Controller
 */
@Controller
@RequestMapping("/admin/buildingCraneCert")
public class BuildingCraneCertCtrl extends BaseCtrl {

    @Autowired
    private BuildingCraneCertService buildingCraneCertService;

    /**
    * list页面导航
    * @return
    */
    @RequiresPermissions("buildingCraneCert:view")
    @RequestMapping("/toList")
    public String toList() {
        return "admin/filing/buildingCraneCert/buildingCraneCertList";
    }

    /**
    * 获取查询的分页数据
    * @param buildingCraneCert
    * @return
    */
    @RequestMapping("/list")
    @RequiresPermissions("buildingCraneCert:view")
    @ResponseBody
    public PageInfo<BuildingCraneCert> list(BuildingCraneCert buildingCraneCert) {
        return  buildingCraneCertService.selectPage(buildingCraneCert);
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"buildingCraneCert:edite","buildingCraneCert:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        if(null != id){
            model.addAttribute("buildingCraneCert" ,buildingCraneCertService.getOne(id));
        }
        return "admin/filing/buildingCraneCert/buildingCraneCertForm";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("buildingCraneCert:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("buildingCraneCert" ,buildingCraneCertService.getOne(id));
        }
        return "admin/filing/buildingCraneCert/buildingCraneCertDetail";
    }

    /**
     * 新增
     * @param buildingCraneCert
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"buildingCraneCert:add"})
    @ResponseBody
    public AjaxResult save(@Valid BuildingCraneCert buildingCraneCert) {
         return toAjax(buildingCraneCertService.add(buildingCraneCert));
    }

    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("buildingCraneCert:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("buildingCraneCert" ,buildingCraneCertService.getOne(id));
        }
        return "admin/filing/buildingCraneCert/buildingCraneCertEdit";
    }

    /**
    * 更新
    * @param buildingCraneCert
    * @return
    */
    @PostMapping("/update")
    @RequiresPermissions("buildingCraneCert:edit")
    @ResponseBody
    public AjaxResult update(@Valid BuildingCraneCert buildingCraneCert) {
        return toAjax(buildingCraneCertService.update(buildingCraneCert));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("buildingCraneCert:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(buildingCraneCertService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("buildingCraneCert:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",buildingCraneCertService.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param buildingCraneCert
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"buildingCraneCert:add", "buildingCraneCert:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(BuildingCraneCert buildingCraneCert) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = buildingCraneCert.getId();
        buildingCraneCert.setId(null);
        List<BuildingCraneCert> buildingCraneCertList = buildingCraneCertService.selectList(buildingCraneCert);
        if(StringUtils.isNotEmpty(buildingCraneCertList)){
            if(buildingCraneCertList.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(buildingCraneCertList.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }

}
