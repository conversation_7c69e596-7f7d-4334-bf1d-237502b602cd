package com.tzstcl.filing.buildingCraneCert.service;

import com.alibaba.fastjson.JSONObject;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.service.BaseService;
import com.tzstcl.filing.buildingCraneCert.model.BuildingCraneCert;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：
 * 日期：2025年03月20日
 * 说明：建筑起重机械备案证数据表Servic
 */
public interface BuildingCraneCertService extends BaseService<BuildingCraneCert> {

    /**
    *
    * 批量增加建筑起重机械备案证数据表
    * @param buildingCraneCertList
    * <AUTHOR> @date 2025年03月20日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<BuildingCraneCert> buildingCraneCertList);

    AjaxResult craneCertVerify(JSONObject buildingData, String buildingList);

    AjaxResult preBussinessVerify(List<JSONObject> buildingDataList, String buildingList);

    AjaxResult bussinessVerify(List<JSONObject> buildingDataList, String buildingList);

    AjaxResult assignCode(List<JSONObject> list, String buildingDataList,String appid);


}
