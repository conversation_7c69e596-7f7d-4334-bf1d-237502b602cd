package com.tzstcl.filing.buildingCraneCert.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.commons.utils.DateUtils;
import com.tzstcl.commons.utils.SnowflakeIdWorker;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.filing.area.mapper.AreaMapper;
import com.tzstcl.filing.area.model.Area;
import com.tzstcl.filing.buildingCraneCert.service.BuildingCraneCertService;
import com.tzstcl.filing.buildingCraneCert.model.BuildingCraneCert;
import com.tzstcl.filing.buildingCraneCert.mapper.BuildingCraneCertMapper;
import com.tzstcl.filing.constructionMachineryCert.model.ConstructionMachineryCert;
import com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication;
import com.tzstcl.filing.equipmentFilingApplication.service.FilingEquipmentFilingApplicationService;
import com.tzstcl.filing.pushData.model.PushData;
import com.tzstcl.filing.pushResult.mapper.PushResultMapper;
import com.tzstcl.filing.pushResult.model.PushResult;
import com.tzstcl.filing.tripartitePush.utils.CallAPI;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.converters.DateConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static com.tzstcl.filing.tripartitePush.utils.CallAPI.APIInvoke;
import static com.tzstcl.filing.tripartitePush.utils.CallAPI.APIInvokeJson;

/**
 * 公司：天筑科技股份有限公司
 * 作者：
 * 日期：2025年03月20日
 * 说明：建筑起重机械备案证数据表ServiceImpl
 */
@Slf4j
@Service
public class BuildingCraneCertServiceImpl extends BaseServiceImpl<BuildingCraneCertMapper,BuildingCraneCert> implements BuildingCraneCertService{
    @Value("${nation.building.check.url}")
    private String chreckUrl  ;
    @Value("${nation.building.preBussinessVer.url}")
    private String preBussinessVerUrl;
    @Value("${nation.building.bussinessDataVer.url}")
    private String bussinessDataVerUrl;
    @Value("${nation.building.assignCode.url}")
    private String assignCodeUrl;
    @Autowired
    private AreaMapper areaMapper;
    @Autowired
    PushResultMapper pushResultMapper;
    @Autowired
    private FilingEquipmentFilingApplicationService filingEquipmentFilingApplicationService;
    /**
    *
    * 批量增加建筑起重机械备案证数据表
    * @param buildingCraneCertList
    * <AUTHOR> @date 2025年03月20日
    * @return Integer 插入的记录数
    */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<BuildingCraneCert> buildingCraneCertList){return this.mapper.insertBatch(buildingCraneCertList);}
    /**
     * [设备唯一编号查验接口]
     * @param buildingList
     * @param buildingList
     * @return com.tzstcl.base.model.AjaxResult
     * <AUTHOR>
     * @date 2025/3/20 11:06
     */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public AjaxResult craneCertVerify(JSONObject buildingData, String buildingList) {
        log.info("开始请求token....");
        String accessToken = CallAPI.getToken2(true).getAccessToken();
        log.info("accessToken:{}", accessToken);
        log.info("开始请求apiInvokeJson...");
        String s = APIInvokeJson(chreckUrl, accessToken, null, buildingData);
        log.info("aPIInvokeJson:{}", s);
        JSONObject jsonObject =new JSONObject();
        try{
            jsonObject=JSONObject.parseObject(s);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.put("ReturnCode",jsonObject.get("ReturnCode"))
                    .put("ReturnMsg",jsonObject.get("ReturnMsg"))
                    .put("equipmentUniqueCode",jsonObject.get("equipmentUniqueCode"))
                    .put("firstFilingDate",jsonObject.get("firstFilingDate"))
                    .put("ErrorCode",jsonObject.get("ErrorCode"));
            log.info("ajaxResult:{}", ajaxResult);
             PushResult pushResult =new PushResult();
             pushResult.setType("B");
             pushResult.setReturnCode(jsonObject.getString("ReturnCode"));
            pushResult.setReturnMsg(jsonObject.getString("ReturnMsg"));
            pushResult.setReturnData(jsonObject.toJSONString());
           // PushResult pushResult = JSONObject.parseObject(s, PushResult.class);
         BuildingCraneCert buildingCraneCert = JSONObject.parseObject(buildingList,BuildingCraneCert.class);

            buildingCraneCert.setPushFlag("S");
            buildingCraneCert.setPushTime(DateUtils.getNowDate());
                pushResult.setAssociationId(buildingCraneCert.getBusinessDataId());
                pushResult.setType("B");

            this.mapper.insert(buildingCraneCert);
            pushResultMapper.insert(pushResult);
            return ajaxResult;
        }catch (Exception e){
            return AjaxResult.error().put("接口调用异常",s);
        }


    }
    /**
     * [业务办理初步校验接口]
     * @param buildingDataList
     * @param buildingList
     * @return com.tzstcl.base.model.AjaxResult
     * <AUTHOR>
     * @date 2025/3/20 14:20
     */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public AjaxResult preBussinessVerify(List<JSONObject> buildingDataList, String buildingList) {
        String accessToken = CallAPI.getToken2(true).getAccessToken();
        String s = APIInvoke(preBussinessVerUrl, accessToken, null, buildingDataList);
        JSONObject jsonObject =new JSONObject();

        try{
            jsonObject=JSONObject.parseObject(s);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.put("ReturnCode",jsonObject.get("ReturnCode"))
                    .put("ReturnMsg",jsonObject.get("ReturnMsg"))
                    .put("ReturnData",jsonObject.get("ReturnData"));
            PushResult pushResult = JSONObject.parseObject(s, PushResult.class);
            List<BuildingCraneCert> list = JSONObject.parseArray(buildingList,BuildingCraneCert.class);
            list.forEach(item->{
                item.setPushFlag("S");
                item.setPushTime(DateUtils.getNowDate());
                pushResult.setAssociationId(item.getBusinessDataId());
                pushResult.setType("B");
            });
            this.mapper.insertBatch(list);
            pushResultMapper.insert(pushResult);
            return ajaxResult;
        }catch (Exception e){
            e.printStackTrace();
            return AjaxResult.error().put("接口调用异常",s);
        }
    }

    /**
     * 业务数据校验接口
     * @param buildingDataList
     * @param buildingList
     * @return
     */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public AjaxResult bussinessVerify(List<JSONObject> buildingDataList, String buildingList) {
        String accessToken = CallAPI.getToken2(true).getAccessToken();
        String s = APIInvoke(bussinessDataVerUrl, accessToken, null, buildingDataList);
        JSONObject jsonObject =new JSONObject();

        try{
            jsonObject=JSONObject.parseObject(s);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.put("ReturnCode",jsonObject.get("ReturnCode"))
                    .put("ReturnMsg",jsonObject.get("ReturnMsg"))
                    .put("ReturnData",jsonObject.get("ReturnData"));
            PushResult pushResult = JSONObject.parseObject(s, PushResult.class);
            List<BuildingCraneCert> list = JSONObject.parseArray(buildingList,BuildingCraneCert.class);
            list.forEach(item->{
                item.setPushFlag("S");
                item.setPushTime(DateUtils.getNowDate());
                pushResult.setAssociationId(item.getBusinessDataId());
                pushResult.setType("B");
            });
            this.mapper.insertBatch(list);
            pushResultMapper.insert(pushResult);
            return ajaxResult;
        }catch (Exception e){
            return AjaxResult.error().put("接口调用异常",s);
        }
    }
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public AjaxResult assignCode(List<JSONObject> buildingDataList, String buildingList,String appid) {
        String accessToken = CallAPI.getToken2(true).getAccessToken();
        String s = APIInvoke(assignCodeUrl, accessToken, null, buildingDataList);
        try{
            JSONObject jsonObject = JSONObject.parseObject(s);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.put("ReturnCode",jsonObject.get("ReturnCode"))
                    .put("ReturnMsg",jsonObject.get("ReturnMsg"))
                    .put("ReturnData",jsonObject.get("ReturnData"));
            PushResult pushResult = JSONObject.parseObject(s, PushResult.class);
            List<BuildingCraneCert> list = JSONObject.parseArray(buildingList,BuildingCraneCert.class);
            list.forEach(item->{
                item.setPushFlag("S");
                item.setPushTime(DateUtils.getNowDate());
                pushResult.setAssociationId(item.getBusinessDataId());
                pushResult.setType("B");
                //记录赋码成功的数据到业务表
                if ("1".equals(jsonObject.get("ReturnCode"))){
                    try {
                        FilingEquipmentFilingApplication filingEquipmentFilingApplication = getFilingEquipmentFilingApplication(item);
                        if (Objects.nonNull(filingEquipmentFilingApplication)){
                            filingEquipmentFilingApplication.setUserId(appid);
                            filingEquipmentFilingApplication.setCreateBy(appid);
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        logger.error("保存到省平台失败,失败原因:{}",e.getMessage());
                    }
                }
            });
            //记录推送数据
            this.mapper.insertBatch(list);
            //记录推送结果
            pushResultMapper.insert(pushResult);

            return ajaxResult;
        }catch (Exception e){
            return AjaxResult.error().put("接口调用异常",s);
        }
    }

    private FilingEquipmentFilingApplication getFilingEquipmentFilingApplication(BuildingCraneCert item) {
        try {

            //根据备案编号判断该数据是否重复
            FilingEquipmentFilingApplication param = new FilingEquipmentFilingApplication();
            param.setFilingCode(item.getCertNum());
            List<FilingEquipmentFilingApplication> list = filingEquipmentFilingApplicationService.selectList(param);
            if (list == null || list.isEmpty()){
                FilingEquipmentFilingApplication filingApplication = new FilingEquipmentFilingApplication();
                log.info("filingApplication:{}",filingApplication);

                DateConverter converter = new DateConverter();
                converter.setPattern(new String("yyyy-MM-dd"));
                ConvertUtils.register(converter, Date.class);

                BeanUtils.copyProperties(filingApplication,item);
                filingApplication.setFilingCode(item.getCertNum());
                filingApplication.setAuditStatus("1");
                filingApplication.setEquipmentType(item.getEquipmentCategoryCode());
                filingApplication.setDeviceName(item.getEquipmentName());
                filingApplication.setSpecificationModel(item.getEquipmentSpecifications());
                if (StringUtils.isNotEmpty(item.getRatedCapacity())){
                    filingApplication.setLiftingWeight(item.getRatedCapacity());
                }
                filingApplication.setPropertyUnit(item.getOwnerCorpName());
                filingApplication.setUnitAddress(item.getOwnerRegAddress());
//                filingApplication.setUnitAreaCode(item.getUnitAreaCode());
//                Area area = areaMapper.selectAreaById(item.getUnitAreaCode());
//                filingApplication.setRemarks(area.getName());
//                if (3==area.getLevel()){
//                    Area area1 = areaMapper.selectAreaById(area.getParentId());
//                    filingApplication.setRemarks(area1.getName()+"/"+area.getName());
//                }
                filingApplication.setPropertyUnitCode(item.getOwnerCorpCode());
                filingApplication.setLegalRepresentative(item.getOwnerLegalPerson());
                //filingApplication.setLegalRepresentativeContactNumber(item.getLegalRepresentativeContactNumber());
                //filingApplication.setTechnicalDirector(item.getTechnicalDirector());
                //filingApplication.setTechnicalDirectorContactNumber(equipmentFilingApplication.getTechnicalDirectorContactNumber());
                //filingApplication.setEquipmentManager(equipmentFilingApplication.getEquipmentManager());
               // filingApplication.setEquipmentManagerContactNumber(equipmentFilingApplication.getEquipmentManagerContactNumber());
                filingApplication.setManufacturer(item.getManufactureCorpName());
                filingApplication.setFactoryTime(item.getExFactoryDate());
                filingApplication.setFactoryLicenseNumber(item.getExFactoryNumber());
                filingApplication.setManufacturingLicenseNumber(item.getManufactureLicCode());
                //filingApplication.setFilingApplicationFormPath(item.getFilingApplicationFormPath());
                //filingApplication.setBusinessLicensePath(item.getBusinessLicensePath());
                //filingApplication.setEquipmentManufacturingLicensePath(item.getEquipmentManufacturingLicensePath());
                //filingApplication.setProductCertificationPath(item.getProductCertificationPath());
                //filingApplication.setPurchaseSaleCertificatePath(item.getPurchaseSaleCertificatePath());
                filingApplication.setOldNumberFlag(StringUtils.isEmpty(item.getOldCertNum()) ? "0" : "1");
                filingApplication.setOldNumber(item.getOldCertNum());
                if ("1".equals(filingApplication.getOldNumberFlag())){
                    filingApplication.setOldNumber(item.getOldCertNum());
                }
                filingApplication.setCreateTime(new Date());
                Long id  = SnowflakeIdWorker.getInstance().nextId();
                filingApplication.setId(id);
                return filingApplication;
            }

        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        }
        return null;
    }


}
