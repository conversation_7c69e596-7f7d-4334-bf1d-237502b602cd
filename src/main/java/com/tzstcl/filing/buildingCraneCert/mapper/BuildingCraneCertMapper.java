package com.tzstcl.filing.buildingCraneCert.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import com.tzstcl.filing.buildingCraneCert.model.BuildingCraneCert;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：
 * 日期：2025年03月20日
 * 说明：建筑起重机械备案证数据表Mapper
 */
@Mapper
public interface BuildingCraneCertMapper extends BaseMapper<BuildingCraneCert>  {

    /**
    *
    * 批量增加建筑起重机械备案证数据表
    * @param buildingCraneCertList
    * <AUTHOR> @date 2025年03月20日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<BuildingCraneCert> buildingCraneCertList);

}