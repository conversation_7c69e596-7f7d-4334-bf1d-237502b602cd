package com.tzstcl.filing.pushData.ctrl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.filing.pushData.model.PushData;
import com.tzstcl.filing.pushData.service.PushDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：推送全国表Controller
 */
@Controller
@RequestMapping("/admin/pushData")
public class PushDataCtrl extends BaseCtrl {

    @Autowired
    private PushDataService pushDataService;

    /**
    * list页面导航
    * @return
    */
    @RequiresPermissions("pushData:view")
    @RequestMapping("/toList")
    public String toList() {
        return "admin/filing/pushData/pushDataList";
    }

    /**
    * 获取查询的分页数据
    * @param pushData
    * @return
    */
    @RequestMapping("/list")
    @RequiresPermissions("pushData:view")
    @ResponseBody
    public PageInfo<PushData> list(PushData pushData) {
        return  pushDataService.selectPage(pushData);
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"pushData:edite","pushData:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        if(null != id){
            model.addAttribute("pushData" ,pushDataService.getOne(id));
        }
        return "admin/filing/pushData/pushDataForm";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("pushData:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("pushData" ,pushDataService.getOne(id));
        }
        return "admin/filing/pushData/pushDataDetail";
    }

    /**
     * 新增
     * @param pushData
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"pushData:add"})
    @ResponseBody
    public AjaxResult save(@Valid PushData pushData) {
         return toAjax(pushDataService.add(pushData));
    }

    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("pushData:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("pushData" ,pushDataService.getOne(id));
        }
        return "admin/filing/pushData/pushDataEdit";
    }

    /**
    * 更新
    * @param pushData
    * @return
    */
    @PostMapping("/update")
    @RequiresPermissions("pushData:edit")
    @ResponseBody
    public AjaxResult update(@Valid PushData pushData) {
        return toAjax(pushDataService.update(pushData));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("pushData:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(pushDataService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("pushData:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",pushDataService.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param pushData
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"pushData:add", "pushData:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(PushData pushData) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = pushData.getId();
        pushData.setId(null);
        List<PushData> pushDataList = pushDataService.selectList(pushData);
        if(StringUtils.isNotEmpty(pushDataList)){
            if(pushDataList.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(pushDataList.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }

}
