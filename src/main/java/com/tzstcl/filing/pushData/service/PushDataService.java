package com.tzstcl.filing.pushData.service;

import com.alibaba.fastjson.JSONObject;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.service.BaseService;
import com.tzstcl.filing.pushData.model.PushData;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：推送全国表Servic
 */
public interface PushDataService extends BaseService<PushData> {

    /**
    *
    * 批量增加推送全国表
    * @param pushDataList
    * <AUTHOR>
    * @date 2024年05月23日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<PushData> pushDataList);

    /**
     * [校验数据]
     * @param pushDataList
     * @return com.tzstcl.base.model.AjaxResult
     * <AUTHOR>
     * @date 2024/5/20 14:32
     */
    AjaxResult craneCertVerify(List<JSONObject> pushDataList,String pushList);

    AjaxResult encodeCert(List<JSONObject> list, String pushDataList);
}
