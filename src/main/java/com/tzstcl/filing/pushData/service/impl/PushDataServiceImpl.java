package com.tzstcl.filing.pushData.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.commons.utils.DateUtils;
import com.tzstcl.filing.pushData.service.PushDataService;
import com.tzstcl.filing.pushData.model.PushData;
import com.tzstcl.filing.pushData.mapper.PushDataMapper;
import com.tzstcl.filing.pushResult.mapper.PushResultMapper;
import com.tzstcl.filing.pushResult.model.PushResult;
import com.tzstcl.filing.tripartitePush.utils.CallAPI;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

import static com.tzstcl.filing.tripartitePush.utils.CallAPI.APIInvoke;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：推送全国表ServiceImpl
 */
@Service
public class PushDataServiceImpl extends BaseServiceImpl<PushDataMapper,PushData> implements PushDataService{
    @Value("${nation.check.url}")
    private String chreckUrl  ;
    @Autowired
    PushResultMapper pushResultMapper;
    @Value("${nation.encode.url}")
    private String encodeUrl;
    /**
    *
    * 批量增加推送全国表
    * @param pushDataList
    * <AUTHOR>
    * @date 2024年05月23日
    * @return Integer 插入的记录数
    */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<PushData> pushDataList){return this.mapper.insertBatch(pushDataList);}

    @Override
    @Transactional
    public AjaxResult craneCertVerify(List<JSONObject> pushDataList,String pushList) {
        String accessToken = CallAPI.getToken(true).getAccessToken();
        String s = APIInvoke(chreckUrl, accessToken, null, pushDataList);
        JSONObject jsonObject =new JSONObject();

        try{
            jsonObject=JSONObject.parseObject(s);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.put("code",jsonObject.get("ReturnCode"))
                    .put("ReturnMsg",jsonObject.get("ReturnMsg"))
                    .put("ReturnData",jsonObject.get("ReturnData"));
            PushResult pushResult = JSONObject.parseObject(s, PushResult.class);
            List<PushData> list = JSONObject.parseArray(pushList,PushData.class);
            list.forEach(item->{
                item.setPushFlag("S");
                item.setPushTime(DateUtils.getNowDate());
                pushResult.setAssociationId(item.getBusinessDataId());
            });
            this.mapper.insertBatch(list);
            pushResultMapper.insert(pushResult);
            return ajaxResult;
        }catch (Exception e){
            return AjaxResult.error().put("接口调用异常",s);
        }


    }
    @Transactional
    @Override
    public AjaxResult encodeCert(List<JSONObject> pushDataList, String pshlist) {
        String accessToken = CallAPI.getToken(true).getAccessToken();
        String s = APIInvoke(encodeUrl, accessToken, null, pushDataList);
        JSONObject jsonObject =new JSONObject();

        try{
            jsonObject=JSONObject.parseObject(s);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.put("code",jsonObject.get("ReturnCode"))
                    .put("ReturnMsg",jsonObject.get("ReturnMsg"))
                    .put("ReturnData",jsonObject.get("ReturnData"));
            PushResult pushResult = JSONObject.parseObject(s, PushResult.class);
            List<PushData> list = JSONObject.parseArray(pshlist,PushData.class);
            list.forEach(item->{
                item.setPushFlag("S");
                item.setPushTime(DateUtils.getNowDate());
                pushResult.setAssociationId(item.getBusinessDataId());
            });
            this.mapper.insertBatch(list);
            pushResultMapper.insert(pushResult);
            return ajaxResult;
        }catch (Exception e){
            return AjaxResult.error().put("接口调用异常",s);
        }

    }
}
