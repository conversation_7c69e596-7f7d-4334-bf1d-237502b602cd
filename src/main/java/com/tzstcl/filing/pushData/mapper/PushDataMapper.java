package com.tzstcl.filing.pushData.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import com.tzstcl.filing.pushData.model.PushData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：推送全国表Mapper
 */
@Mapper
public interface PushDataMapper extends BaseMapper<PushData>  {

    /**
    *
    * 批量增加推送全国表
    * @param pushDataList
    * <AUTHOR>
    * @date 2024年05月23日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<PushData> pushDataList);

}