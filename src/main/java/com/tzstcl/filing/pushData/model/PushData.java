package com.tzstcl.filing.pushData.model;

import com.tzstcl.base.model.BaseModel;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：推送全国表实体类
 */
@Data
public class PushData extends BaseModel<PushData> implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
     *business_data_id
     */
    private String businessDataId;
    /**
     *行政区划代码
     */
    private String areaCode;
    /**
     *证照编号
     */
    private String certNum;
    /**
     *发证机关
     */
    private String issuAuthName;
    /**
     *发证机关代码
     */
    private String issuAuthCode;
    /**
     *发证日期
     */
    private String issuDate;
    /**
     *类别代码
     */
    private String deviceCategoryCode;
    /**
     *规格型号
     */
    private String deviceModel;
    /**
     *出厂编号
     */
    private String factoryNum;
    /**
     *备案编号
     */
    private String recordNum;
    /**
     *制造单位
     */
    private String manufactureCorpName;
    /**
     *制造单位统一社会信用代码
     */
    private String manufactureCorpCode;
    /**
     *产权单位
     */
    private String propertyCorpName;
    /**
     *产权单位统一社会信用代码
     */
    private String propertyCorpCode;
    /**
     *工程名称
     */
    private String projectName;
    /**
     *工程项目地址
     */
    private String projectLocation;
    /**
     *工程项目地址所在区/县级区划代码
     */
    private String projectAreaCode;
    /**
     *是否办理施工许可证
     */
    private String applyConstructionPermit;
    /**
     *建筑工程施工许可证编号
     */
    private String constructionPermitNum;
    /**
     *使用单位
     */
    private String useCorpList;
    /**
     *维保单位
     */
    private String maintenanceCorpName;
    /**
     *维保单位统一社会信用代码
     */
    private String maintenanceCorpCode;
    /**
     *使用单位项目负责人
     */
    private String useCorpManager;
    /**
     *使用单位项目负责人身份证件号码
     */
    private String useCorpManagerId;
    /**
     *安装单位
     */
    private String installCorpName;
    /**
     *安装单位统一社会信用代码
     */
    private String installCorpCode;
    /**
     *检测单位
     */
    private String testCorpName;
    /**
     *检测单位统一社会信用代码
     */
    private String testCorpCode;
    /**
     *检测日期
     */
    private String testDate;
    /**
     *证书状态代码
     */
    private String certStatus;
    /**
     *证书状态描述
     */
    private String certStatusDescription;
    /**
     *关联证照标识
     */
    private String associatedCertId;
    /**
     *业务信息
     */
    private String businessInformation;
    /**
     *操作类型
     */
    private String operateType;
    /**
     *推送标识
     */
    private String pushFlag;
    /**
     *推送时间
     */
    private Date pushTime;
    /**
     *安装时间
     */
    private String installDate;
    /**
     *安装位置
     */
    private String installPosition;

}