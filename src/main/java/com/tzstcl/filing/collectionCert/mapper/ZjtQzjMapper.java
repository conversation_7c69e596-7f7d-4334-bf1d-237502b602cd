package com.tzstcl.filing.collectionCert.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import com.tzstcl.filing.collectionCert.model.ZjtQzj;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月24日
 * 说明：电子证照数据推送Mapper
 */
@Mapper
public interface ZjtQzjMapper extends BaseMapper<ZjtQzj>  {

    /**
    *
    * 批量增加电子证照数据推送
    * @param zjtQzjList
    * <AUTHOR>
    * @date 2024年05月24日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<ZjtQzj> zjtQzjList);

}