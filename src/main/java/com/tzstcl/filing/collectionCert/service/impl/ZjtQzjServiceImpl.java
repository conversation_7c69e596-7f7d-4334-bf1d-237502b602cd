package com.tzstcl.filing.collectionCert.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.commons.utils.DateUtils;
import com.tzstcl.filing.collectionCert.service.ZjtQzjService;
import com.tzstcl.filing.collectionCert.model.ZjtQzj;
import com.tzstcl.filing.collectionCert.mapper.ZjtQzjMapper;
import com.tzstcl.filing.pushResult.mapper.PushResultMapper;
import com.tzstcl.filing.pushResult.model.PushResult;
import com.tzstcl.filing.tripartitePush.utils.CallAPI;
import com.tzstcl.filing.updatePushData.model.UpdatePushData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.tzstcl.filing.tripartitePush.utils.CallAPI.APIInvoke;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月24日
 * 说明：电子证照数据推送ServiceImpl
 */
@Service
public class ZjtQzjServiceImpl extends BaseServiceImpl<ZjtQzjMapper,ZjtQzj> implements ZjtQzjService{
    @Autowired
    PushResultMapper pushResultMapper;
    @Value("${nation.collect.url}")
    private String collectUrl;
    @Value("${nation.building.collection.url}")
    private String collectCertUrl;
    /**
    *
    * 批量增加电子证照数据推送
    * @param zjtQzjList
    * <AUTHOR>
    * @date 2024年05月24日
    * @return Integer 插入的记录数
    */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<ZjtQzj> zjtQzjList){return this.mapper.insertBatch(zjtQzjList);}
    @Transactional
    @Override
    public AjaxResult collectionCert(List<JSONObject> list, String zjtQZJDataList) {
        String accessToken = CallAPI.getToken(true).getAccessToken();
        String s = APIInvoke(collectUrl, accessToken, null, list);
        JSONObject jsonObject =new JSONObject();

        try{
            jsonObject=JSONObject.parseObject(s);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.put("code",jsonObject.get("ReturnCode"))
                    .put("ReturnMsg",jsonObject.get("ReturnMsg"))
                    .put("ReturnData",jsonObject.get("ReturnData"));
            PushResult pushResult = JSONObject.parseObject(s, PushResult.class);
            List<ZjtQzj> ZjtQzjList = JSONObject.parseArray(zjtQZJDataList,ZjtQzj.class);
            ZjtQzjList.forEach(item->{
                item.setSyncFlag("S");
                item.setSysncDate(DateUtils.getNowDate());
                pushResult.setAssociationId(item.getFmcode());
            });
            this.mapper.insertBatch(ZjtQzjList);
            pushResultMapper.insert(pushResult);
            return ajaxResult;
        }catch (Exception e){
            return AjaxResult.error().put("接口调用异常",s);
        }

    }
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public AjaxResult collectData(List<JSONObject> list, String zjtQZJDataList) {
        String accessToken = CallAPI.getToken2(true).getAccessToken();
        String s = APIInvoke(collectCertUrl, accessToken, null, list);
        JSONObject jsonObject =new JSONObject();

        try{
            jsonObject=JSONObject.parseObject(s);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.put("ReturnCode",jsonObject.get("ReturnCode"))
                    .put("ReturnMsg",jsonObject.get("ReturnMsg"))
                    .put("ReturnData",jsonObject.get("ReturnData"));
            PushResult pushResult = JSONObject.parseObject(s, PushResult.class);
            List<ZjtQzj> ZjtQzjList = JSONObject.parseArray(zjtQZJDataList,ZjtQzj.class);
            ZjtQzjList.forEach(item->{
                item.setSyncFlag("S");
                item.setSysncDate(DateUtils.getNowDate());
                item.setType("B");
                pushResult.setAssociationId(item.getFmcode());
                pushResult.setType("B");
            });
            this.mapper.insertBatch(ZjtQzjList);

            pushResultMapper.insert(pushResult);
            return ajaxResult;
        }catch (Exception e){
            return AjaxResult.error().put("接口调用异常",s);
        }

    }
}
