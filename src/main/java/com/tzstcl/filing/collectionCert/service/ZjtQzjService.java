package com.tzstcl.filing.collectionCert.service;

import com.alibaba.fastjson.JSONObject;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.service.BaseService;
import com.tzstcl.filing.collectionCert.model.ZjtQzj;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月24日
 * 说明：电子证照数据推送Servic
 */
public interface ZjtQzjService extends BaseService<ZjtQzj> {

    /**
    *
    * 批量增加电子证照数据推送
    * @param zjtQzjList
    * <AUTHOR>
    * @date 2024年05月24日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<ZjtQzj> zjtQzjList);

    /**
     * 归集电子证照数据
     * @param list
     * @param zjtQZJDataList
     * @return
     */

    AjaxResult collectionCert(List<JSONObject> list, String zjtQZJDataList);

    AjaxResult collectData(List<JSONObject> list, String zjtQZJDataList);
}
