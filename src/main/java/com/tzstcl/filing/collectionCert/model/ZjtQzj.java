package com.tzstcl.filing.collectionCert.model;

import com.tzstcl.base.model.BaseModel;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月24日
 * 说明：电子证照数据推送实体类
 */
@Data
public class ZjtQzj extends BaseModel<ZjtQzj> implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
     *二维码赋码
     */
    private String fmcode;
    /**
     *证照标识
     */
    private String certId;
    /**
     *证照预览地址
     */
    private String certPreviewUrl;
    /**
     *同步标识
     */
    private String syncFlag;
    /**
     *同步时间
     */
    private Date sysncDate;
    /**
     *证书编号
     */
    private String certNum;

    private String type;

}
