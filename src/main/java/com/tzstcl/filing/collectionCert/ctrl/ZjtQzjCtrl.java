package com.tzstcl.filing.collectionCert.ctrl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.filing.collectionCert.model.ZjtQzj;
import com.tzstcl.filing.collectionCert.service.ZjtQzjService;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月24日
 * 说明：电子证照数据推送Controller
 */
@Controller
@RequestMapping("/admin/zjtQzj")
public class ZjtQzjCtrl extends BaseCtrl {

    @Autowired
    private ZjtQzjService zjtQzjService;

    /**
    * list页面导航
    * @return
    */
    @RequiresPermissions("zjtQzj:view")
    @RequestMapping("/toList")
    public String toList() {
        return "admin/filing/collectionCert/zjtQzjList";
    }

    /**
    * 获取查询的分页数据
    * @param zjtQzj
    * @return
    */
    @RequestMapping("/list")
    @RequiresPermissions("zjtQzj:view")
    @ResponseBody
    public PageInfo<ZjtQzj> list(ZjtQzj zjtQzj) {
        return  zjtQzjService.selectPage(zjtQzj);
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"zjtQzj:edite","zjtQzj:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        if(null != id){
            model.addAttribute("zjtQzj" ,zjtQzjService.getOne(id));
        }
        return "admin/filing/collectionCert/zjtQzjForm";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("zjtQzj:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("zjtQzj" ,zjtQzjService.getOne(id));
        }
        return "admin/filing/collectionCert/zjtQzjDetail";
    }

    /**
     * 新增
     * @param zjtQzj
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"zjtQzj:add"})
    @ResponseBody
    public AjaxResult save(@Valid ZjtQzj zjtQzj) {
         return toAjax(zjtQzjService.add(zjtQzj));
    }

    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("zjtQzj:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("zjtQzj" ,zjtQzjService.getOne(id));
        }
        return "admin/filing/collectionCert/zjtQzjEdit";
    }

    /**
    * 更新
    * @param zjtQzj
    * @return
    */
    @PostMapping("/update")
    @RequiresPermissions("zjtQzj:edit")
    @ResponseBody
    public AjaxResult update(@Valid ZjtQzj zjtQzj) {
        return toAjax(zjtQzjService.update(zjtQzj));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("zjtQzj:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(zjtQzjService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("zjtQzj:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",zjtQzjService.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param zjtQzj
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"zjtQzj:add", "zjtQzj:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(ZjtQzj zjtQzj) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = zjtQzj.getId();
        zjtQzj.setId(null);
        List<ZjtQzj> zjtQzjList = zjtQzjService.selectList(zjtQzj);
        if(StringUtils.isNotEmpty(zjtQzjList)){
            if(zjtQzjList.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(zjtQzjList.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }

}
