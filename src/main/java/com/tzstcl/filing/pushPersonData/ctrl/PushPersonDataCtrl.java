package com.tzstcl.filing.pushPersonData.ctrl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.filing.pushPersonData.model.PushPersonData;
import com.tzstcl.filing.pushPersonData.service.PushPersonDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：相关信息接口Controller
 */
@Controller
@RequestMapping("/admin/pushPersonData")
public class PushPersonDataCtrl extends BaseCtrl {

    @Autowired
    private PushPersonDataService pushPersonDataService;

    /**
    * list页面导航
    * @return
    */
    @RequiresPermissions("pushPersonData:view")
    @RequestMapping("/toList")
    public String toList() {
        return "admin/filing/pushPersonData/pushPersonDataList";
    }

    /**
    * 获取查询的分页数据
    * @param pushPersonData
    * @return
    */
    @RequestMapping("/list")
    @RequiresPermissions("pushPersonData:view")
    @ResponseBody
    public PageInfo<PushPersonData> list(PushPersonData pushPersonData) {
        return  pushPersonDataService.selectPage(pushPersonData);
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"pushPersonData:edite","pushPersonData:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        if(null != id){
            model.addAttribute("pushPersonData" ,pushPersonDataService.getOne(id));
        }
        return "admin/filing/pushPersonData/pushPersonDataForm";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("pushPersonData:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("pushPersonData" ,pushPersonDataService.getOne(id));
        }
        return "admin/filing/pushPersonData/pushPersonDataDetail";
    }

    /**
     * 新增
     * @param pushPersonData
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"pushPersonData:add"})
    @ResponseBody
    public AjaxResult save(@Valid PushPersonData pushPersonData) {
         return toAjax(pushPersonDataService.add(pushPersonData));
    }

    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("pushPersonData:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("pushPersonData" ,pushPersonDataService.getOne(id));
        }
        return "admin/filing/pushPersonData/pushPersonDataEdit";
    }

    /**
    * 更新
    * @param pushPersonData
    * @return
    */
    @PostMapping("/update")
    @RequiresPermissions("pushPersonData:edit")
    @ResponseBody
    public AjaxResult update(@Valid PushPersonData pushPersonData) {
        return toAjax(pushPersonDataService.update(pushPersonData));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("pushPersonData:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(pushPersonDataService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("pushPersonData:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",pushPersonDataService.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param pushPersonData
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"pushPersonData:add", "pushPersonData:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(PushPersonData pushPersonData) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = pushPersonData.getId();
        pushPersonData.setId(null);
        List<PushPersonData> pushPersonDataList = pushPersonDataService.selectList(pushPersonData);
        if(StringUtils.isNotEmpty(pushPersonDataList)){
            if(pushPersonDataList.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(pushPersonDataList.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }

}
