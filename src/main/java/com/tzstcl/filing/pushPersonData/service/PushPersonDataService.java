package com.tzstcl.filing.pushPersonData.service;

import com.alibaba.fastjson.JSONObject;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.service.BaseService;
import com.tzstcl.filing.pushPersonData.model.PushPersonData;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：相关信息接口Servic
 */
public interface PushPersonDataService extends BaseService<PushPersonData> {

    /**
    *
    * 批量增加相关信息接口
    * @param pushPersonDataList
    * <AUTHOR>
    * @date 2024年05月23日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<PushPersonData> pushPersonDataList);
    /**
    *
    * 推送人员相关信息接口
    * @param list、personDataList
    * <AUTHOR>
    * @date 2024年05月23日
    * @return Integer 插入的记录数
    */

    AjaxResult pushPersonData(List<JSONObject> list, String personDataList);
}
