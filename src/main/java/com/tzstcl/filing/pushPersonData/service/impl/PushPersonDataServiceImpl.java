package com.tzstcl.filing.pushPersonData.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.commons.utils.DateUtils;
import com.tzstcl.filing.collectionCert.model.ZjtQzj;
import com.tzstcl.filing.pushPersonData.service.PushPersonDataService;
import com.tzstcl.filing.pushPersonData.model.PushPersonData;
import com.tzstcl.filing.pushPersonData.mapper.PushPersonDataMapper;
import com.tzstcl.filing.pushResult.mapper.PushResultMapper;
import com.tzstcl.filing.pushResult.model.PushResult;
import com.tzstcl.filing.tripartitePush.utils.CallAPI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.tzstcl.filing.tripartitePush.utils.CallAPI.APIInvoke;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：相关信息接口ServiceImpl
 */
@Service
public class PushPersonDataServiceImpl extends BaseServiceImpl<PushPersonDataMapper,PushPersonData> implements PushPersonDataService{
    @Autowired
    PushResultMapper pushResultMapper;
    @Value("${nation.person.url}")
    private String personUrl;
    /**
    *
    * 批量增加相关信息接口
    * @param pushPersonDataList
    * <AUTHOR>
    * @date 2024年05月23日
    * @return Integer 插入的记录数
    */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<PushPersonData> pushPersonDataList){return this.mapper.insertBatch(pushPersonDataList);}
    @Transactional
    @Override
    public AjaxResult pushPersonData(List<JSONObject> list, String personDataList) {
        String accessToken = CallAPI.getToken(true).getAccessToken();
        String s = APIInvoke(personUrl, accessToken, null, list);
        JSONObject jsonObject =new JSONObject();

        try{
            jsonObject=JSONObject.parseObject(s);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.put("code",jsonObject.get("ReturnCode"))
                    .put("ReturnMsg",jsonObject.get("ReturnMsg"))
                    .put("ReturnData",jsonObject.get("ReturnData"));
            PushResult pushResult = JSONObject.parseObject(s, PushResult.class);
            List<PushPersonData> pushPersonDataList = JSONObject.parseArray(personDataList,PushPersonData.class);
            pushPersonDataList.forEach(item->{
                item.setPushFlag("S");
                item.setPushTime(DateUtils.dateTime(DateUtils.getNowDate()));
                pushResult.setAssociationId(item.getBusinessDataId());
            });
            this.mapper.insertBatch(pushPersonDataList);
            pushResultMapper.insert(pushResult);
            return ajaxResult;
        }catch (Exception e){
            return AjaxResult.error().put("接口调用异常",s);
        }
    }
}
