package com.tzstcl.filing.pushPersonData.model;

import com.tzstcl.base.model.BaseModel;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：相关信息接口实体类
 */
@Data
public class PushPersonData extends BaseModel<PushPersonData> implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
     *业务数据ID
     */
    private String businessDataId;
    /**
     *证照编号
     */
    private String certNum;
    /**
     *证照标识
     */
    private String certId;
    /**
     *所属单位统一社会信用代码
     */
    private String corpCode;
    /**
     *所属单位名称
     */
    private String corpName;
    /**
     *人员类型
     */
    private String personType;
    /**
     *名字
     */
    private String name;
    /**
     *身份证件号码
     */
    private String identityCard;
    /**
     *身份证件号码类型代码
     */
    private String identityCardType;
    /**
     *数据有效标识
     */
    private String dataFlag;
    /**
     *数据无效原因
     */
    private String dataRemark;
    /**
     *推送标识
     */
    private String pushFlag;
    /**
     *推送时间
     */
    private String pushTime;
    /**
     *创建时间
     */
    private Date creatTime;
    /**
     *创建人
     */
    private String creatAt;

}