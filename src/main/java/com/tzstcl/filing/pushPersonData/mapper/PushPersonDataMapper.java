package com.tzstcl.filing.pushPersonData.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import com.tzstcl.filing.pushPersonData.model.PushPersonData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：zlq
 * 日期：2024年05月23日
 * 说明：相关信息接口Mapper
 */
@Mapper
public interface PushPersonDataMapper extends BaseMapper<PushPersonData>  {

    /**
    *
    * 批量增加相关信息接口
    * @param pushPersonDataList
    * <AUTHOR>
    * @date 2024年05月23日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<PushPersonData> pushPersonDataList);

}