package com.tzstcl.archetype.cms.web;

import com.alibaba.fastjson.JSON;
import com.tzstcl.archetype.cms.model.SysNews;
import com.tzstcl.archetype.cms.service.SysNewsService;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.model.BaseWebModel;
import com.tzstcl.commons.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：dingyz
 * 日期：2019年07月13日
 * 说明：cms接口
 */

@RestController
@RequestMapping("/web")
@Slf4j
@CrossOrigin(origins = "*",maxAge = 3600)
public class CmsWeb extends BaseWebModel {

    @Autowired
    private SysNewsService sysNewsService;


    /**
     * 公司：天筑科技股份有限公司
     * 作者：dingyz
     * 日期：2019年07月13日
     * 说明：前端新闻数据显示4条
     */
    @PostMapping("/listForWeb")
    public AjaxResult listForWeb(BaseWebModel webModel){
        //参数校验
        String paramStr = webModel.getData();
        if (StringUtils.isBlank(paramStr)) {
            return AjaxResult.error("请求参数空异常");
        }
        //获取参数
        Map<String,Object> map = null;
     try {
         map = JSON.parseObject(paramStr,HashMap.class);
     }catch (Exception e){
         return  AjaxResult.error("请求参数转换异常");
     }
     String category = map.get("category").toString();

//        JSONObject jsonObject = JSON.parseObject(paramStr);
//        String category = jsonObject.getString("category");
        if(StringUtils.isBlank(category)){
            return AjaxResult.error("请求参数空异常");
        }
        return  sysNewsService.listForWeb(category);
    }

    /**
     * 公司：天筑科技股份有限公司
     * 作者：dingyz
     * 日期：2019年07月15日
     * 说明：内容详情 登录前
     */
    @GetMapping("/getNewsContent/{id}")
    public AjaxResult getNewsContent(@PathVariable("id")Long id){
        return  sysNewsService.getNewsContent(id);
    }


    /**
     * 公司：天筑科技股份有限公司
     * 作者：dingyz
     * 日期：2019年07月15日
     * 说明：内容分页
     */
    @PostMapping("/listNews")
    public AjaxResult listNews(BaseWebModel webModel){
        //参数校验
        String paramStr = webModel.getData();
        if (StringUtils.isBlank(paramStr)) {
            return AjaxResult.error("请求参数空异常");
        }
        //获取参数
        SysNews sysNews = null;
        try {
            sysNews = JSON.parseObject(paramStr, SysNews.class);
        }catch (Exception e){
            return  AjaxResult.error("请求参数转换异常");
        }
        return  sysNewsService.listNews(sysNews);
    }



    /**
     * 公司：天筑科技股份有限公司
     * 作者：dingyz
     * 日期：2019年07月13日
     * 说明：测试
     */
    @GetMapping("/listForWeb2")
    @ResponseBody
    public AjaxResult listForWeb2(SysNews sysNews){
        return  sysNewsService.listNews(sysNews);
    }

    @GetMapping("/listNews2")
    public AjaxResult listNews2(SysNews sysNews){
        return  sysNewsService.listNews(sysNews);
    }

}
