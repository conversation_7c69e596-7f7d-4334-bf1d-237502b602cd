package com.tzstcl.archetype.cms.model;

import com.tzstcl.base.model.BaseModel;
import lombok.Data;

import java.io.Serializable;

/**
 * 公司：天筑科技股份有限公司
 * 作者：dingyz
 * 日期：2019年07月11日
 * 说明：新闻资讯实体类
 */
@Data
public class SysNews extends BaseModel<SysNews> implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
     *标题
     */
    private String title;
    /**
     *作者 区别创建人
     */
    private String author;
    /**
     *内容类型
     */
    private String category;
    /**
     *描述
     */
    private String description;
    /**
     *状态 0保存,1正常 
     */
    private String state;
    /**
     *浏览量
     */
    private Long browseVolume;
    /**
     *点赞 点击
     */
    private Long clicks;
    /**
     *收藏次数
     */
    private Long collection;
    /**
     *是否置顶 0 否1 是
     */
    private String roofPlacement;

    /**
     *自定义
     */
    private String details;



    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Long getBrowseVolume() {
        return browseVolume;
    }

    public void setBrowseVolume(Long browseVolume) {
        this.browseVolume = browseVolume;
    }

    public Long getClicks() {
        return clicks;
    }

    public void setClicks(Long clicks) {
        this.clicks = clicks;
    }

    public Long getCollection() {
        return collection;
    }

    public void setCollection(Long collection) {
        this.collection = collection;
    }

    public String getRoofPlacement() {
        return roofPlacement;
    }

    public void setRoofPlacement(String roofPlacement) {
        this.roofPlacement = roofPlacement;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }
}