package com.tzstcl.archetype.cms.mapper;

import com.tzstcl.archetype.cms.model.SysNews;
import com.tzstcl.base.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：dingyz
 * 日期：2019年07月11日
 * 说明：新闻资讯Mapper
 */
@Mapper
public interface SysNewsMapper extends BaseMapper<SysNews> {

    /**
    *
    * 批量增加新闻资讯
    * @param sysNewsList
    * <AUTHOR>
    * @date 2019年07月11日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<SysNews> sysNewsList);

    /**
     * 公司：天筑科技股份有限公司
     * 作者：dingyz
     * 日期：2019年07月11日
     * 说明：增加浏览量
     */
    void addBrowseVolume(SysNews sysNews);

}