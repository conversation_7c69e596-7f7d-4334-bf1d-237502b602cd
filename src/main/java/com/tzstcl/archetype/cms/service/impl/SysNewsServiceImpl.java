package com.tzstcl.archetype.cms.service.impl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.archetype.cms.mapper.SysNewsMapper;
import com.tzstcl.archetype.cms.model.SysNews;
import com.tzstcl.archetype.cms.service.SysNewsService;
import com.tzstcl.archetype.detail.model.SysNewsDetails;
import com.tzstcl.archetype.detail.service.SysNewsDetailsService;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.service.impl.BaseServiceImpl;
import com.tzstcl.commons.utils.UploadFileUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：dingyz
 * 日期：2019年07月11日
 * 说明：新闻资讯ServiceImpl
 */
@Service
public class SysNewsServiceImpl extends BaseServiceImpl<SysNewsMapper, SysNews> implements SysNewsService {

    @Resource
    private SysNewsDetailsService sysNewsDetailsService;


    @Value("${ck.http}")
    private String ck_http;

    /**
    *
    * 批量增加新闻资讯
    * @param sysNewsList
    * <AUTHOR>
    * @date 2019年07月11日
    * @return Integer 插入的记录数
    */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<SysNews> sysNewsList){
        return this.mapper.insertBatch(sysNewsList);
    }


    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public void save(SysNews sysNews, MultipartFile uploadFile) {
        SysNewsDetails details =sysNewsDetailsService.getOne(sysNews.getId());
        if(uploadFile.getOriginalFilename()!=null && !"".equals(uploadFile.getOriginalFilename())){
            String type = "newsFile";
            Map<String, Object> file = null;
            try {
                file = UploadFileUtil.saveFile(uploadFile, type);
            } catch (IOException e) {
                e.printStackTrace();
            }
            System.out.println(file.get("filePath"));
            //文件下载地址
            String originalFileName = uploadFile.getOriginalFilename();
            String path = ck_http +file.get("fileUrl").toString();
            details.setFiles(path);
            //文件名
            sysNews.setDescription(originalFileName);
    }
        this.insertOrUpdate(sysNews);
        if (details==null){
            details = new SysNewsDetails();
        }

        details.setContentId(sysNews.getId().toString());
        details.setDetails(sysNews.getDetails());
      sysNewsDetailsService.insertOrUpdate(details);
    }

    /**
     *前端新闻数据显示4条
     **/
    @Override
    public AjaxResult listForWeb(String category) {
        SysNews sysNews = new SysNews();
        sysNews.setCategory(category);
        //初始显示4条数据
         sysNews.setPageSize(4);
        PageInfo<SysNews> sysNewsPageInfo = selectPage(sysNews);
        return AjaxResult.success("查询4条数据",sysNewsPageInfo);
    }

    /**
     * 公司：天筑科技股份有限公司
     * 作者：dingyz
     * 日期：2019年07月15日
     * 说明：新闻资讯/通知公告详情
     */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public AjaxResult getNewsContent(Long id) {
        SysNews sysNews = this.getOne(id);
        SysNewsDetails details = sysNewsDetailsService.getOne(id);
        if(details!=null){
            sysNews.setDetails(details.getDetails());
        }
        //增加浏览量
        this.addBrowseVolume(sysNews);
        return AjaxResult.success("详情查询成功",sysNews);
    }

    @Override
    public AjaxResult listNews(SysNews sysNews) {

        PageInfo<SysNews> sysNewsPageInfo = selectPage(sysNews);

        return AjaxResult.success("查询成功",sysNewsPageInfo);
    }

    /**
     * 公司：天筑科技股份有限公司
     * 作者：dingyz
     * 日期：2019年07月11日
     * 说明：增加浏览量
     */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public void addBrowseVolume(SysNews sysNews) {
             mapper.addBrowseVolume(sysNews);
    }


}