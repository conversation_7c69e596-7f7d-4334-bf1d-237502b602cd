package com.tzstcl.archetype.cms.service;

import com.tzstcl.archetype.cms.model.SysNews;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.base.service.BaseService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：dingyz
 * 日期：2019年07月11日
 * 说明：新闻资讯Servic
 */
public interface SysNewsService extends BaseService<SysNews> {

    /**
    *
    * 批量增加新闻资讯
    * @param sysNewsList
    * <AUTHOR>
    * @date 2019年07月11日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<SysNews> sysNewsList);

     void save(SysNews sysNews, MultipartFile uploadFile);

    /**
     * 公司：天筑科技股份有限公司
     * 作者：dingyz
     * 日期：2019年07月11日
     * 说明：前端显示的初始4条cms数据
     * category 内容的类型 新闻资讯 or 通知公告
     */
        AjaxResult listForWeb(String category);

        AjaxResult getNewsContent(Long id);
    /**
     * 公司：天筑科技股份有限公司
     * 作者：dingyz
     * 日期：2019年07月15日
     * 说明：新闻分页
     * category 内容的类型 新闻资讯 or 通知公告
     */
        AjaxResult listNews(SysNews sysNews);

        void  addBrowseVolume(SysNews sysNews);

}