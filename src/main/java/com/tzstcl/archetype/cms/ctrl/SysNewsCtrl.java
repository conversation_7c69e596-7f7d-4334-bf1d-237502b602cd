package com.tzstcl.archetype.cms.ctrl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.archetype.cms.model.SysNews;
import com.tzstcl.archetype.cms.service.SysNewsService;
import com.tzstcl.archetype.detail.model.SysNewsDetails;
import com.tzstcl.archetype.detail.service.SysNewsDetailsService;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.StringUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：dingyz
 * 日期：2019年07月11日
 * 说明：新闻资讯Controller
 */
@Controller
@RequestMapping("/admin/sysNews")
public class SysNewsCtrl extends BaseCtrl {

    @Autowired
    private SysNewsService sysNewsService;

    @Resource
    private SysNewsDetailsService sysNewsDetailsService;



    /**
    * list页面导航
    * @return
    */
    @RequiresPermissions("sysNews:view")
    @RequestMapping("/toList")
    public String toList() {
        return "admin/cms/sysNewsList";
    }

    /**
    * 获取查询的分页数据
    * @param sysNews
    * @return
    */
    @RequestMapping("/list")
    @RequiresPermissions("sysNews:view")
    @ResponseBody
    public PageInfo<SysNews> list(SysNews sysNews) {
        return  sysNewsService.selectPage(sysNews);
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"sysNews:edite","sysNews:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        if(null != id){
            model.addAttribute("sysNews" ,sysNewsService.getOne(id));
        }
        return "admin/cms/sysNewsForm";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("sysNews:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            SysNews sysNews = sysNewsService.getOne(id);
            SysNewsDetails details = sysNewsDetailsService.getOne(sysNews.getId());
            sysNews.setDetails(details.getDetails());
            model.addAttribute("fileUrl",details.getFiles());
            model.addAttribute("sysNews" ,sysNews);
        }
        return "admin/cms/sysNewsDetail";
    }


    /**
     * 新增
     * @param sysNews
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"sysNews:add"})
    @ResponseBody
    public AjaxResult save(@Valid SysNews sysNews, MultipartFile uploadFile) {
        sysNewsService.save(sysNews,uploadFile);
        return AjaxResult.success();
    }

    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("sysNews:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            SysNews sysNews = sysNewsService.getOne(id);
            SysNewsDetails details = sysNewsDetailsService.getOne(sysNews.getId());
            sysNews.setDetails(details.getDetails());
            model.addAttribute("fileUrl",details.getFiles());
            model.addAttribute("sysNews" ,sysNews);
        }
        return "admin/cms/sysNewsEdit";
    }

    /**
    * 更新
    * @param sysNews
    * @return
    */
    @PostMapping("/update")
    @RequiresPermissions("sysNews:edit")
    @ResponseBody
    public AjaxResult update(@Valid SysNews sysNews) {
        return toAjax(sysNewsService.update(sysNews));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("sysNews:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(sysNewsService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("sysNews:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",sysNewsService.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param sysNews
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"sysNews:add", "sysNews:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(SysNews sysNews) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = sysNews.getId();
        sysNews.setId(null);
        List<SysNews> sysNewsList = sysNewsService.selectList(sysNews);
        if(StringUtils.isNotEmpty(sysNewsList)){
            if(sysNewsList.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(sysNewsList.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }

}