package com.tzstcl.archetype.cms.ctrl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.archetype.cms.model.SysNews;
import com.tzstcl.archetype.cms.service.SysNewsService;
import com.tzstcl.base.ctrl.BaseCtrl;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 公司：天筑科技股份有限公司
 * 作者：dingyz
 * 日期：2019年07月11日
 * 说明：通知公告Controller
 */
@Controller
@RequestMapping("/admin/notice")
public class NoticeCtrl extends BaseCtrl {


    @Autowired
    private SysNewsService sysNewsService;

    /**
     * list页面导航
     * @return
     */
    @RequiresPermissions("notice:view")
    @RequestMapping("/toList")
    public String toList() {
        return "admin/cms/noticeList";
    }

    /**
     * 获取查询的分页数据
     * @param sysNews
     * @return
     */
    @RequestMapping("/list")
    @RequiresPermissions("notice:view")
    @ResponseBody
    public PageInfo<SysNews> list(SysNews sysNews) {
        return  sysNewsService.selectPage(sysNews);
    }

    /**
     * form页面导航
     * @return
     */
    @RequiresPermissions(value={"notice:edite","notice:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        if(null != id){
            model.addAttribute("sysNews" ,sysNewsService.getOne(id));
        }
        return "admin/cms/noticeForm";
    }
}
