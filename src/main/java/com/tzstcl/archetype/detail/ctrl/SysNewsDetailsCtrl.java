package com.tzstcl.archetype.detail.ctrl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.archetype.detail.model.SysNewsDetails;
import com.tzstcl.archetype.detail.service.SysNewsDetailsService;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.StringUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：dingyz
 * 日期：2019年07月11日
 * 说明：内容详情表Controller
 */
@Controller
@RequestMapping("/admin/sysNewsDetails")
public class SysNewsDetailsCtrl extends BaseCtrl {

    @Autowired
    private SysNewsDetailsService sysNewsDetailsService;

    /**
    * list页面导航
    * @return
    */
    @RequiresPermissions("sysNewsDetails:view")
    @RequestMapping("/toList")
    public String toList() {
        return "admin/archetype/detail/sysNewsDetailsList";
    }

    /**
    * 获取查询的分页数据
    * @param sysNewsDetails
    * @return
    */
    @RequestMapping("/list")
    @RequiresPermissions("sysNewsDetails:view")
    @ResponseBody
    public PageInfo<SysNewsDetails> list(SysNewsDetails sysNewsDetails) {
        return  sysNewsDetailsService.selectPage(sysNewsDetails);
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"sysNewsDetails:edite","sysNewsDetails:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        if(null != id){
            model.addAttribute("sysNewsDetails" ,sysNewsDetailsService.getOne(id));
        }
        return "admin/archetype/detail/sysNewsDetailsForm";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("sysNewsDetails:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("sysNewsDetails" ,sysNewsDetailsService.getOne(id));
        }
        return "admin/archetype/detail/sysNewsDetailsDetail";
    }

    /**
    * form页面导航 新增
    * @return
    */
    @RequiresPermissions("sysNewsDetails:add")
    @GetMapping("/add")
    public String toAdd() {
        return "admin/archetype/detail/sysNewsDetailsAdd";
    }

    /**
     * 新增
     * @param sysNewsDetails
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"sysNewsDetails:add"})
    @ResponseBody
    public AjaxResult save(@Valid SysNewsDetails sysNewsDetails) {
         return toAjax(sysNewsDetailsService.add(sysNewsDetails));
    }

    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("sysNewsDetails:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("sysNewsDetails" ,sysNewsDetailsService.getOne(id));
        }
        return "admin/archetype/detail/sysNewsDetailsEdit";
    }

    /**
    * 更新
    * @param sysNewsDetails
    * @return
    */
    @PostMapping("/update")
    @RequiresPermissions("sysNewsDetails:edit")
    @ResponseBody
    public AjaxResult update(@Valid SysNewsDetails sysNewsDetails) {
        return toAjax(sysNewsDetailsService.update(sysNewsDetails));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("sysNewsDetails:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(sysNewsDetailsService.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("sysNewsDetails:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",sysNewsDetailsService.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param sysNewsDetails
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"sysNewsDetails:add", "sysNewsDetails:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(SysNewsDetails sysNewsDetails) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = sysNewsDetails.getId();
        sysNewsDetails.setId(null);
        List<SysNewsDetails> sysNewsDetailsList = sysNewsDetailsService.selectList(sysNewsDetails);
        if(StringUtils.isNotEmpty(sysNewsDetailsList)){
            if(sysNewsDetailsList.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(sysNewsDetailsList.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }

}