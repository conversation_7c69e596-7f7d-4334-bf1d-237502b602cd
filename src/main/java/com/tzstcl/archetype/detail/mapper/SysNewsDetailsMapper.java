package com.tzstcl.archetype.detail.mapper;

import com.tzstcl.archetype.detail.model.SysNewsDetails;
import com.tzstcl.base.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：dingyz
 * 日期：2019年07月11日
 * 说明：内容详情表Mapper
 */
@Mapper
public interface SysNewsDetailsMapper extends BaseMapper<SysNewsDetails> {

    /**
    *
    * 批量增加内容详情表
    * @param sysNewsDetailsList
    * <AUTHOR>
    * @date 2019年07月11日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<SysNewsDetails> sysNewsDetailsList);

}