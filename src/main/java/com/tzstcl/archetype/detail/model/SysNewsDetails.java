package com.tzstcl.archetype.detail.model;

import com.tzstcl.base.model.BaseModel;
import lombok.Data;

import java.io.Serializable;

/**
 * 公司：天筑科技股份有限公司
 * 作者：dingyz
 * 日期：2019年07月11日
 * 说明：内容详情表实体类
 */
@Data
public class SysNewsDetails extends BaseModel<SysNewsDetails> implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
     *缩略图路径
     */
    private String coverPicture;
    /**
     *内容表id
     */
    private String contentId;
    /**
     *内容详情
     */
    private String details;
    /**
     *附件路径
     */
    private String files;

    public String getCoverPicture() {
        return coverPicture;
    }

    public void setCoverPicture(String coverPicture) {
        this.coverPicture = coverPicture;
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public String getFiles() {
        return files;
    }

    public void setFiles(String files) {
        this.files = files;
    }
}