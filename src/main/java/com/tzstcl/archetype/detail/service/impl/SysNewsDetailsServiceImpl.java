package com.tzstcl.archetype.detail.service.impl;

import com.tzstcl.archetype.detail.mapper.SysNewsDetailsMapper;
import com.tzstcl.archetype.detail.model.SysNewsDetails;
import com.tzstcl.archetype.detail.service.SysNewsDetailsService;
import com.tzstcl.base.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：dingyz
 * 日期：2019年07月11日
 * 说明：内容详情表ServiceImpl
 */
@Service
public class SysNewsDetailsServiceImpl extends BaseServiceImpl<SysNewsDetailsMapper, SysNewsDetails> implements SysNewsDetailsService{

    /**
    *
    * 批量增加内容详情表
    * @param sysNewsDetailsList
    * <AUTHOR>
    * @date 2019年07月11日
    * @return Integer 插入的记录数
    */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<SysNewsDetails> sysNewsDetailsList){
        return this.mapper.insertBatch(sysNewsDetailsList);
    }

}