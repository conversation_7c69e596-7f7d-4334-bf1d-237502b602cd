package com.tzstcl.archetype.detail.service;

import com.tzstcl.archetype.detail.model.SysNewsDetails;
import com.tzstcl.base.service.BaseService;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：dingyz
 * 日期：2019年07月11日
 * 说明：内容详情表Servic
 */
public interface SysNewsDetailsService extends BaseService<SysNewsDetails> {

    /**
    *
    * 批量增加内容详情表
    * @param sysNewsDetailsList
    * <AUTHOR>
    * @date 2019年07月11日
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<SysNewsDetails> sysNewsDetailsList);

}