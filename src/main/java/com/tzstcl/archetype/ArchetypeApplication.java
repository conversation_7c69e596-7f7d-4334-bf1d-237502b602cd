package com.tzstcl.archetype;

import com.tzstcl.framework.config.DruidConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;

@Import({DruidConfig.class})
@SpringBootApplication(
        exclude = {DataSourceAutoConfiguration.class}
)
@EnableAspectJAutoProxy(proxyTargetClass = true)
@ComponentScan({"com.tzstcl.*"})
@MapperScan({"com.tzstcl.*.mapper", "com.tzstcl.*.*.mapper"})
public class ArchetypeApplication extends SpringBootServletInitializer {
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(new Class[]{ArchetypeApplication.class});
    }

    public static void main(String[] args) {
        SpringApplication.run(ArchetypeApplication.class, args);
    }
}
