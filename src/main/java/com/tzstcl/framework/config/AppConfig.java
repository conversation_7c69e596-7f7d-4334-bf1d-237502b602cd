package com.tzstcl.framework.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 公司：天筑科技股份有限公司
 * 作者：朱国强
 * 日期：2018年08月23日
 * 说明：项目配置
 */
@Component
@ConfigurationProperties(prefix = "project")
public class AppConfig {
    /**
     * 管理后台登陆页面地址
     */
    private String loginPage;
    /**
     * 管理后台登陆路径
     */
    private String loginPath;
    /**
     * 管理后台登陆后的菜单页路径
     */
    private String loginIndexPath;
    /**
     * 管理后台登出路径
     */
    private String logoutPath;

    /**
     * 未获取权限页面路径
     */
    private String hasPermissionErrorPath;

    /**
     * 管理后台需要权限拦截路径
     */
    private String loginInterceptPaths;

    /**
     * 管理后台权限排除路径，逗号分隔
     */
    private String loginInterceptExcludePaths;

    /**
     * 日志记录拦截路径
     */
    private String loggerInterceptPaths;

    /**
     * 日志记录拦截排除路径，逗号分隔
     */
    private String loggerInterceptExcludePaths;

    /**
     * 文件上传的绝对路径
     */
    private String uploadFileAbsolutePath;

    /**
     * 文件上传相对路径
     */
    private String uploadFileRelativePath;

    /**
     * 手机接口拦截路径
     */
    private String appInterceptPath;

    /**
     * 手机接口拦截排除路径
     */
    private String appInterceptExcludePaths;


    /**
     * pc登录验证码开关
     */
    private boolean captchaEnabled=false;

    public String getLoginPage() {
        return loginPage;
    }

    public void setLoginPage(String loginPage) {
        this.loginPage = loginPage;
    }

    public String getLoginPath() {
        return loginPath;
    }

    public void setLoginPath(String loginPath) {
        this.loginPath = loginPath;
    }

    public String getLoginIndexPath() {
        return loginIndexPath;
    }

    public void setLoginIndexPath(String loginIndexPath) {
        this.loginIndexPath = loginIndexPath;
    }

    public String getLogoutPath() {
        return logoutPath;
    }

    public void setLogoutPath(String logoutPath) {
        this.logoutPath = logoutPath;
    }

    public String getLoginInterceptPaths() {
        return loginInterceptPaths;
    }

    public void setLoginInterceptPaths(String loginInterceptPaths) {
        this.loginInterceptPaths = loginInterceptPaths;
    }

    public String getLoginInterceptExcludePaths() {
        return loginInterceptExcludePaths;
    }

    public void setLoginInterceptExcludePaths(String loginInterceptExcludePaths) {
        this.loginInterceptExcludePaths = loginInterceptExcludePaths;
    }

    public String getUploadFileAbsolutePath() {
        return uploadFileAbsolutePath;
    }

    public void setUploadFileAbsolutePath(String uploadFileAbsolutePath) {
        this.uploadFileAbsolutePath = uploadFileAbsolutePath;
    }

    public String getUploadFileRelativePath() {
        return uploadFileRelativePath;
    }

    public void setUploadFileRelativePath(String uploadFileRelativePath) {
        this.uploadFileRelativePath = uploadFileRelativePath;
    }

    public String getAppInterceptPath() {
        return appInterceptPath;
    }

    public void setAppInterceptPath(String appInterceptPath) {
        this.appInterceptPath = appInterceptPath;
    }

    public String getAppInterceptExcludePaths() {
        return appInterceptExcludePaths;
    }

    public void setAppInterceptExcludePaths(String appInterceptExcludePaths) {
        this.appInterceptExcludePaths = appInterceptExcludePaths;
    }

    public String getHasPermissionErrorPath() {
        return hasPermissionErrorPath;
    }

    public void setHasPermissionErrorPath(String hasPermissionErrorPath) {
        this.hasPermissionErrorPath = hasPermissionErrorPath;
    }

    public String getLoggerInterceptPaths() {
        return loggerInterceptPaths;
    }

    public void setLoggerInterceptPaths(String loggerInterceptPaths) {
        this.loggerInterceptPaths = loggerInterceptPaths;
    }

    public String getLoggerInterceptExcludePaths() {
        return loggerInterceptExcludePaths;
    }

    public void setLoggerInterceptExcludePaths(String loggerInterceptExcludePaths) {
        this.loggerInterceptExcludePaths = loggerInterceptExcludePaths;
    }

    public boolean getCaptchaEnabled() {
        return captchaEnabled;
    }

    public void setCaptchaEnabled(boolean captchaEnabled) {
        this.captchaEnabled = captchaEnabled;
    }
}
