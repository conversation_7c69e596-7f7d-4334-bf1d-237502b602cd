package com.tzstcl.framework.config;

import at.pollux.thymeleaf.shiro.dialect.ShiroDialect;
import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.framework.shiro.CaptchaValidateFilter;
import com.tzstcl.framework.shiro.LogoutFilter;
import com.tzstcl.framework.shiro.ShiroSessionFilter;
import com.tzstcl.framework.shiro.UserRealm;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 权限配置加载
 *
 * <AUTHOR>
 */
@Configuration
public class ShiroConfig {
    /**
     * 系统基础配置
     */
    @Autowired
    private AppConfig appConfig;

    @Bean
    public UserRealm userRealm() {
        UserRealm userRealm = new UserRealm();
        return userRealm;
    }

    /**
     * 自定义验证码过滤器
     */
    @Bean
    public CaptchaValidateFilter captchaValidateFilter() {
        CaptchaValidateFilter captchaValidateFilter = new CaptchaValidateFilter();
        captchaValidateFilter.setCaptchaEnabled(appConfig.getCaptchaEnabled());
        captchaValidateFilter.setCaptchaType("math");
        return captchaValidateFilter;
    }

    /**
     * 退出过滤器
     */
    public LogoutFilter logoutFilter() {
        LogoutFilter logoutFilter = new LogoutFilter();
        logoutFilter.setLoginUrl(appConfig.getLoginPage());
        return logoutFilter;
    }

    /**
     * Shiro过滤器配置
     */
    @Bean
    public ShiroFilterFactoryBean shiroFilterFactoryBean(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();
        shiroFilterFactoryBean.setSecurityManager(securityManager);
        // 权限认证失败，则跳转到指定页面
        shiroFilterFactoryBean.setUnauthorizedUrl(appConfig.getHasPermissionErrorPath());
        // 身份认证失败，则跳转到登录页面的配置
        shiroFilterFactoryBean.setLoginUrl(appConfig.getLoginPage());
        // Shiro连接约束配置，即过滤链的定义
        LinkedHashMap<String, String> filterChainDefinitionMap = new LinkedHashMap<>();
        String[] excludePath = StringUtils.split(appConfig.getLoginInterceptExcludePaths(), ",");
        for (int i = 0; i < excludePath.length; i++) {
            filterChainDefinitionMap.put(excludePath[i], "anon");//接口排除
        }
        // 退出 logout地址，shiro去清除session
        filterChainDefinitionMap.put(appConfig.getLogoutPath(), "logout");
        filterChainDefinitionMap.put(appConfig.getLoginPath(), "anon,captchaValidate");
        filterChainDefinitionMap.put(appConfig.getLoginPage(), "anon");
        filterChainDefinitionMap.put(appConfig.getHasPermissionErrorPath(), "anon");
        filterChainDefinitionMap.put("/admin/sysUser/updatePassword", "anon");

        filterChainDefinitionMap.put("/admin/filingEquipmentFilingApplication/addFilingApplication", "anon");
        filterChainDefinitionMap.put("/admin/filingEquipmentFilingApplication/updateFilingApplication", "anon");
        filterChainDefinitionMap.put("/admin/equipmentFilingCancellation/addFilingCancellation", "anon");
        filterChainDefinitionMap.put("/admin/equipmentFilingCancellation/updateFilingCancellation", "anon");
        filterChainDefinitionMap.put("/admin/filingEquipmentRegistrationForm/addRegistrationForm", "anon");
        filterChainDefinitionMap.put("/admin/filingEquipmentRegistrationForm/updateRegistrationForm", "anon");
        filterChainDefinitionMap.put("/admin/filingEquipmentUseCancellation/addUseCancellation", "anon");
        filterChainDefinitionMap.put("/admin/filingEquipmentUseCancellation/updateUseCancellation", "anon");
        filterChainDefinitionMap.put("/admin/filingEquipmentInstallationForm/addInstallationForm", "anon");
        filterChainDefinitionMap.put("/admin/filingEquipmentInstallationForm/updateInstallationForm", "anon");
        filterChainDefinitionMap.put("/admin/filingEquipmentFilingApplication/getByDeviceFilingCode", "anon");
        filterChainDefinitionMap.put("/admin/filingEquipmentFilingApplication/getAreaByApp", "anon");

        // 所有请求需要认证
        String[] loginInterceptPaths = StringUtils.split(appConfig.getLoginInterceptPaths(), ",");
        for (int i = 0; i < loginInterceptPaths.length; i++) {
            System.out.println("拦截路径"+loginInterceptPaths[i]);
            filterChainDefinitionMap.put(loginInterceptPaths[i], "authc");//拦截地址
        }
        Map<String, Filter> filters = new LinkedHashMap<>();
        filters.put("captchaValidate", captchaValidateFilter());
        filters.put("logout", logoutFilter());
        filters.put("authc", new ShiroSessionFilter());
        shiroFilterFactoryBean.setFilters(filters);
        shiroFilterFactoryBean.setFilterChainDefinitionMap(filterChainDefinitionMap);
        return shiroFilterFactoryBean;
    }

    /**
     * thymeleaf模板引擎和shiro框架的整合
     */
    @Bean(name = "shiroDialect")
    public ShiroDialect shiroDialect() {
        return new ShiroDialect();
    }

    /**
     * 安全管理器
     */
    @Bean
    public SecurityManager securityManager() {
        DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
        securityManager.setRealm(userRealm());
        SecurityUtils.setSecurityManager(securityManager);
        return securityManager;
    }

    /**
     * 开启Shiro注解通知器
     */
    @Bean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(@Qualifier("securityManager") SecurityManager securityManager) {
        AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor = new AuthorizationAttributeSourceAdvisor();
        authorizationAttributeSourceAdvisor.setSecurityManager(securityManager);
        return authorizationAttributeSourceAdvisor;
    }

}
