package com.tzstcl.framework.config;

import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate客户端连接池配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2018/1/24
 */
@Configuration
public class RestTemplateConfig {

    @Value("${httpclient.maxTotalConnections}")
    private Integer maxTotal;

    @Value("${httpclient.defaultMaxPerRoute}")
    private Integer defaultMaxPerRoute;

    @Value("${httpclient.connectTimeout}")
    private Integer connectTimeout;

    @Value("${httpclient.requestTimeout}")
    private Integer connectionRequestTimeout;

    @Value("${httpclient.socketTimeout}")
    private Integer socketTimeout;

    @Value("${httpclient.validateAfterInactivity}")
    private Integer validateAfterInactivity;

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate(httpRequestFactory());
    }

    @Bean
    public ClientHttpRequestFactory httpRequestFactory() {
        return new HttpComponentsClientHttpRequestFactory(httpClient());
    }

    @Bean
    public HttpClient httpClient() {
        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.getSocketFactory())
                .register("https", SSLConnectionSocketFactory.getSocketFactory())
                .build();
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(registry);
        connectionManager.setMaxTotal(this.maxTotal);
        connectionManager.setDefaultMaxPerRoute(this.defaultMaxPerRoute);
        connectionManager.setValidateAfterInactivity(this.validateAfterInactivity);
        RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(this.socketTimeout) //服务器返回数据(response)的时间，超过抛出read timeout
                .setConnectTimeout(this.connectTimeout) //连接上服务器(握手成功)的时间，超出抛出connect timeout
                .setConnectionRequestTimeout(this.connectionRequestTimeout)//从连接池中获取连接的超时时间，超时间未拿到可用连接，会抛出org.apache.http.conn.ConnectionPoolTimeoutException: Timeout waiting for connection from pool
                .build();
        return HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .setConnectionManager(connectionManager)
                .build();
    }

}
