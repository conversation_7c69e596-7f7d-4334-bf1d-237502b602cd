package com.tzstcl.framework.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 公司：天筑科技股份有限公司
 * 作者：朱国强
 * 日期：2018年08月29日
 * 说明：
 */
@Component
@ConfigurationProperties(prefix = "system")
public class SysConfig {
    /**
     * 项目名称
     */
    private String name;
    /**
     * 版本
     */
    private String version;
    /**
     * 版权年份
     */
    private String copyrightYear;

    /**
     * flowable rest base url
     */
    private String flowableBaseURL;
    /**
     * 获取地址开关
     */
    private static boolean addressEnabled;

    /**
     * 系统默认页面大小
     */
    private static Integer pageSize;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCopyrightYear() {
        return copyrightYear;
    }

    public void setCopyrightYear(String copyrightYear) {
        this.copyrightYear = copyrightYear;
    }


    public static boolean isAddressEnabled() {
        return addressEnabled;
    }

    public void setAddressEnabled(boolean addressEnabled) {
        SysConfig.addressEnabled = addressEnabled;
    }

    public static Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        SysConfig.pageSize = pageSize;
    }

    public String getFlowableBaseURL() {
        return flowableBaseURL;
    }

    public void setFlowableBaseURL(String flowableBaseURL) {
        this.flowableBaseURL = flowableBaseURL;
    }
}
