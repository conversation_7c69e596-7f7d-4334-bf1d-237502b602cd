package com.tzstcl.framework.config;


import org.apache.shiro.session.Session;
import org.apache.shiro.session.mgt.eis.JavaUuidSessionIdGenerator;
import org.apache.shiro.session.mgt.eis.SessionIdGenerator;

import java.io.Serializable;

/**
 * <AUTHOR>
 * <p>
 * 重新生成session id
 */
public class UuidSessionIdGenerator implements SessionIdGenerator {

    UuidSessionIdGenerator(String name) {
        this.name = name;
    }

    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public Serializable generateId(Session session) {
        Serializable uuid = name + new JavaUuidSessionIdGenerator().generateId(session);
        return uuid;
    }


}


