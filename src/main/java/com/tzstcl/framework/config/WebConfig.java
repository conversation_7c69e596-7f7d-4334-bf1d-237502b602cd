package com.tzstcl.framework.config;

import com.tzstcl.commons.utils.StringUtils;
import com.tzstcl.framework.interceptor.APIInterceptor;
import com.tzstcl.framework.interceptor.LoggerInterceptor;
import com.tzstcl.framework.interceptor.WebInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import java.util.Locale;

/**
 * 拦截器
 *
 * <AUTHOR>
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Autowired
    private AppConfig appConfig;
    /**
     * 首页地址
     */
    @Value("${project.indexUrl}")
    private String indexUrl;

    @Autowired
    APIInterceptor apiInterceptor;

    @Autowired
    WebInterceptor webInterceptor;

    /**
     * 默认首页的设置，当输入域名是可以自动跳转到默认指定的网页
     */
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addViewController("/").setViewName("forward:" + indexUrl);
    }

    /**
     * 自定义静态资源文件路径
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/resources/**").addResourceLocations("classpath:/static/");
        registry.addResourceHandler(appConfig.getUploadFileRelativePath() + "**").addResourceLocations("file:" + appConfig.getUploadFileAbsolutePath());
    }

    @Bean
    public LocaleResolver localeResolver() {
        SessionLocaleResolver slr = new SessionLocaleResolver();
        // 默认语言
        slr.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
        return slr;
    }

    @Bean
    public LocaleChangeInterceptor localeChangeInterceptor() {
        LocaleChangeInterceptor lci = new LocaleChangeInterceptor();
        // 参数名
        lci.setParamName("lang");
        return lci;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //添加日志
        InterceptorRegistration registration = registry.addInterceptor(new LoggerInterceptor());
        registration.addPathPatterns(StringUtils.split(appConfig.getLoggerInterceptPaths(), ","))
                .excludePathPatterns(StringUtils.split(appConfig.getLoggerInterceptExcludePaths(), ","));

        //api请求拦截器
        InterceptorRegistration APIregistration = registry.addInterceptor(apiInterceptor);
        APIregistration.addPathPatterns(StringUtils.split(appConfig.getAppInterceptPath(), ",")).excludePathPatterns(StringUtils.split(appConfig.getAppInterceptExcludePaths(), ","));

        //web请求拦截器
        InterceptorRegistration webRegistration = registry.addInterceptor(webInterceptor);
        //todo 排除请求路径，例如未登陆用户可以看到的信息请求
        webRegistration.addPathPatterns("/web/**");

        //国际化
        registry.addInterceptor(localeChangeInterceptor());
    }


}
