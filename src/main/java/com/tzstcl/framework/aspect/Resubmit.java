package com.tzstcl.framework.aspect;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 公司：天筑科技股份有限公司
 * 作者：朱国强
 * 日期：2019年01月03日
 * 说明：防止页面重复提交注解
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Resubmit {
    /**
     * 指定时间内不可重复提交,单位秒
     *
     * @return
     */
    long timeout() default 3;

}
