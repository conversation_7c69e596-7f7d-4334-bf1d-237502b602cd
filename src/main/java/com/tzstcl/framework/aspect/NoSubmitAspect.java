package com.tzstcl.framework.aspect;

import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.IpUtils;
import com.tzstcl.framework.redis.RedisService;
import com.tzstcl.framework.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * 公司：天筑科技股份有限公司
 * 作者：朱国强
 * 日期：2019年07月25日
 * 说明：
 */
@Aspect
@Component
@Order(-10)
@Slf4j
public class NoSubmitAspect {
    protected Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 拦截所有
     * execution(public com.ssslinppp.model.ResultEntity com..*.controller..*.*(..))
     */
    @Pointcut("execution(public * com.tzstcl..*.ctrl..*.*(..))")
    public void ctrlMethed() {
    }

    /**
     * 注解方式
     */
    @Pointcut("@within(Resubmit) || @annotation(Resubmit)")
    public void annotation() {
    }

    /**
     * 拦截处理
     *
     * @param point
     * @return
     * @throws Throwable
     */
    @Around("annotation()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        log.info("验证接口重复提交开始");
        System.out.println("----------------验证接口重复提交---------------------------------");
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .getRequest();
        String ip = IpUtils.getIpAddr(request);
        //获取注解
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        //目标类、方法
        String className = method.getDeclaringClass().getName();
        String name = method.getName();
        String ipKey = String.format("%s#%s", className, name);
        int hashCode = Math.abs(ipKey.hashCode());
        String key = String.format("%s_%d", ip, hashCode);
        Resubmit resubmit = method.getAnnotation(Resubmit.class);
        long timeout = resubmit.timeout();
        if (timeout < 0) {
            timeout = 3;
        }
        RedisService redisService = SpringUtils.getBean(RedisService.class);
        long count = redisService.incrBy(key, 1);
        // 设置有效期
        if (count == 1) {
            redisService.expire(key, timeout);
            log.info("重复提交验证通过");
            Object obj = null;
            obj = point.proceed();
            return obj;
        } else {
            log.info("不能重复提交");
            return AjaxResult.error("不能重复提交");
        }

    }
}
