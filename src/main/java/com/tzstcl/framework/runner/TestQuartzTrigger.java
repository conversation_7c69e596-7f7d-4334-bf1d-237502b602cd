package com.tzstcl.framework.runner;

import com.tzstcl.sys.quartz.utils.QuartzManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 公司：天筑科技股份有限公司
 * 作者：朱国强
 * 日期：2018年09月18日
 * 说明：批处理初始化
 */
@Order(2)
@Component
public class TestQuartzTrigger implements ApplicationRunner {

    private final static Logger LOGGER = LogManager.getLogger(TestQuartzTrigger.class);
    @Autowired
    QuartzManager quartzManager;

    @Override
    public void run(ApplicationArguments args) {
        LOGGER.info("启动测试testQuartz");
        try {
            //20秒执行一次
//            quartzManager.addOrUpdateJob(TestQuartz.class, "testQuartz", "testQuartz", "0/20 * * * * ? *");
        } catch (Exception e) {

            LOGGER.error("启动测试testQuartz异常、异常信息:{}", e.getMessage());
            e.printStackTrace();
        }
    }
}
