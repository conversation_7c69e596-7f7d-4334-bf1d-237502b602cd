# ================================================
#                   \u7CFB\u7EDF\u914D\u7F6E
# ================================================
#\u7CFB\u7EDF\u540D\u79F0
system.name=\u5EFA\u7B51\u8D77\u91CD\u673A\u68B0\u8BBE\u5907\u4F7F\u7528\u767B\u8BB0\u548C\u5907\u6848\u7CFB\u7EDF
#\u7248\u672C
system.version=1.0.1
#\u7248\u6743\u5E74\u4EFD
system.copyrightYear=2019
# \u83B7\u53D6ip\u5730\u5740\u5F00\u5173
system.addressEnabled=true
#\u7CFB\u7EDF\u9ED8\u8BA4\u5217\u8868\u9875\u9762\u6570\u636E\u6761\u6570
system.pageSize=10
# ================================================
#                   \u5DE5\u7A0B\u914D\u7F6E
# ================================================
#\u7F51\u7AD9\u9996\u9875
#project.indexUrl=/index
project.indexUrl=/admin/loginPage
#\u767B\u5F55\u9875\u9762\u5730\u5740
project.loginPage=/admin/loginPage
#\u767B\u5F55\u5730\u5740
project.loginPath=/admin/login
#\u767B\u51FA\u5730\u5740
project.logoutPath=/admin/logout
#\u767B\u9646\u540E\u83DC\u5355\u5730\u5740
project.loginIndexPath=/admin/index
project.hasPermissionErrorPath=/admin/hasPermissionError
#\u767B\u5F55\u62E6\u622A\u5217\u8868
project.loginInterceptPaths=/admin/**
#\u767B\u5F55\u62E6\u622A\u6392\u9664\u5217\u8868
project.loginInterceptExcludePaths=/admin/html/**,/admin/login,/admin/loginPage,/admin/hasPermissionError,/admin/sysDict/getDataDictByType/**,/admin/toRegister,/admin/sysUser/register,/admin/sysUser/checkLoginName,\
  /admin/filingEquipmentFiling/toList,/admin/filingEquipmentFiling/list,\
  /admin/filingEquipmentUseRegistration/list,/admin/filingEquipmentUseRegistration/toList,\
  /admin/filingEquipmentFiling/indexList,/admin/filingEquipmentInstallationForm/allList,/admin/filingEquipmentFilingApplication/syncFilingApplication,/admin/equipmentFilingCancellation/syncFilingCancel,/admin/filingEquipmentRegistrationForm/syncFilingUse,/admin/filingEquipmentUseCancellation/syncUseCancel,/admin/filingEquipmentInstallationForm/syncInstall
#\u65E5\u5FD7\u62E6\u622A\u8DEF\u5F84
project.loggerInterceptPaths=/admin/**,/api/**
#\u65E5\u5FD7\u6392\u9664\u5217\u8868
project.loggerInterceptExcludePaths=/admin/loginPage,/admin/login
#\u624B\u673Aapp\u6392\u9664\u62E6\u622A\u5217\u8868\uFF0C\u5FC5\u987B \u201C/api/\u201D  \u5F00\u5934
project.appInterceptPath=/api/**
#\u624B\u673Aapp\u6392\u9664\u62E6\u622A\u5217\u8868\uFF0C\u5FC5\u987B \u201C/api/\u201D  \u5F00\u5934
project.appInterceptExcludePaths=/api/html/**
#\u662F\u5426\u5F00\u542Fapi\u63A5\u53E3\u9A8C\u8BC1 true \uFF1A\u5F00\u542F\uFF0Cfalse\uFF1A\u4E0D\u542F\u7528
project.appCheckFlag=true
#\u624B\u673Aapp\u63A5\u53E3\u9A8C\u8BC1\u65F6\u7684\u52A0\u5BC6\u4E32
project.appKey=123456879xintong
#\u4E0A\u4F20\u6587\u4EF6\u5B58\u50A8\u8DEF\u5F84
project.uploadFileAbsolutePath=/data/filing/uploadFile/
#\u4E0A\u4F20\u6587\u4EF6\u76F8\u5BF9\u8DEF\u5F84
project.uploadFileRelativePath=/uploadFile/
project.uploadFileTypes=jpg,png,gif,pdf,doc,docx,zip,rar,xls,xlsx
#\u9A8C\u8BC1\u7801\u9A8C\u8BC1\u662F\u5426\u5F00\u542F
project.captchaEnabled=true
#\u9A8C\u8BC1\u7801\u7C7B\u578B
project.captchaType=math
#web\u7AEF\u63A5\u53E3\u9A8C\u8BC1
project.webCheckFlag=false
#web\u7AEF\u63A5\u53E3\u9A8C\u8BC1\u65F6\u7684\u52A0\u5BC6\u4E32
project.webKey=123456879xintong
ck.http =  http://*************:8082
spring.mvc.favicon.enabled=false
# ================================================
#                   \u5F00\u53D1\u73AF\u5883\u914D\u7F6E
# ================================================
server.port=6052
# \u9879\u76EEcontextPath
server.servlet.context-path=/
# tomcat\u7684URI\u7F16\u7801
server.tomcat.uri-encoding=utf-8
# tomcat\u6700\u5927\u7EBF\u7A0B\u6570\uFF0C\u9ED8\u8BA4\u4E3A200
server.tomcat.max-threads=800
# Tomcat\u542F\u52A8\u521D\u59CB\u5316\u7684\u7EBF\u7A0B\u6570\uFF0C\u9ED8\u8BA4\u503C25
server.tomcat.min-spare-threads=30
#server.error.path=/sys/error
# ================================================
#                   \u65E5\u5FD7\u914D\u7F6E
# ================================================
logging.level.com.tzstcl=debug
logging.level.org.springframework=debug
logging.level.org.spring.springboot.dao=debug
logging.file=logs/buildingRenovation.log
#\u5BC6\u7801\u9519\u8BEF{maxRetryCount}\u6B21\u9501\u5B9A10\u5206\u949F
user.password.maxRetryCount=5
# ================================================
#                   \u6570\u636E\u5E93\u8BBF\u95EE\u914D\u7F6E
# ================================================
# \u4E3B\u6570\u636E\u6E90\uFF0C\u9ED8\u8BA4\u7684
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# \u4E3B\u5E93\u6570\u636E\u6E90
spring.datasource.druid.master.url=jdbc:mysql://***********:3306/filingsystem?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8
spring.datasource.druid.master.username=qzj
spring.datasource.druid.master.password=HNzjt@qzj1437
# \u4ECE\u5E93\u6570\u636E\u6E90
# \u4ECE\u6570\u636E\u6E90\u5F00\u5173/\u9ED8\u8BA4\u5173\u95ED\uFF0C\u5357\u5A01\u5171\u4EAB\u6570\u636E\u5E93\u8FDE\u63A5
spring.datasource.druid.slave.enabled=false
spring.datasource.druid.slave.url=jdbc:mysql://***********:3306/filingsystem?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8
spring.datasource.druid.slave.username=qzj
spring.datasource.druid.slave.password=HNzjt@qzj1437

# \u521D\u59CB\u8FDE\u63A5\u6570
spring.datasource.druid.initial-size=10
# \u6700\u5927\u8FDE\u63A5\u6C60\u6570\u91CF
spring.datasource.druid.max-active=100
# \u6700\u5C0F\u8FDE\u63A5\u6C60\u6570\u91CF
spring.datasource.druid.min-idle=10
# \u914D\u7F6E\u83B7\u53D6\u8FDE\u63A5\u7B49\u5F85\u8D85\u65F6\u7684\u65F6\u95F4
spring.datasource.druid.max-wait=60000
# \u6253\u5F00PSCache\uFF0C\u5E76\u4E14\u6307\u5B9A\u6BCF\u4E2A\u8FDE\u63A5\u4E0APSCache\u7684\u5927\u5C0F
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20
# \u914D\u7F6E\u95F4\u9694\u591A\u4E45\u624D\u8FDB\u884C\u4E00\u6B21\u68C0\u6D4B\uFF0C\u68C0\u6D4B\u9700\u8981\u5173\u95ED\u7684\u7A7A\u95F2\u8FDE\u63A5\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
# \u914D\u7F6E\u4E00\u4E2A\u8FDE\u63A5\u5728\u6C60\u4E2D\u6700\u5C0F\u751F\u5B58\u7684\u65F6\u95F4\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.validation-query=SELECT 1 FROM DUAL
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.url-pattern=/monitor/druid/*
spring.datasource.druid.filter.stat.log-slow-sql=true
spring.datasource.druid.filter.stat.slow-sql-millis=1000
spring.datasource.druid.filter.stat.merge-sql=false
spring.datasource.druid.filter.wall.config.multi-statement-allow=true
# ================================================
#                   Spring\u914D\u7F6E
# ================================================
#\u6587\u4EF6\u4E0A\u4F20
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=1024000000
spring.servlet.multipart.max-request-size=1024000000
spring.servlet.multipart.location=/runtime/server/profile-qzj
spring.main.allow-bean-definition-overriding=true
#\u70ED\u90E8\u7F72
spring.devtools.restart.enabled=true
# ================================================
#                   FreeMarker\u914D\u7F6E
# ================================================
# \u662F\u5426\u5F00\u542F\u6A21\u677F\u7F13\u5B58
spring.freemarker.cache=true
# \u7F16\u7801\u683C\u5F0F
spring.freemarker.charset=UTF-8
# \u6A21\u677F\u7684\u5A92\u4F53\u7C7B\u578B\u8BBE\u7F6E
spring.freemarker.content-type=text/html
# \u524D\u7F00\u8BBE\u7F6E \u9ED8\u8BA4\u4E3A ""
spring.freemarker.prefix=
# \u540E\u7F00\u8BBE\u7F6E \u9ED8\u8BA4\u4E3A .ftl
spring.freemarker.suffix=.ftl
# ================================================
#                   Thymeleaf\u914D\u7F6E
# ================================================
# \u662F\u5426\u542F\u7528thymeleaf\u6A21\u677F\u89E3\u6790
spring.thymeleaf.enabled=true
# \u662F\u5426\u5F00\u542F\u6A21\u677F\u7F13\u5B58\uFF08\u5EFA\u8BAE\uFF1A\u5F00\u53D1\u73AF\u5883\u4E0B\u8BBE\u7F6E\u4E3Afalse\uFF0C\u751F\u4EA7\u73AF\u5883\u8BBE\u7F6E\u4E3Atrue\uFF09
spring.thymeleaf.cache=false
# Check that the templates location exists.
spring.thymeleaf.check-template-location=true
# \u6A21\u677F\u7684\u5A92\u4F53\u7C7B\u578B\u8BBE\u7F6E\uFF0C\u9ED8\u8BA4\u4E3Atext/html
spring.thymeleaf.servlet.content-type=text/html
# \u6A21\u677F\u7684\u7F16\u7801\u8BBE\u7F6E\uFF0C\u9ED8\u8BA4UTF-8
spring.thymeleaf.encoding=UTF-8
# \u8BBE\u7F6E\u53EF\u4EE5\u88AB\u89E3\u6790\u7684\u89C6\u56FE\uFF0C\u4EE5\u9017\u53F7,\u5206\u9694
#spring.thymeleaf.view-names=
# \u6392\u9664\u4E0D\u9700\u8981\u88AB\u89E3\u6790\u89C6\u56FE\uFF0C\u4EE5\u9017\u53F7,\u5206\u9694
#spring.thymeleaf.excluded-view-names=
# \u6A21\u677F\u6A21\u5F0F\u8BBE\u7F6E\uFF0C\u9ED8\u8BA4\u4E3AHTML5
spring.thymeleaf.mode=HTML
# \u524D\u7F00\u8BBE\u7F6E\uFF0CSpringBoot\u9ED8\u8BA4\u6A21\u677F\u653E\u7F6E\u5728classpath:/template/\u76EE\u5F55\u4E0B
spring.thymeleaf.prefix=classpath:/templates/
# \u540E\u7F00\u8BBE\u7F6E\uFF0C\u9ED8\u8BA4\u4E3A.html
spring.thymeleaf.suffix=.html
# \u6A21\u677F\u5728\u6A21\u677F\u94FE\u4E2D\u88AB\u89E3\u6790\u7684\u987A\u5E8F
#spring.thymeleaf.template-resolver-order=
# ================================================
#                   mybatis\u914D\u7F6E
# ================================================
#mybatis.type-aliases-package=com.tzstcl
mybatis.mapper-locations=classpath*:mybatis/**/*Mapper.xml
# \u52A0\u8F7D\u5168\u5C40\u7684\u914D\u7F6E\u6587\u4EF6
mybatis.configLocation=classpath:mybatis/mybatis-config.xml
# PageHelper
pagehelper.helper-dialect=mysql
pagehelper.reasonable=false
pagehelper.support-methods-arguments=false
pagehelper.params=count=countSql
# \u65F6\u95F4\u6233\u5168\u5C40\u914D\u7F6E  \u4E0D\u8D77\u4F5C\u7528\u539F\u56E0\u4E3A \u9700\u8981 implements WebMvcConfigurer
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jackson.default-property-inclusion=non_null
# ================================================
#                   quartz\u914D\u7F6E
# ================================================
#\u9ED8\u8BA4\u662F\u8FD0\u884C\u5728MEMORY\u4E2D
spring.quartz.job-store-type=jdbc
#\u6BCF\u6B21\u542F\u52A8\u90FD\u521D\u59CB\u5316\u6570\u636E\u5E93\uFF0C3\u79CD\u6A21\u5F0F\uFF08ALWAYS\u3001EMBEDDED\u3001NEVER\uFF09
#spring.quartz.jdbc.initialize-schema=NEVER
#quartz\u914D\u7F6E\u6587\u4EF6
spring.quartz.properties.org.quartz.scheduler.instanceName=clusteredScheduler
spring.quartz.properties.org.quartz.scheduler.instanceId=AUTO
spring.quartz.properties.org.quartz.jobStore.tablePrefix=qrtz_
spring.quartz.properties.org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.StdJDBCDelegate
spring.quartz.properties.org.quartz.jobStore.class=org.quartz.impl.jdbcjobstore.JobStoreTX
spring.quartz.properties.org.quartz.jobStore.isClustered=true
spring.quartz.properties.org.quartz.jobStore.clusterCheckinInterval=10000
spring.quartz.properties.org.quartz.jobStore.useProperties=false
spring.quartz.properties.org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
spring.quartz.properties.org.quartz.threadPool.threadCount=10
spring.quartz.properties.org.quartz.threadPool.threadPriority=5
spring.quartz.properties.org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread=true
# ================================================
#                   redis\u57FA\u7840\u914D\u7F6E
# ================================================
spring.cache.type=redis
spring.redis.host=***********
spring.redis.port=6379
spring.redis.database=8
#spring.redis.password=hebi@2019abc
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-wait=1ms
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
spring.redis.lettuce.shutdown-timeout=100ms
# ================================================
#                   Shiro\u914D\u7F6E
# ================================================
shiro.session.timeout=30
# ================================================
#                   \u9632\u6B62XSS\u653B\u51FB
# ================================================
xss.enable=true
# \u6392\u9664\u94FE\u63A5\uFF08\u591A\u4E2A\u7528\u9017\u53F7\u5206\u9694\uFF09
xss.excludes=/system/notice/*
# \u5339\u914D\u94FE\u63A5
xss.urlPatterns=/system/*,/monitor/*,/tool/*
# ================================================
#                   httpclient\u914D\u7F6E
# ================================================
httpclient.connectTimeout=30000
httpclient.requestTimeout=30000
httpclient.socketTimeout=120000
httpclient.defaultMaxPerRoute=100
httpclient.maxTotalConnections=300
httpclient.validateAfterInactivity=30000

# ================================================
#                       \u963F\u91CC\u5927\u9C7C\u77ED\u4FE1\u53D1\u9001\u914D\u7F6E
# ================================================
aliyun.sms.accessKeyId=LTAIcG3aE5wM9qIf
aliyun.sms.accessKeySecret=b5PrEWO0FkEW4CNnljJvFJVhw3xNEk
#\u77ED\u4FE1\u7B7E\u540D\uFF0C-\u53EF\u5728\u77ED\u4FE1\u63A7\u5236\u53F0\u4E2D\u627E\u5230
aliyun.sms.signName=\u5929\u7B51\u79D1\u6280\u80A1\u4EFD\u6709\u9650\u516C\u53F8
aliyun.sms.captcha.templateCode=SMS_160576731
aliyun.sms.workflow.templateCode=SMS_160576687

# ================================================
#            ?????
# ================================================
# appkey
nation.key=0183cc93-0dbb-4183-97b7-1c56504ee2e4
#appSecret
nation.Secret=6b71191d-35a9-4405-b7b2-0c020afc74b6
#???token??
#nation.base.url=http://219.142.101.192
nation.base.url=http://10.0.20.140:19000

nationl.token.url=${nation.base.url}/epoint-soa-web/rest/oauth2/token
#????
nation.check.url=${nation.base.url}/epoint-gateway/zjbzasdzzz/rest/dzzzqzjxsyjdzrest/qzjxsydjz_check
#????
nation.encode.url=${nation.base.url}/epoint-gateway/zjbzasdzzz/rest/dzzzqzjxsyjdzrest/qzjxsydjz_fm
#????
nation.update.url=${nation.base.url}/epoint-gateway/zjbzasdzzz/rest/dzzzqzjxsyjdzrest/qzjxsydjz_update
#????
nation.collect.url=${nation.base.url}/epoint-gateway/zjbzasdzzz/rest/dzzzqzjxsyjdzrest/qzjxsydjz_gj
#????
nation.person.url=${nation.base.url}/epoint-gateway/zjbzasdzzz/rest/dzzzqzjxsyjdzrest/qzjxsydjz_ryxx
#text
## appkey
#nation.key=0d92a7fa-d2ca-43e7-94ed-150c765b263c
##appSecret
#nation.Secret=efdb9dc3-c130-4133-b63c-f4e9dbb08bae
##??
# ================================================
#            ???
# ================================================

# appkey
nation.building.key=b61c8bb7-9664-42b2-b4dd-0a9cf7f8092a
#appSecret
nation.building.Secret=0f63617b-e193-4502-a6f6-10c9dc0dfcc5
#??
#????????????
nation.building.check.url=${nation.base.url}/epoint-gateway/zjbqzjxbadzzz/rest/dzzzdatarest/getqzjxbadetail
#????????????
nation.building.preBussinessVer.url=${nation.base.url}/epoint-gateway/zjbqzjxbadzzz/rest/dzzzdatarest/ywblcheck
#??????????
nation.building.bussinessDataVer.url=${nation.base.url}/epoint-gateway/zjbqzjxbadzzz/rest/dzzzdatarest/ywsjcheck
#??????????
nation.building.assignCode.url=${nation.base.url}/epoint-gateway/zjbqzjxbadzzz/rest/dzzzdatarest/dzzzfm
# ??????
nation.building.collection.url=${nation.base.url}/epoint-gateway/zjbqzjxbadzzz/rest/dzzzdatarest/dzzzwjgj
#??????????
nation.building.changeInfo.url=${nation.base.url}/epoint-gateway/zjbqzjxbadzzz/rest/dzzzdatarest/infoupdate
#??????????
nation.building.changeStatus.url=${nation.base.url}/epoint-gateway/zjbqzjxbadzzz/rest/dzzzdatarest/statusupdate
#??????????
nation.building.amend.url=${nation.base.url}/epoint-gateway/zjbqzjxbadzzz/rest/dzzzdatarest/ywsjcorrect
#??sso??


#??sso??
national.quality.afety.push.SSOUrl=${nation.base.url}/epoint-soa-web

certFileUrl=http://222.143.32.83:9014
certPreviewUrl=https://hngcjs.hnjs.henan.gov.cn/qzj/preview/onlinePreview?url=
