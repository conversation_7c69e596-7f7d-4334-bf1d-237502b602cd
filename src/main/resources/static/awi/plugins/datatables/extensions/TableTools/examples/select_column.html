<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">

	<title>TableTools example - Row selection - row selector on specific cells</title>
	<link rel="stylesheet" type="text/css" href="../../../media/css/jquery.dataTables.css">
	<link rel="stylesheet" type="text/css" href="../css/dataTables.tableTools.css">
	<link rel="stylesheet" type="text/css" href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/4.0.3/css/font-awesome.css">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
	<style type="text/css" class="init">

	tr td:first-child {
		text-align: center;
	}

	tr td:first-child:before {
		content: "\f096"; /* fa-square-o */
		font-family: FontAwesome;
	}

	tr.selected td:first-child:before {
		content: "\f046"; /* fa-check-square-o */
	}

	</style>
	<script type="text/javascript" language="javascript" src="../../../media/js/jquery.js"></script>
	<script type="text/javascript" language="javascript" src="../../../media/js/jquery.dataTables.js"></script>
	<script type="text/javascript" language="javascript" src="../js/dataTables.tableTools.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>
	<script type="text/javascript" language="javascript" class="init">



$(document).ready(function() {
	$('#example').DataTable( {
		ajax: "../../../examples/ajax/data/objects.txt",
		columns: [
			{ data: null, defaultContent: '', orderable: false },
			{ data: 'name' },
			{ data: 'position' },
			{ data: 'office' },
			{ data: 'extn' },
			{ data: 'start_date' },
			{ data: 'salary' }
		],
		order: [ 1, 'asc' ],
		dom: 'T<"clear">lfrtip',
		tableTools: {
			sRowSelect:   'os',
			sRowSelector: 'td:first-child',
			aButtons:     [ 'select_all', 'select_none' ]
		}
	} );
} );


	</script>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>TableTools example <span>Row selection - row selector on specific cells</span></h1>

			<div class="info">
				<p>By default, TableTools' row selector option will register a row selection click on any part of the row. Although this is often desirable, you might wish at
				times to limit the row selection to just a single column, or other elements in the row. This might be useful, for example, with <a href=
				"//editor.datatables.net">Editor's</a> inline editing, so you don't select the row on click of a cell that is to be edited.</p>

				<p>The <code>sRowSelector</code> method provides this ability, allowing a custom jQuery selector to be passed in. TableTools will use the parent row of any element
				that is selected by the end user.</p>

				<p>In this case, the row selector is attached to the cells in the first column of the table, and <a href="http://fortawesome.github.io/Font-Awesome">Font
				Awesome</a> is used to display a checkbox indicating the selection state of the row, in addition to the row background colouring.</p>
			</div>

			<table id="example" class="display" cellspacing="0" width="100%">
				<thead>
					<tr>
						<th>

						<th>Name</th>
						<th>Position</th>
						<th>Office</th>
						<th>Age</th>
						<th>Start date</th>
						<th>Salary</th>
					</tr>
				</thead>
			</table>

			<ul class="tabs">
				<li class="active">Javascript</li>
				<li>HTML</li>
				<li>CSS</li>
				<li>Ajax</li>
				<li>Server-side script</li>
			</ul>

			<div class="tabs">
				<div class="js">
					<p>The Javascript shown below is used to initialise the table shown in this example:</p><code class="multiline language-js">$(document).ready(function() {
	$('#example').DataTable( {
		ajax: &quot;../../../examples/ajax/data/objects.txt&quot;,
		columns: [
			{ data: null, defaultContent: '', orderable: false },
			{ data: 'name' },
			{ data: 'position' },
			{ data: 'office' },
			{ data: 'extn' },
			{ data: 'start_date' },
			{ data: 'salary' }
		],
		order: [ 1, 'asc' ],
		dom: 'T&lt;&quot;clear&quot;&gt;lfrtip',
		tableTools: {
			sRowSelect:   'os',
			sRowSelector: 'td:first-child',
			aButtons:     [ 'select_all', 'select_none' ]
		}
	} );
} );</code>

					<p>In addition to the above code, the following Javascript library files are loaded for use in this example:</p>

					<ul>
						<li><a href="../../../media/js/jquery.js">../../../media/js/jquery.js</a></li>
						<li><a href="../../../media/js/jquery.dataTables.js">../../../media/js/jquery.dataTables.js</a></li>
						<li><a href="../js/dataTables.tableTools.js">../js/dataTables.tableTools.js</a></li>
					</ul>
				</div>

				<div class="table">
					<p>The HTML shown below is the raw HTML table element, before it has been enhanced by DataTables:</p>
				</div>

				<div class="css">
					<div>
						<p>This example uses a little bit of additional CSS beyond what is loaded from the library files (below), in order to correctly display the table. The
						additional CSS used is shown below:</p><code class="multiline language-css">tr td:first-child {
		text-align: center;
	}

	tr td:first-child:before {
		content: &quot;\f096&quot;; /* fa-square-o */
		font-family: FontAwesome;
	}

	tr.selected td:first-child:before {
		content: &quot;\f046&quot;; /* fa-check-square-o */
	}</code>
					</div>

					<p>The following CSS library files are loaded for use in this example to provide the styling of the table:</p>

					<ul>
						<li><a href="../../../media/css/jquery.dataTables.css">../../../media/css/jquery.dataTables.css</a></li>
						<li><a href="../css/dataTables.tableTools.css">../css/dataTables.tableTools.css</a></li>
						<li><a href=
						"//cdnjs.cloudflare.com/ajax/libs/font-awesome/4.0.3/css/font-awesome.css">//cdnjs.cloudflare.com/ajax/libs/font-awesome/4.0.3/css/font-awesome.css</a></li>
					</ul>
				</div>

				<div class="ajax">
					<p>This table loads data by Ajax. The latest data that has been loaded is shown below. This data will update automatically as any additional data is
					loaded.</p>
				</div>

				<div class="php">
					<p>The script used to perform the server-side processing for this table is shown below. Please note that this is just an example script using PHP. Server-side
					processing scripts can be written in any language, using <a href="//datatables.net/manual/server-side">the protocol described in the DataTables
					documentation</a>.</p>
				</div>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<h2>Other examples</h2>

				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Examples</a></h3>
						<ul class="toc active">
							<li><a href="./simple.html">Basic initialisation</a></li>
							<li><a href="./swf_path.html">Setting the SWF path</a></li>
							<li><a href="./new_init.html">Initialisation with `new`</a></li>
							<li><a href="./defaults.html">Defaults</a></li>
							<li><a href="./select_single.html">Row selection - single row select</a></li>
							<li><a href="./select_multi.html">Row selection - multi-row select</a></li>
							<li><a href="./select_os.html">Row selection - operating system style</a></li>
							<li class="active"><a href="./select_column.html">Row selection - row selector on specific cells</a></li>
							<li><a href="./multiple_tables.html">Multiple tables</a></li>
							<li><a href="./multi_instance.html">Multiple toolbars</a></li>
							<li><a href="./collection.html">Button collections</a></li>
							<li><a href="./plug-in.html">Plug-in button types</a></li>
							<li><a href="./button_text.html">Custom button text</a></li>
							<li><a href="./alter_buttons.html">Button arrangement</a></li>
							<li><a href="./ajax.html">Ajax loaded data</a></li>
							<li><a href="./pdf_message.html">PDF message</a></li>
							<li><a href="./bootstrap.html">Bootstrap styling</a></li>
							<li><a href="./jqueryui.html">jQuery UI styling</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and <a href="http://www.datatables.net/plug-ins">plug-ins</a>
					which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>