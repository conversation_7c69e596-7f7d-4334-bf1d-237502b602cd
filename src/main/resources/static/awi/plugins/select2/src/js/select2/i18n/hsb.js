define(function () {
  // Upper Sorbian
  var charsWords = ['znam<PERSON><PERSON><PERSON>', 'znam<PERSON><PERSON><PERSON>', 'znam<PERSON><PERSON><PERSON>','znam<PERSON><PERSON><PERSON><PERSON>'];
  var itemsWords = ['zapisk', 'zapiskaj', 'zapiski','zapiskow'];

  var pluralWord = function pluralWord(numberOfChars, words) {
    if (numberOfChars === 1) {
        return words[0];
    } else if (numberOfChars === 2) {
      return words[1];
    }  else if (numberOfChars > 2 && numberOfChars <= 4) {
      return words[2];
    } else if (numberOfChars >= 5) {
      return words[3];
    }
  };
  
  return {
    errorLoading: function () {
      return 'Wuslědki njedachu so začitać.';
    },
    inputTooLong: function (args) {
      var overChars = args.input.length - args.maximum;

      return 'Prošu zhašej ' + overChars + ' ' + 
        pluralWord(overChars, charsWords);
    },
    inputTooShort: function (args) {
      var remainingChars = args.minimum - args.input.length;
      
      return 'Prošu zapodaj znajm<PERSON> ' + remainingChars + ' ' +
        pluralWord(remainingChars, charsWords);
    },
    loadingMore: function () {
      return 'Dalše wuslědki so začitaja…';
    },
    maximumSelected: function (args) {
      return 'Móžeš jenož ' + args.maximum + ' ' +
        pluralWord(args.maximum, itemsWords) + 'wubrać';
    },
    noResults: function () {
      return 'Žane wuslědki namakane';
    },
    searching: function () {
      return 'Pyta so…';
    }
  };
});
