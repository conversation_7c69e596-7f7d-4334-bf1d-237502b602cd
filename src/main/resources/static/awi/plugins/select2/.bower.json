{"name": "select2", "description": "Select2 is a jQuery based replacement for select boxes. It supports searching, remote data sets, and infinite scrolling of results.", "main": ["dist/js/select2.js", "src/scss/core.scss"], "license": "MIT", "repository": {"type": "git", "url": "**************:select2/select2.git"}, "homepage": "https://github.com/ivaynberg/select2", "version": "4.0.5", "_release": "4.0.5", "_resolution": {"type": "version", "tag": "4.0.5", "commit": "ebf10c93db7d6d7a0d1330119d4c6f32cbd231d7"}, "_source": "https://github.com/ivaynberg/select2.git", "_target": "^4.0.3", "_originalSource": "select2"}