!function () {
    var e = window.document.currentScript.src, e = /(\S*\/)cascader(\.min)?\.js(\?.*)?/i.exec(e);
    e && 2 <= e.length && (!e[3] || -1 !== e[3].indexOf("css=true")) && layui.link(e[1] + "cascader" + (e[2] || "") + ".css")
}(), layui.define(["jquery"], function (e) {
    var r = layui.jquery;

    function h(e) {
        (e = e || window.event).stopPropagation ? e.stopPropagation() : e.cancelBubble = !0
    }

    function l(e, t, i, s) {
        this.data = e, this.cascader = t, this.config = t.config, this.props = t.props, this.level = i, this.parentNode = s, this.icons = t.icons, this._checked = 0, this._loading = !1, this.nodeId = t.data.nodeId++
    }

    function i() {
        return {
            elem: "", value: null, options: [], empty: "暂无数据", placeholder: "请选择", disabled: !1, clearable: !1, showAllLevels: !0, collapseTags: !1, minCollapseTagsNumber: 1, separator: " / ", filterable: !1, filterMethod: function (e, t) {
                return e.path.some(function (e) {
                    return -1 !== e.label.indexOf(t)
                })
            }, debounce: 300, beforeFilter: function (e) {
                return !0
            }, popperClass: "", extendClass: !1, extendStyle: !1, disabledFixed: !1, maxSize: 0, props: {
                strictMode: !1, expandTrigger: "click", multiple: !1, checkStrictly: !1, lazy: !1, lazyLoad: function (e, t) {
                }, value: "value", label: "label", children: "children", disabled: "disabled", leaf: "leaf"
            }
        }
    }

    function t(e) {
        this.config = r.extend(!0, i(), e), this.data = {nodeId: 1, nodes: [], menuData: [], activeNodeId: null, activeNode: null, checkedNodeIds: [], checkedNodes: []}, this.showPanel = !1, this.event = {change: [], open: [], close: [], destroy: []}, this.filtering = !1, this._init(), this.closeEventId = 0, this._maxSizeMode = null
    }

    l.prototype = {
        constructor: l, get topParentNode() {
            return !this.parentNode && this || this.topParentNode
        }, childrenNode: void 0, get loading() {
            return this._loading
        }, set loading(e) {
            var t, i, s = this.$li;
            return s && (t = this.icons.right, i = this.icons.loading, s = s.find("i"), e ? (s.addClass(i), s.removeClass(t)) : (s.addClass(t), s.removeClass(i))), this._loading = e
        }, get label() {
            return this.data[this.props.label]
        }, get value() {
            return this.data[this.props.value]
        }, get disabled() {
            var e = this.props.multiple, t = this.config.maxSize, i = this.cascader.data.checkedNodeIds, s = this.props.disabled, n = this.props.checkStrictly;
            if (e && 0 !== t && i.length >= t && -1 === i.indexOf(this.nodeId)) {
                if (n) return !0;
                if (!this.getAllLeafChildren().map(function (e) {
                    return e.nodeId
                }).some(function (e) {
                    return -1 !== i.indexOf(e)
                })) return !0
            }
            return n ? this.data[s] : this.path.some(function (e) {
                return e.data[s]
            })
        }, get children() {
            return this.data[this.props.children]
        }, set children(e) {
            this.data[this.props.children] = e
        }, get leaf() {
            var e = this.data[this.props.leaf];
            return "boolean" == typeof e ? e : !this.children || this.children.length <= 0
        }, get activeNodeId() {
            return this.cascader.data.activeNodeId
        }, get checkedNodeIds() {
            return this.cascader.data.checkedNodeIds
        }, get path() {
            var e = this.parentNode;
            return e ? e.path.concat([this]) : [this]
        }, get isFiltering() {
            return this.cascader.isFiltering
        }, get $tag() {
            var t = this.cascader, e = this.config.showAllLevels, i = this.config.disabled, s = this.disabled, n = this.config.disabledFixed, e = this.getPathLabel(e), e = t.get$tag(e, !(i || s && n)), a = this;
            return e.find("i").click(function (e) {
                h(e), a.selectedValue(), t.removeTag(a.value, a)
            }), e
        }, getPathLabel: function (e) {
            var t = this.path, i = this.config.separator, e = e ? t.map(function (e) {
                return e.label
            }).join(i) : t[t.length - 1].label;
            return e
        }, init: function () {
            var e = this.props.multiple, t = this.props.checkStrictly, i = this.icons.from, s = this.icons.right, n = "", a = this.label;
            this.leaf || (n = s), this.$li = r('<li role="menuitem" id="cascader-menu" tabindex="-1" class="el-cascader-node" aria-haspopup="true" aria-owns="cascader-menu"><span class="el-cascader-node__label">' + a + '</span><i class="' + i + " " + n + '"></i></li>'), e || t ? !e && t ? this._renderRadioCheckStrictly() : e && !t ? this._renderMultiple() : e && t && this._renderMultipleCheckStrictly() : this._renderRadio()
        }, initSuggestionLi: function () {
            var e = this.getPathLabel(!0);
            this.$suggestionLi = r('<li tabindex="-1" class="el-cascader__suggestion-item"><span>' + e + "</span></li>"), this._renderFiltering()
        }, bind: function (e) {
            this.init(), e.append(this.$li)
        }, bindSuggestion: function (e) {
            this.initSuggestionLi(), e.append(this.$suggestionLi)
        }, _renderFiltering: function () {
            var t = this.$suggestionLi, i = this.nodeId, e = this.icons.from, s = this.icons.ok, n = this, a = this.cascader, l = this.props.multiple, o = '<i class="' + e + " " + s + ' el-icon-check"></i>';
            t.click(function (e) {
                h(e), n.selectedValue(), l ? -1 === n.checkedNodeIds.indexOf(i) ? (t.removeClass("is-checked"), t.find(".el-icon-check").remove()) : (t.addClass("is-checked"), t.append(o)) : a.close()
            }), (l && -1 !== n.checkedNodeIds.indexOf(i) || !l && n.activeNodeId === i) && (t.addClass("is-checked"), t.append(o))
        }, _renderRadio: function () {
            var e = this.$li, t = this.nodeId, i = this.icons.from, s = this.icons.ok, n = this.level, a = this.leaf, l = this, o = this.cascader, c = this.cascader.data.activeNode, d = this.parentNode;
            l.activeNodeId && c.path.some(function (e) {
                return e.nodeId === t
            }) && (l.activeNodeId === t && e.prepend('<i class="' + i + " " + s + ' el-cascader-node__prefix"></i>'), e.addClass("is-active"), e.addClass("in-checked-path")), this.disabled ? e.addClass("is-disabled") : (e.addClass("is-selectable"), d && (d.$li.siblings().removeClass("in-active-path"), d.$li.addClass("in-active-path")), this._liClick(function (e) {
                h(e);
                var t = l.childrenNode;
                a && "click" === e.type && (l.selectedValue(), o.close()), o._appendMenu(t, n + 1, l)
            }))
        }, _renderRadioCheckStrictly: function () {
            var e = this.$li, t = this.nodeId, i = this.level, s = this.leaf, n = this, a = this.cascader, l = a.data.activeNode, o = this.parentNode,
                c = (e.addClass("is-selectable"), r('<label role="radio" tabindex="0" class="el-radio"><span class="el-radio__input"><span class="el-radio__inner"></span><input type="radio" aria-hidden="true" tabindex="-1" class="el-radio__original" value="' + t + '"></span><span class="el-radio__label"><span></span></span></label>'));
            if (this.$radio = c, e.prepend(c), o && (o.$li.siblings().removeClass("in-active-path"), o.$li.addClass("in-active-path")), this._liClick(function (e) {
                h(e);
                var t = n.childrenNode;
                !n.disabled && s && "click" === e.type && n.selectedValue(), a._appendMenu(t, i + 1, n)
            }), n.activeNodeId && l.path.some(function (e) {
                return e.nodeId === t
            }) && (n.activeNodeId === t && c.find(".el-radio__input").addClass("is-checked"), e.addClass("is-active"), e.addClass("in-checked-path")), this.disabled) return c.addClass("is-disabled"), void c.find(".el-radio__input").addClass("is-disabled");
            c.click(function (e) {
                e.preventDefault(), s || n.selectedValue()
            })
        }, _renderMultiple: function () {
            var e = this.$li, i = this.level, s = this.leaf, n = this, a = this.cascader, t = this._checked, l = this.parentNode, o = (e.addClass("el-cascader-node"), r('<label class="el-checkbox"><span class="el-checkbox__input"><span class="el-checkbox__inner"></span><input type="checkbox" aria-hidden="false" class="el-checkbox__original" value=""></span></label>'));
            if (this.$checked = o, e.prepend(o), 1 === t ? this.$checked.find(".el-checkbox__input").addClass("is-checked") : 2 === t && this.$checked.find(".el-checkbox__input").addClass("is-indeterminate"), l && (l.$li.siblings().removeClass("in-active-path"), l.$li.addClass("in-active-path")), this._liClick(function (e) {
                h(e);
                var t = n.childrenNode;
                !n.disabled && s && "click" === e.type && n.selectedValue(), a._appendMenu(t, i + 1, n)
            }), this.disabled) return e.addClass("is-disabled"), o.addClass("is-disabled"), void o.find(".el-checkbox__input").addClass("is-disabled");
            o.click(function (e) {
                e.preventDefault(), s || (e = n.childrenNode, n.selectedValue(), a._appendMenu(e, i + 1, n))
            })
        }, _renderMultipleCheckStrictly: function () {
            var e = this.$li, i = this.level, s = this.leaf, n = this, a = this.cascader, t = a.data.checkedNodeIds, l = a.data.checkedNodes, o = this.nodeId, c = this.parentNode,
                d = (e.addClass("el-cascader-node is-selectable"), r('<label class="el-checkbox"><span class="el-checkbox__input"><span class="el-checkbox__inner"></span><input type="checkbox" aria-hidden="false" class="el-checkbox__original" value=""></span></label>'));
            if (this.$checked = d, e.prepend(d), l.some(function (e) {
                return e.path.some(function (e) {
                    return e.nodeId === o
                })
            }) && (e.addClass("in-checked-path"), -1 !== t.indexOf(o) && this.$checked.find(".el-checkbox__input").addClass("is-checked")), c && (c.$li.siblings().removeClass("in-active-path"), c.$li.addClass("in-active-path")), this._liClick(function (e) {
                h(e);
                var t = n.childrenNode;
                !n.disabled && s && "click" === e.type && n.selectedValue(), a._appendMenu(t, i + 1, n)
            }), this.disabled) return d.addClass("is-disabled"), void d.find(".el-checkbox__input").addClass("is-disabled");
            d.click(function (e) {
                e.preventDefault(), s || (n.selectedValue(), e = n.childrenNode, a._appendMenu(e, i + 1, n))
            })
        }, transferParent: function (e, t, i) {
            if ((this !== (i = i || this) || t) && !1 === (e && e(this))) return;
            this.parentNode && this.parentNode.transferParent(e, t, i)
        }, transferChildren: function (e, t, i) {
            if ((this !== (i = i || this) || t) && !1 === (e && e(this))) return;
            var s = this.getChildren();
            if (s && 0 < s.length) for (var n in s) s[n].transferChildren(e, t, i)
        }, selectedValue: function () {
            var t, e, i, s = this.nodeId, n = this.cascader, a = this.props.multiple, l = this.props.checkStrictly, o = this.leaf;
            a || !o && !l ? a && (t = n.data.checkedNodeIds, o = n.data.checkedNodes, a = this.config.disabledFixed, l ? -1 === (l = t.indexOf(s)) ? i = o.concat([this]) : (i = o.concat()).splice(l, 1) : (e = this.getAllLeafChildren(), i = 1 === (1 !== this._checked && a ? this._getMultipleChecked(e) : this._checked) ? o.filter(function (t) {
                return !e.some(function (e) {
                    return t.nodeId === e.nodeId
                })
            }) : (l = e.filter(function (e) {
                return -1 === t.indexOf(e.nodeId)
            }), o.concat(l))), a = i.map(function (e) {
                return e.nodeId
            }), n._setCheckedValue(a, i)) : n._setActiveValue(s, this)
        }, _liLoad: function (t, i) {
            var e = this.leaf, s = this.props.lazy, n = this.props.lazyLoad, a = this.children, l = this, o = this.cascader, c = this.level, d = this.props.multiple, r = this.props.checkStrictly;
            e || a && 0 !== a.length || !s ? i && i(t) : l.loading || (l.loading = !0, n(l, function (e) {
                l.loading = !1, l.setChildren(o.initNodes(e, c + 1, l)), l.children = e, i && i(t), d && !r && l.transferParent(function (e) {
                    e.syncStyle()
                }, !0)
            }))
        }, _liClick: function (t) {
            var e = this.leaf, i = this.$li, s = this;

            function n(e) {
                s._liLoad(e, t)
            }

            "click" !== this.props.expandTrigger && !e || i.click(n), "hover" === this.props.expandTrigger && i.mouseenter(n)
        }, setChildren: function (e) {
            this.childrenNode = e
        }, getChildren: function () {
            return this.childrenNode
        }, syncStyle: function () {
            var e = this.props.multiple, t = this.props.checkStrictly;
            e ? t ? this._sync.syncMultipleCheckStrictly(this) : this._sync.syncMultiple(this) : t ? this._sync.syncRadioCheckStrictly(this) : this._sync.syncRadio(this)
        }, _sync: {
            syncRadio: function (e) {
                var t = e.$li, i = e.icons.from, s = e.icons.ok, n = e.props.multiple, a = e.props.checkStrictly, l = e.nodeId;
                !t || n || a || (n = e.cascader.data.activeNode, e.activeNodeId === l ? 0 === t.find("." + s).length && t.prepend('<i class="' + i + " " + s + ' el-cascader-node__prefix"></i>') : t.find("." + s).remove(), n && n.path.some(function (e) {
                    return e.nodeId === l
                }) ? (t.addClass("is-active"), t.addClass("in-checked-path")) : (t.removeClass("is-active"), t.removeClass("in-checked-path")))
            }, syncRadioCheckStrictly: function (e) {
                var t, i = e.$li, s = e.props.checkStrictly, n = e.props.multiple;
                i && !n && s && (n = e.$radio, s = e.cascader.data.activeNode, t = e.nodeId, e.activeNodeId === t ? n.find(".el-radio__input").addClass("is-checked") : n.find(".el-radio__input").removeClass("is-checked"), s && s.path.some(function (e) {
                    return e.nodeId === t
                }) ? (i.addClass("is-active"), i.addClass("in-checked-path")) : (i.removeClass("is-active"), i.removeClass("in-checked-path")))
            }, syncMultiple: function (e) {
                var t = e.$li, i = e.props.checkStrictly, s = e.props.multiple, n = e.config.disabledFixed;
                s && !i && (s = e.getAllLeafChildren(n), i = e._getMultipleChecked(s), e._checked = i, t && (n = e.$checked.find(".el-checkbox__input"), 0 === i ? (n.removeClass("is-checked"), n.removeClass("is-indeterminate")) : 1 === i ? (n.removeClass("is-indeterminate"), n.addClass("is-checked")) : 2 === i && (n.removeClass("is-checked"), n.addClass("is-indeterminate"))))
            }, syncMultipleCheckStrictly: function (e) {
                var t, i = e.$li, s = e.props.checkStrictly, n = e.props.multiple;
                i && n && s && (n = e.cascader.data.checkedNodes, s = e.checkedNodeIds, t = e.nodeId, n = n.some(function (e) {
                    return e.path.some(function (e) {
                        return e.nodeId === t
                    })
                }), e = e.$checked.find(".el-checkbox__input"), s.some(function (e) {
                    return e === t
                }) ? e.addClass("is-checked") : e.removeClass("is-checked"), n ? i.addClass("in-checked-path") : i.removeClass("in-checked-path"))
            }
        }, getAllLeafChildren: function (t) {
            var i;
            return this.leaf ? [this] : (i = [], this.transferChildren(function (e) {
                if (e.disabled && !t) return !1;
                e.leaf && i.push(e)
            }), i)
        }, expandPanel: function () {
            var e = this.path, s = this.cascader;
            e.forEach(function (e, t, i) {
                t !== i.length - 1 && (t = e.childrenNode, s._appendMenu(t, e.level + 1, e.parentNode))
            })
        }, _getMultipleChecked: function (e) {
            for (var t = this.cascader.data.checkedNodeIds, i = e.map(function (e) {
                return e.nodeId
            }), s = 0, n = 0; n < i.length; n++) {
                var a = i[n];
                if (2 === s) break;
                s = -1 !== t.indexOf(a) ? 0 < n && 1 !== s ? 2 : 1 : 1 === s ? 2 : 0
            }
            return s
        }
    }, t.prototype = {
        constructor: t, get props() {
            return this.config.props
        }, get isFiltering() {
            return this.filtering
        }, set isFiltering(e) {
            this.filtering !== e && (this.filtering = !!e, e = this.$panel, this.filtering ? (e.find(".el-cascader-panel").hide(), e.find(".el-cascader__suggestion-panel").show()) : (e.find(".el-cascader-panel").show(), e.find(".el-cascader__suggestion-panel").hide(), this.$tagsInput && this.$tagsInput.val("")))
        }, set maxSizeMode(e) {
            this._maxSizeMode !== e && (this._maxSizeMode = e, this.refreshMenu())
        }, icons: {from: "layui-icon", down: "layui-icon-down", close: "layui-icon-close", right: "layui-icon-right", ok: "layui-icon-ok", loading: "layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop"}, _init: function () {
            this._checkConfig(this.config), this._initInput(), this._initPanel(), this.setOptions(this.config.options);

            function e() {
                i._resetXY()
            }

            function t() {
                i._resetXY()
            }

            var i = this, s = r(window);
            s.scroll(e), i.event.destroy.push(function () {
                s.unbind("scroll", e)
            });
            s.resize(t), i.event.destroy.push(function () {
                s.unbind("resize", t)
            }), this.$div.click(function (e) {
                i.config.disabled || (i.showPanel ? i.close() : i.open())
            })
        }, _checkConfig: function (e) {
            var t = e.elem;
            if (!t || 0 === r(t).length) throw new Error("缺少elem节点选择器");
            t = e.maxSize;
            if ("number" != typeof t || t < 0) throw new Error("maxSize应是一个大于等于0的有效的number值");
            if (!Array.isArray(e.options)) throw new Error("options不是一个有效的数组")
        }, _initRoot: function () {
            var e = this.props.lazy, t = this.props.lazyLoad, i = this, s = this.data.nodes;
            0 < s.length || !e ? this._appendMenu(s, 0) : e && (this._appendMenu(s, 0), t({root: !0, level: 0}, function (e) {
                i.data.nodes = i.initNodes(e, 0, null), i._appendMenu(i.data.nodes, 0)
            }))
        }, setConfig: function (e) {
            var t = this;
            t._checkConfig(e), t.clearCheckedNodes(!0), e.props && !e.props.multiple ? t.$tags && t.$tags.hide() : t.$tags && t.$tags.show(), t.config = r.extend(!0, i(), e), t.setOptions(e.options)
        }, setOptions: function (e) {
            this.config.options = e, this.data.nodes = this.initNodes(e, 0, null), this._initRoot(), this.setValue(this.config.value)
        }, _resetXY: function () {
            var e, t, i, s, n, a, l = this.$div, o = l.offset(), c = this.$panel;
            c && (e = window.innerHeight, n = window.innerWidth, t = c.height(), a = c.width(), i = l.height(), l = l[0].getBoundingClientRect(), s = c.find(".popper__arrow"), n = Math.min(n - l.x - a - 5, 0), e - (l.top + i) < t && l.top > t + 20 ? (c.attr("x-placement", "top-start"), c.css({
                top: o.top - 20 - t + "px",
                left: o.left + n + "px"
            })) : (c.attr("x-placement", "bottom-start"), a = Math.max(t - (e - l.y - i - 15), 0), c.css({top: o.top + i - a + "px", left: o.left + n + "px"})), s.css("left", 35 - n + "px"))
        }, get $menus() {
            return this.$panel && this.$panel.find(".el-cascader-panel .el-cascader-menu")
        }, _initInput: function () {
            var e = r(this.config.elem), t = this, i = (null === this.config.value && e.attr("value") && (this.config.value = e.attr("value")), this.config.placeholder), s = this.icons.from, n = this.icons.down, a = this.props.multiple, l = this.config.extendClass, o = this.config.extendStyle;
            this.$div = r('<div class="el-cascader"></div>'), o && (o = e.attr("style")) && this.$div.attr("style", o), l && (o = e.attr("class")) && o.split(" ").forEach(function (e) {
                t.$div.addClass(e)
            }), this.$input = r('<div class="el-input el-input--suffix"><input type="text" readonly="readonly" autocomplete="off" placeholder="' + i + '" class="el-input__inner"><span class="el-input__suffix"><span class="el-input__suffix-inner"><i class="el-icon-arrow-down ' + s + " " + n + '" style="font-size: 12px"></i></span></span></div>'), this.$div.append(this.$input), this.$inputRow = this.$input.find(".el-input__inner"), this.$tags = r('<div class="el-cascader__tags">\x3c!----\x3e</div>'), this.$div.append(this.$tags), a || this.$tags.hide(), this._initHideElement(e).after(this.$div), this.$icon = this.$input.find("i"), this._initFilterableInputEvent(), this.disabled(this.config.disabled)
        }, _initHideElement: function (e) {
            if ("input" === e.prop("tagName").toLowerCase()) return e.hide(), e.attr("type", "hidden"), this.$ec = e;
            var t, i = e[0].attributes, s = r("<input />");
            for (t in Object.keys(i)) {
                var n = i[t];
                s.attr(n.name, n.value)
            }
            return s.hide(), s.attr("type", "hidden"), this.$ec = s, e.before(s), e.remove(), s
        }, _initFilterableInputEvent: function () {
            var s, i, n, a, l, o, e, t;

            function c(e) {
                var t = this;
                s && clearTimeout(s), s = setTimeout(function () {
                    s = null;
                    var e, i = r(t).val();
                    i ? (o.open(), "function" == typeof n && n(i) && (o.isFiltering = !0, e = o.getNodes().filter(function (e) {
                        var t = l ? e.disabled : e.path.some(function (e) {
                            return e.disabled
                        });
                        return !(!e.leaf && !l || t || "function" != typeof a || !a(e, i))
                    }), o._setSuggestionMenu(e))) : o.isFiltering = !1
                }, i)
            }

            this.config.filterable && (e = this.props.multiple, i = this.config.debounce, t = this.config.placeholder, n = this.config.beforeFilter, a = this.config.filterMethod, l = this.props.checkStrictly, o = this, e ? (this.$tagsInput = r('<input type="text" autocomplete="off" placeholder="' + t + '" class="el-cascader__search-input">'), e = this.$tagsInput, this.$tags.append(e), e.on("keydown", c), e.click(function (e) {
                o.isFiltering && h(e)
            })) : ((t = this.$inputRow).removeAttr("readonly"), t.on("keydown", c), t.click(function (e) {
                o.isFiltering && h(e)
            })))
        }, _initPanel: function () {
            var e = this.$panel, t = this.config.popperClass || "";
            e || (this.$panel = r('<div class="el-popper el-cascader__dropdown ' + t + '" style="position: absolute; z-index: 109891015;display: none;" x-placement="bottom-start"><div class="el-cascader-panel"></div><div class="popper__arrow" style="left: 35px;"></div></div>'), (e = this.$panel).appendTo("body"), e.click(function (e) {
                h(e)
            }), this._initSuggestionPanel())
        }, _appendMenu: function (e, t, i, s) {
            var n, a;
            this._removeMenu(t), i && i.leaf || (n = this.data.menuData, a = r('<div class="el-scrollbar el-cascader-menu" role="menu" id="cascader-menu"><div class="el-cascader-menu__wrap el-scrollbar__wrap" style="margin-bottom: -17px; margin-right: -17px;"><ul class="el-scrollbar__view el-cascader-menu__list"></ul></div></div>'), this.$panel.find(".el-cascader-panel").append(a), this._appendLi(a, e), e = {
                nodes: e,
                level: t,
                parentNode: i,
                scrollbar: {top: 0, left: 0}
            }, s && (e.scrollbar = s.scrollbar), this._initScrollbar(a, e), this._resetXY(), n.push(e))
        }, _removeMenu: function (e) {
            var t = e - 1, t = ((-1 != t ? this.$panel.find(".el-cascader-panel .el-cascader-menu:gt(" + t + ")") : this.$panel.find(".el-cascader-panel .el-cascader-menu")).remove(), this.data.menuData);
            t.length > e && t.splice(e, t.length - e)
        }, _appendLi: function (e, t) {
            var i = e.find(".el-cascader-menu__list");
            if (!t || 0 === t.length) return e = this.config.empty, void i.append('<div class="el-cascader-menu__empty-text">' + e + "</div>");
            r.each(t, function (e, t) {
                t.bind(i)
            })
        }, refreshMenu: function () {
            var e = this.data.menuData.concat([]), t = this;
            e.forEach(function (e) {
                t._appendMenu(e.nodes, e.level, e.parentNode, e)
            })
        }, _initSuggestionPanel: function () {
            var e;
            !this.config.filterable || (e = this.$suggestionPanel) || (this.$suggestionPanel = r('<div class="el-cascader__suggestion-panel el-scrollbar" style="display: none;"><div class="el-scrollbar__wrap" style="margin-bottom: -17px; margin-right: -17px;"><ul class="el-scrollbar__view el-cascader__suggestion-list" style="min-width: 222px;"></ul></div></div>'), e = this.$suggestionPanel, this.$panel.find(".popper__arrow").before(e), e.click(function (e) {
                h(e)
            }))
        }, _setSuggestionMenu: function (e) {
            var t = this.$suggestionPanel, i = t.find(".el-cascader__suggestion-list");
            i.empty(), t.find(".el-scrollbar__bar").remove(), e && 0 !== e.length ? (r.each(e, function (e, t) {
                t.bindSuggestion(i)
            }), this._initScrollbar(t, {scrollbar: {top: 0, left: 0}}), this._resetXY()) : i.append('<li class="el-cascader__empty-text">无匹配数据</li>')
        }, initNodes: function (e, t, i) {
            var s, n = [];
            for (s in e) {
                var a = new l(e[s], this, t, i);
                null !== a.value && void 0 !== a.value && (n.push(a), a.children && 0 < a.children.length && a.setChildren(this.initNodes(a.children, t + 1, a)))
            }
            return n
        }, _setActiveValue: function (e, t) {
            var i;
            this.data.activeNodeId !== e && (i = this.data.activeNode, this.data.activeNodeId = e, this.data.activeNode = t, i && i.transferParent(function (e) {
                e.syncStyle()
            }, !0), t && t.transferParent(function (e) {
                e.syncStyle()
            }, !0), this.change(t && t.value, t), null !== e && this._setClear())
        }, _setCheckedValue: function (e, t) {
            var i = this.data.checkedNodes, s = this.config.maxSize, s = 0 < e.length && 0 !== s && e.length >= s && (e = e.slice(0, s), t = t.slice(0, s), !0), n = (this.data.checkedNodeIds = e || [], this.data.checkedNodes = t || [], []), a = [];
            i.forEach(function (e) {
                e.path.forEach(function (e) {
                    -1 === a.indexOf(e.nodeId) && (n.push(e), a.push(e.nodeId))
                })
            }), t.forEach(function (e) {
                e.path.forEach(function (e) {
                    -1 === a.indexOf(e.nodeId) && (n.push(e), a.push(e.nodeId))
                })
            }), n.forEach(function (e) {
                e.syncStyle()
            }), this.change(t.map(function (e) {
                return e.value
            }), t), this._setClear(), this.maxSizeMode = s
        }, setValue: function (e) {
            if ((this.data.activeNodeId || 0 < this.data.checkedNodeIds.length) && this.clearCheckedNodes(), e) {
                var i = this.props.strictMode;
                if (i && !Array.isArray(e)) throw new Error("严格模式下,value必须是一个包含父子节点数组结构.");
                var t = this.getNodes(this.data.nodes), s = this.props.checkStrictly, n = this.props.multiple, a = this.config.disabledFixed;
                if (n) {
                    var n = t.filter(function (t) {
                        return !(!s && !t.leaf || t.disabled && !a) && (i ? e.some(function (e) {
                            if (!Array.isArray(e)) throw new Error("多选严格模式下,value必须是一个二维数组结构.");
                            var i = t.path;
                            return e.length === i.length && e.every(function (e, t) {
                                return i[t].value === e
                            })
                        }) : -1 !== e.indexOf(t.value))
                    }), l = n.map(function (e) {
                        return e.nodeId
                    });
                    this._setCheckedValue(l, n), 0 < n.length && n[0].expandPanel()
                } else for (var o = 0; o < t.length; o++) {
                    var c = t[o];
                    if (s || c.leaf) {
                        var d, r = !1;
                        if (i ? (d = c.path, r = e.length === d.length && e.every(function (e, t) {
                            return d[t].value === e
                        })) : c.value === e && (r = !0), r) {
                            this._setActiveValue(c.nodeId, c), c.expandPanel();
                            break
                        }
                    }
                }
            }
        }, getNodes: function (e, t) {
            t = t || [], e = e || this.data.nodes;
            var i = this;
            return e.forEach(function (e) {
                t.push(e);
                e = e.getChildren();
                e && i.getNodes(e, t)
            }), t
        }, _initScrollbar: function (i, t) {
            var e = r('<div class="el-scrollbar__bar is-onhoriztal"><div class="el-scrollbar__thumb" style="transform: translateX(0%);"></div></div><div class="el-scrollbar__bar is-vertical"><div class="el-scrollbar__thumb" style="transform: translateY(0%);"></div></div>'), s = (i.append(e), r(e[1]).find(".el-scrollbar__thumb")), n = r(e[0]).find(".el-scrollbar__thumb"), l = i.find(".el-scrollbar__wrap"),
                e = this.$panel, a = i.find("li"), o = (Math.max(e.height(), i.height()) - 6) / (a.height() * a.length), c = e.width() / a.width();

            function d(e, t) {
                o < 1 && (s.css("height", 100 * o + "%"), s.css("transform", "translateY(" + e / i.height() * 100 + "%)")), c < 1 && (n.css("width", 100 * c + "%"), n.css("transform", "translateY(" + t / i.width() * 100 + "%)"))
            }

            s.mousedown(function (e) {
                e.stopImmediatePropagation(), h(e);

                function t() {
                    return !1
                }

                function i(e) {
                    e.stopImmediatePropagation(), e = a + (e.clientY - n) / o, l.scrollTop(e)
                }

                var s = r(document), n = (s.bind("selectstart", t), e.clientY), a = l.scrollTop();
                s.bind("mousemove", i), s.one("mouseup", function (e) {
                    h(e), e.stopImmediatePropagation(), s.off("mousemove", i), s.off("selectstart", t)
                })
            }), l.scroll(function () {
                var e = r(this);
                t.scrollbar.top = e.scrollTop(), t.scrollbar.left = e.scrollLeft(), d(t.scrollbar.top, t.scrollbar.left)
            }), l.scrollTop(t.scrollbar.top), d(t.scrollbar.top, t.scrollbar.left)
        }, _fillingPath: function () {
            var t, i, e, s, n = this.props.multiple, a = this.config.showAllLevels, l = this.config.separator, o = this.config.collapseTags, c = this.$inputRow, d = this.config.placeholder, r = this;
            n ? (this.$tags.find(".el-tag").remove(), t = this.$tagsInput, c.css("height", ""), n = this.data.checkedNodes, s = Math.max(this.config.minCollapseTagsNumber, 1), 0 < n.length && (i = [], e = n, (e = o ? n.slice(0, Math.min(n.length, s)) : e).forEach(function (e) {
                i.push(e.$tag)
            }), o && n.length > s && i.push(r.get$tag("+ " + (n.length - s), !1)), i.forEach(function (e) {
                t ? t.before(e) : r.$tags.append(e)
            })), e = r.$tags.height(), c.height() < e && c.css("height", e + 4 + "px"), this._resetXY(), 0 < n.length ? (c.removeAttr("placeholder"), t && t.removeAttr("placeholder", d)) : (c.attr("placeholder", d), t && t.attr("placeholder", d))) : (s = (o = this.data.activeNode) && o.path || [], a ? this._$inputRowSetValue(s.map(function (e) {
                return e.label
            }).join(l)) : this._$inputRowSetValue(o && o.label || ""))
        }, _$inputRowSetValue: function (e) {
            var t = this.$inputRow;
            t.attr("value", e = e || ""), t.val(e)
        }, get$tag: function (e, t) {
            var i = this.icons.from, s = this.icons.close;
            return r('<span class="el-tag el-tag--info el-tag--small el-tag--light"><span>' + e + "</span>" + (t ? '<i class="el-tag__close el-icon-close ' + i + " " + s + '"></i>' : "") + "</span>")
        }, _setClear: function () {
            var t = this;

            function i() {
                t.$icon.removeClass(t.icons.close), t.$icon.addClass(t.icons.down)
            }

            t.$div.mouseenter(function () {
                t.$icon.removeClass(t.icons.down), t.$icon.addClass(t.icons.close)
            }), t.$div.mouseleave(function () {
                i()
            }), t.$icon.off("click");
            var e = this.props.multiple ? 0 < this.data.checkedNodeIds.length : !!this.data.activeNodeId;
            e && !this.config.disabled && this.config.clearable ? t.$icon.one("click", function (e) {
                h(e), t.close(), t.clearCheckedNodes(), i(), t.$icon.off("mouseenter"), t.$div.off("mouseenter"), t.$div.off("mouseleave")
            }) : (i(), t.$icon.off("mouseenter"), t.$div.off("mouseenter"), t.$div.off("mouseleave"))
        }, disabled: function (e) {
            this.config.disabled = !!e, this.config.disabled ? (this.$div.addClass("is-disabled"), this.$div.find(".el-input--suffix").addClass("is-disabled"), this.$inputRow.attr("disabled", "disabled"), this.$tagsInput && this.$tagsInput.attr("disabled", "disabled").hide()) : (this.$div.removeClass("is-disabled"), this.$div.find(".el-input--suffix").removeClass("is-disabled"), this.$inputRow.removeAttr("disabled"), this.$tagsInput && this.$tagsInput.removeAttr("disabled").show()), this._setClear(), this._fillingPath()
        }, change: function (t, i) {
            this.props.multiple ? t && 0 < t.length ? this.$ec.attr("value", JSON.stringify(t)) : this.$ec.removeAttr("value") : this.$ec.attr("value", t || ""), this._fillingPath(), this.event.change.forEach(function (e) {
                "function" == typeof e && e(t, i)
            })
        }, close: function (e) {
            !this.showPanel || e && this.closeEventId !== e || (this.showPanel = !1, this.$div.find(".layui-icon-down").removeClass("is-reverse"), this.$panel.slideUp(100), this.visibleChange(!1), this.$input.removeClass("is-focus"), this.config.filterable && (this.isFiltering = !1, this._fillingPath()), this.event.close.forEach(function (e) {
                "function" == typeof e && e()
            }))
        }, open: function () {
            var e;
            this.showPanel || (this.showPanel = !0, this.closeEventId++, e = this, setTimeout(function () {
                r(document).one("click", e.close.bind(e, e.closeEventId))
            }), this._resetXY(), this.$div.find(".layui-icon-down").addClass("is-reverse"), this.$panel.slideDown(200), this.visibleChange(!0), this.$input.addClass("is-focus"), this.event.open.forEach(function (e) {
                "function" == typeof e && e()
            }))
        }, destroy: function () {
            this.$div.remove(), this.$panel.remove(), this.event.destroy.forEach(function (e) {
                e && e()
            })
        }, visibleChange: function (e) {
        }, removeTag: function (e, t) {
        }, getCheckedValues: function () {
            var e, t = this.props.strictMode;
            return this.props.multiple ? (e = this.data.checkedNodes, t ? e.map(function (e) {
                return e.path.map(function (e) {
                    return e.value
                })
            }) : e.map(function (e) {
                return e.value
            })) : (e = this.data.activeNode, t ? e && e.path.map(function (e) {
                return e.value
            }) : e && e.value)
        }, getCheckedNodes: function () {
            var e, t = this.props.strictMode;
            return this.props.multiple ? (e = this.data.checkedNodes, t ? e && e.map(function (e) {
                return e.path
            }) : e) : (e = this.data.activeNode, t ? e && e.path : e)
        }, clearCheckedNodes: function (e) {
            var t;
            this.props.multiple ? (t = this.config.disabledFixed, !e && t ? (t = (e = this.data.checkedNodes.filter(function (e) {
                return e.disabled
            })).map(function (e) {
                return e.nodeId
            }), this._setCheckedValue(t, e)) : this._setCheckedValue([], [])) : this._setActiveValue(null, null)
        }
    };
    e("layCascader", function (e) {
        e = new t(e);
        return function () {
            var n = this;
            return {
                setOptions: function (e) {
                    n.setOptions(e)
                }, setValue: function (e) {
                    n.setValue(e)
                }, changeEvent: function (e) {
                    if ("function" != typeof e) throw new Error("changeEvent回调事件不是一个有效的方法");
                    n.event.change.push(e)
                }, closeEvent: function (e) {
                    if ("function" != typeof e) throw new Error("closeEvent回调事件不是一个有效的方法");
                    n.event.close.push(e)
                }, openEvent: function (e) {
                    if ("function" != typeof e) throw new Error("openEvent回调事件不是一个有效的方法");
                    n.event.open.push(e)
                }, destroyEvent: function (e) {
                    if ("function" != typeof e) throw new Error("destroyEvent回调事件不是一个有效的方法");
                    n.event.destroy.push(e)
                }, disabled: function (e) {
                    n.disabled(e)
                }, close: function () {
                    n.close()
                }, open: function () {
                    n.open()
                }, destroy: function () {
                    n.destroy()
                }, getCheckedNodes: function () {
                    return n.getCheckedNodes()
                }, getCheckedValues: function () {
                    return n.getCheckedValues()
                }, clearCheckedNodes: function (e) {
                    n.clearCheckedNodes(e)
                }, expandNode: function (e) {
                    for (var t = n.getNodes(n.data.nodes), i = 0; i < t.length; i++) {
                        var s = t[i];
                        if (s.value === e) {
                            s.expandPanel();
                            break
                        }
                    }
                }, getConfig: function () {
                    return r.extend(!0, {}, n.config)
                }, setConfig: function (e) {
                    n.setConfig(e)
                }, getData: function () {
                    return r.extend(!0, {}, n.data)
                }
            }
        }.call(e)
    })
});