<!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 ie9-and-less ie8-and-less ie7-and-less" lang="en"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 ie9-and-less ie8-and-less ie7-and-less" lang="en"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 ie9-and-less ie8-and-less" lang="en"> <![endif]-->
<!--[if IE 9]>    <html class="no-js ie9 ie9-and-less" lang="en"> <![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="en"> <!--<![endif]-->
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="chrome=1">
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
        <title>PACE — Automatic page load progress bars</title>
        <meta name="description" content="Pace is a Javascript and CSS library to automatically add beautiful progress and activity indicators for page loads and ajax navigation. It is free and open source and was developed by HubSpot developers Adam Schwartz (@adamfschwartz) and Zack Bloom (@zackbloom).">
        <link rel="icon" href="http://static.hubspot.com/favicon.ico">
        <script type="text/javascript" src="//use.typekit.net/jbn8qxr.js"></script>
        <script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/modernizr/2.6.2/modernizr.min.js"></script>
        <script type="text/javascript">try{Typekit.load();}catch(e){}</script>
        <script src="//ajax.googleapis.com/ajax/libs/jquery/1.10.1/jquery.min.js"></script>
        <script src="/pace/docs/lib/color.js"></script>
        <script src="/pace/docs/lib/themes.js"></script>
        <script src="/pace/pace.js"></script>
        <link href="/pace/docs/resources/flash-white.css" rel="stylesheet" />
    </head>
    <body>
        <style id="app-colors">
            .section.colored, a.download-src-link, .button {
                background: #29d;
            }

            a, a:hover, a:active, .header .title .title2:before {
                color: #29d;
            }
        </style>
        <style>
            html, body {
                margin: 0;
                height: 100%;
                color: #000;
            }

            a, a:hover, a:active {
                text-decoration: none
            }

            body {
                font-family: "proxima-nova", "Helvetica Neue", sans-serif;
            }

            @media (min-width: 1700px) { html { font-size: 188%; } }
            @media (max-width: 1700px) { html { font-size: 188%; } }
            @media (max-width: 1680px) { html { font-size: 186%; } }
            @media (max-width: 1660px) { html { font-size: 184%; } }
            @media (max-width: 1640px) { html { font-size: 182%; } }
            @media (max-width: 1620px) { html { font-size: 180%; } }
            @media (max-width: 1600px) { html { font-size: 178%; } }
            @media (max-width: 1580px) { html { font-size: 176%; } }
            @media (max-width: 1560px) { html { font-size: 174%; } }
            @media (max-width: 1540px) { html { font-size: 172%; } }
            @media (max-width: 1520px) { html { font-size: 170%; } }
            @media (max-width: 1500px) { html { font-size: 168%; } }
            @media (max-width: 1480px) { html { font-size: 166%; } }
            @media (max-width: 1460px) { html { font-size: 164%; } }
            @media (max-width: 1440px) { html { font-size: 162%; } }
            @media (max-width: 1420px) { html { font-size: 160%; } }
            @media (max-width: 1400px) { html { font-size: 158%; } }
            @media (max-width: 1380px) { html { font-size: 156%; } }
            @media (max-width: 1360px) { html { font-size: 154%; } }
            @media (max-width: 1340px) { html { font-size: 152%; } }
            @media (max-width: 1320px) { html { font-size: 150%; } }
            @media (max-width: 1300px) { html { font-size: 148%; } }
            @media (max-width: 1280px) { html { font-size: 146%; } }
            @media (max-width: 1260px) { html { font-size: 144%; } }
            @media (max-width: 1240px) { html { font-size: 142%; } }
            @media (max-width: 1220px) { html { font-size: 140%; } }
            @media (max-width: 1200px) { html { font-size: 138%; } }
            @media (max-width: 1180px) { html { font-size: 136%; } }
            @media (max-width: 1160px) { html { font-size: 134%; } }
            @media (max-width: 1140px) { html { font-size: 132%; } }
            @media (max-width: 1120px) { html { font-size: 130%; } }
            @media (max-width: 1100px) { html { font-size: 128%; } }
            @media (max-width: 1080px) { html { font-size: 126%; } }
            @media (max-width: 1060px) { html { font-size: 124%; } }
            @media (max-width: 1040px) { html { font-size: 122%; } }
            @media (max-width: 1020px) { html { font-size: 120%; } }
            @media (max-width: 1000px) { html { font-size: 118%; } }
            @media (max-width: 980px) { html { font-size: 116%; } }
            @media (max-width: 960px) { html { font-size: 114%; } }
            @media (max-width: 940px) { html { font-size: 112%; } }
            @media (max-width: 920px) { html { font-size: 110%; } }
            @media (max-width: 900px) { html { font-size: 108%; } }
            @media (max-width: 880px) { html { font-size: 106%; } }
            @media (max-width: 860px) { html { font-size: 104%; } }
            @media (max-width: 840px) { html { font-size: 102%; } }
            @media (max-width: 820px) { html { font-size: 100%; } }
            @media (max-width: 800px) { html { font-size: 98%; } }
            @media (max-width: 780px) { html { font-size: 96%; } }
            @media (max-width: 760px) { html { font-size: 94%; } }
            @media (max-width: 740px) { html { font-size: 92%; } }
            @media (max-width: 720px) { html { font-size: 90%; } }
            @media (max-width: 700px) { html { font-size: 88%; } }
            @media (max-width: 680px) { html { font-size: 86%; } }
            @media (max-width: 660px) { html { font-size: 84%; } }
            @media (max-width: 640px) { html { font-size: 82%; } }
            @media (max-width: 620px) { html { font-size: 80%; } }
            @media (max-width: 600px) { html { font-size: 78%; } }
            @media (max-width: 580px) { html { font-size: 76%; } }
            @media (max-width: 560px) { html { font-size: 74%; } }
            @media (max-width: 540px) { html { font-size: 72%; } }
            @media (max-width: 520px) { html { font-size: 70%; } }
            @media (max-width: 500px) { html { font-size: 68%; } }
            @media (max-width: 480px) { html { font-size: 66%; } }
            @media (max-width: 460px) { html { font-size: 64%; } }
            @media (max-width: 440px) { html { font-size: 62%; } }
            @media (max-width: 420px) { html { font-size: 60%; } }
            @media (max-width: 400px) { html { font-size: 58%; } }
            @media (max-width: 380px) { html { font-size: 56%; } }
            @media (max-width: 360px) { html { font-size: 54%; } }
            @media (max-width: 340px) { html { font-size: 52%; } }
            @media (max-width: 320px) { html { font-size: 50%; } }

            .header {
                color: #000;
                -webkit-transition: opacity 1s;
                -moz-transition: opacity 1s;
                -o-transition: opacity 1s;
                transition: opacity 1s;
                text-align: center;
                margin: auto;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                position: absolute;
                width: 29rem;
                max-width: 100%;
                height: 100%;
                z-index: 1;
            }
            .header h1 {
                font-size: 10rem;
                font-weight: 800;
                text-transform: uppercase;
                letter-spacing: .05em;
            }
            .header h2 {
                font-size: 5rem;
                font-weight: 800;
                text-transform: uppercase;
                letter-spacing: .1em;
            }
            .header .subtitle {
                position: absolute;
                margin: auto;
                top: 0rem;
                left: 0;
                right: 0;
                bottom: 1rem;
                padding-top: 9rem;
                height: 4rem;
                text-align: center;
            }
            .header .subtitle h3 {
                margin: 0;
                font-size: 1.8rem;
            }
            .header .title h1 {
                position: absolute;
                margin: auto;
                top: 0;
                left: 0;
                right: 0;
                bottom: 3rem;
                height: 1.4em;
                text-align: center;
            }
            .header .title .title1 {
                color: #eee;
                color: rgba(0, 0, 0, .1);
            }
            .header .title span {
                display: block;
                position: absolute;
                top: 0;
            }
            .header .title .title2:before {
                position: absolute;
                top: 16.5%;
                bottom: 34%;
                left: 2%;
                right: 2%;
                content: "Progress Automatically. Certain to Entertain.";
                font-size: 1rem;
                line-height: 1.3em;
                padding: 1rem 15rem 1rem 1rem;
                background: #fff;
                opacity: 0;
                text-align: left;
                -webkit-box-sizing: border-box;
                -moz-box-sizing: border-box;
                box-sizing: border-box;
                -webkit-transform: translateY(-15px);
                -webkit-transition: all .3s;
                -moz-transition: all .3s;
                transition: all .3s;
                z-index: 10;
            }
            .header .title .title2:hover:before {
                opacity: 1;
                -webkit-transform: translateY(0px);
            }
            .header .title .char1 { left: .5%; width: 22%; }
            .header .title .char2 { left: 25%; width: 22%; }
            .header .title .char3 { left: 51%; width: 22%; }
            .header .title .char4 { left: 79.5%; width: 22%; }

            .section {
                position: relative;
                height: 100%;
                padding-top: 1rem;
                padding-bottom: 1rem;
                -webkit-box-sizing: border-box;
                -moz-box-sizing: border-box;
                box-sizing: border-box;
            }

            .section.colored {
                color: #fff;
            }

            .section.colored .header .title .title2 {
                color: #fff;
            }

            .section.colored .header .subtitle {
                color: #fff;
            }

            .header .title .title2 span {
                width: 0px;
                -webkit-transition: width 2s linear;
                -moz-transition: width 2s linear;
                -ms-transition: width 2s linear;
                -o-transition: width 2s linear;
                transition: width 2s linear;
                overflow: hidden;
            }

            .page-loaded .header .title .title2 .char1 { width: 21%; }
            .page-loaded .header .title .title2 .char2 { width: 24%; }
            .page-loaded .header .title .title2 .char3 { width: 24%; }
            .page-loaded .header .title .title2 .char4 { width: 21%; }


            .up-arrow, .down-arrow {
                width: 100%;
                text-align: center;
                font-size: 2rem;
            }

            .down-arrow {
                position: absolute;
                left: 0;
                -webkit-transform: rotate(180deg);
                -moz-transform: rotate(180deg);
                -ms-transform: rotate(180deg);
                -o-transform: rotate(180deg);
                transform: rotate(180deg);
                bottom: 30px;
            }

            .section.colored .down-arrow {
                color: rgba(255, 255, 255, .5);
            }


            h2 {
                text-align: center;
            }
            .themes {
                overflow: hidden;
                text-align: center;
            }
            .theme {
                width: 46%;
            }
            .theme.even {
                text-align: center;
                float: left;
                clear: both;
            }
            .theme.odd {
                text-align: center;
                float: right;
            }
            .browser {
                background: #e0e0e0;
                border: 4px solid #e0e0e0;
                width: 100%;
                height: 7rem;
                padding-top: 20px;
                margin: 0 0 10px;
                -webkit-box-sizing: border-box;
                -moz-box-sizing: border-box;
                box-sizing: border-box;
            }
            .browser iframe {
                border: 0;
                background: #fff;
                height: 100%;
                width: 100%;
            }
            input[type="color"] {
                width: 15rem;
                height: 2.3rem;
                font-size: 1rem;
                position: relative;
                font-family: inherit;
                cursor: pointer;
            }
            input[type="color"]::before {
                content: " ";
                display: block;
                position: absolute;
                top: 0;
                left: 3px;
                right: 3px;
                height: 100%;
                border: 6px solid #fff;
                -webkit-box-sizing: border-box;
                -moz-box-sizing: border-box;
                box-sizing: border-box;
            }
            input[type="color"]::after {
                content: "Choose a color";
                display: block;
                text-align: center;
                position: absolute;
                top: 0;
                left: -2px;
                right: -2px;
                height: 100%;
                font-size: .7rem;
                line-height: 2rem;
                color: #fcfcfc;
                color: rgba(255, 255, 255, 1);
                font-weight: 800;
                text-transform: uppercase;
                letter-spacing: 2px;
                border: 6px solid #fff;
                -webkit-box-sizing: border-box;
                -moz-box-sizing: border-box;
                box-sizing: border-box;
            }
            input[type="color"]:active::after {
                color: rgba(255, 255, 255, 0.4);
            }

            .themes h3 {
                margin-top: 1.5em;
                margin-bottom: 0;
            }

            .themes h3 + p {
                margin-top: 0;
            }

            .page {
                text-align: center;
                max-width: 28rem;
                padding: 0 10px;
                margin: 0 auto;
            }

            .themes a, .themes a:hover, .themes a:active {
                text-decoration: none;
                font-size: .7rem;
                text-transform: uppercase;
                letter-spacing: 2px;
            }

            .themes a.author, .themes a:hover.author {
                margin: -1em 0;
                display: block;
                font-size: .5rem;
                color: #888;
            }

            .themes a:hover.author {
                color: #444;
            }

            .themes a.author:before {
                content: "by: ";
                color: #ccc;
            }

            a.button {
                padding: 0.5rem 1rem;
                color: #fff;
                cursor: pointer;
                font-size: 1rem;
                text-transform: uppercase;
                letter-spacing: .1em;
                text-decoration: none;
            }
            .themes-pitch {
                padding: 15px;
            }
            .block {
                padding: 10px 0;
            }
            .color-label {
                display: none;
            }
        </style>

        <div class="section colored">
            <div class="header">
                <div class="title">
                    <h1 class="title1"><span class="char1">p</span><span class="char2">a</span><span class="char3">c</span><span class="char4">e</span></h1>
                    <h1 class="title2"><span class="char1">p</span><span class="char2">a</span><span class="char3">c</span><span class="char4">e</span></h1>
                </div>
                <div class="subtitle">
                    <h3>Automatic page load progress bar</h3>
                </div>
            </div>
            <div class="down-arrow">⇪</div>
        </div>

        <div class="section">
            <div class="page">
                <h2>What is Pace?</h2>
                <p style="text-align: left">Include pace.js and a CSS theme of your choice, and you get a beautiful progress indicator for your page load and ajax navigation.</p>
                <p style="text-align: left">No need to hook into any of your code, progress is detected automatically.</p>
                <br/>
                <h2>Install</h2>
                <p>The easiest way to add Pace to your site is with <a href="http://eager.io" style="color: #bf0c78">Eager</a>.
                <p>Click Install to see a live preview of Pace on your website.</p>
                <iframe style="height: 48px; width: 180px" src="//install.eager.io?appId=kYKTiQjoVjQk" allowtransparency="true" scroll="no" frameBorder="0"></iframe>
                <br/>
                <br/>
                <h2>Download</h2>
                <p>If you’re a developer, download Pace directly here:</p>
                <p><a class="button" href="https://raw.github.com/HubSpot/pace/v1.0.0/pace.min.js">Pace.js</a></p>
                <br/>
                <h2>Themes</h2>
                <label class="color-label" for="color-select">Enter a color:</label>
                <input type="color" value="#2299dd" id="color-select"></input>
                <div class="themes"></div>
                <p class="block">Submit a theme! <a href="https://github.com/HubSpot/pace">Fork us on GitHub</a></p>
                <p class="block"><a class="button" href="http://github.hubspot.com/pace/">Documentation</a></p>
                <br/>
                <p style="font-size: 0.6rem"><a href="http://dev.hubspot.com">HubSpot</a></p>
                <br/>
                <br/>
                <script>
                    $(function(){
                        if (!Modernizr.inputtypes.color)
                            $('.color-label').show();

                        var themes = [{
                            name: 'minimal',
                            title: 'Minimal',
                            author: 'adamschwartz'
                        }, {
                            name: 'flash',
                            title: 'Flash',
                            author: 'adamschwartz'
                        }, {
                            name: 'barber-shop',
                            title: 'Barber Shop',
                            author: 'adamschwartz'
                        }, {
                            name: 'mac-osx',
                            title: 'Mac OSX',
                            author: 'adamschwartz'
                        }, {
                            name: 'fill-left',
                            title: 'Fill Left',
                            author: 'adamschwartz'
                        }, {
                            name: 'flat-top',
                            title: 'Flat Top',
                            author: 'adamschwartz'
                        }, {
                            name: 'big-counter',
                            title: 'Big Counter',
                            author: 'adamschwartz'
                        }, {
                            name: 'corner-indicator',
                            title: 'Corner Indicator',
                            author: 'adamschwartz'
                        }, {
                            name: 'bounce',
                            title: 'Bounce',
                            author: 'adamschwartz'
                        }, {
                            name: 'loading-bar',
                            title: 'Loading Bar',
                            author: 'gavJackson'
                        }, {
                            name: 'center-circle',
                            title: 'Center Circle',
                            author: 'adamschwartz'
                        }, {
                            name: 'center-atom',
                            title: 'Center Atom',
                            author: 'kennyglenn'
                        }, {
                            name: 'center-radar',
                            title: 'Center Radar',
                            author: 'kennyglenn'
                        }, {
                            name: 'center-simple',
                            title: 'Center Simple',
                            author: 'dineshgithub'
                        }];

                        var init = function() {
                            $.each(themes, function(i, theme){
                                $('.themes').append('<div class="theme ' + (i % 2 === 0 ? 'even' : 'odd') + '">'+
                                    '<h3>' + theme.title + '</h3>' +
                                    (theme.author ? '<br/><a class="author" href="https://github.com/' + theme.author + '" target="_blank" rel="nofollow">@' + theme.author + '</a></h4>' : '') +
                                    '<p><a href="#" class="download-link" data-theme="' + theme.name + '">download</a></p>'+
                                    '<div class="browser"><iframe data-theme="' + theme.name + '"></iframe></div>' +
                                '</div>');
                            });

                            var $color = $('input[type="color"]');

                            var cssCache = {};
                            var initFrame = function(frame, color){
                                if ($color.val() != '#2299dd')
                                    color = $color.val();

                                var $styleEl = $('<style>')
                                $(frame).contents().find('body').append($styleEl);

                                var context = {};
                                if (color)
                                    context.color = color;

                                var themeName = $(frame).data('theme')
                                loadTheme(themeName, function(theme){
                                    body = compileTheme(theme, context);

                                    cssCache[themeName] = body;

                                    $styleEl.html(body);
                                });

                                $(frame).contents().find('.pace').addClass('pace-active');
                            };

                            var updateColor = function(color){
                                $('.browser iframe').each(function(){
                                    initFrame(this, color);
                                });
                            };

                            var downloadTheme = function(theme){
                                location.href = 'data:text/css,' + encodeURIComponent(cssCache[theme]);
                            };

                            $('.browser iframe').each(function(){
                                var _this = this;

                                doc = (this.contentWindow || this.documentWindow).document;
                                doc.open();
                                doc.write('' +
                                    '<div class="pace">' +
                                        '<div class="pace-progress" data-progress="50" data-progress-text="50%" style="-webkit-transform: translate3d(50%, 0px, 0px); -ms-transform: translate3d(50%, 0px, 0px); transform: translate3d(50%, 0px, 0px);">' +
                                            '<div class="pace-progress-inner"></div>' +
                                        '</div>' +
                                        '<div class="pace-activity"></div>' +
                                    '</div>' +
                                '');
                                doc.close();

                                // For some strange reason, the Color library we depend on defines itself
                                // asyncronously
                                setTimeout(function(){
                                    initFrame(_this);
                                }, 0);
                            });

                            $color.on('change keyup', function(){
                                var color = $(this).val();
                                updateColor(color);

                                var css = '.section.colored, a.button { background: ' + color + '; } a, a:hover, a:active, .header .title .title2:before { color: ' + color + '; }';
                                if ($('html').hasClass('ie8-and-less')) {
                                    $('#app-colors').get(0).styleSheet.cssText = css
                                } else {
                                    $('#app-colors').html(css);
                                }
                            });

                            $('a.download-link').click(function(e){
                                e.preventDefault();

                                var theme = $(this).data('theme');

                                downloadTheme(theme);
                            });
                        };

                        var initTimeout = setTimeout(function(){
                            $(window).unbind('scroll', scrollOnce);
                            init();
                        }, 3000);

                        var scrollOnce = function() {
                            $(window).unbind('scroll', scrollOnce);
                            clearTimeout(initTimeout);
                            init();
                        };

                        $(window).bind('scroll', scrollOnce);
                    });
                </script>
            </div>
        </div>

        <script>setTimeout(function(){ document.body.className = 'page-loaded' }, 0);</script>

        <!-- Share -->

        <style>
            #retweet_button {
                position: fixed;
                bottom: 30px;
                left: 30px;
                width: 100px;
                -webkit-filter: grayscale(1) contrast(1.3);
                z-index: 3;
            }
            #retweet_button:hover {
                -webkit-filter: none;
            }
            #github_stars {
                position: fixed;
                bottom: 30px;
                left: 130px;
                width: 100px;
                -webkit-filter: grayscale(1) contrast(1.3);
                z-index: 3;
            }
            #github_stars:hover {
                -webkit-filter: none;
            }
            .hn-share-button-wrapper {
                position: fixed;
                bottom: 29px;
                left: 227px;
                -webkit-filter: grayscale(1) contrast(1.3);
                z-index: 3;
            }
            .hn-share-button-wrapper:hover {
                -webkit-filter: none;
            }
        </style>
        <div id="retweet_button">
            <a href="http://twitter.com/share" class="twitter-share-button" data-url="http://github.hubspot.com/pace/docs/welcome" data-text="pace.js - Add an automatic page load progress bar to your site" data-count="horizontal" data-via="HubSpotDev">Tweet</a>
            <script>
              if (Math.random() >= 0.5)
                var recommends = ['hubspotdev', 'zackbloom', 'adamfschwartz'];
              else
                var recommends = ['hubspotdev', 'adamfschwartz', 'zackbloom'];

              var $button = $('.twitter-share-button');
              if ($button.length)
                $button.data('related', recommends.join(','));
            </script>
            <script type="text/javascript" src="http://platform.twitter.com/widgets.js"></script>
        </div>
        <div id="github_stars">
            <iframe src="http://ghbtns.com/github-btn.html?user=HubSpot&amp;repo=pace&amp;type=watch&amp;count=true&amp;size=small" allowtransparency="true" frameborder="0" scrolling="0" width="200" height="20"></iframe>
        </div>

        <!-- Start of Async HubSpot Analytics Code -->
        <script type="text/javascript">
            (function(d,s,i,r) {
                if (d.getElementById(i)){return;}
                var n=d.createElement(s),e=d.getElementsByTagName(s)[0];
                n.id=i;n.src='//js.hubspot.com/analytics/'+(Math.ceil(new Date()/r)*r)+'/51294.js';
                e.parentNode.insertBefore(n, e);
            })(document,"script","hs-analytics",300000);
        </script>
        <!-- End of Async HubSpot Analytics Code -->

        <script>
          (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
          (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
          m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
          })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

          ga('create', 'UA-45159009-1', 'hubspot.com');
          ga('send', 'pageview');
        </script>

        <!-- Force 3d acceleration always and forever :) -->
        <div style="-webkit-transform: translateZ(0)"></div>
    </body>
</html>
