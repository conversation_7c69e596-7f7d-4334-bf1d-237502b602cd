var App=function(){var a,b=!1,c=!1,d=!1,e=!1,f=[],g="../content/superui/",h="global/img/",i="base/plugins/",j="global/css/",k={blue:"#89C4F4",red:"#F3565D",green:"#1bbc9b",purple:"#9b59b6",grey:"#95a5a6",yellow:"#F8CB00",lightblue:"364150"},l=function(){"rtl"===$("body").css("direction")&&(b=!0),c=!!navigator.userAgent.match(/MSIE 8.0/),d=!!navigator.userAgent.match(/MSIE 9.0/),e=!!navigator.userAgent.match(/MSIE 10.0/),e&&$("html").addClass("ie10"),(e||d||c)&&$("html").addClass("ie")},m=function(){for(var a=0;a<f.length;a++){var b=f[a];b.call()}},n=function(){var a=($(window).height(),$(".main-footer")),b=$(".main-header"),c=$(".content-tabs"),d=App.getViewPort().height-a.outerHeight()-b.outerHeight();c.is(":visible")&&(d-=c.outerHeight()),$(".tab_iframe").css({height:d,width:"100%"})},o=function(){var a=App.getViewPort().height-$(".page-footer").outerHeight()-$(".page-header").outerHeight()-$(".content-tabs").height();return a},p=function(){jQuery(".page-sidebar-menu").on("click"," li > a.iframeOpen",function(a){a.preventDefault(),App.scrollTop(),$("#iframe-main").attr("src",$(this).attr("href"))})},q=function(){var a=document.documentElement;a.requestFullscreen?a.requestFullscreen():a.mozRequestFullScreen?a.mozRequestFullScreen():a.webkitRequestFullScreen?a.webkitRequestFullScreen():App.alert({message:"该浏览器不支持全屏！",type:"danger"})},r=function(){var a;if(c){var b;$(window).resize(function(){b!=document.documentElement.clientHeight&&(a&&clearTimeout(a),a=setTimeout(function(){m(),n()},50),b=document.documentElement.clientHeight)})}else $(window).resize(function(){a&&clearTimeout(a),a=setTimeout(function(){m(),n()},50)})},s=function(){$("body").on("click",".portlet > .portlet-title > .tools > a.remove",function(a){a.preventDefault();var b=$(this).closest(".portlet");$("body").hasClass("page-portlet-fullscreen")&&$("body").removeClass("page-portlet-fullscreen"),b.find(".portlet-title .fullscreen").tooltip("destroy"),b.find(".portlet-title > .tools > .reload").tooltip("destroy"),b.find(".portlet-title > .tools > .remove").tooltip("destroy"),b.find(".portlet-title > .tools > .config").tooltip("destroy"),b.find(".portlet-title > .tools > .collapse, .portlet > .portlet-title > .tools > .expand").tooltip("destroy"),b.remove()}),$("body").on("click",".portlet > .portlet-title .fullscreen",function(a){a.preventDefault();var b=$(this).closest(".portlet");if(b.hasClass("portlet-fullscreen"))$(this).removeClass("on"),b.removeClass("portlet-fullscreen"),$("body").removeClass("page-portlet-fullscreen"),b.children(".portlet-body").css("height","auto");else{var c=App.getViewPort().height-b.children(".portlet-title").outerHeight()-parseInt(b.children(".portlet-body").css("padding-top"))-parseInt(b.children(".portlet-body").css("padding-bottom"));$(this).addClass("on"),b.addClass("portlet-fullscreen"),$("body").addClass("page-portlet-fullscreen"),b.children(".portlet-body").css("height",c)}}),$("body").on("click",".portlet > .portlet-title > .tools > a.reload",function(a){a.preventDefault();var b=$(this).closest(".portlet").children(".portlet-body"),c=$(this).attr("data-url"),d=$(this).attr("data-error-display");c?(App.blockUI({target:b,animate:!0,overlayColor:"none"}),$.ajax({type:"GET",cache:!1,url:c,dataType:"html",success:function(a){App.unblockUI(b),b.html(a),App.initAjax()},error:function(a,c,e){App.unblockUI(b);var f="Error on reloading the content. Please check your connection and try again.";"toastr"==d&&toastr?toastr.error(f):"notific8"==d&&$.notific8?($.notific8("zindex",11500),$.notific8(f,{theme:"ruby",life:3e3})):alert(f)}})):(App.blockUI({target:b,animate:!0,overlayColor:"none"}),window.setTimeout(function(){App.unblockUI(b)},1e3))}),$('.portlet .portlet-title a.reload[data-load="true"]').click(),$("body").on("click",".portlet > .portlet-title > .tools > .collapse, .portlet .portlet-title > .tools > .expand",function(a){a.preventDefault();var b=$(this).closest(".portlet").children(".portlet-body");$(this).hasClass("collapse")?($(this).removeClass("collapse").addClass("expand"),b.slideUp(200)):($(this).removeClass("expand").addClass("collapse"),b.slideDown(200))})},t=function(){if($().uniform){var a=$("input[type=checkbox]:not(.toggle, .md-check, .md-radiobtn, .make-switch, .icheck), input[type=radio]:not(.toggle, .md-check, .md-radiobtn, .star, .make-switch, .icheck)");a.size()>0&&a.each(function(){0===$(this).parents(".checker").size()&&($(this).show(),$(this).uniform())})}},u=function(){if($("body").on("click",".md-checkbox > label, .md-radio > label",function(){var a=$(this),b=$(this).children("span:first-child");b.addClass("inc");var c=b.clone(!0);b.before(c),$("."+b.attr("class")+":last",a).remove()}),$("body").hasClass("page-md")){var a,b,c,d,e;$("body").on("click","a.btn, button.btn, input.btn, label.btn",function(f){a=$(this),0==a.find(".md-click-circle").length&&a.prepend("<span class='md-click-circle'></span>"),b=a.find(".md-click-circle"),b.removeClass("md-click-animate"),b.height()||b.width()||(c=Math.max(a.outerWidth(),a.outerHeight()),b.css({height:c,width:c})),d=f.pageX-a.offset().left-b.width()/2,e=f.pageY-a.offset().top-b.height()/2,b.css({top:e+"px",left:d+"px"}).addClass("md-click-animate"),setTimeout(function(){b.remove()},1e3)})}var f=function(a){""!=a.val()?a.addClass("edited"):a.removeClass("edited")};$("body").on("keydown",".form-md-floating-label .form-control",function(a){f($(this))}),$("body").on("blur",".form-md-floating-label .form-control",function(a){f($(this))}),$(".form-md-floating-label .form-control").each(function(){$(this).val().length>0&&$(this).addClass("edited")})},v=function(){$().iCheck&&$(".icheck").each(function(){var a=$(this).attr("data-checkbox")?$(this).attr("data-checkbox"):"icheckbox_minimal-grey",b=$(this).attr("data-radio")?$(this).attr("data-radio"):"iradio_minimal-grey";a.indexOf("_line")>-1||b.indexOf("_line")>-1?$(this).iCheck({checkboxClass:a,radioClass:b,insert:'<div class="icheck_line-icon"></div>'+$(this).attr("data-label")}):$(this).iCheck({checkboxClass:a,radioClass:b})})},w=function(){$().bootstrapSwitch&&$(".make-switch").bootstrapSwitch()},x=function(){$().confirmation&&$("[data-toggle=confirmation]").confirmation({container:"body",btnOkClass:"btn btn-sm btn-success",btnCancelClass:"btn btn-sm btn-danger"})},y=function(){$("body").on("shown.bs.collapse",".accordion.scrollable",function(a){App.scrollTo($(a.target))})},z=function(){if(location.hash){var a=encodeURI(location.hash.substr(1));$('a[href="#'+a+'"]').parents(".tab-pane:hidden").each(function(){var a=$(this).attr("id");$('a[href="#'+a+'"]').click()}),$('a[href="#'+a+'"]').click()}$().tabdrop&&$(".tabbable-tabdrop .nav-pills, .tabbable-tabdrop .nav-tabs").tabdrop({text:'<i class="fa fa-ellipsis-v"></i>&nbsp;<i class="fa fa-angle-down"></i>'})},A=function(){$("body").on("hide.bs.modal",function(){$(".modal:visible").size()>1&&$("html").hasClass("modal-open")===!1?$("html").addClass("modal-open"):$(".modal:visible").size()<=1&&$("html").removeClass("modal-open")}),$("body").on("show.bs.modal",".modal",function(){$(this).hasClass("modal-scroll")&&$("body").addClass("modal-open-noscroll")}),$("body").on("hide.bs.modal",".modal",function(){$("body").removeClass("modal-open-noscroll")}),$("body").on("hidden.bs.modal",".modal:not(.modal-cached)",function(){$(this).removeData("bs.modal")})},B=function(){$(".tooltips").tooltip(),$(".portlet > .portlet-title .fullscreen").tooltip({container:"body",title:"Fullscreen"}),$(".portlet > .portlet-title > .tools > .reload").tooltip({container:"body",title:"Reload"}),$(".portlet > .portlet-title > .tools > .remove").tooltip({container:"body",title:"Remove"}),$(".portlet > .portlet-title > .tools > .config").tooltip({container:"body",title:"Settings"}),$(".portlet > .portlet-title > .tools > .collapse, .portlet > .portlet-title > .tools > .expand").tooltip({container:"body",title:"Collapse/Expand"})},C=function(){$("body").on("click",".dropdown-menu.hold-on-click",function(a){a.stopPropagation()})},D=function(){$("body").on("click",'[data-close="alert"]',function(a){$(this).parent(".alert").hide(),$(this).closest(".note").hide(),a.preventDefault()}),$("body").on("click",'[data-close="note"]',function(a){$(this).closest(".note").hide(),a.preventDefault()}),$("body").on("click",'[data-remove="note"]',function(a){$(this).closest(".note").remove(),a.preventDefault()})},E=function(){$('[data-hover="dropdown"]').not(".hover-initialized").each(function(){$(this).dropdownHover(),$(this).addClass("hover-initialized")})},F=function(){"function"==typeof autosize&&autosize(document.querySelector("textarea.autosizeme"))},G=function(){$(".popovers").popover(),$(document).on("click.bs.popover.data-api",function(b){a&&a.popover("hide")})},H=function(){App.initSlimScroll(".scroller")},I=function(){jQuery.fancybox&&$(".fancybox-button").size()>0&&$(".fancybox-button").fancybox({groupAttr:"data-rel",prevEffect:"none",nextEffect:"none",closeBtn:!0,helpers:{title:{type:"inside"}}})},J=function(){$().counterUp&&$("[data-counter='counterup']").counterUp({delay:10,time:1e3})},K=function(){(c||d)&&$("input[placeholder]:not(.placeholder-no-fix), textarea[placeholder]:not(.placeholder-no-fix)").each(function(){var a=$(this);""===a.val()&&""!==a.attr("placeholder")&&a.addClass("placeholder").val(a.attr("placeholder")),a.focus(function(){a.val()==a.attr("placeholder")&&a.val("")}),a.blur(function(){""!==a.val()&&a.val()!=a.attr("placeholder")||a.val(a.attr("placeholder"))})})},L=function(){$().select2&&($.fn.select2.defaults.set("theme","bootstrap"),$(".select2me").select2({placeholder:"Select",width:"auto",allowClear:!0}))},M=function(){$("[data-auto-height]").each(function(){var a=$(this),b=$("[data-height]",a),c=0,d=a.attr("data-mode"),e=parseInt(a.attr("data-offset")?a.attr("data-offset"):0);b.each(function(){"height"==$(this).attr("data-height")?$(this).css("height",""):$(this).css("min-height","");var a="base-height"==d?$(this).outerHeight():$(this).outerHeight(!0);a>c&&(c=a)}),c+=e,b.each(function(){"height"==$(this).attr("data-height")?$(this).css("height",c):$(this).css("min-height",c)}),a.attr("data-related")&&$(a.attr("data-related")).css("height",a.height())})};return{init:function(){l(),r(),u(),t(),v(),w(),H(),I(),L(),s(),D(),C(),z(),B(),G(),y(),A(),x(),F(),J(),p(),this.addResizeHandler(M),K()},initAjax:function(){t(),v(),w(),E(),H(),L(),I(),C(),B(),G(),y(),x()},handleFullScreen:function(){q()},fixIframeTab:function(){n()},getIframeLayoutHeight:function(){return o()},initComponents:function(){this.initAjax()},fixIframeCotent:function(){setTimeout(function(){n()},50)},setLastPopedPopover:function(b){a=b},addResizeHandler:function(a){f.push(a)},runResizeHandlers:function(){m()},scrollTo:function(a,b){var c=a&&a.size()>0?a.offset().top:0;a&&($("body").hasClass("page-header-fixed")?c-=$(".page-header").height():$("body").hasClass("page-header-top-fixed")?c-=$(".page-header-top").height():$("body").hasClass("page-header-menu-fixed")&&(c-=$(".page-header-menu").height()),c+=b?b:-1*a.height()),$("html,body").animate({scrollTop:c},"slow")},initSlimScroll:function(a){$(a).each(function(){if(!$(this).attr("data-initialized")){var a;a=$(this).attr("data-height")?$(this).attr("data-height"):$(this).css("height"),$(this).slimScroll({allowPageScroll:!0,size:"7px",color:$(this).attr("data-handle-color")?$(this).attr("data-handle-color"):"#bbb",wrapperClass:$(this).attr("data-wrapper-class")?$(this).attr("data-wrapper-class"):"slimScrollDiv",railColor:$(this).attr("data-rail-color")?$(this).attr("data-rail-color"):"#eaeaea",position:b?"left":"right",height:a,alwaysVisible:"1"==$(this).attr("data-always-visible"),railVisible:"1"==$(this).attr("data-rail-visible"),disableFadeOut:!0}),$(this).attr("data-initialized","1")}})},destroySlimScroll:function(a){$(a).each(function(){if("1"===$(this).attr("data-initialized")){$(this).removeAttr("data-initialized"),$(this).removeAttr("style");var a={};$(this).attr("data-handle-color")&&(a["data-handle-color"]=$(this).attr("data-handle-color")),$(this).attr("data-wrapper-class")&&(a["data-wrapper-class"]=$(this).attr("data-wrapper-class")),$(this).attr("data-rail-color")&&(a["data-rail-color"]=$(this).attr("data-rail-color")),$(this).attr("data-always-visible")&&(a["data-always-visible"]=$(this).attr("data-always-visible")),$(this).attr("data-rail-visible")&&(a["data-rail-visible"]=$(this).attr("data-rail-visible")),$(this).slimScroll({wrapperClass:$(this).attr("data-wrapper-class")?$(this).attr("data-wrapper-class"):"slimScrollDiv",destroy:!0});var b=$(this);$.each(a,function(a,c){b.attr(a,c)})}})},scrollTop:function(){App.scrollTo()},blockUI:function(a){a=$.extend(!0,{},a);var b="";if(b=a.animate?'<div class="loading-message '+(a.boxed?"loading-message-boxed":"")+'"><div class="block-spinner-bar"><div class="bounce1"></div><div class="bounce2"></div><div class="bounce3"></div></div></div>':a.iconOnly?'<div class="loading-message '+(a.boxed?"loading-message-boxed":"")+'"><img src="'+this.getGlobalImgPath()+'loading/loading-spinner-grey.gif" align=""></div>':a.textOnly?'<div class="loading-message '+(a.boxed?"loading-message-boxed":"")+'"><span>&nbsp;&nbsp;'+(a.message?a.message:"LOADING...")+"</span></div>":'<div class="loading-message '+(a.boxed?"loading-message-boxed":"")+'"><img src="'+this.getGlobalImgPath()+'loading/loading-spinner-grey.gif" align=""><span>&nbsp;&nbsp;'+(a.message?a.message:"LOADING...")+"</span></div>",a.target){var c=$(a.target);c.height()<=$(window).height()&&(a.cenrerY=!0),c.block({message:b,baseZ:a.zIndex?a.zIndex:1e3,centerY:void 0!==a.cenrerY&&a.cenrerY,css:{top:"10%",border:"0",padding:"0",backgroundColor:"none"},overlayCSS:{backgroundColor:a.overlayColor?a.overlayColor:"#555",opacity:a.boxed?.05:.1,cursor:"wait"}})}else $.blockUI({message:b,baseZ:a.zIndex?a.zIndex:1e3,css:{border:"0",padding:"0",backgroundColor:"none"},overlayCSS:{backgroundColor:a.overlayColor?a.overlayColor:"#555",opacity:a.boxed?.05:.1,cursor:"wait"}})},unblockUI:function(a){a?$(a).unblock({onUnblock:function(){$(a).css("position",""),$(a).css("zoom","")}}):$.unblockUI()},startPageLoading:function(a){a&&a.animate?($(".page-spinner-bar").remove(),$("body").append('<div class="page-spinner-bar"><div class="bounce1"></div><div class="bounce2"></div><div class="bounce3"></div></div>')):($(".page-loading").remove(),$("body").append('<div class="page-loading"><img src="'+this.getGlobalImgPath()+'loading/loading-spinner-grey.gif"/>&nbsp;&nbsp;<span>'+(a&&a.message?a.message:"Loading...")+"</span></div>"))},stopPageLoading:function(){$(".page-loading, .page-spinner-bar").remove()},alert:function(a){a=$.extend(!0,{container:"",place:"append",type:"success",message:"",close:!0,reset:!0,focus:!0,closeInSeconds:0,icon:""},a);var b=App.getUniqueID("App_alert"),c='<div id="'+b+'" class="custom-alerts alert alert-'+a.type+' fade in">'+(a.close?'<button type="button" class="close" data-dismiss="alert" aria-hidden="true"></button>':"")+(""!==a.icon?'<i class="fa-lg fa fa-'+a.icon+'"></i>  ':"")+a.message+"</div>";return a.reset&&$(".custom-alerts").remove(),a.container?"append"==a.place?$(a.container).append(c):$(a.container).prepend(c):1===$(".page-fixed-main-content").size()?$(".page-fixed-main-content").prepend(c):($("body").hasClass("page-container-bg-solid")||$("body").hasClass("page-content-white"))&&0===$(".page-head").size()?$(".page-title").after(c):$(".page-bar").size()>0?$(".page-bar").after(c):$(".page-breadcrumb, .breadcrumbs").after(c),a.focus&&App.scrollTo($("#"+b)),a.closeInSeconds>0&&setTimeout(function(){$("#"+b).remove()},1e3*a.closeInSeconds),b},initUniform:function(a){a?$(a).each(function(){0===$(this).parents(".checker").size()&&($(this).show(),$(this).uniform())}):t()},updateUniform:function(a){$.uniform.update(a)},initFancybox:function(){I()},getActualVal:function(a){return a=$(a),a.val()===a.attr("placeholder")?"":a.val()},getURLParameter:function(a){var b,c,d=window.location.search.substring(1),e=d.split("&");for(b=0;b<e.length;b++)if(c=e[b].split("="),c[0]==a)return unescape(c[1]);return null},isTouchDevice:function(){try{return document.createEvent("TouchEvent"),!0}catch(a){return!1}},getViewPort:function(){var a=window,b="inner";return"innerWidth"in window||(b="client",a=document.documentElement||document.body),{width:a[b+"Width"],height:a[b+"Height"]}},getUniqueID:function(a){return"prefix_"+Math.floor(Math.random()*(new Date).getTime())},isIE8:function(){return c},isIE9:function(){return d},isRTL:function(){return b},isAngularJsApp:function(){return"undefined"!=typeof angular},getbasePath:function(){return g},setbasePath:function(a){g=a},setGlobalImgPath:function(a){h=a},getGlobalImgPath:function(){return g+h},setGlobalPluginsPath:function(a){i=a},getGlobalPluginsPath:function(){return g+i},getGlobalCssPath:function(){return g+j},getBrandColor:function(a){return k[a]?k[a]:""},getResponsiveBreakpoint:function(a){var b={xs:480,sm:768,md:992,lg:1200};return b[a]?b[a]:0}}}();jQuery(document).ready(function(){App.init()}),/*! Copyright (c) 2011 Piotr Rochala (http://rocha.la)
 * Dual licensed under the MIT (http://www.opensource.org/licenses/mit-license.php)
 * and GPL (http://www.opensource.org/licenses/gpl-license.php) licenses.
 *
 * Improved by keenthemes for Metronic Theme
 * Version: 1.3.2
 *
 */
function(a){jQuery.fn.extend({slimScroll:function(b){var c={width:"auto",height:"250px",size:"7px",color:"#000",position:"right",distance:"1px",start:"top",opacity:.4,alwaysVisible:!1,disableFadeOut:!1,railVisible:!1,railColor:"#333",railOpacity:.2,railDraggable:!0,railClass:"slimScrollRail",barClass:"slimScrollBar",wrapperClass:"slimScrollDiv",allowPageScroll:!1,wheelStep:20,touchScrollStep:200,borderRadius:"7px",railBorderRadius:"7px",animate:!0},d=a.extend(c,b);return this.each(function(){function c(b){if(j){var b=b||window.event,c=0;b.wheelDelta&&(c=-b.wheelDelta/120),b.detail&&(c=b.detail/3);var f=b.target||b.srcTarget||b.srcElement;a(f).closest("."+d.wrapperClass).is(v.parent())&&e(c,!0),b.preventDefault&&!u&&b.preventDefault(),u||(b.returnValue=!1)}}function e(a,b,c){u=!1;var e=a,f=v.outerHeight()-A.outerHeight();if(b&&(e=parseInt(A.css("top"))+a*parseInt(d.wheelStep)/100*A.outerHeight(),e=Math.min(Math.max(e,0),f),e=a>0?Math.ceil(e):Math.floor(e),A.css({top:e+"px"})),p=parseInt(A.css("top"))/(v.outerHeight()-A.outerHeight()),e=p*(v[0].scrollHeight-v.outerHeight()),c){e=a;var g=e/v[0].scrollHeight*v.outerHeight();g=Math.min(Math.max(g,0),f),A.css({top:g+"px"})}"scrollTo"in d&&d.animate?v.animate({scrollTop:e}):v.scrollTop(e),v.trigger("slimscrolling",~~e),h(),i()}function f(){window.addEventListener?(this.addEventListener("DOMMouseScroll",c,!1),this.addEventListener("mousewheel",c,!1)):document.attachEvent("onmousewheel",c)}function g(){o=Math.max(v.outerHeight()/v[0].scrollHeight*v.outerHeight(),s),A.css({height:o+"px"});var a=o==v.outerHeight()?"none":"block";A.css({display:a})}function h(){if(g(),clearTimeout(m),p==~~p){if(u=d.allowPageScroll,q!=p){var a=0==~~p?"top":"bottom";v.trigger("slimscroll",a)}}else u=!1;return q=p,o>=v.outerHeight()?void(u=!0):(A.stop(!0,!0).fadeIn("fast"),void(d.railVisible&&z.stop(!0,!0).fadeIn("fast")))}function i(){d.alwaysVisible||(m=setTimeout(function(){d.disableFadeOut&&j||k||l||(A.fadeOut("slow"),z.fadeOut("slow"))},1e3))}var j,k,l,m,n,o,p,q,r="<div></div>",s=30,u=!1,v=a(this);if("ontouchstart"in window&&window.navigator.msPointerEnabled&&v.css("-ms-touch-action","none"),v.parent().hasClass(d.wrapperClass)){var w=v.scrollTop();if(A=v.parent().find("."+d.barClass),z=v.parent().find("."+d.railClass),g(),a.isPlainObject(b)){if("height"in b&&"auto"==b.height){v.parent().css("height","auto"),v.css("height","auto");var x=v.parent().parent().height();v.parent().css("height",x),v.css("height",x)}if("scrollTo"in b)w=parseInt(d.scrollTo);else if("scrollBy"in b)w+=parseInt(d.scrollBy);else if("destroy"in b)return A.remove(),z.remove(),void v.unwrap();e(w,!1,!0)}}else{d.height="auto"==b.height?v.parent().height():b.height;var y=a(r).addClass(d.wrapperClass).css({position:"relative",overflow:"hidden",width:d.width,height:d.height});v.css({overflow:"hidden",width:d.width,height:d.height});var z=a(r).addClass(d.railClass).css({width:d.size,height:"100%",position:"absolute",top:0,display:d.alwaysVisible&&d.railVisible?"block":"none","border-radius":d.railBorderRadius,background:d.railColor,opacity:d.railOpacity,zIndex:90}),A=a(r).addClass(d.barClass).css({background:d.color,width:d.size,position:"absolute",top:0,opacity:d.opacity,display:d.alwaysVisible?"block":"none","border-radius":d.borderRadius,BorderRadius:d.borderRadius,MozBorderRadius:d.borderRadius,WebkitBorderRadius:d.borderRadius,zIndex:99}),B="right"==d.position?{right:d.distance}:{left:d.distance};z.css(B),A.css(B),v.wrap(y),v.parent().append(A),v.parent().append(z),d.railDraggable&&A.bind("mousedown",function(b){var c=a(document);return l=!0,t=parseFloat(A.css("top")),pageY=b.pageY,c.bind("mousemove.slimscroll",function(a){currTop=t+a.pageY-pageY,A.css("top",currTop),e(0,A.position().top,!1)}),c.bind("mouseup.slimscroll",function(a){l=!1,i(),c.unbind(".slimscroll")}),!1}).bind("selectstart.slimscroll",function(a){return a.stopPropagation(),a.preventDefault(),!1}),"ontouchstart"in window&&window.navigator.msPointerEnabled&&(v.bind("MSPointerDown",function(a,b){n=a.originalEvent.pageY}),v.bind("MSPointerMove",function(a){a.originalEvent.preventDefault();var b=(n-a.originalEvent.pageY)/d.touchScrollStep;e(b,!0),n=a.originalEvent.pageY})),z.hover(function(){h()},function(){i()}),A.hover(function(){k=!0},function(){k=!1}),v.hover(function(){j=!0,h(),i()},function(){j=!1,i()}),v.bind("touchstart",function(a,b){a.originalEvent.touches.length&&(n=a.originalEvent.touches[0].pageY)}),v.bind("touchmove",function(a){if(u||a.originalEvent.preventDefault(),a.originalEvent.touches.length){var b=(n-a.originalEvent.touches[0].pageY)/d.touchScrollStep;e(b,!0),n=a.originalEvent.touches[0].pageY}}),g(),"bottom"===d.start?(A.css({top:v.outerHeight()-A.outerHeight()}),e(0,!0)):"top"!==d.start&&(e(a(d.start).position().top,null,!0),d.alwaysVisible||A.hide()),f()}}),this}}),jQuery.fn.extend({slimscroll:jQuery.fn.slimScroll})}(jQuery);var addTabs=function(a){var b={id:200*Math.random(),urlType:"relative",title:"新页面"};if(a=$.extend(!0,b,a),"relative"==a.urlType){var c=window.location.pathname+"/../";a.url=c+a.url}var d="tab_"+a.id,e="",f="",g=$("#"+d);if(!g[0]){var h=App.getViewPort().height-$(".page-footer").outerHeight()-$(".page-header").outerHeight()-$(".content-tabs").height();e='<a href="javascript:void(0);" id="tab_'+d+'"  data-id="'+d+'"  class="menu_tab" >'+a.title,a.close&&(e+=' <i class="fa fa-remove page_tab_close" style="cursor: pointer;" data-id="'+d+'" onclick="closeTab(this)"></i>'),e+="</a>";var i="";a.content?f='<div role="tabpanel" class="tab-pane" id="'+d+'">'+a.content+"</div>":(App.blockUI({target:"#tab-content",boxed:!0,message:"加载中......"}),f='<div role="tabpanel" class="tab-pane" id="'+d+'">',i='<iframe onload="javascript:App.unblockUI(\'#tab-content\');" src="'+a.url+'" width="100%" height="'+h+'" frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="yes"  allowtransparency="yes" id="iframe_'+d+'" class="  tab_iframe"></iframe>',f+=i,f+="</div>"),$(".page-tabs-content").append(e),g=$(f),$("#tab-content").append(g),g.find("iframe").load(function(){App.fixIframeCotent()})}$(".page-tabs-content > a.active").removeClass("active"),$("#tab-content").find(".active").removeClass("active"),$("#tab_"+d).addClass("active"),scrollToTab($(".menu_tab.active")),g.addClass("active")},closeTab=function(a){var b=$(a).attr("data-id");if($(".page-tabs-content > a.active").attr("id")==="tab_"+b){var c=$("#tab_"+b).prev(),d=$("#"+b).prev();setTimeout(function(){c.addClass("active"),d.addClass("active")},300)}$("#tab_"+b).remove(),$("#"+b).remove()},closeCurrentTab=function(){var a=$(".page-tabs-content").find(".active").find(".fa-remove").parents("a");a&&closeTab(a)},refreshTab=function(){var a=$(".page-tabs-content").find(".active").attr("data-id"),b=$("#iframe_"+a),c=b.attr("src");b.attr("src",c)},closeOtherTabs=function(a){if(a){$(".page-tabs-content").children("[data-id]").find(".fa-remove").parents("a").each(function(){$("#"+$(this).data("id")).remove(),$(this).remove()});var b=$(".page-tabs-content").children();b&&($("#"+b.data("id")).addClass("active"),b.addClass("active"))}else $(".page-tabs-content").children("[data-id]").find(".fa-remove").parents("a").not(".active").each(function(){$("#"+$(this).data("id")).remove(),$(this).remove()})},calSumWidth=function(a){var b=0;return $(a).each(function(){b+=$(this).outerWidth(!0)}),b},scrollTabRight=function(){var a=Math.abs(parseInt($(".page-tabs-content").css("margin-left"))),b=calSumWidth($(".content-tabs").children().not(".menuTabs")),c=$(".content-tabs").outerWidth(!0)-b,d=0;if($(".page-tabs-content").width()<c)return!1;for(var e=$(".menu_tab:first"),f=0;f+$(e).outerWidth(!0)<=a;)f+=$(e).outerWidth(!0),e=$(e).next();for(f=0;f+$(e).outerWidth(!0)<c&&e.length>0;)f+=$(e).outerWidth(!0),e=$(e).next();d=calSumWidth($(e).prevAll()),d>0&&$(".page-tabs-content").animate({marginLeft:0-d+"px"},"fast")},scrollToTab=function(a){var b=calSumWidth($(a).prevAll()),c=calSumWidth($(a).nextAll()),d=calSumWidth($(".content-tabs").children().not(".menuTabs")),e=$(".content-tabs").outerWidth(!0)-d,f=0;if($(".page-tabs-content").outerWidth()<e)f=0;else if(c<=e-$(a).outerWidth(!0)-$(a).next().outerWidth(!0)){if(e-$(a).next().outerWidth(!0)>c){f=b;for(var g=a;f-$(g).outerWidth()>$(".page-tabs-content").outerWidth()-e;)f-=$(g).prev().outerWidth(),g=$(g).prev()}}else b>e-$(a).outerWidth(!0)-$(a).prev().outerWidth(!0)&&(f=b-$(a).prev().outerWidth(!0));$(".page-tabs-content").animate({marginLeft:0-f+"px"},"fast")},scrollTabLeft=function(){var a=Math.abs(parseInt($(".page-tabs-content").css("margin-left"))),b=calSumWidth($(".content-tabs").children().not(".menuTabs")),c=$(".content-tabs").outerWidth(!0)-b,d=0;if($(".page-tabs-content").width()<c)return!1;for(var e=$(".menu_tab:first"),f=0;f+$(e).outerWidth(!0)<=a;)f+=$(e).outerWidth(!0),e=$(e).next();if(f=0,calSumWidth($(e).prevAll())>c){for(;f+$(e).outerWidth(!0)<c&&e.length>0;)f+=$(e).outerWidth(!0),e=$(e).prev();d=calSumWidth($(e).prevAll())}$(".page-tabs-content").animate({marginLeft:0-d+"px"},"fast")},activeTab=function(){var a=$(this).attr("data-id");$(".menu_tab").removeClass("active"),$("#tab-content > .active").removeClass("active"),$("#tab_"+a).addClass("active"),$("#"+a).addClass("active"),scrollToTab($(".menu_tab.active"))};$(function(){$(".menuTabs").on("click",".menu_tab",activeTab)}),/*!
 * jQuery blockUI plugin
 * Version 2.70.0-2014.11.23
 * Requires jQuery v1.7 or later
 *
 * Examples at: http://malsup.com/jquery/block/
 * Copyright (c) 2007-2013 M. Alsup
 * Dual licensed under the MIT and GPL licenses:
 * http://www.opensource.org/licenses/mit-license.php
 * http://www.gnu.org/licenses/gpl.html
 *
 * Thanks to Amir-Hossein Sobhi for some excellent contributions!
 */
!function(){"use strict";function a(a){function b(b,d){var f,p,q=b==window,r=d&&void 0!==d.message?d.message:void 0;if(d=a.extend({},a.blockUI.defaults,d||{}),!d.ignoreIfBlocked||!a(b).data("blockUI.isBlocked")){if(d.overlayCSS=a.extend({},a.blockUI.defaults.overlayCSS,d.overlayCSS||{}),f=a.extend({},a.blockUI.defaults.css,d.css||{}),d.onOverlayClick&&(d.overlayCSS.cursor="pointer"),p=a.extend({},a.blockUI.defaults.themedCSS,d.themedCSS||{}),r=void 0===r?d.message:r,q&&n&&c(window,{fadeOut:0}),r&&"string"!=typeof r&&(r.parentNode||r.jquery)){var s=r.jquery?r[0]:r,t={};a(b).data("blockUI.history",t),t.el=s,t.parent=s.parentNode,t.display=s.style.display,t.position=s.style.position,t.parent&&t.parent.removeChild(s)}a(b).data("blockUI.onUnblock",d.onUnblock);var u,v,w,x,y=d.baseZ;u=a(k||d.forceIframe?'<iframe class="blockUI" style="z-index:'+y++ +';display:none;border:none;margin:0;padding:0;position:absolute;width:100%;height:100%;top:0;left:0" src="'+d.iframeSrc+'"></iframe>':'<div class="blockUI" style="display:none"></div>'),v=a(d.theme?'<div class="blockUI blockOverlay ui-widget-overlay" style="z-index:'+y++ +';display:none"></div>':'<div class="blockUI blockOverlay" style="z-index:'+y++ +';display:none;border:none;margin:0;padding:0;width:100%;height:100%;top:0;left:0"></div>'),d.theme&&q?(x='<div class="blockUI '+d.blockMsgClass+' blockPage ui-dialog ui-widget ui-corner-all" style="z-index:'+(y+10)+';display:none;position:fixed">',d.title&&(x+='<div class="ui-widget-header ui-dialog-titlebar ui-corner-all blockTitle">'+(d.title||"&nbsp;")+"</div>"),x+='<div class="ui-widget-content ui-dialog-content"></div>',x+="</div>"):d.theme?(x='<div class="blockUI '+d.blockMsgClass+' blockElement ui-dialog ui-widget ui-corner-all" style="z-index:'+(y+10)+';display:none;position:absolute">',d.title&&(x+='<div class="ui-widget-header ui-dialog-titlebar ui-corner-all blockTitle">'+(d.title||"&nbsp;")+"</div>"),x+='<div class="ui-widget-content ui-dialog-content"></div>',x+="</div>"):x=q?'<div class="blockUI '+d.blockMsgClass+' blockPage" style="z-index:'+(y+10)+';display:none;position:fixed"></div>':'<div class="blockUI '+d.blockMsgClass+' blockElement" style="z-index:'+(y+10)+';display:none;position:absolute"></div>',w=a(x),r&&(d.theme?(w.css(p),w.addClass("ui-widget-content")):w.css(f)),d.theme||v.css(d.overlayCSS),v.css("position",q?"fixed":"absolute"),(k||d.forceIframe)&&u.css("opacity",0);var z=[u,v,w],A=a(q?"body":b);a.each(z,function(){this.appendTo(A)}),d.theme&&d.draggable&&a.fn.draggable&&w.draggable({handle:".ui-dialog-titlebar",cancel:"li"});var B=m&&(!a.support.boxModel||a("object,embed",q?null:b).length>0);if(l||B){if(q&&d.allowBodyStretch&&a.support.boxModel&&a("html,body").css("height","100%"),(l||!a.support.boxModel)&&!q)var C=i(b,"borderTopWidth"),D=i(b,"borderLeftWidth"),E=C?"(0 - "+C+")":0,F=D?"(0 - "+D+")":0;a.each(z,function(a,b){var c=b[0].style;if(c.position="absolute",2>a)q?c.setExpression("height","Math.max(document.body.scrollHeight, document.body.offsetHeight) - (jQuery.support.boxModel?0:"+d.quirksmodeOffsetHack+') + "px"'):c.setExpression("height",'this.parentNode.offsetHeight + "px"'),q?c.setExpression("width",'jQuery.support.boxModel && document.documentElement.clientWidth || document.body.clientWidth + "px"'):c.setExpression("width",'this.parentNode.offsetWidth + "px"'),F&&c.setExpression("left",F),E&&c.setExpression("top",E);else if(d.centerY)q&&c.setExpression("top",'(document.documentElement.clientHeight || document.body.clientHeight) / 2 - (this.offsetHeight / 2) + (blah = document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop) + "px"'),c.marginTop=0;else if(!d.centerY&&q){var e=d.css&&d.css.top?parseInt(d.css.top,10):0,f="((document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop) + "+e+') + "px"';c.setExpression("top",f)}})}if(r&&(d.theme?w.find(".ui-widget-content").append(r):w.append(r),(r.jquery||r.nodeType)&&a(r).show()),(k||d.forceIframe)&&d.showOverlay&&u.show(),d.fadeIn){var G=d.onBlock?d.onBlock:j,H=d.showOverlay&&!r?G:j,I=r?G:j;d.showOverlay&&v._fadeIn(d.fadeIn,H),r&&w._fadeIn(d.fadeIn,I)}else d.showOverlay&&v.show(),r&&w.show(),d.onBlock&&d.onBlock.bind(w)();if(e(1,b,d),q?(n=w[0],o=a(d.focusableElements,n),d.focusInput&&setTimeout(g,20)):h(w[0],d.centerX,d.centerY),d.timeout){var J=setTimeout(function(){q?a.unblockUI(d):a(b).unblock(d)},d.timeout);a(b).data("blockUI.timeout",J)}}}function c(b,c){var f,g=b==window,h=a(b),i=h.data("blockUI.history"),j=h.data("blockUI.timeout");j&&(clearTimeout(j),h.removeData("blockUI.timeout")),c=a.extend({},a.blockUI.defaults,c||{}),e(0,b,c),null===c.onUnblock&&(c.onUnblock=h.data("blockUI.onUnblock"),h.removeData("blockUI.onUnblock"));var k;k=g?a("body").children().filter(".blockUI").add("body > .blockUI"):h.find(">.blockUI"),c.cursorReset&&(k.length>1&&(k[1].style.cursor=c.cursorReset),k.length>2&&(k[2].style.cursor=c.cursorReset)),g&&(n=o=null),c.fadeOut?(f=k.length,k.stop().fadeOut(c.fadeOut,function(){0===--f&&d(k,i,c,b)})):d(k,i,c,b)}function d(b,c,d,e){var f=a(e);if(!f.data("blockUI.isBlocked")){b.each(function(){this.parentNode&&this.parentNode.removeChild(this)}),c&&c.el&&(c.el.style.display=c.display,c.el.style.position=c.position,c.el.style.cursor="default",c.parent&&c.parent.appendChild(c.el),f.removeData("blockUI.history")),f.data("blockUI.static")&&f.css("position","static"),"function"==typeof d.onUnblock&&d.onUnblock(e,d);var g=a(document.body),h=g.width(),i=g[0].style.width;g.width(h-1).width(h),g[0].style.width=i}}function e(b,c,d){var e=c==window,g=a(c);if((b||(!e||n)&&(e||g.data("blockUI.isBlocked")))&&(g.data("blockUI.isBlocked",b),e&&d.bindEvents&&(!b||d.showOverlay))){var h="mousedown mouseup keydown keypress keyup touchstart touchend touchmove";b?a(document).bind(h,d,f):a(document).unbind(h,f)}}function f(b){if("keydown"===b.type&&b.keyCode&&9==b.keyCode&&n&&b.data.constrainTabKey){var c=o,d=!b.shiftKey&&b.target===c[c.length-1],e=b.shiftKey&&b.target===c[0];if(d||e)return setTimeout(function(){g(e)},10),!1}var f=b.data,h=a(b.target);return h.hasClass("blockOverlay")&&f.onOverlayClick&&f.onOverlayClick(b),h.parents("div."+f.blockMsgClass).length>0||0===h.parents().children().filter("div.blockUI").length}function g(a){if(o){var b=o[a===!0?o.length-1:0];b&&b.focus()}}function h(a,b,c){var d=a.parentNode,e=a.style,f=(d.offsetWidth-a.offsetWidth)/2-i(d,"borderLeftWidth"),g=(d.offsetHeight-a.offsetHeight)/2-i(d,"borderTopWidth");b&&(e.left=f>0?f+"px":"0"),c&&(e.top=g>0?g+"px":"0")}function i(b,c){return parseInt(a.css(b,c),10)||0}a.fn._fadeIn=a.fn.fadeIn;var j=a.noop||function(){},k=/MSIE/.test(navigator.userAgent),l=/MSIE 6.0/.test(navigator.userAgent)&&!/MSIE 8.0/.test(navigator.userAgent),m=(document.documentMode||0,a.isFunction(document.createElement("div").style.setExpression));a.blockUI=function(a){b(window,a)},a.unblockUI=function(a){c(window,a)},a.growlUI=function(b,c,d,e){var f=a('<div class="growlUI"></div>');b&&f.append("<h1>"+b+"</h1>"),c&&f.append("<h2>"+c+"</h2>"),void 0===d&&(d=3e3);var g=function(b){b=b||{},a.blockUI({message:f,fadeIn:"undefined"!=typeof b.fadeIn?b.fadeIn:700,fadeOut:"undefined"!=typeof b.fadeOut?b.fadeOut:1e3,timeout:"undefined"!=typeof b.timeout?b.timeout:d,centerY:!1,showOverlay:!1,onUnblock:e,css:a.blockUI.defaults.growlCSS})};g(),f.css("opacity"),f.mouseover(function(){g({fadeIn:0,timeout:3e4});var b=a(".blockMsg");b.stop(),b.fadeTo(300,1)}).mouseout(function(){a(".blockMsg").fadeOut(1e3)})},a.fn.block=function(c){if(this[0]===window)return a.blockUI(c),this;var d=a.extend({},a.blockUI.defaults,c||{});return this.each(function(){var b=a(this);d.ignoreIfBlocked&&b.data("blockUI.isBlocked")||b.unblock({fadeOut:0})}),this.each(function(){"static"==a.css(this,"position")&&(this.style.position="relative",a(this).data("blockUI.static",!0)),this.style.zoom=1,b(this,c)})},a.fn.unblock=function(b){return this[0]===window?(a.unblockUI(b),this):this.each(function(){c(this,b)})},a.blockUI.version=2.7,a.blockUI.defaults={message:"<h1>Please wait...</h1>",title:null,draggable:!0,theme:!1,css:{padding:0,margin:0,width:"30%",top:"40%",left:"35%",textAlign:"center",color:"#000",border:"3px solid #aaa",backgroundColor:"#fff",cursor:"wait"},themedCSS:{width:"30%",top:"40%",left:"35%"},overlayCSS:{backgroundColor:"#000",opacity:.6,cursor:"wait"},cursorReset:"default",growlCSS:{width:"350px",top:"10px",left:"",right:"10px",border:"none",padding:"5px",opacity:.6,cursor:"default",color:"#fff",backgroundColor:"#000","-webkit-border-radius":"10px","-moz-border-radius":"10px","border-radius":"10px"},iframeSrc:/^https/i.test(window.location.href||"")?"javascript:false":"about:blank",forceIframe:!1,baseZ:1e3,centerX:!0,centerY:!0,allowBodyStretch:!0,bindEvents:!0,constrainTabKey:!0,fadeIn:200,fadeOut:400,timeout:0,showOverlay:!0,focusInput:!0,focusableElements:":input:enabled:visible",onBlock:null,onUnblock:null,onOverlayClick:null,quirksmodeOffsetHack:4,blockMsgClass:"blockMsg",ignoreIfBlocked:!1};var n=null,o=[]}"function"==typeof define&&define.amd&&define.amd.jQuery?define(["jquery"],a):a(jQuery)}(),function(a){a.fn.sidebarMenu=function(b){function c(b,d,e){a.each(d,function(d,f){var g=a('<li class="header"></li>');if(null!=f.isHeader&&f.isHeader===!0)return g.append(f.text),void b.append(g);var h,i=a('<li class="treeview " data-level="'+e+'"></li>');h=a(e>0?'<a style="padding-left:'+20*e+'px"></a>':"<a></a>");var j=a("<i></i>");j.addClass(f.icon);var k=a('<span class="title"></span>');k.addClass("menu-text").text(f.text),h.append(j),h.append(k),h.addClass("nav-link");var l=f.isOpen;if(l===!0&&i.addClass("active"),f.children&&f.children.length>0){var m=a('<span class="pull-right-container"></span>'),n=a('<i class="fa fa-angle-left pull-right"></i>');m.append(n),h.append(m),i.append(h);var o=a("<ul></ul>");o.addClass("treeview-menu"),l===!0?(o.css("display","block"),o.addClass("menu-open")):o.css("display","none"),c(o,f.children,e+1),i.append(o)}else{if(null!=f.targetType&&"blank"===f.targetType)h.attr("href",f.url),h.attr("target","_blank");else if(null!=f.targetType&&"ajax"===f.targetType)h.attr("href",f.url),h.addClass("ajaxify");else if(null!=f.targetType&&"iframe-tab"===f.targetType){var p="addTabs({id:'"+f.id+"',title: '"+f.text+"',close: true,url: '"+f.url+"'});";h.attr("onclick",p)}else null!=f.targetType&&"iframe"===f.targetType?(h.attr("href",f.url),h.addClass("iframeOpen"),a("#iframe-main").addClass("tab_iframe")):(h.attr("href",f.url),h.addClass("iframeOpen"),a("#iframe-main").addClass("tab_iframe"));h.addClass("nav-link");var q=a("<span></span>");null!=f.tip&&f.tip>0&&q.addClass("label").addClass("label-success").text(f.tip),h.append(q),i.append(h)}b.append(i)})}b=a.extend({},a.fn.sidebarMenu.defaults,b||{});var d=a(this),e=0;if(b.data)c(d,b.data,e);else{if(!b.url)return;a.getJSON(b.url,b.param,function(a){c(d,a,e)})}d.on("click","li.treeview a",function(){a(this);a(a.AdminLTE.options.sidebarToggleSelector).click()})},a.fn.sidebarMenu.defaults={url:null,param:null,data:null,isHeader:!1}}(jQuery);