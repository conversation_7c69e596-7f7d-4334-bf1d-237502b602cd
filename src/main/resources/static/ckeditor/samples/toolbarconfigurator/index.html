<!DOCTYPE html>
<!--
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
-->
<!--[if IE 8]><html class="ie8"><![endif]-->
<!--[if gt IE 8]><html><![endif]-->
<!--[if !IE]><!--><html><!--<![endif]-->
<head>
	<meta charset="utf-8">
	<title>Toolbar Configurator</title>
	<script src="../../ckeditor.js"></script>
	<script>
		if ( CKEDITOR.env.ie && CKEDITOR.env.version < 9 )
			CKEDITOR.tools.enableHtml5Elements( document );
	</script>
	<link rel="stylesheet" href="lib/codemirror/codemirror.css">
	<link rel="stylesheet" href="lib/codemirror/show-hint.css">
	<link rel="stylesheet" href="lib/codemirror/neo.css">
	<link rel="stylesheet" href="css/fontello.css">
	<link rel="stylesheet" href="../css/samples.css">
</head>
<body id="toolbar">

<nav class="navigation-a">
	<div class="grid-container">
		<ul class="navigation-a-left grid-width-70">
			<li><a href="https://ckeditor.com/ckeditor-4/">Project Homepage</a></li>
			<li><a href="https://github.com/ckeditor/ckeditor-dev/issues">I found a bug</a></li>
			<li><a href="https://github.com/ckeditor/ckeditor-dev" class="icon-pos-right icon-navigation-a-github">Fork CKEditor on GitHub</a></li>
		</ul>
		<ul class="navigation-a-right grid-width-30">
			<li><a href="https://ckeditor.com/blog/">CKEditor Blog</a></li>
		</ul>
	</div>
</nav>

<header class="header-a">
	<div class="grid-container">
		<h1 class="header-a-logo grid-width-30">
			<a href="../index.html"><img src="../img/logo.svg" onerror="this.src='../img/logo.png'; this.onerror=null;" alt="CKEditor Logo"></a>
		</h1>
		<nav class="navigation-b grid-width-70">
			<ul>
				<li><a href="../index.html"  class="button-a">Start</a></li>
				<li><a href="index.html"  class="button-a button-a-background">Toolbar configurator</a></li>
			</ul>
		</nav>
	</div>
</header>

<main>
	<div class="adjoined-top">
		<div class="grid-container">
			<div class="content grid-width-100">
				<div class="grid-container-nested">
					<h1 class="grid-width-60">
						Toolbar Configurator
						<a href="#help-content" type="button" title="Configurator help" id="help" class="button-a button-a-background button-a-no-text icon-pos-left icon-question-mark">Help</a>
					</h1>

					<div class="grid-width-40 grid-switch-magic">
						<div class="switch">
							<span class="balloon-a balloon-a-se">Select configurator type</span>
							<input type="radio" name="radio" data-num="1" id="radio-basic" />
							<input type="radio" name="radio" data-num="2" id="radio-advanced" />
							<label data-for="1" for="radio-basic">Basic</label>
							<span class="switch-inner">
								<span class="handler"></span>
							</span>
							<label data-for="2" for="radio-advanced">Advanced</label>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="adjoined-bottom">
		<div class="grid-container">
			<div class="grid-width-100">
				<div class="editors-container">
					<div id="editor-basic"></div>
					<div id="editor-advanced"></div>
				</div>
			</div>
		</div>
	</div>

	<div class="grid-container configurator">
		<div class="content grid-width-100">
			<div class="configurator">
				<div>
					<div id="toolbarModifierWrapper"></div>
				</div>
			</div>
		</div>
	</div>

	<div id="help-content">
		<div class="grid-container">
			<div class="grid-width-100">
				<h2>What Am I Doing Here?</h2>

				<div class="grid-container grid-container-nested">
					<div class="basic">
						<div class="grid-width-50">
							<p>Arrange <a href="https://ckeditor.com/docs/ckeditor4/latest/api/CKEDITOR_config.html#cfg-toolbarGroups">toolbar groups</a>, toggle <a href="https://ckeditor.com/docs/ckeditor4/latest/api/CKEDITOR_config.html#cfg-removeButtons">button visibility</a> according to your needs and get your toolbar configuration.</p>
							<p>You can replace the content of the <a href="../../config.js"><code>config.js</code></a> file with the generated configuration. If you already set some configuration options you will need to merge both configurations.</p>
						</div>
						<div class="grid-width-50">
							<p>Read more about different ways of <a href="https://ckeditor.com/docs/ckeditor4/latest/guide/dev_configuration.html">setting configuration</a> and do not forget about <strong>clearing browser cache</strong>.</p>
							<p>Arranging toolbar groups is the recommended way of configuring the toolbar, but if you need more freedom you can use the <a href="#advanced">advanced configurator</a>.</p>
						</div>
					</div>
					<div class="advanced" style="display: none;">
						<div class="grid-width-50">
							<p>With this code editor you can edit your <a href="https://ckeditor.com/docs/ckeditor4/latest/api/CKEDITOR_config.html#cfg-toolbar">toolbar configuration</a> live.</p>
							<p>You can replace the content of the <a href="../../config.js"><code>config.js</code></a> file with the generated configuration. If you already set some configuration options you will need to merge both configurations.</p>
						</div>
						<div class="grid-width-50">
							<p>Read more about different ways of <a href="https://ckeditor.com/docs/ckeditor4/latest/guide/dev_configuration.html">setting configuration</a> and do not forget about <strong>clearing browser cache</strong>.</p>
						</div>
					</div>
				</div>

				<p class="grid-container grid-container-nested">
					<button type="button" class="help-content-close grid-width-100 button-a button-a-background">Got it. Let's play!</button>
				</p>
			</div>
		</div>
	</div>
</main>

<footer class="footer-a grid-container">
	<p class="grid-width-100">
		CKEditor &ndash; The text editor for the Internet &ndash; <a class="samples" href="https://ckeditor.com/">https://ckeditor.com</a>
	</p>
	<p class="grid-width-100" id="copy">
		Copyright &copy; 2003-2019, <a class="samples" href="https://cksource.com/">CKSource</a> &ndash; Frederico Knabben. All rights reserved.
	</p>
</footer>

<script src="lib/codemirror/codemirror.js"></script>
<script src="lib/codemirror/javascript.js"></script>
<script src="lib/codemirror/show-hint.js"></script>

<script src="js/fulltoolbareditor.js"></script>
<script src="js/abstracttoolbarmodifier.js"></script>
<script src="js/toolbarmodifier.js"></script>
<script src="js/toolbartextmodifier.js"></script>
<script src="../js/sf.js"></script>

<script>
	( function() {
		'use strict';

		var mode = ( window.location.hash.substr( 1 ) === 'advanced' ) ? 'advanced' : 'basic',
			configuratorSection = CKEDITOR.document.findOne( 'main > .grid-container.configurator' ),
			basicInstruction = CKEDITOR.document.findOne( '#help-content .basic' ),
			advancedInstruction = CKEDITOR.document.findOne( '#help-content .advanced' ),

			// Configurator mode switcher.
			modeSwitchBasic = CKEDITOR.document.getById( 'radio-basic' ),
			modeSwitchAdvanced = CKEDITOR.document.getById( 'radio-advanced' );

		// Initial setup
		function updateSwitcher() {
			if ( mode === 'advanced' ) {
				modeSwitchAdvanced.$.checked = true;
			} else {
				modeSwitchBasic.$.checked = true;
			}
		}

		updateSwitcher();

		CKEDITOR.document.getWindow().on( 'hashchange', function( e ) {
			var hash = window.location.hash.substr( 1 );
			if ( !( hash === 'advanced' || hash === 'basic' ) ) {
				return;
			}
			mode = hash;
			onToolbarsDone( mode );
		} );

		CKEDITOR.document.getWindow().on( 'resize', function() {
			updateToolbar( ( mode === 'basic' ? toolbarModifier : toolbarTextModifier )[ 'editorInstance' ] );
		} );

		function onRefresh( modifier ) {
			modifier = modifier || this;

			if ( mode === 'basic' && modifier instanceof ToolbarConfigurator.ToolbarTextModifier ) {
				return;
			}

			// CodeMirror container becomes visible, so we need to refresh and to avoid rendering problems.
			if ( mode === 'advanced' && modifier instanceof ToolbarConfigurator.ToolbarTextModifier ) {
				modifier.codeContainer.refresh();
			}

			updateToolbar( modifier.editorInstance );
		}

		function updateToolbar( editor ) {
			var editorContainer = editor.container;

			// Not always editor is loaded.
			if ( !editorContainer ) {
				return;
			}

			var displayStyle = editorContainer.getStyle( 'display' );

			editorContainer.setStyle( 'display', 'block' );

			var newHeight = editorContainer.getSize( 'height' );

			var newMarginTop = parseInt( editorContainer.getComputedStyle( 'margin-top' ), 10 );
			newMarginTop = ( isNaN( newMarginTop ) ? 0 : Number( newMarginTop ) );

			var newMarginBottom = parseInt( editorContainer.getComputedStyle( 'margin-bottom' ), 10 );
			newMarginBottom = ( isNaN( newMarginBottom ) ? 0 : Number( newMarginBottom ) );

			var result = newHeight + newMarginTop + newMarginBottom;

			editorContainer.setStyle( 'display', displayStyle );

			editor.container.getAscendant( 'div' ).setStyle( 'height', result + 'px' );
		}

		var toolbarModifier = new ToolbarConfigurator.ToolbarModifier( 'editor-basic' );

		var done = 0;
		toolbarModifier.init( onToolbarInit );
		toolbarModifier.onRefresh = onRefresh;

		CKEDITOR.document.getById( 'toolbarModifierWrapper' ).append( toolbarModifier.mainContainer );

		var toolbarTextModifier = new ToolbarConfigurator.ToolbarTextModifier( 'editor-advanced' );
		toolbarTextModifier.init( onToolbarInit );
		toolbarTextModifier.onRefresh = onRefresh;

		function onToolbarInit() {
			if ( ++done === 2 ) {
				onToolbarsDone();

				positionSticky.watch( CKEDITOR.document.findOne( '.toolbar' ), function() {
					return mode === 'advanced';
				} );
			}
		}

		function onToolbarsDone() {
			if ( mode === 'basic' ) {
				toggleModeBasic( false );
			} else {
				toggleModeAdvanced( false );
			}

			updateSwitcher();

			setTimeout( function() {
				CKEDITOR.document.findOne( '.editors-container' ).addClass( 'active' );
				CKEDITOR.document.findOne( '#toolbarModifierWrapper' ).addClass( 'active' );
			}, 200 );
		}

		CKEDITOR.document.getById( 'toolbarModifierWrapper' ).append( toolbarTextModifier.mainContainer );

		function toogleModeSwitch( onElement, offElement, onModifier, offModifier ) {
			onElement.addClass( 'fancy-button-active' );
			offElement.removeClass( 'fancy-button-active' );

			onModifier.showUI();
			offModifier.hideUI();
		}

		function toggleModeBasic( callOnRefresh ) {
			callOnRefresh = ( callOnRefresh !== false );
			mode = 'basic';
			window.location.hash = '#basic';
			toogleModeSwitch( modeSwitchBasic, modeSwitchAdvanced, toolbarModifier, toolbarTextModifier );

			configuratorSection.removeClass( 'freed-width' );
			basicInstruction.show();
			advancedInstruction.hide();

			callOnRefresh && onRefresh( toolbarModifier );
		}

		function toggleModeAdvanced( callOnRefresh ) {
			callOnRefresh = ( callOnRefresh !== false );
			mode = 'advanced';
			window.location.hash = '#advanced';
			toogleModeSwitch( modeSwitchAdvanced, modeSwitchBasic, toolbarTextModifier, toolbarModifier );

			configuratorSection.addClass( 'freed-width' );
			advancedInstruction.show();
			basicInstruction.hide();

			callOnRefresh && onRefresh( toolbarTextModifier );
		}

		modeSwitchBasic.on( 'click', toggleModeBasic );
		modeSwitchAdvanced.on( 'click', toggleModeAdvanced );

		//
		// Position:sticky for the toolbar.
		//

		// Will make elements behave like they were styled with position:sticky.
		var positionSticky = {
			// Store object: {
			// 		element: CKEDITOR.dom.element, // Element which will float.
			// 		placeholder: CKEDITOR.dom.element, // Placeholder which is place to prevent page bounce.
			// 		isFixed: boolean // Whether element float now.
			// }
			watched: [],

			active: [],

			staticContainer: null,

			init: function() {
				var element = CKEDITOR.dom.element.createFromHtml(
					'<div class="staticContainer">' +
						'<div class="grid-container" >' +
							'<div class="grid-width-100">' +
								'<div class="inner"></div>' +
							'</div>' +
						'</div>' +
					'</div>' );

				this.staticContainer = element.findOne( '.inner' );

				CKEDITOR.document.getBody().append( element );
			},

			watch: function( element, preventFunc ) {
				this.watched.push( {
					element: element,
					placeholder: new CKEDITOR.dom.element( 'div' ),
					isFixed: false,
					preventFunc: preventFunc
				} );
			},

			checkAll: function() {
				for ( var i = 0; i < this.watched.length; i++ ) {
					this.check( this.watched[ i ] );
				}
			},

			check: function( element ) {
				var isFixed = element.isFixed;
				var shouldBeFixed = this.shouldBeFixed( element );

				// Nothing to be done.
				if ( isFixed === shouldBeFixed ) {
					return;
				}

				var placeholder = element.placeholder;

				if ( isFixed ) {
					// Unfixing.

					element.element.insertBefore( placeholder );
					placeholder.remove();

					element.element.removeStyle( 'margin' );

					this.active.splice( CKEDITOR.tools.indexOf( this.active, element ), 1 );

				} else {
					// Fixing.
					placeholder.setStyle( 'width', element.element.getSize( 'width' ) + 'px' );
					placeholder.setStyle( 'height', element.element.getSize( 'height' ) + 'px' );
					placeholder.setStyle( 'margin-bottom', element.element.getComputedStyle( 'margin-bottom' ) );
					placeholder.setStyle( 'display', element.element.getComputedStyle( 'display' ) );
					placeholder.insertAfter( element.element );

					this.staticContainer.append( element.element );

					this.active.push( element );
				}

				element.isFixed = !element.isFixed;
			},

			shouldBeFixed: function( element ) {
				if ( element.preventFunc && element.preventFunc() ) {
					return false;
				}

				// If element is already fixed we are checking it's placeholder.
				var related = ( element.isFixed ? element.placeholder : element.element ),
					clientRect = related.$.getBoundingClientRect(),
					staticHeight = this.staticContainer.getSize('height' ),
					elemHeight = element.element.getSize( 'height' );

				if ( element.isFixed ) {
					return ( clientRect.top + elemHeight < staticHeight );
				} else {
					return ( clientRect.top < staticHeight );
				}
			}
		};

		positionSticky.init();

		CKEDITOR.document.getWindow().on( 'scroll',
			new CKEDITOR.tools.eventsBuffer( 100, positionSticky.checkAll, positionSticky ).input
		);

		// Make the toolbar sticky.
		positionSticky.watch( CKEDITOR.document.findOne( '.editors-container' ) );

		// Help button and help-content.
		( function() {
			var helpButton = CKEDITOR.document.getById( 'help' ),
				helpContent = CKEDITOR.document.getById( 'help-content' );

			// Don't show help button on IE8 because it's unsupported by Pico Modal.
			if ( CKEDITOR.env.ie && CKEDITOR.env.version == 8 ) {
				helpButton.hide();
			} else {
				// Display help modal when the button is clicked.
				helpButton.on( 'click', function( evt ) {
					SF.modal( {
						// Clone modal content from DOM.
						content: helpContent.getHtml(),

						afterCreate: function( modal ) {
							// Enable modal content button to close the modal.
							new CKEDITOR.dom.element( modal.modalElem() ).findOne( '.help-content-close' ).once( 'click', modal.close );
						}
					} ).show();
				} );
			}
		} )();
	} )();
</script>
</body>
</html>
