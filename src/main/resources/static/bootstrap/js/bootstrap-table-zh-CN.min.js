/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.15.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){"object"==typeof exports&&"undefined"!=typeof module?b(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],b):(a=a||self,b(a.jQuery))})(this,function(a){'use strict';var c=Math.min;function b(a,b){return b={exports:{}},a(b,b.exports),b.exports}a=a&&a.hasOwnProperty("default")?a["default"]:a;var d,e,g,h="undefined"==typeof globalThis?"undefined"==typeof window?"undefined"==typeof global?"undefined"==typeof self?{}:self:global:window:globalThis,i="object",j=function(a){return a&&a.Math==Math&&a},k=j(typeof globalThis==i&&globalThis)||j(typeof window==i&&window)||j(typeof self==i&&self)||j(typeof h==i&&h)||Function("return this")(),l=function(a){try{return!!a()}catch(a){return!0}},m=!l(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}),n={}.propertyIsEnumerable,o=Object.getOwnPropertyDescriptor,p=o&&!n.call({1:2},1),q=p?function(a){var b=o(this,a);return!!b&&b.enumerable}:n,f={f:q},r=function(a,b){return{enumerable:!(1&a),configurable:!(2&a),writable:!(4&a),value:b}},s={}.toString,t=function(a){return s.call(a).slice(8,-1)},u="".split,v=l(function(){return!Object("z").propertyIsEnumerable(0)})?function(a){return"String"==t(a)?u.call(a,""):Object(a)}:Object,w=function(a){if(a==null)throw TypeError("Can't call method on "+a);return a},x=function(a){return v(w(a))},y=function(a){return"object"==typeof a?null!==a:"function"==typeof a},z=function(a,b){if(!y(a))return a;var c,d;if(b&&"function"==typeof(c=a.toString)&&!y(d=c.call(a)))return d;if("function"==typeof(c=a.valueOf)&&!y(d=c.call(a)))return d;if(!b&&"function"==typeof(c=a.toString)&&!y(d=c.call(a)))return d;throw TypeError("Can't convert object to primitive value")},A={}.hasOwnProperty,B=function(a,b){return A.call(a,b)},C=k.document,D=y(C)&&y(C.createElement),E=function(a){return D?C.createElement(a):{}},F=!m&&!l(function(){return 7!=Object.defineProperty(E("div"),"a",{get:function(){return 7}}).a}),G=Object.getOwnPropertyDescriptor,H=m?G:function(a,b){if(a=x(a),b=z(b,!0),F)try{return G(a,b)}catch(a){}return B(a,b)?r(!f.f.call(a,b),a[b]):void 0},I={f:H},J=function(a){if(!y(a))throw TypeError(a+" is not an object");return a},K=Object.defineProperty,L=m?K:function(a,b,c){if(J(a),b=z(b,!0),J(c),F)try{return K(a,b,c)}catch(a){}if("get"in c||"set"in c)throw TypeError("Accessors not supported");return"value"in c&&(a[b]=c.value),a},M={f:L},N=m?function(a,b,c){return M.f(a,b,r(1,c))}:function(a,b,c){return a[b]=c,a},P=function(a,b){try{N(k,a,b)}catch(c){k[a]=b}return b},Q=b(function(a){var b=k["__core-js_shared__"]||P("__core-js_shared__",{});(a.exports=function(a,c){return b[a]||(b[a]=c===void 0?{}:c)})("versions",[]).push({version:"3.1.3",mode:"global",copyright:"\xA9 2019 Denis Pushkarev (zloirock.ru)"})}),R=Q("native-function-to-string",Function.toString),S=k.WeakMap,T="function"==typeof S&&/native code/.test(R.call(S)),U=0,O=Math.random(),V=function(a){return"Symbol("+((a===void 0?"":a)+"")+")_"+(++U+O).toString(36)},W=Q("keys"),X=function(a){return W[a]||(W[a]=V(a))},Y={},Z=k.WeakMap,_=function(a){return g(a)?e(a):d(a,{})};if(T){var aa=new Z,ba=aa.get,ca=aa.has,da=aa.set;d=function(a,b){return da.call(aa,a,b),b},e=function(a){return ba.call(aa,a)||{}},g=function(a){return ca.call(aa,a)}}else{var ea=X("state");Y[ea]=!0,d=function(a,b){return N(a,ea,b),b},e=function(a){return B(a,ea)?a[ea]:{}},g=function(a){return B(a,ea)}}var fa={set:d,get:e,has:g,enforce:_,getterFor:function(a){return function(b){var c;if(!y(b)||(c=e(b)).type!==a)throw TypeError("Incompatible receiver, "+a+" required");return c}}},ga=b(function(a){var b=fa.get,c=fa.enforce,d=(R+"").split("toString");Q("inspectSource",function(a){return R.call(a)}),(a.exports=function(a,b,e,f){var g=!!f&&!!f.unsafe,h=!!f&&!!f.enumerable,i=!!f&&!!f.noTargetGet;return("function"==typeof e&&("string"==typeof b&&!B(e,"name")&&N(e,"name",b),c(e).source=d.join("string"==typeof b?b:"")),a===k)?void(h?a[b]=e:P(b,e)):void(g?!i&&a[b]&&(h=!0):delete a[b],h?a[b]=e:N(a,b,e))})(Function.prototype,"toString",function(){return"function"==typeof this&&b(this).source||R.call(this)})}),ha=k,ia=function(a){return"function"==typeof a?a:void 0},ja=Math.ceil,ka=Math.floor,la=function(a){return isNaN(a=+a)?0:(0<a?ka:ja)(a)},ma=function(a){return 0<a?c(la(a),9007199254740991):0},na=Math.max,oa=function(a,b){var d=la(a);return 0>d?na(d+b,0):c(d,b)},pa=function(a){return function(b,c,d){var e,f=x(b),g=ma(f.length),h=oa(d,g);if(a&&c!=c){for(;g>h;)if(e=f[h++],e!=e)return!0;}else for(;g>h;h++)if((a||h in f)&&f[h]===c)return a||h||0;return!a&&-1}},qa={includes:pa(!0),indexOf:pa(!1)},ra=qa.indexOf,sa=function(a,b){var c,d=x(a),e=0,f=[];for(c in d)!B(Y,c)&&B(d,c)&&f.push(c);for(;b.length>e;)B(d,c=b[e++])&&(~ra(f,c)||f.push(c));return f},ta=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),ua=Object.getOwnPropertyNames||function(a){return sa(a,ta)},va={f:ua},wa=Object.getOwnPropertySymbols,xa={f:wa},ya=function(a,b){return 2>arguments.length?ia(ha[a])||ia(k[a]):ha[a]&&ha[a][b]||k[a]&&k[a][b]}("Reflect","ownKeys")||function(a){var b=va.f(J(a)),c=xa.f;return c?b.concat(c(a)):b},za=function(a,b){for(var c,d=ya(b),e=M.f,f=I.f,g=0;g<d.length;g++)c=d[g],B(a,c)||e(a,c,f(b,c))},Aa=/#|\.prototype\./,Ba=function(a,b){var c=Da[Ca(a)];return!(c!=Fa)||c!=Ea&&("function"==typeof b?l(b):!!b)},Ca=Ba.normalize=function(a){return(a+"").replace(Aa,".").toLowerCase()},Da=Ba.data={},Ea=Ba.NATIVE="N",Fa=Ba.POLYFILL="P",Ga=I.f,Ha=Array.isArray||function(a){return"Array"==t(a)},Ia=function(a){return Object(w(a))},Ja=function(a,b,c){var d=z(b);d in a?M.f(a,d,r(0,c)):a[d]=c},Ka=!!Object.getOwnPropertySymbols&&!l(function(){return!(Symbol()+"")}),La=k.Symbol,Ma=Q("wks"),Na=function(a){return Ma[a]||(Ma[a]=Ka&&La[a]||(Ka?La:V)("Symbol."+a))},Oa=Na("species"),Pa=function(a,b){var c;return Ha(a)&&(c=a.constructor,"function"==typeof c&&(c===Array||Ha(c.prototype))?c=void 0:y(c)&&(c=c[Oa],null===c&&(c=void 0))),new(void 0===c?Array:c)(0===b?0:b)},Qa=Na("species"),Ra=Na("isConcatSpreadable"),Sa=9007199254740991,Ta="Maximum allowed index exceeded",Ua=!l(function(){var a=[];return a[Ra]=!1,a.concat()[0]!==a}),Va=function(a){return!l(function(){var b=[],c=b.constructor={};return c[Qa]=function(){return{foo:1}},1!==b[a](Boolean).foo})}("concat"),Wa=function(a){if(!y(a))return!1;var b=a[Ra];return b===void 0?Ha(a):!!b};(function(a,b){var c,d,e,f,g,h,i=a.target,j=a.global,l=a.stat;if(d=j?k:l?k[i]||P(i,{}):(k[i]||{}).prototype,d)for(e in b){if(g=b[e],a.noTargetGet?(h=Ga(d,e),f=h&&h.value):f=d[e],c=Ba(j?e:i+(l?".":"#")+e,a.forced),!c&&void 0!==f){if(typeof g==typeof f)continue;za(g,f)}(a.sham||f&&f.sham)&&N(g,"sham",!0),ga(d,e,g,a)}})({target:"Array",proto:!0,forced:!Ua||!Va},{concat:function(){var a,b,c,d,e,f=Ia(this),g=Pa(f,0),h=0;for(a=-1,c=arguments.length;a<c;a++)if(e=-1===a?f:arguments[a],Wa(e)){if(d=ma(e.length),h+d>Sa)throw TypeError(Ta);for(b=0;b<d;b++,h++)b in e&&Ja(g,h,e[b])}else{if(h>=Sa)throw TypeError(Ta);Ja(g,h++,e)}return g.length=h,g}}),a.fn.bootstrapTable.locales["zh-CN"]={formatLoadingMessage:function(){return"\u6B63\u5728\u52AA\u529B\u5730\u52A0\u8F7D\u6570\u636E\u4E2D\uFF0C\u8BF7\u7A0D\u5019"},formatRecordsPerPage:function(a){return"\u6BCF\u9875\u663E\u793A ".concat(a," \u6761\u8BB0\u5F55")},formatShowingRows:function(a,b,c,d){return void 0!==d&&0<d&&d>c?"\u663E\u793A\u7B2C ".concat(a," \u5230\u7B2C ").concat(b," \u6761\u8BB0\u5F55\uFF0C\u603B\u5171 ").concat(c," \u6761\u8BB0\u5F55\uFF08\u4ECE ").concat(d," \u603B\u8BB0\u5F55\u4E2D\u8FC7\u6EE4\uFF09"):"\u663E\u793A\u7B2C ".concat(a," \u5230\u7B2C ").concat(b," \u6761\u8BB0\u5F55\uFF0C\u603B\u5171 ").concat(c," \u6761\u8BB0\u5F55")},formatSRPaginationPreText:function(){return"\u4E0A\u4E00\u9875"},formatSRPaginationPageText:function(a){return"\u7B2C".concat(a,"\u9875")},formatSRPaginationNextText:function(){return"\u4E0B\u4E00\u9875"},formatDetailPagination:function(a){return"\u603B\u5171 ".concat(a," \u6761\u8BB0\u5F55")},formatClearSearch:function(){return"\u6E05\u7A7A\u8FC7\u6EE4"},formatSearch:function(){return"\u641C\u7D22"},formatNoMatches:function(){return"\u6CA1\u6709\u627E\u5230\u5339\u914D\u7684\u8BB0\u5F55"},formatPaginationSwitch:function(){return"\u9690\u85CF/\u663E\u793A\u5206\u9875"},formatPaginationSwitchDown:function(){return"\u663E\u793A\u5206\u9875"},formatPaginationSwitchUp:function(){return"\u9690\u85CF\u5206\u9875"},formatRefresh:function(){return"\u5237\u65B0"},formatToggle:function(){return"\u5207\u6362"},formatToggleOn:function(){return"\u663E\u793A\u5361\u7247\u89C6\u56FE"},formatToggleOff:function(){return"\u9690\u85CF\u5361\u7247\u89C6\u56FE"},formatColumns:function(){return"\u5217"},formatColumnsToggleAll:function(){return"\u5207\u6362\u6240\u6709"},formatFullscreen:function(){return"\u5168\u5C4F"},formatAllRows:function(){return"\u6240\u6709"},formatAutoRefresh:function(){return"\u81EA\u52A8\u5237\u65B0"},formatExport:function(){return"\u5BFC\u51FA\u6570\u636E"},formatJumpTo:function(){return"\u8DF3\u8F6C"},formatAdvancedSearch:function(){return"\u9AD8\u7EA7\u641C\u7D22"},formatAdvancedCloseButton:function(){return"\u5173\u95ED"}},a.extend(a.fn.bootstrapTable.defaults,a.fn.bootstrapTable.locales["zh-CN"])});
