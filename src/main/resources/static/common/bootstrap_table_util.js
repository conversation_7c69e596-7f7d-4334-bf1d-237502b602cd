$(function () {
    function init(table, url, columns, hasCheckbox, toolbar, way, pagination) {

        if (toolbar == '') {
            toolbar = toolbar
        }

        $(table).bootstrapTable({
            url: url,                           //请求后台的URL（*）
            method: 'post', //请求方式（*）
            contentType: "application/x-www-form-urlencoded; charset=UTF-8",
            toolbar: toolbar,                   //工具按钮用哪个容器
            striped: true,                      //是否显示行间隔色
            cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
            pagination: pagination,                   //是否显示分页（*）
            sortable: false,                    //是否启用排序
            sortOrder: "asc",                   //排序方式
            queryParamsType: '',
            queryParams: queryParams,           //传递参数（*），这里应该返回一个object，即形如{param1:val1,param2:val2}
            sidePagination: way,           //分页方式：client客户端分页，server服务端分页（*）
            pageNumber: 1,                       //初始化加载第一页，默认第一页
            pageSize: 10,                       //每页的记录行数（*）
            // pageList: [20, 50, 100],            //可供选择的每页的行数（*）
            search: false,                       //是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
            strictSearch: true,
            showColumns: true,                  //是否显示所有的列
            showRefresh: true,                  //是否显示刷新按钮
            minimumCountColumns: 2,             //最少允许的列数
            clickToSelect: true,                //是否启用点击选中行
            //height: 500,                      //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
            uniqueId: "id",                     //每一行的唯一标识，一般为主键列
            showToggle: false,                    //是否显示详细视图和列表视图的切换按钮
            cardView: false,                    //是否显示详细视图
            detailView: false,                  //是否显示父子表
            columns: columns,
            responseHandler: function (res) {
                if (res.size != undefined) {
                    var result = [];
                    result.push({total: res.total, rows: res.list});
                    return result[0];
                } else {
                    return res
                }
            }
        });
    }

    function queryParams(params) {
        var pageParam = serializeObject("searchForm");
        pageParam["pageNum"] = params.pageNumber;//页码
        pageParam["pageSize"] = params.pageSize; //页面大小
        return pageParam;
    }

    var handler = '';

    createBootstrapTable = function (table, url, columns, hasCheckbox, toolbar, way, pagination) {
        init(table, url, columns, hasCheckbox, toolbar, way, pagination ? false : true);
    }

});

var toolbar =

    '<div id="toolbar" class="btn-group">\n' +
    '        <button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="add()"><i class="fa  fa-plus"></i>新增</button>' +
    '        <button id="btn_delete" type="button" class="btn btn-danger btn-xs" onclick="deletes()"><i class="fa fa-trash-o"></i>删除</button>' +
    ' </div>';


// 回显数据字典标签
function selectDictLabel(_datas, _value) {
    var actions = [];
    $.each(_datas, function (index, dict) {
        if (dict.dictValue == _value) {
            actions.push(dict.dictLabel);
            return false;
        }
    });
    if (actions.length == 0) {
        actions.push(_value);
    }
    return actions.join('');
}

// 根据数据字典初始化select的option
function selectOptionInitDict(_datas, _value) {
    var actions = [];
    $.each(_datas, function (index, dict) {
        if (dict.dictValue == _value) {
            actions.push('<option value="' + dict.dictValue + '" selected>' + dict.dictLabel + '</option>');
        } else {
            actions.push('<option value="' + dict.dictValue + '" >' + dict.dictLabel + '</option>');
        }
    });
    return actions.join('');
}

// 根据数据字典初始化radio
function radioInitDict(_datas, radioName, _value) {
    var actions = [];
    $.each(_datas, function (index, dict) {
        if (dict.dictValue == _value) {
            actions.push('<label class="checkbox-inline i-checks"> <input type="radio" name="' + radioName + '" value="' + dict.dictValue + '" checked/>' + dict.dictLabel + '</label> ');
        } else {
            actions.push('<label class="checkbox-inline i-checks"> <input type="radio" name="' + radioName + '" value="' + dict.dictValue + '"/>' + dict.dictLabel + '</label> ');
        }
    });
    return actions.join('');
}

// 根据数据字典初始化checkbox
function checkboxInitDict(_datas, checkboxName, _value) {
    var actions = [];
    $.each(_datas, function (index, dict) {
        if (dict.dictValue == _value) {
            actions.push('<label class="checkbox-inline i-checks"> <input type="checkbox" name="' + checkboxName + '" value="' + dict.dictValue + '" checked/>' + dict.dictLabel + '</label> ');
        } else {
            actions.push('<label class="checkbox-inline i-checks"> <input type="checkbox" name="' + checkboxName + '" value="' + dict.dictValue + '"/>' + dict.dictLabel + '</label> ');
        }
    });
    return actions.join('');
}

/**
 * 通过ajax获取数据字典
 * @param type
 * @param dataArray
 */
function getDictData(type, dataArray, funcName, defaultVale) {
    $.ajax({
        url: '/admin/sysDict/getDataDictByType?dictType=' + type,
        type: 'get',
        cache: false,
        async: false,
        dataType: "json",
        success: function (data) {
            //绑定数据
            // dataArray=JSON.parse(data);
            dataArray = data;
            if (funcName != null) {
                funcName(dataArray, defaultVale);
            }
        },
        error: function (data) {
            layer.msg("获取字典系统异常！")
        }
    });
    return dataArray;
}

function onLoads(href) {
    $("#href").val(href)
    $("#loadhtml").load(href)
}

function getDataName(dataArray, defaultValue) {
    var resultName = defaultValue;
    $.each(dataArray, function (index, node) {
        if (defaultValue == '0') {
            resultName = '';
        }
        if (node.id == defaultValue) {
            resultName = node.name;
        }
    });
    return resultName;
}

function getData(url, dataArray) {
    $.ajax({
        url: url,
        type: 'get',
        async: false,
        dataType: "json",
        success: function (data) {
            if (data) {
                dataArray = data;
            }
        },
        error: function (data) {
            layer.msg("获取数据异常！")
        }
    });
    return dataArray;
}