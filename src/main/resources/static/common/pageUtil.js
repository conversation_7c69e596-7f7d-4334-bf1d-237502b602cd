/**
 * JS分页组件 数据格式参考 mybatis PageHelper 查询的分页方法看会的数据格式
 * @param data
 * @returns {string}
 */
function pagination(data,findList) {

    var count = data.total; // 总数
    var firstPage = data.firstPage; // 首页
    var pageNum = data.pageNum; //当前页
    var prevPage = pageNum - 1; //  上一页
    var nextPage = data.nextPage; // 下一页
    var lastPage = data.pages; // 最后一页

    var length = 5;
    var slider = 2;

    var html;

    if (pageNum == firstPage) {
        html = ' <li class="previous disabled" aria-controls="searchlist" tabindex="0" id="searchlist_previous"><a href="javascript:void(0)" class="prev" title="上一页"><i class="iconfont icon-arrow-left"></i></a><>'
    } else {
        html = '<li class="previous" aria-controls="searchlist" tabindex="0" id="searchlist_previous"><a href="javascript:void(0)" class="prev" title="上一页" onclick="'+findList+'(' + prevPage + ')"><i class="iconfont icon-arrow-left"></i></a><>'
    }

    var start = 0;
    if (pageNum == length) {
        start = 1;
    }
    if (pageNum > length && pageNum < lastPage - slider) {
        start = pageNum - length + 1;
    }

    if (pageNum >= lastPage - slider && pageNum - length >1) {
        start = pageNum - length;
    }

    if (start > 0 && start < lastPage - slider) {
        length = length + start
    } else {
        start = 0;
    }

    for (i = start; i < length; i++) {

        var index = i + 1;

        if (index == pageNum && index < lastPage - slider) {
            html = html + '<li aria-controls="searchlist" tabindex="0" id=""><a href="javascript:void(0)">' + index + '</a><>'
        } else {
            if (index <= lastPage - slider) {
                html = html + '<li aria-controls="searchlist" tabindex="0" id=""><a href="javascript:void(0)" onclick="'+findList+'(' + index + ')">' + index + '</a><>'
            }
        }

        if (index == length && index < lastPage - slider) {
            html = html + '<li aria-controls="searchlist" tabindex="0"><a href="javascript:void(0)" class="dot">...</a><>'
        }
    }


    if (lastPage > 1) {
        for (var i = 1; i <= slider; i++) {
            var index = lastPage - slider + i
            html = html + '<li aria-controls="searchlist" tabindex="0" id=""><a href="javascript:void(0)" onclick="'+findList+'(' + index + ')">' + index + '</a><>'
        }
    }else {
        html = html + '<li aria-controls="searchlist" tabindex="0" id=""><a href="javascript:void(0)">' + firstPage + '</a><>'
    }



    if (pageNum == lastPage) {
        html = html + '<li class="next disabled" aria-controls="searchlist" tabindex="0" id="searchlist_next"><a href="javascript:void(0)" class="next" title="下一页"><i class="iconfont icon-arrow-right"></i></a><>'
    } else {
        html = html + '<li class="next" aria-controls="searchlist" tabindex="0" id="searchlist_next"><a href="javascript:void(0)" class="next" title="下一页" onclick="'+findList+'(' + nextPage + ')"><i class="iconfont icon-arrow-right"></i></a><>'
    }
    return html;
}
