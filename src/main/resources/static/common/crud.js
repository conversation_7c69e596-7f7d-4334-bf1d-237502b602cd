var popForm;

//增加按钮
function add(contentId, FormId) {
    restForm(FormId);
    poplayer(1, "新增", contentId);
}

//新增或者更新
function save(url, FormId) {//前端校验

    var data = $("#" + FormId).serializeArray();//将页面表单序列化成一个JSON结构的对象。注意不是JSON字符串
    request(url, 1, data, function (res) {
        if (res.code == 0) {
            layer.close(popForm)
            layer.msg("保存成功", {time: 1000}, function () {
                // onLoads($("#href").val())
                parent.location.reload();
            })
        } else {
            layer.msg(res.msg, {time: 1000, icon: 5})
        }
    }, function (error) {
        layer.msg("系统异常！")
    })
}

//layer弹出层保存按钮
function savepop(url, FormId) {
    var data = $("#" + FormId).serializeArray();//将页面表单序列化成一个JSON结构的对象。注意不是JSON字符串
    request(url, 1, data, function (res) {
        if (res.code == 0) {
            layer.msg("保存成功", {time: 1000}, function () {
                parent.location.reload();
                parent.layer.close(index);
            });
        } else {
            layer.msg(res.msg, {time: 1000, icon: 5});
        }
    }, function (error) {
        layer.msg("系统异常！");
    });

}

function update(url, id, contentId) {
    var data = {
        id: id
    };
    request(url, 0, data, function (data) {
        $.each(data.data, function (name, ival) {
            var $oinput = $("#" + contentId).find("[name=" + name + "]");
            if ($oinput.attr("type") == "radio" || $oinput.attr("type") == "checkbox") {
                $oinput.each(function () {
                    if (Object.prototype.toString.apply(ival) == '[object Array]') {//是复选框，并且是数组
                        for (var i = 0; i < ival.length; i++) {
                            if ($(this).val() == ival[i])
                                $(this).attr("checked", "checked");
                        }
                    } else {
                        if ($(this).val() == ival) {
                            $(this).attr("checked", "checked");
                        }
                    }
                });
            } else if ($oinput.prop("tagName") == "textarea") {//多行文本框

                $oinput.html(ival);
            } else {
                if ($oinput.is('.select2')) {
                    $oinput.val(ival).trigger('change');
                } else {
                    $oinput.val(ival);
                }

            }
        })
    }, function (error) {
        layer.msg("系统异常！")
    });


    poplayer(1, "修改", contentId);
}

//删除
function deletes(url, id) {
    var ids = [];
    if (id != undefined) {
        ids.push(id)
    } else {
        $.each($('tbody input:checkbox:checked'), function () {
            ids.push($(this).parents('tr').data('uniqueid'));
        });
    }
    if (ids.length < 1) {
        layer.msg("最少请选择一条数据！")
        return;
    }
    var data = {
        ids: ids.toString()
    };

    layer.confirm('确定删除吗？', {
        title: '提示', btn: ['取消', '确定'] //按钮
    }, function () {
        layer.close(layer.index)
    }, function () {
        request(url, 1, data, function (data) {
            if (data.code != -1) {
                layer.msg("删除成功", {time: 1000}, function () {
                    // onLoads($("#href").val())
                    location.reload();
                })
            } else {
                layer.msg(data.msg, {time: 1000, icon: 5})
            }
        }, function (error) {
            layer.msg("系统异常！")
        })
    });
}


function deleteOne(url, id) {
    var data = {
        id: id
    };
    layer.confirm('确定删除吗？', {
        title: '提示', btn: ['取消', '确定'] //按钮
    }, function () {
        layer.close(layer.index)
    }, function () {
        request(url, 1, data, function (data) {
            if (data.code != -1) {
                layer.msg("删除成功", {time: 1000}, function () {
                    // onLoads($("#href").val())
                    location.reload();
                })
            } else {
                layer.msg(data.msg, {time: 1000, icon: 5})
            }
        }, function (error) {
            layer.msg("系统异常！")
        })
    });
}

// 查询
function formSearch(tableId, url, FormId) {
    var data = serializeObject(FormId);
    var opt = {
        url: url,
        silent: true,
        query: data
    };
    $("#" + tableId).bootstrapTable('refresh', opt);
}

//查询条件重置
function restForm(FormId) {
    document.getElementById(FormId).reset();
}

function execute(url, id) {
    var data = {id: id};
    layer.confirm('确定执行吗？', {
        title: '提示', btn: ['确定', '取消'] //按钮
    }, function () {
        // layer.close(layer.index)
    }, function () {
        request(url, 1, data, function (data) {
            if (data.code != -1) {
                layer.msg(data.msg, {time: 1000}, function () {
                    // onLoads($("#href").val())
                    location.reload();
                })
            } else {
                layer.msg(data.msg, {time: 1000, icon: 5})
            }
        }, function (error) {
            layer.msg("系统异常！")
        })
    });
}


/**
 * 弹出层
 * @param type 1：html，2 url
 * @param title 标题
 * @param content   type=1时  div的id，type=2时 url地址
 */
function poplayer(type, title, content) {
    var contentIdOrUrl = "";
    var endexec = null;
    if (1 == type) {
        contentIdOrUrl = $("#" + content);
        endexec = function () {
            // $('#' + content).hide();
        }
    } else {
        contentIdOrUrl = content
    }
    popForm = layer.open({
        type: type,
        // area: ['50%', '80%'],
        area: ['80%', '90%'],
        title: title,
        shade: 0.6,
        maxmin: true,
        shadeClose: true, //点击遮罩关闭层
        closeBtn: 1,
        content: contentIdOrUrl,
        // success: function (layero, index) {
        //     layer.iframeAuto(index);
        // },
        end: endexec
    });
}

function popInit(url, formId, id) {

    if ((!id) || (!formId) || (!url)) {
        return false;
    }
    if ('' == id || 'null' == id) {
        return false;
    }
    var data = {
        id: id
    };
    request(url, 0, data, function (data) {
        if (data.code != 0) {
            layer.msg(data.msg);
        }
        $.each(data.data, function (name, ival) {
            var $oinput = $("#" + formId).find("[name=" + name + "]");
            if ($oinput.attr("type") == "radio" || $oinput.attr("type") == "checkbox") {
                $oinput.each(function () {
                    if (Object.prototype.toString.apply(ival) == '[object Array]') {//是复选框，并且是数组
                        for (var i = 0; i < ival.length; i++) {
                            if ($(this).val() == ival[i])
                                $(this).attr("checked", "checked");
                        }
                    } else {
                        if ($(this).val() == ival) {
                            $(this).attr("checked", "checked");
                        }
                    }
                });
            } else if ($oinput.prop("tagName") == "textarea") {//多行文本框
                if ($oinput.is('.ckeditor')) {
                    // $oinput.html(ival);
                    // 富文本编辑器内容回显
                    editor.setData(ival);
                } else {
                    $oinput.html(ival);
                }

            } else {
                if ($oinput.is('.select2')) {
                    $oinput.val(ival).trigger('change');
                } else {
                    $oinput.val(ival);
                }

            }
        })
    }, function (error) {
        layer.msg("系统异常！")
    })
}


/**
 * 弹出层 编辑
 * @param id
 * @param title 标题
 * @param url
 */
function updatelayer(id,title,url) {
    if(undefined == id || null == id || '' == id ){
        return ;
    }
    if(undefined == url || null == url || '' == url ){
        return ;
    }
    url = url+id;
    var printForm = layer.open({
        type: 2,
        area : ['80%', '90%'],
        title: title,
        shade: 0.6,
        maxmin: false,
        shadeClose: true, //点击遮罩关闭层
        closeBtn: 1,
        content: url,
        success: function (layero, index) {
            // layer.iframeAuto(index);
        }
    });
}


/**
 * 弹出层 详情
 * @param id
 * @param title 标题
 * @param url
 */
function detaillayer(id,title,url) {
    if(undefined == id || null == id || '' == id ){
        return ;
    }
    if(undefined == url || null == url || '' == url ){
        return ;
    }
    url = url+id;
    layer.open({
        type: 2,
        area : ['80%', '90%'],
        title: title,
        shade: 0.6,
        maxmin: false,
        shadeClose: true, //点击遮罩关闭层
        closeBtn: 1,
        content: url,
        success: function (layero, index) {
            // layer.iframeAuto(index);
        }
    });
}