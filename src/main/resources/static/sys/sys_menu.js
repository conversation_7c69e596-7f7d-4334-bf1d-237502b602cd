var select2;
$(function () {
    select2=$('.select2').select2();
    var toolbar1 =
        '<div id="toolbar" class="btn-group">\n' +
        '        <button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="add()">\n' +
        '             <i class="fa  fa-plus"></i>新增\n' +
        '        </button>\n' +
        ' </div>'

    var columns = [
        {
            field: 'name',
            title: '名称',
            formatter: function (value, row, index) {
                if (row.icon == null || row.icon == '') {
                    return value
                } else {
                    return '<i class="' + row.icon + '"><span>' + value + '</span></i>'
                }
            }
        }, {
            field: 'sort',
            title: '排序',
            align: 'center'
        }, {
            field: 'href',
            title: '请求地址',
            align: 'center'
        }, {
            field: 'isShows',
            title: '可见',
            align: 'center',
            formatter: function (value, row, index) {
                if (row.isShows == null || row.isShows  == '1') {
                    return '<button id="btn_edit" type="button" class="btn btn-warning disabled  btn-xs" >隐藏</button>'
                } else {
                    return '<button id="btn_edit" type="button" class="btn btn-success disabled  btn-xs" >显示</button>'
                }
            }
        },
        {
            field: 'permission',
            title: '权限值',
            align: 'center'
        },
        {
            field: 'menuType',
            title: '类型',
            align: 'center',
            formatter: function (value, row, index) {
                if (row.menuType  == '0') {
                    return '<button id="btn_edit" type="button" class="btn bg-navy disabled  btn-xs" >目录</button>'
                } else  if (row.menuType  == '1'){
                    return '<button id="btn_edit" type="button" class="btn bg-purple disabled btn-xs" >菜单</button>'
                }else  if (row.menuType  == '2'){
                    return '<button id="btn_edit" type="button" class="btn bg-orange disabled  btn-xs" >按钮</button>'
                }
            }
        },
        {
            field: 'id',
            title: '操作',
            align: 'center',
            formatter: function (value, row, index) {
                // '<button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="add()"><i class="fa  fa-plus"></i>新增</button>'+
                return '<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="update(\'' + value + '\')"><i class="fa fa-edit"></i> 修改</button>' +
                '<button id="btn_edit" type="button" class="btn btn-danger btn-xs" style="margin-left: 10px" onclick="deletes(\'' + value + '\')"><i class="fa fa-trash-o"></i> 删除</button>';
            }
        }
    ]
    $("#table").bootstrapTreeTable({
        code: 'id',
        parentCode: 'parentId',
        type: 'get',
        url: '/admin/sysMenu/info',
        striped: false,
        expandColumn: '0',
        bordered: true,
        columns: columns,
        toolbar: toolbar1,
    });

    //treeUrl,treeObjId,treeType, nameInputId,  valueInputId
    initTree('/admin/sysMenu/zTreeNodes', 'typeTree', 0, 'parentName', 'parentId');

    $('input[type="checkbox"].flat-red, input[type="radio"].flat-red').iCheck({
        checkboxClass: 'icheckbox_flat-green',
        radioClass: 'iradio_flat-green'
    })

})

//清空表单
function clearForm() {
    $("#menuId").val("")
    $("#name").val("")
    $("#parentId").val("")
    $("#parentName").val("")
    $("#hrefs").val("")
    $("#permission").val("")
    $("#icon").val("")
    $("#sort").val("")
    $("#menuType").val("")
}

function openMenuTree() {
    openTree('zTree');
}


/**
 * 新增
 */
var menuForm;

function add() {
    clearForm()
    menuForm = layer.open({
        type: 1,
        area: (window.innerWidth>992)? "600px":"auto",
        title: '新增',
        shade: 0.6,
        shadeClose: true, //点击遮罩关闭层
        maxmin: false,
        anim: 1,
        content: $("#menu_form"),
        success: function (layero, index) {
            layer.iframeAuto(index);
        },
        end:function () {
            $("#menu_form").hide();
        }
    });
   // select2.val('1').trigger("change");
   //  select2.change('1')
}

function save() {
    var id = $("#menuId").val()
    var name = $("#name").val();
    var parentId = $("#parentId").val();
    var href = $("#hrefs").val();
    var permission = $("#permission").val();
    var menuType = $("#menuType").val();
    var icon = $("#icon").val();
    var sort = $("#sort").val();
    var isShow = $("input[name='isShow']:checked").val()

    if (name == '' || name == null) {
        layer.msg("请输入名称！")
        return
    }

    var data = {
        id: id,
        name: name,
        parentId: parentId,
        href: href,
        permission: permission,
        icon: icon,
        sort: sort,
        isShows: isShow,
        menuType:menuType
    }

    request('/admin/sysMenu/save', 1, data, function (res) {
        if (res.code == 0) {
            layer.close(menuForm)
            layer.msg("保存成功", {time: 1000}, function () {
                // onLoads($("#href").val())
                location.reload();
            })
        }else {
            layer.msg(res.msg, {time: 1000,icon:5})
        }
    }, function (error) {
        layer.msg("系统异常！")
    })
}

/**
 * 修改
 */
function update(id) {
    clearForm()
    var data = {
        id: id
    }
    request('/admin/sysMenu/getById', '0', data, function (data) {
        $("#menuId").val(data.id)
        $("#name").val(data.name)
        $("#parentId").val(data.parentId)
        $("#parentName").val(data.parentName)
        $("#hrefs").val(data.href)
        $("#permission").val(data.permission)
        $("#icon").val(data.icon)
        $("#sort").val(data.sort);
        var r = data.isShows
        //  0 show
        //  1 false
        if( r==1 ){
            $("input[name='isShow']#hidden").iCheck('check')
        }else{
            $("input[name='isShow']#show").iCheck('check')
        }
        // select2.val(data.menuType).trigger("change");
        //select2.change(data.menuType)
    }, function () {
        layer.msg("系统异常！")
    })
    menuForm = layer.open({
        type: 1,
        area: (window.innerWidth>992)? "600px":"auto",
        title: '修改',
        shade: 0.6,
        shadeClose: true, //点击遮罩关闭层
        anim: 1,
        content: $("#menu_form"),
        success: function (layero, index) {
            layer.iframeAuto(index);
        },
        end:function () {
            $("#menu_form").hide();
        }
    });
}

function deletes(id) {
    var data = {
        id: id
    }

    layer.confirm('如存在下级节点会一并删除，确定删除吗？', {
        title: '提示', btn: ['取消', '确定']
    }, function () {
        layer.close(layer.index)
    }, function () {
        request('/admin/sysMenu/delete', 1, data, function (res) {
            if (res.code == 0) {
                layer.msg("删除成功", {time: 1000}, function () {
                    // onLoads($("#href").val())
                    location.reload();
                })
            } else {
                layer.msg(res.msg, {time: 1000})
            }
        }, function (error) {
            layer.msg("系统异常！")
        })
    });
}

function changeForm() {

    var menuType = $("#menuType").val();

    if (menuType == '0'){
        $("#show_tree").hide()
        $("#show_href").hide()
        $("#show_perm").hide()
        $("#show_sort").show()
        $("#show_icon").show()
        $("#show_status").show()
        //目录
        $("#icon").val('fa fa-folder');
    }
    if (menuType == '1') {
        $("#show_tree").show()
        $("#show_href").show()
        $("#show_perm").show()
        $("#show_sort").show()
        $("#show_icon").show()
        $("#show_status").show()
        //菜单
        $("#icon").val('fa fa-file-text');
    }
    if (menuType == '2'){
        $("#show_perm").show()
        $("#show_href").hide()
        $("#show_sort").hide()
        $("#show_icon").hide()
        $("#show_status").hide()
        $("#icon").val('');
    }
}
