var dictForm;
$(function () {
    var columns = [
        {
            field: 'ck',
            checkbox: true
        },
        {
            field: 'id',
            title: '字典id',
            visible: false
        },
        {
            field: 'dictType',
            title: '字典类型'
        },{
            field: 'dictName',
            title: '字典名称'
        },

        {
            field: 'dictValue',
            title: '字典值'
        },
        {
            field: 'dictLabel',
            title: '字典标签'
        },
        {
            field: 'sort',
            title: '排序'
        },
        {
            field: 'status',
            title: '状态',
            formatter: function (value, row, index) {
                if (row.status == null || row.status == '1') {
                    return '<button id="btn_edit" type="button" class="btn btn-warning disabled btn-xs" >停用</button>'
                } else {
                    return '<button id="btn_edit" type="button" class="btn btn-success disabled btn-xs" >启用</button>'
                }
            }
        },
        {
            field: 'createTime',
            title: '创建时间'
        }, {
            field: 'id',
            title: '操作',
            formatter: function (value, row, index) {
                return '<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="update(\'' + value + '\')"><i class="fa fa-edit"></i> 修改</button>' +
                    '<button type="button" class="btn btn-danger  btn-xs" onclick="deletes(\'' + value + '\')" style="margin-left: 10px;"><i class="fa fa-trash-o"></i> 删除</button>';
            }
        }
    ]
    createBootstrapTable($("#table"), '/admin/sysDict/list', columns, true, toolbar, 'server')
})

// 清空表单
function clearForm() {
    // $("#label").val("")
    // $("#name").val("")
    // $("#dictType").val("")
    // $("#dictValue").val("")
    // $("#remarks").val("")
    $("#dictId").val("");
    $("#dictLabel").val("");
    $("#dictName").val("");
    $("#dictType").val("");
    $("#dictValue").val("");
    $("#sort").val("");
    $("input[name='status']:first").attr("checked", true);
    $("#remarks").val("");
}

//点击新增事件控制
function add() {
    clearForm()
    dictForm = layer.open({
        type: 1,
        area: '600px',
        title: '新增',
        shade: 0.6,
        shadeClose: true, //点击遮罩关闭层
        maxmin: false,
        anim: 1,
        content: $("#dict_form"),
        success: function (layero, index) {
            layer.iframeAuto(index);
        }  ,end:function () {
            $("#dict_form").hide()
        }
    });
}

//新增用户
function save() {
    var id = $("#dictId").val()
    var dictLabel = $("#dictLabel").val()
    var dictName = $("#dictName").val()
    var dictType = $("#dictType").val();
    var dictValue = $("#dictValue").val();
    var status = $("#status").val();
    var sort = $("#sort").val();
    var remarks = $("#remarks").val();

    if (dictName == null || dictName == '') {
        return layer.msg('请输入字典名称')
    }
    if (dictLabel == null || dictLabel == '') {
        return layer.msg('请输入字典标签')
    }
    if (dictType == null || dictType == '') {
        return layer.msg('请输入字典类型')
    }
    if (dictValue == null || dictValue == '') {
        return layer.msg('请输入字典值')
    }

    var data = {
        id: id,
        dictLabel: dictLabel,
        dictName: dictName,
        dictType: dictType,
        dictValue: dictValue,
        status: status,
        sort: sort,
        remarks: remarks
    }
    request('/admin/sysDict/save', 1, data, function (res) {
        if (res.code == 0) {
            layer.close(dictForm)
            layer.msg("保存成功", {time: 1000}, function () {
                // onLoads($("#href").val())
                location.reload();
            })
        } else {
            layer.msg(res.msg, {time: 1000, icon: 5})
        }
    }, function (error) {
        layer.msg("系统异常！")
    })
}

//查询当前用户数据进行修改
function update(id) {
    clearForm()
    var data = {
        id: id
    }
    request('/admin/sysDict/get', 0, data, function (data) {

        // console.log(data)

        $("#dictId").val(data.data.id);
        $("#dictLabel").val(data.data.dictLabel);
        $("#dictName").val(data.data.dictName);
        $("#dictType").val(data.data.dictType);
        $("#dictValue").val(data.data.dictValue);
        $("#sort").val(data.data.sort);
        var r = data.status;
        $("input[name='status'][value=" + r + "]").attr("checked", true);
        $("#remarks").val(data.data.remarks);
    }, function (error) {
        layer.msg("系统异常！")
    })
    dictForm = layer.open({
        type: 1,
        area: '650px',
        title: '修改',
        shade: 0.6,
        shadeClose: true, //点击遮罩关闭层
        maxmin: false,
        anim: 1,
        content: $("#dict_form"),
        success: function (layero, index) {
            layer.iframeAuto(index);
        }  ,end:function () {
            $("#dict_form").hide()
        }
    });
}

function deletes(id) {
    var ids = [];
    if (id != undefined) {
        ids.push(id)
    } else {
        $.each($('tbody input:checkbox:checked'), function () {
            ids.push($(this).parents('tr').data('uniqueid'));
        });
    }
    if (ids.length < 1) {
        layer.msg("最少请选择一条数据！")
        return;
    }
    var data = {
        ids: ids.toString()
    }

    layer.confirm('确定删除吗？', {
        title: '提示', btn: ['取消', '确定'] //按钮
    }, function () {
        layer.close(layer.index)
    }, function () {
        request('/admin/sysDict/delete', 1, data, function (data) {
            if (data.code != -1) {
                layer.msg("删除成功", {time: 1000}, function () {
                    // onLoads($("#href").val())
                    location.reload();
                })
            } else {
                layer.msg(data.msg, {time: 1000, icon: 5})
            }
        }, function (error) {
            layer.msg("系统异常！")
        })
    });


}


function formSearch() {
    var dictName = $("#searchDictName").val()
    var dictLabel = $("#searchDictLabel").val()

    var data = {
        dictName: dictName,
        dictLabel: dictLabel
    }
    var opt = {
        url: '/admin/sysDict/list',
        silent: true,
        query: data
    };
    $("#table" ).bootstrapTable('refresh', opt);
}

function restForm() {
    document.getElementById("searchForm").reset()
    formSearch();

}

function updateDataDictCache() {

    request('/admin/sysDict/updateDataDictCache', 1, null, function (data) {
        if (data.code != -1) {
            layer.msg(data.msg, {time: 1000});
        } else {
            layer.msg(data.msg, {time: 1000, icon: 5})
        }
    }, function (error) {
        layer.msg("系统异常！")
    });
}