$(function () {
    $('input').iCheck({
        checkboxClass: 'icheckbox_square-blue',
        radioClass: 'iradio_square-blue',
        increaseArea: '10%' /* optional */
    });

    $('.imgcode').click(function () {
        var url = ctx + "captcha/captchaImage?type=" + captchaType + "&s=" + Math.random();
        $(".imgcode").attr("src", url);
    });

    $('form').bootstrapValidator({
        message: 'This value is not valid',
        feedbackIcons: {
            valid: 'glyphicon glyphicon-ok',
            invalid: 'glyphicon glyphicon-remove',
            validating: 'glyphicon glyphicon-refresh'
        },
        fields: {
            name: {
                message: '用户名验证失败',
                validators: {
                    notEmpty: {
                        message: '用户名不能为空'
                    }
                }
            },
            password: {
                validators: {
                    notEmpty: {
                        message: '请输入密码'
                    }
                }
            }
        }
    });
});
function changePassword(uid,password){
    $("#id").val(uid);
    $("#oldPassword").val(password);
    //修改密码
    layer.open({
        title :'修改密码',
        area: ['500px', '300px'],
        type: 1,
        shadeClose: true, //开启遮罩关闭
        content: $("#changePassword"),
        btn: ['确定', '取消'],
        success: function(layero, index){
            $("#oldPassword").val("");
            $("#newPassword").val("");
            $("#newPassword2").val("");
        },
        btn1: function(index, layero){
            var obj = {};
            obj.password = $("#newPassword").val();
            var newPassword2 = $("#newPassword2").val();

            if(obj.password){
                // if(/^(?![A-Za-z0-9]+$)(?![a-z0-9\W]+$)(?![A-Za-z\W]+$)(?![A-Z0-9\W]+$)[a-zA-Z0-9\W]{8,}$/.test(obj.password) || /(?![0-9A-Z]+$)(?![0-9a-z]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,18}$/.test(obj.password)){
                if(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&.#])[A-Za-z\d@$!%*?&.#]{8,16}$/.test(obj.password)){

                }else {
                    console.log(obj.password);
                    layer.msg("密码必须包含大小写字母、数字、特殊字符、至少八位");
                    return false;
                }
            }else{
                layer.msg("新密码不能为空");
                return false;
            }

            if(obj.password != newPassword2){
                layer.msg("两次填写的新密码不相同");
                return false;
            }

            // requestAsync('/orgUser/updatePassword',1, obj, function (data) {
            //     if(data.code == 0){
            //         layer.msg("密码修改成功，请重新登录！", {time: 2000}, function(){
            //             parent.location.reload();
            //         });
            //     }else{
            //         layer.msg(data.msg);
            //     }
            // });
            $.ajax({
                url: '/admin/sysUser/updatePassword',
                data: $("#form").serialize(),
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    if (result.code == 0) {
                        layer.msg('修改成功,再次登录后生效');
                        layer.close(index);
                    }else {
                        layer.msg("修改失败");
                    }
                },
                error: function () {
                    layer.alert("修改失败！")
                }
            });
        },
        btn2: function(index, layero){
            layer.close(index);
        }
    });
}
function login(){
    var name = $("#name").val()
    var password = $("#password").val();
    var validateCode = $("input[name='vcode']").val();
    if (name == '' || name == null) {
        return
    }
    if (password == '' || password == null) {
        return
    }
    var data = {
        loginName: name,
        password: password,
        validateCode: validateCode
    }
    request('/admin/login', 1, data, function (data) {

        // 判断图形验证码的
        if (data == null || data == '') {
            $('.imgcode').click();
            layer.msg('<span style="color: black">'+"验证码错误！"+'</span>', {icon: 5})
            return
        }
        if (data.code == 0) {
            location.href = "/admin/index"
        }else if (data.msg.includes("弱密码")) {
            $('.imgcode').click();
            // 校验弱密码
            layer.open({
                type: 1,
                title: false, //不显示标题栏
                closeBtn: false,
                area: '300px;',
                shade: 0.8,
                id: 'LAY_layuipro', //设定一个id，防止重复弹出
                btn: ['修改密码'],
                btnAlign: 'c',
                moveType: 1, //拖拽模  式，0或者1
                content: '<div style="padding: 45px; line-heoight: 22px; background-color: #393D49; color: #fff; font-weight: 300;">您的密码为弱密码！<br>请点击下方按钮去修改密码！<br></div>',
                success: function (layer) {
                    $('.layui-layer-btn0').click(function () {
                        var uid = data.msg.split(":")[1];
                        changePassword(uid,data.password);
                    });
                }
            });
        } else {
            layer.msg('<span style="color: black">'+data.msg+'</span>', {icon: 5})
        }
    }, function (error) {

    })
}

$("#login").click(function(){
    login();

})
$(window).keydown(function (e){

    if(e.keyCode == 13){
        $("#vcode").blur();

        login();
        //alert($("#vcode").val());
    }
    })