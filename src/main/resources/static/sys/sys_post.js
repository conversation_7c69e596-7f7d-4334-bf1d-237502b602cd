//初始化页面的js
$(function () {

    var columns = [
        {
            field: 'ck',
            checkbox: true
        },
        {
            field: 'postName',
            title: '岗位名称'
        },
        {
            field: 'postCode',
            title: '岗位名称'
        },
        {
            field: 'postSort',
            title: '排序'
        }, {
            field: 'status',
            title: '状态',
            align: 'center',
            formatter: function (value, row, index) {
                if (row.status == null || row.status == '1') {
                    return '<button id="btn_edit" type="button" class="btn btn-warning disabled btn-xs" >停用</button>'
                } else {
                    return '<button id="btn_edit" type="button" class="btn btn-success disabled btn-xs" >启用</button>'
                }
            }
        },
        {
            field: 'createTime',
            title: '创建时间'
        }, {
            field: 'id',
            title: '操作',
            formatter: function (value, row, index) {
                return '<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="update(\'' + value + '\')"><i class="fa fa-edit"></i> 修改</button>'+
                    '<button type="button" class="btn btn-danger  btn-xs" onclick="deletes(\'' + value + '\')" style="margin-left: 10px;"><i class="fa fa-trash-o"></i> 删除</button>';
            }
        }
    ]
    createBootstrapTable($("#table"), '/admin/sysPost/info', columns, true, toolbar, 'client')

})

// 清空表单
function clearForm() {
    $("#postName").val("")
    $("#postCode").val("")
    $("#postSort").val("")
    $("#remarks").val("")
}


var postForm;

//点击新增事件控制
function add() {
    clearForm()
    postForm = layer.open({
        type: 1,
        area: (window.innerWidth>992)? "600px":"auto",
        title: '新增',
        shade: 0.6,
        shadeClose: true, //点击遮罩关闭层
        maxmin: false,
        anim: 1,
        content: $("#post_form"),
        success: function (layero, index) {
            layer.iframeAuto(index);
        } ,end:function () {
            $("#post_form").hide()
        }
    });
}

//新增用户
function save() {
    var id = $("#postId").val()
    var postName = $("#postName").val();
    var postCode = $("#postCode").val();
    var postSort = $("#postSort").val();
    var remarks = $("#remarks").val();
    var status = $("input[name='status']:checked").val();

    if (postName == '' || postName  == null) {
        layer.msg("请输入名称！")
        return
    }
    if (postCode == '' || postCode == null) {
        layer.msg("请输入编码！")
        return
    }

    var data = {
        id: id,
        postName: postName,
        postCode: postCode,
        postSort: postSort,
        status:status,
        remarks: remarks
    }
    request('/admin/sysPost/save', 1, data, function (res) {
        if (res.msg == "success") {
            layer.close(postForm)
            layer.msg("保存成功", {time: 1000}, function () {
                // onLoads($("#href").val())
                location.reload();
            })
        }
    }, function (error) {
        layer.msg("系统异常！")
    })
}

//查询当前用户数据进行修改
function update(id) {
    clearForm()
    var data = {
        id: id
    }

    request('/admin/sysPost/getById', 0, data, function (data) {
        $("#postId").val(data.id)
        $("#postName").val(data.postName)
        $("#postCode").val(data.postCode)
        $("#postSort").val(data.postSort)
        $("#remarks").val(data.remarks)
        var r = data.status
        $("input[name='status'][value=" + r + "]").attr("checked", true);
    }, function (error) {
        layer.msg("系统异常！")
    })
    postForm = layer.open({
        type: 1,
        area: (window.innerWidth>992)? "600px":"auto",
        title: '修改',
        shade: 0.6,
        shadeClose: true, //点击遮罩关闭层
        maxmin: false,
        anim: 1,
        content: $("#post_form"),
        success: function (layero, index) {
            layer.iframeAuto(index);
        } ,end:function () {
            $("#post_form").hide()
        }
    });
}

function deletes(id) {
    var ids = [];
    if (id != undefined) {
        ids.push(id)
    } else {
        $.each($('tbody input:checkbox:checked'), function () {
            ids.push($(this).parents('tr').data('uniqueid'));
        });
    }

    if (ids.length < 1) {
        layer.msg("最少请选择一条数据！")
        return;
    }
    var data = {
        ids: ids.toString()
    }

    layer.confirm('确定删除吗？', {
        title:'提示',btn: ['取消', '确定'] //按钮
    }, function () {
        layer.close(layer.index)
    }, function () {
        request('/admin/sysPost/delete', 0, data, function (res) {
            if (res.code == 0) {
                layer.msg("删除成功", {time: 1000}, function () {
                    // onLoads($("#href").val())
                    location.reload();
                })
            }else {
                layer.msg(res.msg, {time: 1000})
            }
        }, function (error) {
            layer.msg("系统异常！")
        })
    });
}

function formSearch() {
    var postName = $("#searchName").val()

    var data = {
        postName: postName
    }

    request('/admin/sysPost/info', 1, data, function (res) {
        $("#table").bootstrapTable('load', res);
    }, function (error) {
        layer.msg(error.msg)
    })
}

function restForm() {
    document.getElementById("searchForm").reset()
    formSearch();
}