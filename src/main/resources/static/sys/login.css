
* {
    padding: 0;
    margin: 0;
    list-style: none;
    text-decoration: none;
    color: #fff;
    font-size: 14px
}

body {
    background: url("../awi/dist/img/bg.jpg") #0014bd center top no-repeat;
}

.con {
    width: 1300px;
    margin: 0 auto;
    overflow: hidden;
}

.header h2 {
    text-align: center;
    font-size: 44px;
    font-weight: bold;
    letter-spacing: 2px;
    line-height: 80px;
}

.header ul {
    float: right;
    margin-top: 60px;
}

.header ul li {
    float: left;
}

.header ul li a {
    padding: 0 10px;
    line-height: 20px;
}

.main {
    padding: 80px 0;
}

.nav-tabs > li > a {
    color: #444;
}

.main .left {
    width: 70%;
    float: left;
    background: rgba(255, 255, 255, 0.5);
}

.main .left img {
    width: 100%;
}

.tab-content ul li {
    overflow: hidden;
}

.details p {
    color: #444;
}

.details p span {
    color: #f00;
}

.tab-content ul {
    margin: 0;
}

.tab-content ul li p {
    float: left;
    width: 25%;
    line-height: 32px;
    text-align: center;
    color: #444;
    margin: 0;
}

.tab-content .scroll-text ul li p {
    color: #f00;
}

.tab-content .scroll-text {
    height: 388px;
    overflow: hidden;
}

.tab-content .scroll-text ul {
    height: 388px;
}

.main .right {
    width: 38%;
    margin: 0 auto;
}

.footer p {
    text-align: center;
    line-height: 2em;
}

.form {
    width: 90%;
    margin: 0 auto;
    padding: 20px 0 20px 0;
    background: rgba(255, 255, 255, 0.5);
}

.form h3 {
    font-size: 20px;
    color: #444;
    line-height: 60px;
    text-align: center;
}
.form li {
    display: flex;
    justify-content: space-around;
    padding: 0 36px;
}
.form ul li {
    padding: 10px 10%;
    overflow: hidden;
}

.form ul li a {
    display: inline-block;
    text-align: center;
    color: #f00;
}

.form input {
    height: 40px;
    display: block;
    width: 100%;
    border: none;
    outline: none;
    line-height: 40px;
    color: #444;
    box-sizing: border-box;
}

#name {
    background: url("../common/img/icon_user.png") #fff 8px center no-repeat;
    padding-left: 48px;
    background-size: 10%;
}

#password {
    background: url("../common/img/icon_password.png") #fff 8px center no-repeat;
    padding-left: 48px;
    background-size: 10%;
}

.form ul li img {
    float: right;
    height: 40px;
    display: block;
}

#checkbox {
    width: auto;
    display: inline-block;
    vertical-align: middle;
}

.login-btn {
    color: #fff;
    background-color: #1884a8;
    border-color: #1884a8;
}

.login-btn.focus, .login-btn:focus {
    color: #fff;
    background-color: #1884a8;
    border-color: #1884a8
}

.login-btn:hover {
    color: #fff;
    background-color: #1884a8;
    border-color: #1884a8
}

.login-btn.active, .login-btn:active, .open > .dropdown-toggle.login-btn {
    color: #fff;
    background-color: #1884a8;
    border-color: #1884a8
}

.login-btn.active.focus, .login-btn.active:focus, .login-btn.active:hover, .login-btn:active.focus, .login-btn:active:focus, .login-btn:active:hover, .open > .dropdown-toggle .login-btn.focus, .open > .dropdown-toggle.login-btn:focus, .open > .dropdown-toggle.login-btn:hover {
    color: #fff;
    background-color: #1884a8;
    border-color: #1884a8
}

.login-btn.active, .login-btn:active, .open > .dropdown-toggle.login-btn {
    background-image: none
}

.login-btn.disabled.focus, .login-btn.disabled:focus, .login-btn.disabled:hover, .login-btn[disabled].focus, .login-btn[disabled]:focus, .login-btn[disabled]:hover, fieldset[disabled] .login-btn.focus, fieldset[disabled] .login-btn:focus, fieldset[disabled] .login-btn:hover {
    background-color: #0014bd;
    border-color: #0014bd
}

.login-btn.badge {
    color: #0014bd;
    background-color: #fff
}
