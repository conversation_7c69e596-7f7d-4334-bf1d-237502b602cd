$(function () {

    var toolbar1 =
        '<div id="toolbar" class="btn-group">\n' +
        '        <button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="add()">\n' +
        '             <i class="fa  fa-plus"></i>新增\n' +
        '        </button>\n' +
        ' </div>'

    var columns = [
        {
            field: 'deptName',
            title: '名称'
        }, {
            field: 'orderNum',
            title: '排序',
            align: 'center'
        }, {
            field: 'status',
            title: '状态',
            align: 'center',
            formatter: function (value, row, index) {
                if (row.status == null || row.status  == '1') {
                    return '<button id="btn_edit" type="button" class="btn btn-warning disabled btn-xs" >停用</button>'
                } else {
                    return '<button id="btn_edit" type="button" class="btn btn-success  disabled btn-xs" >启用</button>'
                }
            }
        }, {
            field: 'createTime',
            title: '创建时间',
            align: 'center'
        },
        {
            field: 'id',
            title: '操作',
            align: 'center',
            formatter: function (value, row, index) {
                return '<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="update(\'' + value + '\')"><i class="fa fa-edit"></i> 修改</button>' +
                '<button id="btn_edit" type="button" class="btn btn-danger btn-xs" style="margin-left: 10px" onclick="deletes(\'' + value + '\')"><i class="fa fa-trash-o"></i> 删除</button>';
            }
        }
    ]
    $("#table").bootstrapTreeTable({
        code: 'id',
        parentCode: 'parentId',
        type: 'get',
        url: '/admin/sysDept/info',
        striped: false,
        expandColumn: '0',
        bordered: true,
        columns: columns,
        toolbar: toolbar1,
    });

    // 初始化ztree
    var setting = {
        data: {
            simpleData: {
                enable: true, // 设置启用简单数据格式[{id, pId, name}, {id, pId, name}]
                idKey: "id",  // 节点数据中保存唯一标识的属性名称
                pIdKey: "pId",  // 节点数据中保存其父节点唯一标识的属性名称
                rootPId: null  // 根节点id
            }
        }
    };
    request('/admin/sysDept/zTreeNodes', 0, null, function (res) {
        if (res.code == 0) {
            zTree = $.fn.zTree.init($("#typeTree"), setting, res.data);
        } else {
            layer.msg("树表初始化失败！")
        }
    }, function (error) {
        layer.msg("系统异常！")
    })

})

//清空表单
function clearForm() {
    $("#deptId").val("")
    $("#deptName").val("")
    $("#orderNum").val("")
    $("#leader").val("")
    $("#parentId").val("")
    $("#parentName").val("")
    $("#phone").val("")
}

//树形菜单
var zTree;

function openDeptTree() {
    openTree($("#zTree"),"parentId","parentName");
}

/*设置选中的节点*/
function doSetSelectedNode(index) {
    var selectedNodes = zTree.getSelectedNodes();
    var node = selectedNodes[0];
    $("#parentName").val(node.name);
    $("#parentId").val(node.id);
    layer.close(index);
}


/**
 * 新增
 */
var dept_form;

function add() {
    clearForm()
    dept_form = layer.open({
        type: 1,
        area: (window.innerWidth>992)? "600px":"auto",
        title: '新增',
        shade: 0.6,
        shadeClose: true, //点击遮罩关闭层
        maxmin: false,
        anim: 1,
        content: $("#dept_form"),
        success: function (layero, index) {
            layer.iframeAuto(index);
        },
        end:function () {
            $("#dept_form").hide()
        }
    });
}

function save() {
    var id = $("#deptId").val()
    var parentId = $("#parentId").val();
    var deptName = $("#deptName").val();
    var orderNum = $("#orderNum").val();
    var leader = $("#leader").val();
    var phone = $("#phone").val();
    var status = $("input[name='status']:checked").val()

    if (deptName == '' || deptName == null) {
        layer.msg("请输入名称！",{icon:5})
        return
    }

    var data = {
        id: id,
        deptName: deptName,
        parentId: parentId,
        orderNum: orderNum,
        leader: leader,
        phone: phone,
        status: status
    }

    request('/admin/sysDept/save', 1, data, function (res) {
        if (res.msg == "success") {
            layer.close(dept_form)
            layer.msg("保存成功", {time: 1000}, function () {
                // onLoads($("#href").val())
                location.reload();
            })
        }
    }, function (error) {
        layer.msg("系统异常！")
    })
}

/**
 * 修改
 */
function update(id) {
    clearForm()
    var data = {
        id: id
    }
    request('/admin/sysDept/getById', 0, data, function (data) {
        $("#deptId").val(data.id)
        $("#deptName").val(data.deptName)
        $("#parentId").val(data.parentId)
        $("#parentName").val(data.parentName)
        $("#orderNum").val(data.orderNum)
        $("#leader").val(data.leader)
        $("#phone").val(data.phone)
        var r = data.status
        $("input[name='status'][value=" + r + "]").attr("checked", true);

    }, function (error) {
        layer.msg("系统异常！")
    })
    dept_form = layer.open({
        type: 1,
        area: (window.innerWidth>992)? "600px":"auto",
        title: '修改',
        shade: 0.6,
        shadeClose: true, //点击遮罩关闭层
        anim: 1,
        content: $("#dept_form"),
        success: function (layero, index) {
            layer.iframeAuto(index);
        },
        end:function () {
            $("#dept_form").hide();
        }
    });
}

function deletes(id) {
    var data = {
        id: id
    }
    layer.confirm('确定删除吗？', {
        title:'提示',btn: ['取消', '确定'] //按钮
    }, function () {
        layer.close(layer.index)
    }, function () {
        request('/admin/sysDept/delete', 0, data, function (res) {
            if (res.code == 0) {
                layer.msg("删除成功", {time: 1000}, function () {
                    // onLoads($("#href").val())
                    location.reload();
                })
            }else {
                layer.msg(res.msg, {time: 1000})
            }
        }, function (error) {
            layer.msg("系统异常！")
        })
    });
}

