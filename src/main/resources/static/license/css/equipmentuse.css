body{font-family:'微软雅黑';color:#000; font-size: 16px;}
.container{
			width: 1000px;
			margin:10px auto;
		}
		input{
			outline: none;
			line-height: 32px;
		}
		table{
			table-layout: fixed;
			border-collapse: collapse;
			width: 100%;	
		}
		.table-bordered td{
			padding: 15px 5px;
			border:1px solid #000;
			line-height: 32px;
		}
		.narrow td{
			padding:5px;
		}
		.min td{
			padding:2px 5px;
		}
		.table-bordered td table td{
			border:none;
		}
		.table-bordered td .table-bordered td{
			padding: 10px 5px;
			border:1px solid #000;
			line-height: 32px;
		}
		.text-center{
			text-align: center;
		}
		.form-control{
			border:none;
			width: 100%;
			-webkit-box-shadow:none;
			box-shadow:none;
			border-bottom: 1px solid #000;
			border-radius: 0;
		}
		.inline-control{
			border:none;
			width: auto;
			-webkit-box-shadow:none;
			box-shadow:none;
			border-bottom: 1px solid #000;
			border-radius: 0;
		}
		.input-control{
			border:none;
			width: 100%;
		}
		.bordertop{
			margin-top: 20px;
		}
		.checkbox{
			padding:5px 0;
		}