package ${packageName}.mapper;

import com.tzstcl.base.mapper.BaseMapper;
import ${packageName}.model.${className};
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：${author!'admin'}
 * 日期：${(currentDate?string("yyyy年MM月dd日"))!}
 * 说明：${functionName}Mapper
 */
@Mapper
public interface ${className}Mapper extends BaseMapper<${className}>  {

    /**
    *
    * 批量增加${functionName}
    * @param ${unClassName}List
    * <AUTHOR>
    * @date ${(currentDate?string("yyyy年MM月dd日"))!}
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<${className}> ${unClassName}List);

}