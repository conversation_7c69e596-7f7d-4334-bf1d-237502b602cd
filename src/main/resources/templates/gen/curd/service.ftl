package ${packageName}.service;

import com.tzstcl.base.service.BaseService;
import ${packageName}.model.${className};

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：${author!'admin'}
 * 日期：${(currentDate?string("yyyy年MM月dd日"))!}
 * 说明：${functionName}Servic
 */
public interface ${className}Service extends BaseService<${className}> {

    /**
    *
    * 批量增加${functionName}
    * @param ${unClassName}List
    * <AUTHOR>
    * @date ${(currentDate?string("yyyy年MM月dd日"))!}
    * @return Integer 插入的记录数
    */
    Integer insertBatch(List<${className}> ${unClassName}List);

}