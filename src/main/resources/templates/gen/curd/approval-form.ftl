<!-- 通用审核发起页面 -->
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>表单发起页面模板</title>
    <!-- Tell the browser to be responsive to screen width -->
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <!-- Bootstrap 3.3.6 -->
    <link rel="stylesheet" th:href="@{/resources/bootstrap/css/bootstrap.min.css}">
    <link rel="stylesheet" th:href="@{/resources/xt/css/table-form.css}">
    <link rel="stylesheet" th:href="@{/resources/file-input/css/fileinput.min.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/z-tree3.5/css/simple/zTreeStyle.css}" type="text/css">
    <script th:src="@{/resources/awi/plugins/jQuery/jquery-2.2.3.min.js}"></script>
    <script th:src="@{/resources/awi/plugins/jQuery/jquery.serializejson.js}"></script>
    <script th:src="@{/resources/bootstrap/js/bootstrap.min.js}"></script>
    <script th:src="@{/resources/file-input/js/fileinput.min.js}"></script>
    <script th:src="@{/resources/file-input/js/locales/zh.js}"></script>
    <script th:src="@{/resources/awi/plugins/z-tree3.5/js/jquery.ztree.core.js}"></script>
    <script th:src="@{/resources/awi/plugins/z-tree3.5/js/jquery.ztree.excheck.js}"></script>
    <style type="text/css">
        body {
            padding: 20px;
        }

        table select {
            width: 120px;
            height: 32px;
            vertical-align: middle;
            margin-left: 6px;
        }

        .delete {
            font-size: 16px;
            width: 18px;
            height: 18px;
            border-radius: 10px;
            background-color: #f00;
            color: #fff;
            text-align: center;
            line-height: 18px;
        }

        .col-sm-7 {
            position: relative;
        }

        .change {
            position: absolute;
            top: 0;
            left: 0;
            height: 165px;
            overflow-y: scroll;
            overflow-x: hidden;
            border: 1px solid #ddd;
            z-index: 999;
        }

        .cooperation {
            width: 169px;
            height: 115px;
        }

        .handlebar, .cooperationbar {
            padding: 0 4px;
        }

        .change::-webkit-scrollbar {
            width: 4px;
        }

        #cooperation, #handle {
            width: 86%;
        }

        #paint {
            margin-left: -15px;
            background: url("./images/file.png") no-repeat 15px center;
            cursor: pointer;
        }
    </style>
</head>
<body>
<div id="form-save-to-db">
    <div class="row">
        <div class="col-md-offset-2 col-md-8" style="text-align: center">
            <h3>${formName}</h3>
        </div>
    </div>
    <div class="row">
        <div class="col-md-offset-2 col-md-8">
            <!-- 流程基本信息-->
            <form id="base-proc-form">
                <table>
                    <tr>
                        <td colspan="2" style="text-align: left; border-right: none;">流水号:
                            <input type="text" value="201911090987" name="serialNo" id="serialNo" readonly
                                   style="border: none; border-bottom:1px solid  #ddd;text-align: center;">
                        </td>
                        <td colspan="2" style="text-align: right;border-left: none;">紧急程度
                            <select>
                                <option value="一般">一般</option>
                                <option value="紧急">紧急</option>
                            </select>
                        </td>
                    </tr>
                </table>
            </form>
            <!-- 流程表单基本数据-->
            <form id="base-data-form">
                <table>
                    <tr>
                        <th colspan="4">基本信息</th>
                    </tr>
                    <#list formBaseList as formBase>
                        <#if  formBase_index%2==0>
                            <tr>
                            <td>${formBase.labelName}</td>
                            <td class="form-group"><input type="text" class="form-input" name="${formBase.inputName}"
                                                          placeholder="${formBase.comment}"></td>
                        <#elseif formBase_index%2==1>
                            <td>${formBase.labelName}</td>
                            <td class="form-group"><input type="text" class="form-input" name="${formBase.inputName}"
                                                          placeholder="${formBase.comment}"></td>
                            </tr>

                        </#if>
                        <#if formBase_index%2==0&&!formBase_has_next>
                            <td colspan="2"></td>
                            </tr>
                        </#if>
                    </#list>

                </table>
            </form>
            <form id="attachments-form">
                <table class="noborder">
                    <tr class="attachment">
                        <th colspan="4" class="alignleft">附件信息</th>
                    </tr>
                    <tr>
                        <td colspan="4">
                            <input class="form-control" id="uploadFile" type="file" multiple>
                            <input type="hidden" name="attachments">
                        </td>
                    </tr>
                </table>
            </form>
            <form id="details-form">
                <table class="noborder">
                    <tr class="attachment">
                        <th colspan="4" class="alignleft">明细信息</th>
                    </tr>
                    <tr>
                        <td colspan="4">

                        </td>
                    </tr>
                </table>
            </form>
            <table class="noborder">
                <tr>
                    <td></td>
                    <td colspan="2" style="text-align: center;">
                        <button type="button" class="btn btn-success">保存</button>
                        <button type="button" data-toggle="modal" data-target="#myModal" class="btn btn-info">保存并发起审批
                        </button>
                    </td>
                    <td></td>
                </tr>
            </table>
        </div>
    </div>
</div>
<!-- 弹出框  人员办理设置 开始-->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     th:include="process-start">
</div>
<!-- 弹出框  人员办理设置 结束-->
<!--  初始化表单，-->
<script type="text/javascript" th:src="@{/resources/xt/js/approve-form-init.js}"></script>
</body>
</html>
