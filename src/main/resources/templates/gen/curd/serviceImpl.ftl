package ${packageName}.service.impl;

import com.tzstcl.base.service.impl.BaseServiceImpl;
import ${packageName}.service.${className}Service;
import ${packageName}.model.${className};
import ${packageName}.mapper.${className}Mapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：${author!'admin'}
 * 日期：${(currentDate?string("yyyy年MM月dd日"))!}
 * 说明：${functionName}ServiceImpl
 */
@Service
public class ${className}ServiceImpl extends BaseServiceImpl<${className}Mapper,${className}> implements ${className}Service{

    /**
    *
    * 批量增加${functionName}
    * @param ${unClassName}List
    * <AUTHOR>
    * @date ${(currentDate?string("yyyy年MM月dd日"))!}
    * @return Integer 插入的记录数
    */
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    @Override
    public Integer insertBatch(List<${className}> ${unClassName}List){return this.mapper.insertBatch(${unClassName}List);}

}