<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="${packageName}.mapper.${className}Mapper" >
    <resultMap id="BaseResultMap" type="${packageName}.model.${className}" >
    <#list columns as column>
        <#if column.name=='id'>
        <id column="id" property="id" jdbcType="BIGINT" />
        <#else>
        <result column="${column.name}" property="${column.javaField}" jdbcType="${column.newJdbcType}" />
        </#if>
    </#list>
    </resultMap>

    <sql id="Base_Column_List" >
    <#list columns as column>
        ${column.name}<#if column_has_next>,</#if>
    </#list>
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from ${tableName}
        where id = ${"#"}{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="${packageName}.model.${className}">
        select
        <include refid="Base_Column_List" />
        from ${tableName}
        where
             id = ${"#"}{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="${packageName}.model.${className}">
        select
        <include refid="Base_Column_List" />
        from ${tableName}
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = ${"#"}{id}
            </if>
        <#list columns as column>
        <#-- 如果不是基类属性 -->
            <#if column.isNotBaseField && column.name!="id">
            <if test="${column.javaField} != null ">
                and ${column.name}=${"#"}{${column.javaField}}
            </if>
           </#if>
       </#list>
         </where>
    </select>

    <insert id="insert" parameterType="${packageName}.model.${className}">
        insert into ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <#list columns as column>
                <if test="${column.javaField} != null">
                    ${column.name},
                </if>
            </#list>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
             <#list columns as column>
            <if test="${column.javaField} != null ">
                ${"#"}{${column.javaField}},
            </if>
             </#list>
        </trim>
    </insert>

    <update id="update" parameterType="${packageName}.model.${className}">
        update ${tableName}
        <set>
            <#list columns as column>
                <#if (column.name!='id'  )>
            <if test="${column.javaField} != null">
                ${column.name}=${"#"}{${column.javaField}},
            </if>
                </#if>
            </#list>
        </set>
        WHERE   id = ${"#"}{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update ${tableName} set
        del_flag='1'
        WHERE   id = ${"#"}{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update ${tableName} set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        ${"#"}{id}
        </foreach>
    </update>

    <#--批量导入-->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into ${tableName}(<#list columns as column>${column.name}<#if column_has_next>,  </#if></#list>) values
        <foreach item="item" index="index" collection="list" separator=",">
            (<#list columns as column>${"#"}{item.${column.javaField}}<#if column_has_next>,  </#if></#list>)
        </foreach>
    </insert>

</mapper>