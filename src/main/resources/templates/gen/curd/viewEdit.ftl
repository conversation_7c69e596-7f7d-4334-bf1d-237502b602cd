<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="${functionName}编辑"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="${unClassName}form" class="container-fluid" >

    <form id="${unClassName}-form-edit" class="form-horizontal" autocomplete="off" th:object="${'$'}{${unClassName}}">
            <input type="hidden" id="id" name="id" th:value="*{id}"/>
            <#list columns as column>
            <#-- 如果不是基类属性 -->
            <#if column.isNotBaseField>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="${column.javaField!''}" class="col-sm-4 control-label require">${column.comments!''}</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="${column.javaField!''}" name="${column.javaField!''}" th:value="*{${column.javaField!''}}"/>
                   </div>
                </div>
            </div>
            </#if>
            </#list>
        <div class="form-group" >
            <button type="button" class="btn btn-info col-sm-offset-6" onclick="formSave()">提交</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>

<script type="text/javascript" th:inline="javascript">
    initValidator();
    function formSave(){
        var bootstrapValidator = $('#${unClassName}-form-edit').data('bootstrapValidator');
        if(bootstrapValidator.isValid()) {
            save('/${unClassName}/update' ,'${unClassName}-form-edit')
        }
    }
    function initValidator() {
        $('#${unClassName}-form-edit').bootstrapValidator({
            message: '输入值不满足要求',
            excluded : [':disabled',':hidden'],
            verbose:false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
        <#list columns as column>
        <#-- 如果不是基类属性 -->
        <#if column.isNotBaseField>
        <#if column.javaField !=''>
        ${column.javaField}: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: ${column.dataLength},
                    message: '不能超过${column.dataLength}位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/${unClassName}/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        ${column.javaField}: $('#${column.javaField}').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        </#if>
        </#if>
        </#list>
    }
    });
        $('#${unClassName}-form-edit').data('bootstrapValidator').validate();
    }
</script>

</body>
</html>
