package ${packageName}.ctrl;

import com.github.pagehelper.PageInfo;
import com.tzstcl.base.ctrl.BaseCtrl;
import com.tzstcl.base.model.AjaxResult;
import com.tzstcl.commons.utils.StringUtils;
import ${packageName}.model.${className};
import ${packageName}.service.${className}Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司：天筑科技股份有限公司
 * 作者：${author!'admin'}
 * 日期：${(currentDate?string("yyyy年MM月dd日"))!}
 * 说明：${functionName}Controller
 */
@Controller
@RequestMapping("/admin/${unClassName}")
public class ${className}Ctrl extends BaseCtrl {

    @Autowired
    private ${className}Service ${unClassName}Service;

    /**
    * list页面导航
    * @return
    */
    @RequiresPermissions("${unClassName}:view")
    @RequestMapping("/toList")
    public String toList() {
        return "admin/${moduleName}/${subModuleName}/${unClassName}List";
    }

    /**
    * 获取查询的分页数据
    * @param ${unClassName}
    * @return
    */
    @RequestMapping("/list")
    @RequiresPermissions("${unClassName}:view")
    @ResponseBody
    public PageInfo<${className}> list(${className} ${unClassName}) {
        return  ${unClassName}Service.selectPage(${unClassName});
    }

    /**
    * form页面导航
    * @return
    */
    @RequiresPermissions(value={"${unClassName}:edite","${unClassName}:add"},logical=Logical.OR)
    @RequestMapping("/toForm")
    public String toForm(@RequestParam(value="id", required=false)  Long id, Model model) {
        if(null != id){
            model.addAttribute("${unClassName}" ,${unClassName}Service.getOne(id));
        }
        return "admin/${moduleName}/${subModuleName}/${unClassName}Form";
    }

    /**
    * form页面导航 详情
    * @return
    */
    @RequiresPermissions("${unClassName}:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("${unClassName}" ,${unClassName}Service.getOne(id));
        }
        return "admin/${moduleName}/${subModuleName}/${unClassName}Detail";
    }

    /**
     * 新增
     * @param ${unClassName}
     * @return
     */
    @RequestMapping("/add")
    @RequiresPermissions(value={"${unClassName}:add"})
    @ResponseBody
    public AjaxResult save(@Valid ${className} ${unClassName}) {
         return toAjax(${unClassName}Service.add(${unClassName}));
    }

    /**
    * form页面导航 更新
    * @return
    */
    @RequiresPermissions("${unClassName}:edit")
    @GetMapping("/edit/{id}")
    public String toUpdate(@PathVariable("id")Long id, Model model) {
        if(null != id){
            model.addAttribute("${unClassName}" ,${unClassName}Service.getOne(id));
        }
        return "admin/${moduleName}/${subModuleName}/${unClassName}Edit";
    }

    /**
    * 更新
    * @param ${unClassName}
    * @return
    */
    @PostMapping("/update")
    @RequiresPermissions("${unClassName}:edit")
    @ResponseBody
    public AjaxResult update(@Valid ${className} ${unClassName}) {
        return toAjax(${unClassName}Service.update(${unClassName}));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @RequestMapping("/delete")
    @RequiresPermissions("${unClassName}:delete")
    @ResponseBody
    public AjaxResult delete(String ids) {
        return toAjax(${unClassName}Service.deleteBatchIds(ids));
    }

    /**
     * 获取单条信息
     * @param id
     * @return
     */
    @RequestMapping("/get")
    @RequiresPermissions("${unClassName}:view")
    @ResponseBody
    public AjaxResult get(Long id) {
        return  success("获取信息成功",${unClassName}Service.getOne(id));
    }

    /**
    * 校验唯一性
    * 权限配置为：多权限任选一，有新增和修改其一权限就可以访问
    * @param ${unClassName}
    * @return
    */
    @PostMapping("/checkUnique")
    @RequiresPermissions(value={"${unClassName}:add", "${unClassName}:edit"},logical=Logical.OR)
    @ResponseBody
    public Map<String,Boolean> checkUnique(${className} ${unClassName}) {
        Map<String,Boolean> result = new HashMap<String,Boolean>(4);
        result.put("valid",true);
        Long id = ${unClassName}.getId();
        ${unClassName}.setId(null);
        List<${className}> ${unClassName}List = ${unClassName}Service.selectList(${unClassName});
        if(StringUtils.isNotEmpty(${unClassName}List)){
            if(${unClassName}List.size()>1){
                result.put("valid",false);
            }else{
                if(id != null){
                    // 新增
                    result.put("valid",false);
                }else{
                    // 修改
                    if(!id.equals(${unClassName}List.get(0).getId())){
                    result.put("valid",false);
                    }
                }
            }
        }
        return result;
    }

}