<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="${functionName}"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <#--搜索栏-->
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-sm-1" for="searchName">名称</label>
                    <div class="col-sm-3">
                        <input type="text" class="form-control col-sm-10" id="searchName" name="name"
                               placeholder="请输入名称">
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="restForm('searchForm')"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="formSearch('${unClassName}table','/admin/${unClassName}/list' ,'searchForm')">
                            <i class="fa fa-search"></i>查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <#--数据列表-->
    <table id="${unClassName}table"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";

    var addFlag = [[${'$'}{@permission.hasPermi('${unClassName}:add')}]];
    var editFlag = [[${'$'}{@permission.hasPermi('${unClassName}:edite')}]];
    var delFlag = [[${'$'}{@permission.hasPermi('${unClassName}:delete')}]];
    var designFlag = [[${'$'}{@permission.hasPermi('${unClassName}:design')}]];

    $(function () {
        var columns = [
            {
                field: 'ck',
                checkbox: true,
                align: 'center',
                valign: 'middle'
            },
            <#list columns as column>
            <#-- 如果不是基类属性 -->
            <#if column.isNotBaseField>
            {
                field: "${column.javaField!''}",
                title: "${column.comments!''}",
                align: 'center',
                valign: 'middle'
            },
            </#if>
            </#list>
            {
                field: 'createTime',
                title: '创建时间',
                align: 'center',
                valign: 'middle'
            },
            {
                field: 'id',
                title: '操作',
                align: 'center',
                valign: 'middle',
                cellStyle:{
                    css:{"white-space":"nowrap"}
                },
                formatter: function (value, row, index) {
                    var actions = [];
                    if ("hidden" != editFlag) {
                        actions.push('<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="updatelayer(\'' + value + '\',\'编辑\',\'/admin/${unClassName}/edit/\')"><i class="fa fa-edit"></i>编辑</button>');
                    }
                    if ("hidden" != designFlag) {
                        actions.push('<button id="btn_detail" type="button" class="btn btn-success  btn-xs" onclick="detaillayer(\'' + value + '\',\'详情\',\'/admin/${unClassName}/detail/\')"><i class="fa fa-eye"></i>详情</button>');
                    }
                    if ("hidden" != delFlag) {
                        actions.push('<button type="button" class="btn btn-danger  btn-xs" onclick="deletes(\'/admin/${unClassName}/delete\',\'' + value + '\')" ><i class="fa fa-trash-o"></i>删除</button>');
                    }
                    return actions.join('');
                }
            }
        ];
        var toolbar = [];
        toolbar.push('<div id="toolbar" class="btn-group">');
        if("hidden" != addFlag){
            toolbar.push('<button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="poplayer(2,\'新增\',\'/admin/${unClassName}/toForm\')"><i class="fa fa-plus"></i>新增</button>');
        }
        if("hidden" != delFlag){
            toolbar.push('<button id="btn_delete" type="button" class="btn btn-danger btn-xs" onclick="deletes(\'/admin/${unClassName}/delete\')" ><i class="fa fa-trash-o"></i>删除</button>');
        }
        toolbar.push('</div>');
        createBootstrapTable($("#${unClassName}table"), '/admin/${unClassName}/list', columns, false, toolbar.join(''), 'server');
    });
</script>
</body>
</html>