package ${packageName}.model;

import com.tzstcl.base.model.BaseModel;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 公司：天筑科技股份有限公司
 * 作者：${author!'admin'}
 * 日期：${(currentDate?string("yyyy年MM月dd日"))!}
 * 说明：${functionName}实体类
 */
@Data
public class ${className} extends BaseModel<${className}> implements Serializable {

	private static final long serialVersionUID = 1L;
<#list columns as column>
<#-- 如果不是基类属性 -->
    <#if column.isNotBaseField>
    /**
     *${column.comments!''}
     */
    private ${column.javaType} ${column.javaField};
    </#if>
</#list>

}