<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="${functionName}详情"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="${unClassName}Detail" class="container-fluid" >
    <form id="${unClassName}-form-edit" class="form-horizontal" autocomplete="off" th:object="${'$'}{${unClassName}}">
        <input type="hidden" id="id" name="id" th:value="*{id}"/>
        <#list columns as column>
        <#-- 如果不是基类属性 -->
            <#if column.isNotBaseField>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="${column.javaField!''}" class="col-sm-4 control-label require">${column.comments!''}</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="${column.javaField!''}" name="${column.javaField!''}" th:value="*{${column.javaField!''}}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
            </#if>
        </#list>
        <div class="box-footer col-md-6 col-md-offset-5">
            <button type="button" class="btn btn-info pull-right" onclick="formClose()">关闭</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script type="text/javascript">
    function formClose() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    }
</script>
</body>
</html>
