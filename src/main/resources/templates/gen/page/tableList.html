<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title>数据表列表</title>
    <link th:replace="admin/include/head :: head">
</head>
<body>
<section class="content">
    <div class="row">
        <div class="col-xs-12">
            <table id="gentable">
            </table>
        </div>
    </div>

</section>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
</body>

<div id="genform" class="col-md-12  box" style="display: none">

    <form id="form" class="form-horizontal required-validate">
        <div class="box-body">
            <div class="form-group">
                <label for="tableName" class="col-sm-2 control-label">表名</label>
                <div class="col-sm-10">
                    <input type="hidden" id="jobId" name="jobId">
                    <input type="text" class="form-control" id="tableName"
                           name="tableName" autocomplete="off"
                    >
                </div>
            </div>
            <div class="form-group">
                <label for="functionName" class="col-sm-2 control-label">表注释</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="functionName"
                           name="functionName" autocomplete="off"
                    >
                </div>
            </div>
            <div class="form-group">
                <label for="moduleName" class="col-sm-2 control-label">模块名称</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="moduleName"
                           name="moduleName" autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="subModuleName" class="col-sm-2 control-label">子模块名称</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="subModuleName"
                           name="subModuleName" autocomplete="off">
                </div>
            </div>

            <div class="form-group">
                <label for="author" class="col-sm-2 control-label">作者</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="author"
                           name="author" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="box-footer">
            <button type="button" class="btn btn-info pull-right" onclick="save('/admin/gen/genCode' ,'form')">提交
            </button>
        </div>
    </form>
</div>

<script type="text/javascript">
    var select2;
    $(function () {
        select2 = $('.select2').select2()
        var columns = [
            {
                field: 'tableName',
                title: '表名称'
            }, {
                field: 'tableComment',
                title: '表注释'
            },
            {
                field: 'createTime',
                title: '创建时间'
            }, {
                field: '',
                title: '操作',
                formatter: function (value, row, index) {
                    var actions = [];
                    actions.push('<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="showGen(\'' + row.tableName + '\',\'' + row.tableComment + '\')"><i class="fa fa-edit"></i> 代码生成</button>');
                    return actions.join('');
                }
            }
        ]

        var toolbar1 = '';
        createBootstrapTable($("#gentable"), '/admin/gen/getTableList', columns, true, toolbar1, 'client');
    });

    //显示代码生成信息页面
    function showGen(tableName, tableComment) {
        $("#tableName").val(tableName)
        $("#functionName").val(tableComment)

        genform = layer.open({
            type: 1,
            area: '650px',
            title: '修改',
            shade: 0.6,
            shadeClose: true, //点击遮罩关闭层
            maxmin: false,
            anim: 1,
            content: $("#genform"),
            success: function (layero, index) {
                layer.iframeAuto(index);
            }, end: function () {
                $("#genform").hide();
            }
        });
    }

    //表单验证
    $('#form').bootstrapValidator({
        message: '这个值无效',
        feedbackIcons: {
            valid: 'glyphicon glyphicon-ok',
            invalid: 'glyphicon glyphicon-remove',
            validating: 'glyphicon glyphicon-refresh'
        },
        fields: {
            jobClassPath: {
                message: '任务类无效',
                validators: {
                    notEmpty: {
                        message: '任务类不能为空'
                    },
                    stringLength: {
                        min: 3,
                        max: 200,
                        message: '任务类必须在3到200之间'
                    }
                }
            },
            jobName: {
                message: '任务名称无效',
                validators: {
                    notEmpty: {
                        message: '任务名称不能为空'
                    },
                    stringLength: {
                        min: 3,
                        max: 30,
                        message: '任务名称必须在3到30之间'
                    }
                }
            },
            jobGroup: {
                message: '任务组名称无效',
                validators: {
                    notEmpty: {
                        message: '任务组名不能为空'
                    },
                    stringLength: {
                        min: 3,
                        max: 30,
                        message: '任务组名必须在3到30之间'
                    }
                }
            },
            status: {
                message: '状态无效',
                validators: {
                    notEmpty: {
                        message: '状态不能为空'
                    }
                }
            }
        }
    })
        .on('error.field.bv', function (e, data) {
            //  console.log(data.field, data.element, '-->error');
        })
        .on('success.field.bv', function (e, data) {
            // console.log(data.field, data.element, '-->success');
        });

    //更新job
    function updateJob(url, id, contentId) {
        var data = {id: id};
        request(url, 0, data, function (data) {
            $("#jobId").val(data.data.jobId);
            $("#jobClassPath").val(data.data.jobClassPath);
            $("#jobName").val(data.data.jobName);
            $("#jobGroup").val(data.data.jobGroup);
            $("#cronExpression").val(data.data.cronExpression);
            $("#misfirePolicy").val(data.data.misfirePolicy).trigger("change");
            $("input[name='status'][value=" + data.data.status + "]").attr("checked", true);
            popForm = layer.open({
                type: 1,
                area: '650px',
                title: '修改',
                shade: 0.6,
                shadeClose: true, //点击遮罩关闭层
                maxmin: false,
                anim: 1,
                content: $("#" + contentId),
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }, end: function () {
                    $("#" + contentId).hide();
                }
            });
        }, function (error) {
            layer.msg("系统异常！")
        })
    }
</script>
</html>