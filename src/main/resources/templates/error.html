<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>error</title>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="icon" th:href="@{/resources/favicon.ico}" type="image/x-icon" />
    <link rel="shortcut icon" th:href="@{/resources/favicon.ico}" type="image/x-icon" />
    <link rel="stylesheet" th:href="@{/resources/bootstrap/css/bootstrap.min.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/dist/css/font-awesome.min.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/select2/dist/css/select2.min.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/dist/css/ionicons.min.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/iCheck/all.css}">
    <link rel="stylesheet" th:href="@{/resources/bootstrap/css/bootstrap-table.min.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/layer/theme/default/layer.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/bootstrapvalidator/css/bootstrapValidator.min.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/dist/css/AdminLTE.min.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/dist/css/skins/all-skins.css}">
    <link rel="stylesheet" th:href="@{/resources/common/common.css}">

    <!--[if lt IE 9]>
    <script th:src="@{/resources/awi/plugins/ie9/html5shiv.min.js}"></script>
    <script th:src="@{/resources/awi/plugins/ie9/respond.min.js}"></script>
    <![endif]-->
    <!-- Google Font -->
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic">
   </head>
<body>
<section class="content">
    <div class="error-page">
<!--         <h2 class="text-yellow headline"> error</h2>-->
        <div class="error-content">
            <h3><i class="fa fa-warning text-yellow"></i>操作错误</h3>
        </div>
        [[${msg}]]
        <!-- /.error-content -->
    </div>
    <!-- /.error-page -->
</section>

</body>
</html>