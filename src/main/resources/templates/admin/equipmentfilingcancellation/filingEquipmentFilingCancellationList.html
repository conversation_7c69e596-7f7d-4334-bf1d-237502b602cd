<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="起重机设备注销申请"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-sm-1" for="searchName">名称</label>
                    <div class="col-sm-3">
                        <input type="text" class="form-control col-sm-10" id="searchName" name="name"
                               placeholder="请输入名称">
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="restForm('searchForm')"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="formSearch('filingEquipmentFilingCancellationtable','/admin/filingEquipmentFilingCancellation/list' ,'searchForm')">
                            <i class="fa fa-search"></i>查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <table id="filingEquipmentFilingCancellationtable"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";

    var addFlag = [[${@permission.hasPermi('filingEquipmentFilingCancellation:add')}]];
    var editFlag = [[${@permission.hasPermi('filingEquipmentFilingCancellation:edite')}]];
    var delFlag = [[${@permission.hasPermi('filingEquipmentFilingCancellation:delete')}]];
    var designFlag = [[${@permission.hasPermi('filingEquipmentFilingCancellation:design')}]];

    $(function () {
        var columns = [
            {
                field: 'ck',
                checkbox: true,
                align: 'center',
                valign: 'middle'
            },
            {
                field: "userId",
                title: "审核发起人id",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "propertyUnit",
                title: "产权单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "deviceName",
                title: "设备名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "specificationModel",
                title: "规格型号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "manufacturer",
                title: "生产厂家",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "factoryTime",
                title: "出厂时间",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "manufacturingLicenseNumber",
                title: "制造许可证编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "applicationDate",
                title: "申请日期，年",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "factoryLicenseNumber",
                title: "出厂编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "purchaseTime",
                title: "购买时间",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "unitAddress",
                title: "产权单位地址",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "legalRepresentative",
                title: "法定代表人",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "legalRepresentativeContactNumber",
                title: "联系电话",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "technicalDirector",
                title: "技术负责人",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "technicalDirectorContactNumber",
                title: "技术负责人联系方式",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "equipmentManager",
                title: "设备管理负责人",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "equipmentManagerContactNumber",
                title: "设备管理负责人联系方式",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "liftingWeight",
                title: "起重重量",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "cancellationReason",
                title: "注销原因",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "cancellationApplicationFormPath",
                title: "注销申请表文件路径",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "filingCode",
                title: "备案编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "filingPaper",
                title: "备案证文件路径",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "filingCard",
                title: "备案牌文件路径",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "auditStatus",
                title: "审核状态，0未审核，1审核通过，2驳回",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "auditReject",
                title: "审核退回原因",
                align: 'center',
                valign: 'middle'
            },
            {
                field: 'createTime',
                title: '创建时间',
                align: 'center',
                valign: 'middle'
            },
            {
                field: 'id',
                title: '操作',
                align: 'center',
                valign: 'middle',
                cellStyle:{
                    css:{"white-space":"nowrap"}
                },
                formatter: function (value, row, index) {
                    var actions = [];
                    if ("hidden" != editFlag) {
                        actions.push('<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="updatelayer(\'' + value + '\',\'编辑\',\'/admin/filingEquipmentFilingCancellation/edit/\')"><i class="fa fa-edit"></i>编辑</button>');
                    }
                    if ("hidden" != designFlag) {
                        actions.push('<button id="btn_detail" type="button" class="btn btn-success  btn-xs" onclick="detaillayer(\'' + value + '\',\'详情\',\'/admin/filingEquipmentFilingCancellation/detail/\')"><i class="fa fa-eye"></i>详情</button>');
                    }
                    if ("hidden" != delFlag) {
                        actions.push('<button type="button" class="btn btn-danger  btn-xs" onclick="deletes(\'/admin/filingEquipmentFilingCancellation/delete\',\'' + value + '\')" ><i class="fa fa-trash-o"></i>删除</button>');
                    }
                    return actions.join('');
                }
            }
        ];
        var toolbar = [];
        toolbar.push('<div id="toolbar" class="btn-group">');
        if("hidden" != addFlag){
            toolbar.push('<button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="poplayer(2,\'新增\',\'/admin/filingEquipmentFilingCancellation/toForm\')"><i class="fa fa-plus"></i>新增</button>');
        }
        if("hidden" != delFlag){
            toolbar.push('<button id="btn_delete" type="button" class="btn btn-danger btn-xs" onclick="deletes(\'/admin/filingEquipmentFilingCancellation/delete\')" ><i class="fa fa-trash-o"></i>删除</button>');
        }
        toolbar.push('</div>');
        createBootstrapTable($("#filingEquipmentFilingCancellationtable"), '/admin/filingEquipmentFilingCancellation/list', columns, false, toolbar.join(''), 'server');
    });
</script>
</body>
</html>