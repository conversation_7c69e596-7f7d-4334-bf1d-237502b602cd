<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head >
    <title th:text="起重机设备注销申请表单"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body >

    <div class="container-fluid">
                <!-- form start -->
                <form id="form" class="form-horizontal">
                    <input type="hidden" id="id" name="id">
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="userId" class="col-sm-2 control-label">审核发起人id</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="userId" name="userId" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="propertyUnit" class="col-sm-2 control-label">产权单位</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="propertyUnit" name="propertyUnit" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="deviceName" class="col-sm-2 control-label">设备名称</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="deviceName" name="deviceName" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="specificationModel" class="col-sm-2 control-label">规格型号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="specificationModel" name="specificationModel" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="manufacturer" class="col-sm-2 control-label">生产厂家</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="manufacturer" name="manufacturer" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="factoryTime" class="col-sm-2 control-label">出厂时间</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="factoryTime" name="factoryTime" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="manufacturingLicenseNumber" class="col-sm-2 control-label">制造许可证编号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="manufacturingLicenseNumber" name="manufacturingLicenseNumber" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="applicationDate" class="col-sm-2 control-label">申请日期，年</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="applicationDate" name="applicationDate" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="factoryLicenseNumber" class="col-sm-2 control-label">出厂编号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="factoryLicenseNumber" name="factoryLicenseNumber" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="purchaseTime" class="col-sm-2 control-label">购买时间</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="purchaseTime" name="purchaseTime" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="unitAddress" class="col-sm-2 control-label">产权单位地址</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="unitAddress" name="unitAddress" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="legalRepresentative" class="col-sm-2 control-label">法定代表人</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="legalRepresentative" name="legalRepresentative" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="legalRepresentativeContactNumber" class="col-sm-2 control-label">联系电话</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="legalRepresentativeContactNumber" name="legalRepresentativeContactNumber" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="technicalDirector" class="col-sm-2 control-label">技术负责人</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="technicalDirector" name="technicalDirector" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="technicalDirectorContactNumber" class="col-sm-2 control-label">技术负责人联系方式</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="technicalDirectorContactNumber" name="technicalDirectorContactNumber" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="equipmentManager" class="col-sm-2 control-label">设备管理负责人</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="equipmentManager" name="equipmentManager" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="equipmentManagerContactNumber" class="col-sm-2 control-label">设备管理负责人联系方式</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="equipmentManagerContactNumber" name="equipmentManagerContactNumber" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="liftingWeight" class="col-sm-2 control-label">起重重量</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="liftingWeight" name="liftingWeight" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="cancellationReason" class="col-sm-2 control-label">注销原因</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="cancellationReason" name="cancellationReason" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="cancellationApplicationFormPath" class="col-sm-2 control-label">注销申请表文件路径</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="cancellationApplicationFormPath" name="cancellationApplicationFormPath" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="filingCode" class="col-sm-2 control-label">备案编号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="filingCode" name="filingCode" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="filingPaper" class="col-sm-2 control-label">备案证文件路径</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="filingPaper" name="filingPaper" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="filingCard" class="col-sm-2 control-label">备案牌文件路径</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="filingCard" name="filingCard" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="auditStatus" class="col-sm-2 control-label">审核状态，0未审核，1审核通过，2驳回</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="auditStatus" name="auditStatus" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="auditReject" class="col-sm-2 control-label">审核退回原因</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="auditReject" name="auditReject" autocomplete="off">
                                    </div>
                                </div>
                            </div>


                        <div class="form-group">
                            <button type="button" class="btn btn-info col-sm-offset-6" onclick="formSave()">提交</button>
                        </div>
                          <!-- /.box-body -->
                </form>
              <!-- customer form end -->
    </div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";
    var index = parent.layer.getFrameIndex(window.name);
    initValidator();
    function formSave(){
        var bootstrapValidator = $('#form').data('bootstrapValidator');
        if(bootstrapValidator.isValid()) {
            savepop('/admin/filingEquipmentFilingCancellation/add' ,'form','saveBtn')
        }
    }

    function initValidator() {
        $('#form').bootstrapValidator({
            message: '输入值不满足要求',
            excluded : [':disabled',':hidden'],
            verbose:false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
        userId: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        userId: $('#userId').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        propertyUnit: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 30,
                    message: '不能超过30位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        propertyUnit: $('#propertyUnit').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        deviceName: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        deviceName: $('#deviceName').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        specificationModel: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 30,
                    message: '不能超过30位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        specificationModel: $('#specificationModel').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        manufacturer: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 30,
                    message: '不能超过30位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        manufacturer: $('#manufacturer').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        factoryTime: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        factoryTime: $('#factoryTime').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        manufacturingLicenseNumber: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 30,
                    message: '不能超过30位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        manufacturingLicenseNumber: $('#manufacturingLicenseNumber').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        applicationDate: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 10,
                    message: '不能超过10位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        applicationDate: $('#applicationDate').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        factoryLicenseNumber: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 30,
                    message: '不能超过30位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        factoryLicenseNumber: $('#factoryLicenseNumber').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        purchaseTime: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        purchaseTime: $('#purchaseTime').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        unitAddress: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 200,
                    message: '不能超过200位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        unitAddress: $('#unitAddress').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        legalRepresentative: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        legalRepresentative: $('#legalRepresentative').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        legalRepresentativeContactNumber: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        legalRepresentativeContactNumber: $('#legalRepresentativeContactNumber').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        technicalDirector: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        technicalDirector: $('#technicalDirector').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        technicalDirectorContactNumber: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        technicalDirectorContactNumber: $('#technicalDirectorContactNumber').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        equipmentManager: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        equipmentManager: $('#equipmentManager').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        equipmentManagerContactNumber: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        equipmentManagerContactNumber: $('#equipmentManagerContactNumber').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        liftingWeight: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 10,
                    message: '不能超过10位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        liftingWeight: $('#liftingWeight').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        cancellationReason: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 255,
                    message: '不能超过255位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        cancellationReason: $('#cancellationReason').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        cancellationApplicationFormPath: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 255,
                    message: '不能超过255位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        cancellationApplicationFormPath: $('#cancellationApplicationFormPath').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        filingCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        filingCode: $('#filingCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        filingPaper: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 255,
                    message: '不能超过255位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        filingPaper: $('#filingPaper').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        filingCard: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 255,
                    message: '不能超过255位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        filingCard: $('#filingCard').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        auditStatus: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 5,
                    message: '不能超过5位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        auditStatus: $('#auditStatus').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        auditReject: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 255,
                    message: '不能超过255位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentFilingCancellation/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        auditReject: $('#auditReject').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
    }
    });
    }
</script>
</body>
</html>