<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>建筑起重机械设备使用登记和备案系统</title>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" th:href="@{/resources/bootstrap/css/bootstrap.min.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/dist/css/font-awesome.min.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/dist/css/ionicons.min.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/dist/css/AdminLTE.min.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/dist/css/skins/all-skins.min.css}">

    <!--http://aimodu.org:7777/admin/index_iframe.html?q=audio&search=#-->
    <style type="text/css">
        html {
            overflow: hidden;
        }
        .logo-lg{
            line-height: normal;
            font-size: 18px;
            padding-top: 1px;
        }
    </style>
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script th:src="@{/resources/awi/plugins/ie9/html5shiv.min.js}"></script>
    <script th:src="@{/resources/awi/plugins/ie9/respond.min.js}"></script>
    <![endif]-->

</head>
<body class="hold-transition skin-blue-light ski sidebar-mini fixed">
<div id="wrapper">

    <header class="main-header">
        <!-- Logo -->
        <a href="#" class="logo">
            <!-- mini logo for sidebar mini 50x50 pixels -->
            <span class="logo-mini">河南省建筑起重机械管理信息系统</span>
            <!-- logo for regular state and mobile devices -->
            <span class="logo-lg">河南省建筑起重机械管理信息系统</span>
        </a>
        <!--顶部右侧导航-->
        <nav class="navbar navbar-static-top" role="navigation">
            <a href="#" class="sidebar-toggle" data-toggle="offcanvas" role="button">
                <span class="sr-only">Toggle navigation</span>
            </a>
            <div class="navbar-custom-menu">
                <ul class="nav navbar-nav">
                    <li class="dropdown user user-menu">
                        <a href="" class="dropdown-toggle" data-toggle="dropdown">
                            <img th:if="${session.loginUser.photo!=null && session.loginUser.photo!=''}"
                                 th:src="${session.loginUser.photo}" class="user-image"
                                 alt="User Image">
                            <img th:if="${session.loginUser.photo==null || session.loginUser.photo==''}"
                                 th:src="@{/resources/awi/dist/img/user4-128x128.jpg}" class="user-image"
                                 alt="User Image">
                            <span class="hidden-xs">欢迎：[[${session.loginUser.name}]]</span>
                        </a>
                        <ul class="dropdown-menu animated fadeInRight">
                            <li>
                                <a href="javascript:void(0);" id="personalInfo">个人资料</a>
                            </li>
                            <li>
                                <a href="/admin/logout">安全退出</a>
                            </li>
                        </ul>

                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- Left side column. contains the logo and sidebar -->
    <aside class="main-sidebar">
        <!-- sidebar: style can be found in sidebar.less -->
        <section class="sidebar">
            <!-- Sidebar user panel -->
            <div class="user-panel">
                <div class="pull-left image">
                    <img th:if="${session.loginUser.photo!=null && session.loginUser.photo!=''}"
                         th:src="${session.loginUser.photo}" class="img-circle" alt="User Image">
                    <img th:if="${session.loginUser.photo==null || session.loginUser.photo==''}"
                         th:src="@{/resources/awi/dist/img/user4-128x128.jpg}" class="img-circle" alt="User Image">
                </div>
                <div class="pull-left info">
                    <p>[[${session.loginUser.name}]]</p>
                    <a href="#"><i class="fa fa-circle text-success"></i> 在线</a>
                </div>
            </div>
            <!-- search form -->
            <!--<form action="#" method="get" class="sidebar-form">
                <div class="input-group">
                    <input type="text" name="q" class="form-control" placeholder="Search...">
                    <span class="input-group-btn">
                <button type="button" name="search" id="search-btn" class="btn btn-flat" onclick="search_menu()"><i
                        class="fa fa-search"></i>
                </button>
              </span>
                </div>
            </form>-->
            <!-- /.search form -->
            <ul class="sidebar-menu">
            </ul>
        </section>
        <!-- /.sidebar -->
    </aside>

    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper" id="content-wrapper" style="min-height: 421px;">
        <!--bootstrap tab风格 多标签页-->
        <div class="content-tabs">
            <button class="roll-nav roll-left tabLeft" onclick="scrollTabLeft()">
                <i class="fa fa-backward"></i>
            </button>
            <nav class="page-tabs menuTabs tab-ui-menu" id="tab-menu">
                <div class="page-tabs-content" style="margin-left: 0px;">

                </div>
            </nav>
            <button class="roll-nav roll-right tabRight" onclick="scrollTabRight()">
                <i class="fa fa-forward" style="margin-left: 3px;"></i>
            </button>
            <div class="btn-group roll-nav roll-right">
                <button class="dropdown tabClose" data-toggle="dropdown">
                    页签操作<i class="fa fa-caret-down" style="padding-left: 3px;"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-right" style="min-width: 128px;">
                    <li><a class="tabReload" href="javascript:refreshTab();">刷新当前</a></li>
                    <li><a class="tabCloseCurrent" href="javascript:closeCurrentTab();">关闭当前</a></li>
                    <li><a class="tabCloseAll" href="javascript:closeOtherTabs(true);">全部关闭</a></li>
                    <li><a class="tabCloseOther" href="javascript:closeOtherTabs();">除此之外全部关闭</a></li>
                </ul>
            </div>
            <button class="roll-nav roll-right fullscreen" onclick="App.handleFullScreen()"><i
                    class="fa fa-arrows-alt"></i></button>
        </div>
        <div class="content-iframe " style="background-color: #ffffff; ">
            <div class="tab-content " id="tab-content">

            </div>
        </div>
    </div>
    <!-- /.content-wrapper -->

    <footer class="main-footer">
        <div class="pull-right hidden-xs">
            <b>Version</b>1.0.0
        </div>
        <strong>技术支持：<a target="_blank" href="http://www.tzstcl.com">天筑科技股份有限公司</a></strong>
    </footer>

</div>
<!-- ./wrapper -->

<!-- jQuery 2.2.3 -->
<script th:src="@{/resources/awi/plugins/jQuery/jquery-2.2.3.min.js}"></script>
<!-- Bootstrap 3.3.6 -->
<script th:src="@{/resources/bootstrap/js/bootstrap.min.js}"></script>
<!-- Slimscroll -->
<script th:src="@{/resources/awi/plugins/slimScroll/jquery.slimscroll.min.js}"></script>
<!-- FastClick -->
<script th:src="@{/resources/awi/plugins/fastclick/fastclick.js}"></script>
<!-- AdminLTE App -->
<script th:src="@{/resources/awi/dist/js/app.js}"></script>
<!-- AdminLTE for demo purposes -->
<script th:src="@{/resources/awi/dist/js/demo.js}"></script>

<!--tabs-->
<script th:src="@{/resources/awi/dist/js/app_iframe.js}"></script>

<script th:src="@{/resources/common/common.js}"></script>
<script th:src="@{/resources/echarts/echarts.common.min.js}"></script>


<script type="text/javascript">
    /**
     * 本地搜索菜单
     */
    function search_menu() {
        //要搜索的值
        var text = $('input[name=q]').val();

        var $ul = $('.sidebar-menu');
        $ul.find("a.nav-link").each(function () {
            var $a = $(this).css("border", "");

            //判断是否含有要搜索的字符串
            if ($a.children("span.menu-text").text().indexOf(text) >= 0) {

                //如果a标签的父级是隐藏的就展开
                $ul = $a.parents("ul");

                if ($ul.is(":hidden")) {
                    $a.parents("ul").prev().click();
                }

                //点击该菜单
                $a.click().css("border", "1px solid");

//                return false;
            }
        });
    }

    function escape2Html(str) {
        var arrEntities = {'lt': '<', 'gt': '>', 'nbsp': ' ', 'amp': '&', 'quot': '"'};
        return str.replace(/&(lt|gt|nbsp|amp|quot);/ig, function (all, t) {
            return arrEntities[t];
        });
    }

    $(function () {
        App.setbasePath("/resources/");
        App.setGlobalImgPath("awi/dist/img/");

        addTabs({
            id: '10001',
            title: '欢迎页',
            close: false,
            url: '/admin/sys/welcome',
            urlType: "abosulte"
        });

        App.fixIframeCotent();

        var menus = eval("(" + escape2Html('[[${menus}]]') + ")");
        $('.sidebar-menu').sidebarMenu({data: menus});
    });

    $("#personalInfo").click(function () {
        addTabs({
            id: '10002',
            title: '个人资料',
            close: true,
            url: '/admin/sysUser/toPersonalInfo',
            urlType: "abosulte"
        });
    })
</script>
</body>
</html>
