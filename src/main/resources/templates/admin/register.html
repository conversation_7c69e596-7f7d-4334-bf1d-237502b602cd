<!DOCTYPE html>
<html lang="en">
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>注册</title>
    <link rel="stylesheet" th:href="@{/resources/bootstrap/css/bootstrap.min.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/bootstrapvalidator/css/bootstrapValidator.min.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/laydate/theme/default/laydate.css}">
    <script th:src="@{/resources/awi/plugins/jQuery/jquery-2.2.3.min.js}"></script>
    <script th:src="@{/resources/awi/plugins/bootstrapvalidator/js/bootstrapValidator.js}"></script>
    <script th:src="@{/resources/awi/plugins/bootstrapvalidator/js/language/zh_CN.js}"></script>
    <script th:src="@{/resources/awi/plugins/layer/layer.js}"></script>
    <script th:src="@{/resources/common/common.js}"></script>
    <style type="text/css">
        * {
            margin: 0;
            padding: 0;
            color: #353535;
        }

        a {
            text-decoration: none;
        }

        .w1200 {
            width: 70%;
            margin: 0 auto;
            overflow: hidden;
        }

        .registerBan {
            background-size: 100% 100%;
        }

        .logo {
            width: 146px;
            display: block;
            margin-top: 27px;
        }

        h2 {
            font-size: 40px;
            color: #fff;
            padding: 20px 0;
            text-align: center;
            margin-left: -15%;
        }

        .registerBan p {
            margin-left: -13%;
            padding-bottom: 50px;
        }

        .registerBan p span {
            margin-right: 36px;
            padding: 0 30px;
            display: inline-block;
            line-height: 38px;
            color: #fff;
            border-radius: 20px;
            border: 1px solid #fff;
        }

        label {
            display: inline-block;
            min-width: 80px;
            text-align: right;
            font-size: 16px;
            letter-spacing: 1px;
        }

        form {
            margin: 20px auto;
            width: 700px;
        }

        form h3 {
            margin: 40px 0 50px;
            font-size: 24px;
            font-weight: normal;
            text-align: center;
        }

        form p {
            margin-bottom: 25px;
        }

        form input {
            margin-left: 20px;
            padding: 10px 15px;
            min-width: 560px;
            border: 1px solid #E1E1E1;
            line-height: 20px;
        }

        form #agree {
            display: inline-block;
            width: auto;
            min-width: auto;
        }

        #saveBtn {
            margin: 70px auto 10px;
            display: block;
            line-height: 48px;
            width: 100%;
            background: #43A9FB;
            color: #fff;
            border: transparent;
            font-size: 16px;
            letter-spacing: 3px;
        }

        #captcha {
            margin-left: 30px;
            width: 230px;
            background: #43A9FB;
            color: #fff;
            border: transparent;
            line-height: 42px;
        }

    </style>
</head>
<body>
<div>
    <form id="addUserForm" action="" method="post">
        <h3>新用户注册</h3>
        <div class="form-group"><label for="name">用户名：</label><br><input type="text" id="name" name="name" placeholder="输入用户名" onblur=""></div>
        <div class="form-group"><label>社会统一信用代码:</label><br><input type="text" id="mobile" name="mobile" placeholder="请输入企业社会统一信用代码"></div>
        <div class="form-group"><label>登录名:</label><br><input type="text" id="loginName" name="loginName" placeholder="请输入账号登录名"></div>
        <div class="form-group"><label>设置密码</label><br><input type="password" id="password" name="password" placeholder="密码必须包含大小写字母、数字、特殊字符、至少八位"></div>
        <div class="form-group"><label>确认密码</label><br><input type="password" id="password2" name="password2" placeholder="密码必须包含大小写字母、数字、特殊字符、至少八位"></div>
        </p>
        <button type="button" id="saveBtn" onclick="register()" >注册</button>
        <p style="text-align: right;">已有账号，<a href="/admin/sysUser/toLogin" style="color: #43A9FB;">马上登录</a></p>
    </form>
</div>
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>

<script>
    function toLogin() {
        window.location.href="/admin/sysUser/toLogin";
    }

    function register(){
        $("#saveBtn").attr("disabled");
        $('#addUserForm').data('bootstrapValidator').validate();
        var bootstrapValidator = $('#addUserForm').data('bootstrapValidator');
        var name = $('#name').val();
        var loginName = $('#loginName').val();
        var password = $("#password").val();
        var mobile = $("#mobile").val();
        console.log(bootstrapValidator.isValid());
        if(bootstrapValidator.isValid()) {
            // if(/^(?![A-Za-z0-9]+$)(?![a-z0-9\W]+$)(?![A-Za-z\W]+$)(?![A-Z0-9\W]+$)[a-zA-Z0-9\W]{8,}$/.test(password) || /(?![0-9A-Z]+$)(?![0-9a-z]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,18}$/.test(password)){
            if(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&.#])[A-Za-z\d@$!%*?&.#]{8,16}$/.test(password)){

            }else {
                layer.msg("密码必须包含大小写字母、数字、特殊字符、至少八位");
                return false;
            }
            $.ajax({
                url:"/admin/sysUser/register",
                type:"POST",
                dataType:"json",
                data:{
                    "name":name,
                    "loginName":loginName,
                    "password":password,
                    "mobile":mobile
                },
                success:function (res) {
                    if(res.code==0){
                        layer.msg("注册成功，跳转至登录",{time: 900, icon: 1});
                        setTimeout(toLogin, 1000);
                    }else{
                        layer.msg("注册失败");
                    }
                },

            })
        }
    }

    $('#addUserForm').bootstrapValidator({
        message: '输入值不满足要求',
        excluded : [':disabled'],
        verbose:false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
        fields: {
            name: {
                validators: {
                    notEmpty: {
                        message: '必填'
                    },
                    stringLength: {
                        max: 64,
                        message: '不能超过64位'
                    }
                }
            },
            mobile: {
                validators: {
                    notEmpty: {
                        message: '必填'
                    },
                    regexp: {
                        regexp: /[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}/,
                        message: '请输入正确18位社会统一信用代码'
                    },
                    stringLength: {
                        max: 128,
                        message: '不能超过128位'
                    }
                }
            },
            loginName: {
                validators: {
                    notEmpty: {
                        message: '必填'
                    },
                    stringLength: {
                        max: 128,
                        message: '不能超过128位'
                    },
                    remote: {
                        // 验证地址
                        url: '/admin/sysUser/checkLoginName',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                            return {
                                laborSign: $("#loginName").val()
                            };
                        },
                        message: '用户名已被占用',
                        // 每秒检测两次
                        delay: 500,
                        //请求方式
                        type: 'POST'
                    }
                }
            },
            password: {
                validators: {
                    notEmpty: {
                        message: '必填'
                    },
                    stringLength: {
                        min: 8,
                        max: 16,
                        message: '密码必须包含大小写字母、数字、特殊字符、至少八位'
                    },
                    //相同
                    identical: {
                        field: 'password2',
                        message: '两次密码不一致'
                    },
                    regexp: {
                        regexp: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&.#])[A-Za-z\d@$!%*?&.#]{8,16}$/,
                        message: '密码必须包含大小写字母、数字、特殊字符、至少八位'
                    }
                }
            },
            password2: {
                validators: {
                    notEmpty: {
                        message: '必填'
                    },
                    stringLength: {
                        min: 8,
                        max: 16,
                        message: '密码必须包含大小写字母、数字、特殊字符、至少八位'
                    },
                    identical: {
                        field: 'password',
                        message: '两次密码不一致'
                    },
                    regexp: {
                        regexp: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&.#])[A-Za-z\d@$!%*?&.#]{8,16}$/,
                        message: '密码必须包含大小写字母、数字、特殊字符、至少八位'
                    }
                }
            }
        }
    });
    $('#addUserForm').data('bootstrapValidator').validate();
</script>
</body>
</html>