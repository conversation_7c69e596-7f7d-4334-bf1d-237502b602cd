<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="个人资料"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<link rel="stylesheet" th:href="@{/resources/file-input/css/fileinput.min.css}">
<body class="skin-blue sidebar-mini  pace-done" style="height: auto;">
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <!-- form start -->
            <form id="form" class="form-horizontal">
                <input type="hidden" id="id" name="id">
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-3">
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name" class="col-sm-2 control-label">用户名</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="name" th:value="*{name}"
                                           autocomplete="off" readonly="readonly">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="oldPassword" class="col-sm-2 control-label">旧密码</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="oldPassword" name="oldPassword"
                                           autocomplete="off" placeholder="若不修改密码，请不要填写">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="newPassword" class="col-sm-2 control-label">密码</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="newPassword" name="newPassword"
                                           autocomplete="off" placeholder="若不修改密码，请不要填写">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="rePassword" class="col-sm-2 control-label">重新输入密码</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="rePassword" name="rePassword"
                                           autocomplete="off" placeholder="若不修改密码，请不要填写">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="file" class="col-sm-2 control-label">用户头像</label>
                                <div class="col-sm-10">
                                    <input type="file" id="file" name="file" class="file-loading">
                                    <input type="hidden" id="photo" name="photo">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="remarks" class="col-sm-2 control-label">用户签名</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="remarks" name="remarks"
                                           autocomplete="off" placeholder="个人描述">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                        </div>
                        <div class="col-md-6">
                            <div class="col-md-2">
                            </div>
                            <div class="col-sm-10">
                                <button type="button" class="btn btn-info" id="updateRelation" onclick="saveForm()">提交</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.box-body -->
            </form>
            <!-- customer form end -->
        </div>
    </div>
</section>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/file-input/js/fileinput.min.js}"></script>
<script th:src="@{/resources/file-input/js/locales/zh.js}"></script>
</body>

<script th:inline="javascript">

    function saveForm() {

        var password = $("#newPassword").val();
        var rePassword = $("#rePassword").val();
        // if(/^(?![A-Za-z0-9]+$)(?![a-z0-9\W]+$)(?![A-Za-z\W]+$)(?![A-Z0-9\W]+$)[a-zA-Z0-9\W]{8,}$/.test(password) || /(?![0-9A-Z]+$)(?![0-9a-z]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,18}$/.test(password)){
        if(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&.#])[A-Za-z\d@$!%*?&.#]{8,16}$/.test(password)){

        }else {
            layer.msg("密码必须包含大小写字母、数字、特殊字符、至少八位");
            return false;
        }
        if (password!= rePassword) {
            layer.msg("两次密码不一致！");
            return false;
        }

        $.ajax({
            url: '/admin/sysUser/updateUserInfo',
            data: $("#form").serialize(),
            dataType: 'json',
            type: 'post',
            success: function (result) {
                if (result.code == 0) {
                    layer.alert('修改成功,再次登录后生效', function () {
                        parent.location.reload();
                    });
                }else if(result.code == -1){
                    layer.alert(result.msg, {icon: 5})
                }
            },
            error: function () {
                layer.alert("修改失败！", {icon: 5})
            }
        })
    }

    //初始化文件上传组件
    var _fileinput = $("#file").fileinput({
        // you must set a valid URL here else you will get an error
        uploadUrl: '/uploadImg',
        allowedFileExtensions: ['jpg', 'png', 'gif'],
        overwriteInitial: false,
        maxFileSize: 1000,
        maxFilesNum: 1,
        showPreview: false,
        showUpload: true,
        showCaption: true,
        language: 'zh',
        showUpload: true
    });
    _fileinput.on("fileuploaded", function (event, resp, previewId, index) {
        console.log(resp);
        var _fileinfo = resp.response;
        $("#photo").val(_fileinfo.fileUrl)
    });


</script>
</html>