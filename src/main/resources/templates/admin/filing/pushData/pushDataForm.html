<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head >
    <title th:text="推送全国表表单"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body >

    <div class="container-fluid">
                <!-- form start -->
                <form id="form" class="form-horizontal">
                    <input type="hidden" id="id" name="id">
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="businessDataId" class="col-sm-2 control-label">business_data_id</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="businessDataId" name="businessDataId" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="areaCode" class="col-sm-2 control-label">行政区划代码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="areaCode" name="areaCode" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="certNum" class="col-sm-2 control-label">证照编号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="certNum" name="certNum" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="issuAuthName" class="col-sm-2 control-label">发证机关</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="issuAuthName" name="issuAuthName" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="issuAuthCode" class="col-sm-2 control-label">发证机关代码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="issuAuthCode" name="issuAuthCode" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="issuDate" class="col-sm-2 control-label">发证日期</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="issuDate" name="issuDate" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="deviceCategoryCode" class="col-sm-2 control-label">类别代码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="deviceCategoryCode" name="deviceCategoryCode" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="deviceModel" class="col-sm-2 control-label">规格型号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="deviceModel" name="deviceModel" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="factoryNum" class="col-sm-2 control-label">出厂编号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="factoryNum" name="factoryNum" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="recordNum" class="col-sm-2 control-label">备案编号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="recordNum" name="recordNum" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="manufactureCorpName" class="col-sm-2 control-label">制造单位</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="manufactureCorpName" name="manufactureCorpName" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="manufactureCorpCode" class="col-sm-2 control-label">制造单位统一社会信用代码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="manufactureCorpCode" name="manufactureCorpCode" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="propertyCorpName" class="col-sm-2 control-label">产权单位</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="propertyCorpName" name="propertyCorpName" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="propertyCorpCode" class="col-sm-2 control-label">产权单位统一社会信用代码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="propertyCorpCode" name="propertyCorpCode" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="projectName" class="col-sm-2 control-label">工程名称</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="projectName" name="projectName" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="projectLocation" class="col-sm-2 control-label">工程项目地址</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="projectLocation" name="projectLocation" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="projectAreaCode" class="col-sm-2 control-label">工程项目地址所在区/县级区划代码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="projectAreaCode" name="projectAreaCode" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="applyConstructionPermit" class="col-sm-2 control-label">是否办理施工许可证</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="applyConstructionPermit" name="applyConstructionPermit" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="constructionPermitNum" class="col-sm-2 control-label">建筑工程施工许可证编号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="constructionPermitNum" name="constructionPermitNum" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="useCorpList" class="col-sm-2 control-label">使用单位</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="useCorpList" name="useCorpList" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="maintenanceCorpName" class="col-sm-2 control-label">维保单位</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="maintenanceCorpName" name="maintenanceCorpName" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="maintenanceCorpCode" class="col-sm-2 control-label">维保单位统一社会信用代码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="maintenanceCorpCode" name="maintenanceCorpCode" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="useCorpManager" class="col-sm-2 control-label">使用单位项目负责人</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="useCorpManager" name="useCorpManager" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="useCorpManagerId" class="col-sm-2 control-label">使用单位项目负责人身份证件号码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="useCorpManagerId" name="useCorpManagerId" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="installCorpName" class="col-sm-2 control-label">安装单位</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="installCorpName" name="installCorpName" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="installCorpCode" class="col-sm-2 control-label">安装单位统一社会信用代码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="installCorpCode" name="installCorpCode" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="testCorpName" class="col-sm-2 control-label">检测单位</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="testCorpName" name="testCorpName" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="testCorpCode" class="col-sm-2 control-label">检测单位统一社会信用代码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="testCorpCode" name="testCorpCode" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="testDate" class="col-sm-2 control-label">检测日期</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="testDate" name="testDate" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="certStatus" class="col-sm-2 control-label">证书状态代码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="certStatus" name="certStatus" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="certStatusDescription" class="col-sm-2 control-label">证书状态描述</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="certStatusDescription" name="certStatusDescription" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="associatedCertId" class="col-sm-2 control-label">关联证照标识</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="associatedCertId" name="associatedCertId" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="businessInformation" class="col-sm-2 control-label">业务信息</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="businessInformation" name="businessInformation" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="operateType" class="col-sm-2 control-label">操作类型</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="operateType" name="operateType" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="pushFlag" class="col-sm-2 control-label">推送标识</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="pushFlag" name="pushFlag" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="pushTime" class="col-sm-2 control-label">推送时间</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="pushTime" name="pushTime" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="installDate" class="col-sm-2 control-label">安装时间</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="installDate" name="installDate" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="installPosition" class="col-sm-2 control-label">安装位置</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="installPosition" name="installPosition" autocomplete="off">
                                    </div>
                                </div>
                            </div>


                        <div class="form-group">
                            <button type="button" class="btn btn-info col-sm-offset-6" onclick="formSave()">提交</button>
                        </div>
                          <!-- /.box-body -->
                </form>
              <!-- customer form end -->
    </div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";
    var index = parent.layer.getFrameIndex(window.name);
    initValidator();
    function formSave(){
        var bootstrapValidator = $('#form').data('bootstrapValidator').validate();
        if(bootstrapValidator.isValid()) {
            savepop('/admin/pushData/add' ,'form','saveBtn')
        }
    }

    function initValidator() {
        $('#form').bootstrapValidator({
            message: '输入值不满足要求',
            excluded : [':disabled',':hidden'],
            verbose:false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
        businessDataId: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        businessDataId: $('#businessDataId').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        areaCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        areaCode: $('#areaCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        certNum: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        certNum: $('#certNum').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        issuAuthName: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        issuAuthName: $('#issuAuthName').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        issuAuthCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        issuAuthCode: $('#issuAuthCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        issuDate: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        issuDate: $('#issuDate').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        deviceCategoryCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        deviceCategoryCode: $('#deviceCategoryCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        deviceModel: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        deviceModel: $('#deviceModel').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        factoryNum: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        factoryNum: $('#factoryNum').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        recordNum: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        recordNum: $('#recordNum').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        manufactureCorpName: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        manufactureCorpName: $('#manufactureCorpName').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        manufactureCorpCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        manufactureCorpCode: $('#manufactureCorpCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        propertyCorpName: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        propertyCorpName: $('#propertyCorpName').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        propertyCorpCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        propertyCorpCode: $('#propertyCorpCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        projectName: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        projectName: $('#projectName').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        projectLocation: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        projectLocation: $('#projectLocation').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        projectAreaCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        projectAreaCode: $('#projectAreaCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        applyConstructionPermit: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        applyConstructionPermit: $('#applyConstructionPermit').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        constructionPermitNum: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        constructionPermitNum: $('#constructionPermitNum').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        useCorpList: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 0,
                    message: '不能超过0位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        useCorpList: $('#useCorpList').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        maintenanceCorpName: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        maintenanceCorpName: $('#maintenanceCorpName').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        maintenanceCorpCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        maintenanceCorpCode: $('#maintenanceCorpCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        useCorpManager: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        useCorpManager: $('#useCorpManager').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        useCorpManagerId: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        useCorpManagerId: $('#useCorpManagerId').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        installCorpName: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        installCorpName: $('#installCorpName').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        installCorpCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        installCorpCode: $('#installCorpCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        testCorpName: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        testCorpName: $('#testCorpName').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        testCorpCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        testCorpCode: $('#testCorpCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        testDate: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        testDate: $('#testDate').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        certStatus: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        certStatus: $('#certStatus').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        certStatusDescription: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        certStatusDescription: $('#certStatusDescription').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        associatedCertId: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        associatedCertId: $('#associatedCertId').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        businessInformation: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        businessInformation: $('#businessInformation').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        operateType: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        operateType: $('#operateType').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        pushFlag: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        pushFlag: $('#pushFlag').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        pushTime: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 0,
                    message: '不能超过0位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        pushTime: $('#pushTime').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        installDate: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        installDate: $('#installDate').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        installPosition: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        installPosition: $('#installPosition').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
    }
    });
    }
</script>
</body>
</html>