<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="推送全国表"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-sm-1" for="searchName">名称</label>
                    <div class="col-sm-3">
                        <input type="text" class="form-control col-sm-10" id="searchName" name="name"
                               placeholder="请输入名称">
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="restForm('searchForm')"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="formSearch('pushDatatable','/admin/pushData/list' ,'searchForm')">
                            <i class="fa fa-search"></i>查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <table id="pushDatatable"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";

    var addFlag = [[${@permission.hasPermi('pushData:add')}]];
    var editFlag = [[${@permission.hasPermi('pushData:edite')}]];
    var delFlag = [[${@permission.hasPermi('pushData:delete')}]];
    var designFlag = [[${@permission.hasPermi('pushData:design')}]];

    $(function () {
        var columns = [
            {
                field: 'ck',
                checkbox: true,
                align: 'center',
                valign: 'middle'
            },
            {
                field: "businessDataId",
                title: "business_data_id",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "areaCode",
                title: "行政区划代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "certNum",
                title: "证照编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "issuAuthName",
                title: "发证机关",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "issuAuthCode",
                title: "发证机关代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "issuDate",
                title: "发证日期",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "deviceCategoryCode",
                title: "类别代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "deviceModel",
                title: "规格型号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "factoryNum",
                title: "出厂编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "recordNum",
                title: "备案编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "manufactureCorpName",
                title: "制造单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "manufactureCorpCode",
                title: "制造单位统一社会信用代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "propertyCorpName",
                title: "产权单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "propertyCorpCode",
                title: "产权单位统一社会信用代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "projectName",
                title: "工程名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "projectLocation",
                title: "工程项目地址",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "projectAreaCode",
                title: "工程项目地址所在区/县级区划代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "applyConstructionPermit",
                title: "是否办理施工许可证",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "constructionPermitNum",
                title: "建筑工程施工许可证编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "useCorpList",
                title: "使用单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "maintenanceCorpName",
                title: "维保单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "maintenanceCorpCode",
                title: "维保单位统一社会信用代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "useCorpManager",
                title: "使用单位项目负责人",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "useCorpManagerId",
                title: "使用单位项目负责人身份证件号码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installCorpName",
                title: "安装单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installCorpCode",
                title: "安装单位统一社会信用代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "testCorpName",
                title: "检测单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "testCorpCode",
                title: "检测单位统一社会信用代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "testDate",
                title: "检测日期",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "certStatus",
                title: "证书状态代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "certStatusDescription",
                title: "证书状态描述",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "associatedCertId",
                title: "关联证照标识",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "businessInformation",
                title: "业务信息",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "operateType",
                title: "操作类型",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "pushFlag",
                title: "推送标识",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "pushTime",
                title: "推送时间",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installDate",
                title: "安装时间",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installPosition",
                title: "安装位置",
                align: 'center',
                valign: 'middle'
            },
            {
                field: 'createTime',
                title: '创建时间',
                align: 'center',
                valign: 'middle'
            },
            {
                field: 'id',
                title: '操作',
                align: 'center',
                valign: 'middle',
                cellStyle:{
                    css:{"white-space":"nowrap"}
                },
                formatter: function (value, row, index) {
                    var actions = [];
                    if ("hidden" != editFlag) {
                        actions.push('<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="updatelayer(\'' + value + '\',\'编辑\',\'/admin/pushData/edit/\')"><i class="fa fa-edit"></i>编辑</button>');
                    }
                    if ("hidden" != designFlag) {
                        actions.push('<button id="btn_detail" type="button" class="btn btn-success  btn-xs" onclick="detaillayer(\'' + value + '\',\'详情\',\'/admin/pushData/detail/\')"><i class="fa fa-eye"></i>详情</button>');
                    }
                    if ("hidden" != delFlag) {
                        actions.push('<button type="button" class="btn btn-danger  btn-xs" onclick="deletes(\'/admin/pushData/delete\',\'' + value + '\')" ><i class="fa fa-trash-o"></i>删除</button>');
                    }
                    return actions.join('');
                }
            }
        ];
        var toolbar = [];
        toolbar.push('<div id="toolbar" class="btn-group">');
        if("hidden" != addFlag){
            toolbar.push('<button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="poplayer(2,\'新增\',\'/admin/pushData/toForm\')"><i class="fa fa-plus"></i>新增</button>');
        }
        if("hidden" != delFlag){
            toolbar.push('<button id="btn_delete" type="button" class="btn btn-danger btn-xs" onclick="deletes(\'/admin/pushData/delete\')" ><i class="fa fa-trash-o"></i>删除</button>');
        }
        toolbar.push('</div>');
        createBootstrapTable($("#pushDatatable"), '/admin/pushData/list', columns, false, toolbar.join(''), 'server');
    });
</script>
</body>
</html>