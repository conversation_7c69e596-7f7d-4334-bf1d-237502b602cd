<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="推送全国表详情"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="pushDataDetail" class="container-fluid" >
    <form id="pushData-form-edit" class="form-horizontal" autocomplete="off" th:object="${pushData}">
        <input type="hidden" id="id" name="id" th:value="*{id}"/>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="businessDataId" class="col-sm-4 control-label require">business_data_id</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="businessDataId" name="businessDataId" th:value="*{businessDataId}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="areaCode" class="col-sm-4 control-label require">行政区划代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="areaCode" name="areaCode" th:value="*{areaCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="certNum" class="col-sm-4 control-label require">证照编号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="certNum" name="certNum" th:value="*{certNum}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="issuAuthName" class="col-sm-4 control-label require">发证机关</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="issuAuthName" name="issuAuthName" th:value="*{issuAuthName}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="issuAuthCode" class="col-sm-4 control-label require">发证机关代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="issuAuthCode" name="issuAuthCode" th:value="*{issuAuthCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="issuDate" class="col-sm-4 control-label require">发证日期</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="issuDate" name="issuDate" th:value="*{issuDate}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="deviceCategoryCode" class="col-sm-4 control-label require">类别代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="deviceCategoryCode" name="deviceCategoryCode" th:value="*{deviceCategoryCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="deviceModel" class="col-sm-4 control-label require">规格型号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="deviceModel" name="deviceModel" th:value="*{deviceModel}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="factoryNum" class="col-sm-4 control-label require">出厂编号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="factoryNum" name="factoryNum" th:value="*{factoryNum}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="recordNum" class="col-sm-4 control-label require">备案编号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="recordNum" name="recordNum" th:value="*{recordNum}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="manufactureCorpName" class="col-sm-4 control-label require">制造单位</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="manufactureCorpName" name="manufactureCorpName" th:value="*{manufactureCorpName}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="manufactureCorpCode" class="col-sm-4 control-label require">制造单位统一社会信用代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="manufactureCorpCode" name="manufactureCorpCode" th:value="*{manufactureCorpCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="propertyCorpName" class="col-sm-4 control-label require">产权单位</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="propertyCorpName" name="propertyCorpName" th:value="*{propertyCorpName}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="propertyCorpCode" class="col-sm-4 control-label require">产权单位统一社会信用代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="propertyCorpCode" name="propertyCorpCode" th:value="*{propertyCorpCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="projectName" class="col-sm-4 control-label require">工程名称</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="projectName" name="projectName" th:value="*{projectName}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="projectLocation" class="col-sm-4 control-label require">工程项目地址</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="projectLocation" name="projectLocation" th:value="*{projectLocation}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="projectAreaCode" class="col-sm-4 control-label require">工程项目地址所在区/县级区划代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="projectAreaCode" name="projectAreaCode" th:value="*{projectAreaCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="applyConstructionPermit" class="col-sm-4 control-label require">是否办理施工许可证</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="applyConstructionPermit" name="applyConstructionPermit" th:value="*{applyConstructionPermit}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="constructionPermitNum" class="col-sm-4 control-label require">建筑工程施工许可证编号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="constructionPermitNum" name="constructionPermitNum" th:value="*{constructionPermitNum}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="useCorpList" class="col-sm-4 control-label require">使用单位</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="useCorpList" name="useCorpList" th:value="*{useCorpList}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="maintenanceCorpName" class="col-sm-4 control-label require">维保单位</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="maintenanceCorpName" name="maintenanceCorpName" th:value="*{maintenanceCorpName}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="maintenanceCorpCode" class="col-sm-4 control-label require">维保单位统一社会信用代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="maintenanceCorpCode" name="maintenanceCorpCode" th:value="*{maintenanceCorpCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="useCorpManager" class="col-sm-4 control-label require">使用单位项目负责人</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="useCorpManager" name="useCorpManager" th:value="*{useCorpManager}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="useCorpManagerId" class="col-sm-4 control-label require">使用单位项目负责人身份证件号码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="useCorpManagerId" name="useCorpManagerId" th:value="*{useCorpManagerId}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="installCorpName" class="col-sm-4 control-label require">安装单位</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="installCorpName" name="installCorpName" th:value="*{installCorpName}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="installCorpCode" class="col-sm-4 control-label require">安装单位统一社会信用代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="installCorpCode" name="installCorpCode" th:value="*{installCorpCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="testCorpName" class="col-sm-4 control-label require">检测单位</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="testCorpName" name="testCorpName" th:value="*{testCorpName}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="testCorpCode" class="col-sm-4 control-label require">检测单位统一社会信用代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="testCorpCode" name="testCorpCode" th:value="*{testCorpCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="testDate" class="col-sm-4 control-label require">检测日期</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="testDate" name="testDate" th:value="*{testDate}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="certStatus" class="col-sm-4 control-label require">证书状态代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="certStatus" name="certStatus" th:value="*{certStatus}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="certStatusDescription" class="col-sm-4 control-label require">证书状态描述</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="certStatusDescription" name="certStatusDescription" th:value="*{certStatusDescription}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="associatedCertId" class="col-sm-4 control-label require">关联证照标识</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="associatedCertId" name="associatedCertId" th:value="*{associatedCertId}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="businessInformation" class="col-sm-4 control-label require">业务信息</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="businessInformation" name="businessInformation" th:value="*{businessInformation}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="operateType" class="col-sm-4 control-label require">操作类型</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="operateType" name="operateType" th:value="*{operateType}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="pushFlag" class="col-sm-4 control-label require">推送标识</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="pushFlag" name="pushFlag" th:value="*{pushFlag}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="pushTime" class="col-sm-4 control-label require">推送时间</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="pushTime" name="pushTime" th:value="*{pushTime}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="installDate" class="col-sm-4 control-label require">安装时间</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="installDate" name="installDate" th:value="*{installDate}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="installPosition" class="col-sm-4 control-label require">安装位置</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="installPosition" name="installPosition" th:value="*{installPosition}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
        <div class="box-footer col-md-6 col-md-offset-5">
            <button type="button" class="btn btn-info pull-right" onclick="formClose()">关闭</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script type="text/javascript">
    function formClose() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    }
</script>
</body>
</html>
