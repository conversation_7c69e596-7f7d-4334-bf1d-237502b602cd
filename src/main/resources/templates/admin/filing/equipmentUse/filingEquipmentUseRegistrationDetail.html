<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备使用备案表详情"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="filingEquipmentUseRegistrationDetail" class="container-fluid" >
    <form id="filingEquipmentUseRegistration-form-edit" class="form-horizontal" autocomplete="off" th:object="${filingEquipmentUseRegistration}">
        <input type="hidden" id="id" name="id" th:value="*{id}"/>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="useUnit" class="col-sm-4 control-label require">使用单位</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="useUnit" name="useUnit" th:value="*{useUnit}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="userContact" class="col-sm-4 control-label require">使用单位联系人</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="userContact" name="userContact" th:value="*{userContact}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="userContactNumber" class="col-sm-4 control-label require">使用单位联系电话</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="userContactNumber" name="userContactNumber" th:value="*{userContactNumber}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="propertyUnit" class="col-sm-4 control-label require">产权单位</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="propertyUnit" name="propertyUnit" th:value="*{propertyUnit}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="deviceName" class="col-sm-4 control-label require">设备名称</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="deviceName" name="deviceName" th:value="*{deviceName}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="specificationModel" class="col-sm-4 control-label require">规格型号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="specificationModel" name="specificationModel" th:value="*{specificationModel}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="factoryTime" class="col-sm-4 control-label require">出厂日期</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="factoryTime" name="factoryTime" th:value="*{factoryTime}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="deviceFilingCode" class="col-sm-4 control-label require">设备备案编号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="deviceFilingCode" name="deviceFilingCode" th:value="*{deviceFilingCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="engineeringName" class="col-sm-4 control-label require">工程名称</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="engineeringName" name="engineeringName" th:value="*{engineeringName}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="projectManager" class="col-sm-4 control-label require">项目经理</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="projectManager" name="projectManager" th:value="*{projectManager}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="installationUnit" class="col-sm-4 control-label require">安装单位</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="installationUnit" name="installationUnit" th:value="*{installationUnit}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="installationUnitQualificationLevel" class="col-sm-4 control-label require">安装单位资质等级</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="installationUnitQualificationLevel" name="installationUnitQualificationLevel" th:value="*{installationUnitQualificationLevel}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="siteInstallationLeader" class="col-sm-4 control-label require">现场安装负责人</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="siteInstallationLeader" name="siteInstallationLeader" th:value="*{siteInstallationLeader}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="installationUnitQualificationCertificateNumber" class="col-sm-4 control-label require">安装单位资质证号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="installationUnitQualificationCertificateNumber" name="installationUnitQualificationCertificateNumber" th:value="*{installationUnitQualificationCertificateNumber}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="installationUnitLicenseSafetyPermitNumber" class="col-sm-4 control-label require">安装单位安全许可证号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="installationUnitLicenseSafetyPermitNumber" name="installationUnitLicenseSafetyPermitNumber" th:value="*{installationUnitLicenseSafetyPermitNumber}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="installationStartTime" class="col-sm-4 control-label require">安装开始时间</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="installationStartTime" name="installationStartTime" th:value="*{installationStartTime}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="installationEndTime" class="col-sm-4 control-label require">安装终止时间</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="installationEndTime" name="installationEndTime" th:value="*{installationEndTime}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="testingUnit" class="col-sm-4 control-label require">检验检测单位</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="testingUnit" name="testingUnit" th:value="*{testingUnit}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="testingDate" class="col-sm-4 control-label require">检测日期</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="testingDate" name="testingDate" th:value="*{testingDate}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="testingLeadere" class="col-sm-4 control-label require">检验检测负责人</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="testingLeadere" name="testingLeadere" th:value="*{testingLeadere}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="jointInspectionDate" class="col-sm-4 control-label require">联合验收日期</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="jointInspectionDate" name="jointInspectionDate" th:value="*{jointInspectionDate}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="firstInstallationHeight" class="col-sm-4 control-label require">首次安装高度，单位：米</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="firstInstallationHeight" name="firstInstallationHeight" th:value="*{firstInstallationHeight}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="finalUseHeight" class="col-sm-4 control-label require">最终使用高度，单位：米</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="finalUseHeight" name="finalUseHeight" th:value="*{finalUseHeight}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="equipmentType" class="col-sm-4 control-label require">设备类别，T塔式起重机，S施工升降机(不含物料提升机)，W物料提升机，Q其他起重机械</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="equipmentType" name="equipmentType" th:value="*{equipmentType}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="auditRejectReason" class="col-sm-4 control-label require">审核驳回意见</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="auditRejectReason" name="auditRejectReason" th:value="*{auditRejectReason}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="auditStatus" class="col-sm-4 control-label require">确认状态，0未审核，1审核通过，2审核驳回</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="auditStatus" name="auditStatus" th:value="*{auditStatus}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="auditTime" class="col-sm-4 control-label require">审核时间，YYYY-MM-DD HH:mm:ss</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="auditTime" name="auditTime" th:value="*{auditTime}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="useFilingCode" class="col-sm-4 control-label require">备案编号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="useFilingCode" name="useFilingCode" th:value="*{useFilingCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
        <div class="box-footer col-md-6 col-md-offset-5">
            <button type="button" class="btn btn-info pull-right" onclick="formClose()">关闭</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script type="text/javascript">
    function formClose() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    }
</script>
</body>
</html>
