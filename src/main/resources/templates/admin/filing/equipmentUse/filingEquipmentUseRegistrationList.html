<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备使用备案表"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-sm-1" for="deviceFilingCode">设备编号</label>
                    <div class="col-sm-2">
                        <input type="text" class="form-control col-sm-10" id="deviceFilingCode" name="deviceFilingCode"
                               placeholder="请输入名称">
                    </div>
                    <label class="control-label col-sm-1" for="cancel">登记类型</label>
                    <div class="col-sm-2">
                        <select  class="form-control col-sm-10" id="cancel" name="cancel"
                                 placeholder="请输入名称">
                            <option value="" selected disabled>全部</option>
                            <option value="false">使用登记</option>
                            <option value="true">注销登记</option>
                        </select>
                    </div>
                        <label class="control-label col-sm-1" for="equipmentType">设备类型</label>
                    <div class="col-sm-2">
                        <select class="form-control col-sm-10" id="equipmentType" name="equipmentType">
                            <option value="">请选择</option>
                            <option value="T">塔式起重机</option>
                            <option value="S">施工升降机(不含物料提升机)</option>
                            <option value="W">物料提升机</option>
                            <option value="Q">其他起重机械</option>
                        </select>
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="restForm('searchForm')"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="formSearch('filingEquipmentUseRegistrationtable','/checkMessage/useList' ,'searchForm')">
                            <i class="fa fa-search"></i>查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <table id="filingEquipmentUseRegistrationtable"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";
    $(function () {
        var columns = [
            {
                field: 'ck',
                checkbox: true,
                align: 'center',
                valign: 'middle'
            },
            {
                field: "useUnit",
                title: "使用单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "userContact",
                title: "使用单位联系人",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "propertyUnit",
                title: "产权单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "deviceName",
                title: "设备名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "specificationModel",
                title: "规格型号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "deviceFilingCode",
                title: "设备备案编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "engineeringName",
                title: "工程名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "projectManager",
                title: "项目经理",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installationUnit",
                title: "安装单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installationStartTime",
                title: "安装开始时间",
                align: 'center',
                valign: 'middle',
                formatter: function (value) {
                    return changeDateFormat("yyyy-MM-dd", value);
                }
            },
            {
                field: "testingUnit",
                title: "检验检测单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "testingDate",
                title: "检测日期",
                align: 'center',
                valign: 'middle',
                formatter: function (value) {
                    return changeDateFormat("yyyy-MM-dd", value);
                }
            },
            {
                field: "testingLeadere",
                title: "检验检测负责人",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "equipmentType",
                title: "设备类别",
                align: 'center',
                valign: 'middle',
                formatter:function(value,row,index){
                    if(value=='T'){
                        return "塔式起重机"
                    } else if(value=='S'){
                        return "施工升降机"
                    }else if (value=="W"){
                        return "物料提升机"
                    }else if (value=='Q'){
                        return "其他"
                    }
                }

            },
            {
                field: "useFilingCode",
                title: "使用登记编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "cancellationStatus",
                title: "注销状态",
                align: 'center',
                valign: 'middle',
                formatter:function (value,raw,index) {
                    if (value==1){
                        return "已注销"
                    } else if(value==0){
                        return "未注销"
                    }
                }
            },

        ];
        var toolbar = [];
        createBootstrapTable($("#filingEquipmentUseRegistrationtable"), '/checkMessage/useList', columns, false, toolbar.join(''), 'server');
    });
</script>
</body>
</html>
