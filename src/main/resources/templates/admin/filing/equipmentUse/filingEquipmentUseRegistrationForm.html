<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head >
    <title th:text="设备使用备案表表单"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body >

    <div class="container-fluid">
                <!-- form start -->
                <form id="form" class="form-horizontal">
                    <input type="hidden" id="id" name="id">
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="useUnit" class="col-sm-2 control-label">使用单位</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="useUnit" name="useUnit" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="userContact" class="col-sm-2 control-label">使用单位联系人</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="userContact" name="userContact" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="userContactNumber" class="col-sm-2 control-label">使用单位联系电话</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="userContactNumber" name="userContactNumber" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="propertyUnit" class="col-sm-2 control-label">产权单位</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="propertyUnit" name="propertyUnit" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="deviceName" class="col-sm-2 control-label">设备名称</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="deviceName" name="deviceName" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="specificationModel" class="col-sm-2 control-label">规格型号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="specificationModel" name="specificationModel" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="factoryTime" class="col-sm-2 control-label">出厂日期</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="factoryTime" name="factoryTime" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="deviceFilingCode" class="col-sm-2 control-label">设备备案编号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="deviceFilingCode" name="deviceFilingCode" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="engineeringName" class="col-sm-2 control-label">工程名称</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="engineeringName" name="engineeringName" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="projectManager" class="col-sm-2 control-label">项目经理</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="projectManager" name="projectManager" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="installationUnit" class="col-sm-2 control-label">安装单位</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="installationUnit" name="installationUnit" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="installationUnitQualificationLevel" class="col-sm-2 control-label">安装单位资质等级</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="installationUnitQualificationLevel" name="installationUnitQualificationLevel" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="siteInstallationLeader" class="col-sm-2 control-label">现场安装负责人</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="siteInstallationLeader" name="siteInstallationLeader" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="installationUnitQualificationCertificateNumber" class="col-sm-2 control-label">安装单位资质证号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="installationUnitQualificationCertificateNumber" name="installationUnitQualificationCertificateNumber" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="installationUnitLicenseSafetyPermitNumber" class="col-sm-2 control-label">安装单位安全许可证号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="installationUnitLicenseSafetyPermitNumber" name="installationUnitLicenseSafetyPermitNumber" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="installationStartTime" class="col-sm-2 control-label">安装开始时间</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="installationStartTime" name="installationStartTime" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="installationEndTime" class="col-sm-2 control-label">安装终止时间</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="installationEndTime" name="installationEndTime" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="testingUnit" class="col-sm-2 control-label">检验检测单位</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="testingUnit" name="testingUnit" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="testingDate" class="col-sm-2 control-label">检测日期</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="testingDate" name="testingDate" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="testingLeadere" class="col-sm-2 control-label">检验检测负责人</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="testingLeadere" name="testingLeadere" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="jointInspectionDate" class="col-sm-2 control-label">联合验收日期</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="jointInspectionDate" name="jointInspectionDate" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="firstInstallationHeight" class="col-sm-2 control-label">首次安装高度，单位：米</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="firstInstallationHeight" name="firstInstallationHeight" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="finalUseHeight" class="col-sm-2 control-label">最终使用高度，单位：米</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="finalUseHeight" name="finalUseHeight" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="equipmentType" class="col-sm-2 control-label">设备类别，T塔式起重机，S施工升降机(不含物料提升机)，W物料提升机，Q其他起重机械</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="equipmentType" name="equipmentType" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="auditRejectReason" class="col-sm-2 control-label">审核驳回意见</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="auditRejectReason" name="auditRejectReason" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="auditStatus" class="col-sm-2 control-label">确认状态，0未审核，1审核通过，2审核驳回</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="auditStatus" name="auditStatus" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="auditTime" class="col-sm-2 control-label">审核时间，YYYY-MM-DD HH:mm:ss</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="auditTime" name="auditTime" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="useFilingCode" class="col-sm-2 control-label">备案编号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="useFilingCode" name="useFilingCode" autocomplete="off">
                                    </div>
                                </div>
                            </div>


                        <div class="form-group">
                            <button type="button" class="btn btn-info col-sm-offset-6" onclick="formSave()">提交</button>
                        </div>
                          <!-- /.box-body -->
                </form>
              <!-- customer form end -->
    </div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";
    var index = parent.layer.getFrameIndex(window.name);
    initValidator();
    function formSave(){
        var bootstrapValidator = $('#form').data('bootstrapValidator').validate();
        if(bootstrapValidator.isValid()) {
            savepop('/admin/filingEquipmentUseRegistration/add' ,'form','saveBtn')
        }
    }

    function initValidator() {
        $('#form').bootstrapValidator({
            message: '输入值不满足要求',
            excluded : [':disabled',':hidden'],
            verbose:false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
        useUnit: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        useUnit: $('#useUnit').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        userContact: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        userContact: $('#userContact').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        userContactNumber: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 15,
                    message: '不能超过15位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        userContactNumber: $('#userContactNumber').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        propertyUnit: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        propertyUnit: $('#propertyUnit').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        deviceName: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 30,
                    message: '不能超过30位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        deviceName: $('#deviceName').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        specificationModel: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 30,
                    message: '不能超过30位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        specificationModel: $('#specificationModel').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        factoryTime: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        factoryTime: $('#factoryTime').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        deviceFilingCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        deviceFilingCode: $('#deviceFilingCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        engineeringName: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 50,
                    message: '不能超过50位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        engineeringName: $('#engineeringName').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        projectManager: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 10,
                    message: '不能超过10位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        projectManager: $('#projectManager').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        installationUnit: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        installationUnit: $('#installationUnit').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        installationUnitQualificationLevel: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        installationUnitQualificationLevel: $('#installationUnitQualificationLevel').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        siteInstallationLeader: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 10,
                    message: '不能超过10位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        siteInstallationLeader: $('#siteInstallationLeader').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        installationUnitQualificationCertificateNumber: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        installationUnitQualificationCertificateNumber: $('#installationUnitQualificationCertificateNumber').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        installationUnitLicenseSafetyPermitNumber: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        installationUnitLicenseSafetyPermitNumber: $('#installationUnitLicenseSafetyPermitNumber').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        installationStartTime: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 0,
                    message: '不能超过0位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        installationStartTime: $('#installationStartTime').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        installationEndTime: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 0,
                    message: '不能超过0位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        installationEndTime: $('#installationEndTime').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        testingUnit: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 20,
                    message: '不能超过20位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        testingUnit: $('#testingUnit').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        testingDate: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 0,
                    message: '不能超过0位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        testingDate: $('#testingDate').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        testingLeadere: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 10,
                    message: '不能超过10位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        testingLeadere: $('#testingLeadere').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        jointInspectionDate: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 0,
                    message: '不能超过0位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        jointInspectionDate: $('#jointInspectionDate').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        firstInstallationHeight: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 10,
                    message: '不能超过10位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        firstInstallationHeight: $('#firstInstallationHeight').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        finalUseHeight: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 10,
                    message: '不能超过10位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        finalUseHeight: $('#finalUseHeight').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        equipmentType: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 1,
                    message: '不能超过1位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        equipmentType: $('#equipmentType').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        auditRejectReason: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 255,
                    message: '不能超过255位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        auditRejectReason: $('#auditRejectReason').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        auditStatus: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 1,
                    message: '不能超过1位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        auditStatus: $('#auditStatus').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        auditTime: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 0,
                    message: '不能超过0位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        auditTime: $('#auditTime').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        useFilingCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 30,
                    message: '不能超过30位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/filingEquipmentUseRegistration/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        useFilingCode: $('#useFilingCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
    }
    });
    }
</script>
</body>
</html>
