<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <title>index</title>
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/datepicker/datepicker3.css}">

    <style>
        .contaienr {
            padding: 10px;
            background-color: #f2f2f2;
        }

        .numContainer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            /*border-top: 2px solid #ccc;*/
            /*border-bottom: 2px solid #ccc;*/
            color: #fff;
            background-color: #FFFFFF;
            padding: 30px 15px;
        }

        .numContainer > div {
            width: 13%;
            height: 100px;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }

        .application {
            background-color: #1E88E5;
        }

        .applicationCancel {
            background-color: #00ACC1;
        }

        .install {
            background-color: #43A047;
        }

        .uninstall {
            background-color: #C0CA33;
        }

        .use {
            background-color: #FFA000;
        }

        .useCancel {
            background-color: #F4511E;
        }

        .bigFont {
            font-size: 26px;
        }

        /*222222222*/
        .summarizing {
            margin: 15px 0;
            padding: 10px;
        }

        .summarizingHistogarm {
            height: 500px;
        }

        /*333333333*/
        .ranking {
            margin: 15px 0;
            padding: 10px;
        }

        .echartsContainer {
            display: flex;
            gap: 10px;
        }

        .rankingMap {
            height: 600px;
            width: 50%;
        }

        .rankingHistogram {
            height: 600px;
            width: 50%;
        }

        .titleContainer {
            margin: 10px 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .title {
            font-weight: bold;
        }

        .more {
            font-size: 14px;
            color: #999;
            cursor: pointer;
        }

        .selectInput {
            height: 34px;
            outline: #FFFFFF;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 2px 6px;
            width: 200px;
            color: #757575;
        }

        .datepicker {
            padding: 0 6px;
        }

        .label {
            font-size: 14px;
            margin-left: 10px;
        }
        .el-cascader{
            width: auto!important;
        }
        #search{
            padding: 6px 12px;
            background: #3c8dbc;
            border-radius: 4px;
            color: #fff;
            cursor: pointer;
        }
    </style>
</head>
<body>
<div class="contaienr">
    <div class="titleContainer">
        <span class="title">数据总览</span>
        <div>
            <span class="label">区域：</span>
            <select type="text" class="selectInput" id="city" name="city" value="410000"
                    autocomplete="off"></select>
            <span class="label">年份：</span>
            <input type="text" class="datepicker selectInput" id="year" name="year"
                   autocomplete="off" placeholder="请选择年份">
            <span class="label" id="search">搜索</span>

        </div>
    </div>
    <div class="numContainer">
        <div class="application">
            <div>已备案设备</div>
            <div class="bigFont" id="filingApplicationCount">0</div>
        </div>
        <div class="applicationCancel">
            <div>已注销设备</div>
            <div class="bigFont" id="cancellationCount">0</div>

        </div>
        <div class="install">
            <div>已安装设备</div>
            <div class="bigFont" id="installationFormCount">0</div>

        </div>
        <div class="uninstall">
            <div>已拆卸设备</div>
            <div class="bigFont" id="uninstallationFormCount">0</div>

        </div>
        <div class="use">
            <div>已使用登记设备</div>
            <div class="bigFont" id="registrationFormCount">0</div>

        </div>
        <div class="useCancel">
            <div>已注销使用登记设备</div>
            <div class="bigFont" id="useCancellationCount">0</div>
        </div>
    </div>
    <div class="summarizing">
        <div class="titleContainer">
            <span class="title">地市汇总</span>
            <span class="more">更多</span>
        </div>
        <div class="summarizingHistogarm" id="summarizingHistogarm"></div>
    </div>
    <div class="ranking">
        <div class="titleContainer">
            <span class="title">数据排名</span>
            <div>
                <span class="label">统计维度：</span>
                <select class="selectInput" id="searchType">
                    <option value="0">备案申请统计</option>
                    <option value="1">备案注销统计</option>
                    <option value="2">安装申请统计</option>
                    <option value="3">拆卸申请统计</option>
                    <option value="4">使用登记申请统计</option>
                    <option value="5">使用登记注销统计</option>
                </select>
            </div>

            <!--            </span>-->

        </div>
        <div class="echartsContainer">
            <div class="rankingMap" id="rankingMap"></div>
            <div class="rankingHistogram" id="rankingHistogram"></div>
        </div>
    </div>
</div>

<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/awi/plugins/jQuery/jquery-2.2.3.min.js}"></script>
<script th:src="@{/resources/awi/plugins/echarts/echarts.min.js}"></script>
<script th:src="@{/resources/awi/plugins/datepicker/bootstrap-datepicker.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/layui_layui.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/cascader.min.js}"></script>
<script type="text/javascript" th:inline="javascript">
    let myChartSummarizing = echarts.init(document.getElementById('summarizingHistogarm'));
    let myChartRankingMap = echarts.init(document.getElementById('rankingMap'));
    let myChartRankingHistogram = echarts.init(document.getElementById('rankingHistogram'));
    let summarizingData = []
    let rankingData = []
    let optionSummarizing
    let optionRankingMap
    let optionRankingHistogram
    let date = new Date()

    $('.datepicker').datepicker({
        format: 'yyyy',
        language: "zh-CN",
        autoclose: true,
        clearBtn: false,
        startView: 2,
        minViewMode: 2,
        maxViewMode: 2,
    }).datepicker("setDate", 'now');

    $(function () {
        layui.use('layCascader', function () {
            // var id = 0;
            var layCascader = layui.layCascader;
            var areaLayCascader = layCascader({
                elem: '#city',
                placeholder: '河南省',
                props: {
                    checkStrictly: true,
                    lazy: true,
                    lazyLoad: function (node, resolve) {
                        // $("#city").trigger('change')
                        var level = node.level;
                        //0
                        if (level == 0 && node.root) {
                            resolve([{label: '河南省', value: 410000, leaf: false}]);
                        } else {
                            $.ajax({
                                url: '/admin/area/getCountyList',
                                type: 'GET', // 请求类型
                                data: {
                                    parentId: node.value
                                },
                                dataType: 'json',
                                success: function (response) {
                                    var nodes = response.map(function (item) {
                                        return {
                                            value: item.id,
                                            label: item.name,
                                            leaf: level >= 1
                                        };
                                    });
                                    resolve(nodes);
                                },
                                error: function (error) {
                                    console.log('加载子节点数据失败:', error);
                                    resolve([]);

                                }
                            });
                        }
                    }
                }
            });
            areaLayCascader.changeEvent(function (value, node) {
                areaLayCascader.close()
            })
        });
    })
    $("#search").click(function () {
        getNum($("#city").val(), $("#year").val())
    })

    // $('#year').datepicker().on('changeDate', function (e) {
    //     getNum($("#city").val(), $("#year").val())
    // });
    //
    // // $("#city").change(function () {
    // setTimeout(()=>{
    //     $(".el-input__inner").blur(function () {
    //         console.log('+++++++++')
    //         getNum($("#city").val(), $("#year").val())
    //     })
    // },500)


    $("#searchType").change(function () {
        getRanking($('#searchType').val())
        initChartOptions()
        myChartRankingMap.setOption(optionRankingMap);

        myChartRankingHistogram.setOption(optionRankingHistogram, true);
    })
    getNum(410000, $("#year").val())
    getSummarizing()
    getRanking('0')

    function initChartOptions() {
        optionSummarizing = {
            backgroundColor: "#fff",
            title: {
                show: false
            },
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(13,5,30,.6)',
                borderWidth: 1,
                borderColor: 'rgba(13,5,30,.6)',
                padding: 5,
                textStyle: {
                    color: '#fff'
                }
            },
            legend: {
                right: 'center',
                top: '30',
                width: '800',
                itemWidth: 14,
                itemHeight: 10,
                itemGap: 16,
                textStyle: {
                    fontSize: 14,
                    fontFamily: 'Source Han Sans CN',
                    fontWeight: 'normal',
                    color: '#000'
                }
            },
            grid: {
                x: '3%',
                y: '15%',
                x2: '3%',
                y2: '5%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                minInterval: 1,
                axisLabel: {
                    // color: 'rgba(255, 255, 255, 0.8)',
                    color: '#000',
                    fontSize: 14,
                    interval: 0,
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#2D4377'
                    }
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: false,
                    lineStyle: {
                        color: '#2D4377',
                        type: "dotted",
                    }
                },
                data: summarizingData.map((item) => {
                    return item.areaName
                })
            },
            yAxis: {
                type: 'value',
                // name: "(人)",
                min: 0,
                position: 'left',
                nameTextStyle: {
                    color: "#000",
                    fontSize: 14,
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#2D4377'
                    }
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#2D4377',
                        type: "dotted",
                    }
                },
                axisLabel: {
                    formatter: '{value}',
                    textStyle: {
                        color: "#000",
                        // color: "#FFFFFF",
                    }
                },
            },
            series: [{
                name: '设备备案申请',
                type: 'bar',
                barWidth: 10,
                barGap: '40%',
                itemStyle: {
                    borderWidth: 1,
                    borderRadius: [3, 3, 0, 0],
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0, color: '#1E88E5' // 0% 处的颜色
                        }, {
                            offset: 1, color: '#1565C0' // 100% 处的颜色
                        }]
                    }
                },
                data: summarizingData.map((item) => {
                    return item.filingApplicationCount
                })
            },
                {
                    name: '设备备案注销申请',
                    type: 'bar',
                    barWidth: 10,
                    barGap: '40%',
                    itemStyle: {
                        borderWidth: 1,
                        borderRadius: [3, 3, 0, 0],
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0, color: '#00ACC1' // 0% 处的颜色
                            }, {
                                offset: 1, color: '#00838F' // 100% 处的颜色
                            }]
                        }
                    },
                    data: summarizingData.map((item) => {
                        return item.cancellationCount
                    })
                }, {
                    name: '设备安装申请',
                    type: 'bar',
                    barWidth: 10,
                    barGap: '40%',
                    itemStyle: {
                        borderWidth: 1,
                        borderRadius: [3, 3, 0, 0],
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0, color: '#43A047' // 0% 处的颜色
                            }, {
                                offset: 1, color: '#2E7D32' // 100% 处的颜色
                            }]
                        }
                    },
                    data: summarizingData.map((item) => {
                        return item.installationFormCount
                    })
                }, {
                    name: '设备拆卸申请',
                    type: 'bar',
                    barWidth: 10,
                    barGap: '40%',
                    itemStyle: {
                        borderWidth: 1,
                        borderRadius: [3, 3, 0, 0],
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0, color: '#C0CA33' // 0% 处的颜色
                            }, {
                                offset: 1, color: '#9E9D24' // 100% 处的颜色
                            }]
                        }
                    },
                    data: summarizingData.map((item) => {
                        return item.uninstallationFormCount
                    })
                },
                {
                    name: '设备使用登记申请',
                    type: 'bar',
                    barWidth: 10,
                    barGap: '40%',
                    itemStyle: {
                        borderWidth: 1,
                        borderRadius: [3, 3, 0, 0],
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0, color: '#FFB300' // 0% 处的颜色
                            }, {
                                offset: 1, color: '#FF8F00' // 100% 处的颜色
                            }]
                        }
                    },
                    data: summarizingData.map((item) => {
                        return item.registrationFormCount
                    })
                },
                {
                    name: '设备使用注销申请',
                    type: 'bar',
                    barWidth: 10,
                    barGap: '40%',
                    itemStyle: {
                        borderWidth: 1,
                        borderRadius: [3, 3, 0, 0],
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0, color: '#F4511E' // 0% 处的颜色
                            }, {
                                offset: 1, color: '#D84315' // 100% 处的颜色
                            }]
                        }
                    },
                    data: summarizingData.map((item) => {
                        return item.useCancellationCount
                    })
                }]
        };
        optionRankingMap = {
            backgroundColor: '#fff',
            tooltip: {
                trigger: 'item',
                formatter: '{b}<br/>{c}次',
                backgroundColor: 'rgba(13,5,30,.6)',
                borderWidth: 1,
                borderColor: 'rgba(13,5,30,.6)',
                padding: 5,
                textStyle: {
                    color: '#fff'
                }
            },
            visualMap: {
                min: 0,
                max: 100,
                left: '20',
                top: 'bottom',
                text: ['高', '低'],
                calculable: true,
                show: true,
                inRange: {
                    color: ['#ffffff', '#ddf0fd', '#bbe1fa', '#90bcd9', '#6597b8', '#3a7297']
                }
            },
            series: [{
                // name: '',
                type: 'map',
                map: 'henan',
                roam: false,
                // geoIndex: 0,
                label: {
                    normal: {
                        show: true
                    },
                    emphasis: {
                        show: true
                    }
                },

                layoutCenter: ['50%', '50%'],
                layoutSize: '90%',
                itemStyle: {
                    normal: {
                        areaColor: "#ffffff",
                        color: "#FFF",
                        label: {
                            show: true
                        }
                    },
                },
                // data: convertData(rankingData),
                data: rankingData,
            }]
        };
        optionRankingHistogram = {
            backgroundColor: "#fff",
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: 20,
                right: 40,
                top: 20,
                bottom: 20,
                containLabel: true
            },
            xAxis: {
                type: 'value',
                splitNumber: 3,
                minInterval: 1,
                axisTick: {
                    show: false
                },
                axisLine: {
                    show: false
                },
                axisLabel: {
                    textStyle: {
                        color: "#000",
                        fontSize: 14
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: '#ddd',
                        type: 'dashed'
                    }
                }
            },
            yAxis: {
                type: 'category',
                data: rankingData.map((item) => {
                    return item.name
                }),
                axisTick: {
                    show: false,
                    alignWithLabel: true
                },
                axisLine: {
                    show: false
                },
                axisLabel: {
                    interval: 0,
                    // rotate: 30,
                    textStyle: {
                        color: "#000",
                        fontSize: 14
                    }
                },
                splitLine: {
                    show: false
                }
            },
            series: [{
                type: 'bar',
                // showBackground: true,
                backgroundStyle: {
                    color: 'rgba(180, 180, 180, 0.2)'
                },
                barCategoryGap: '60%',
                itemStyle: {
                    borderRadius: 10,
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 1,
                        y2: 0,
                        colorStops: [
                            {offset: 0, color: '#3776cc'}, // 0% 处的颜色
                            {offset: 1, color: '#65dce8'} // 100% 处的颜色
                        ],
                        global: false // 缺省为 false
                    }
                },
                label: {
                    show: true,
                    position: 'right',
                    color: '#000',
                    fontSize: 14
                },
                data: rankingData.map((item) => {
                    return item.value
                })
            }]
        };
    }

    initChartOptions()

    function getNum(areaCode, year) {
        $.ajax({
            url: '/admin/index/headCode',
            method: 'get',
            data: {
                areaCode: areaCode,
                year: year
            },
            success: function (res) {
                $('.bigFont').each(function () {
                    var id = $(this).attr('id');
                    // 赋值
                    if (res.hasOwnProperty(id)) {
                        $(this).text(res[id] + '台');
                    }
                });
            }
        })
    }

    function getSummarizing() {
        $.ajax({
            url: '/admin/index/tableCode',
            method: 'get',
            async: false,
            success: function (res) {
                // summarizingData = res.slice(0, 11)
                summarizingData = res

            }
        })
    }

    function getRanking(code) {
        $.ajax({
            url: '/admin/index/mapCode',
            method: 'get',
            data: {
                code: code
            },
            async: false,
            success: function (res) {
                rankingData = res.map(obj => {
                    const key = Object.keys(obj)[0];
                    const value = obj[key];
                    return {
                        name: key,
                        value: value
                    };
                }).reverse();
                // console.log('rankingData',rankingData)
            }
        })
    }

    myChartSummarizing.setOption(optionSummarizing);


    let henan = "/resources/awi/plugins/echarts/map_henan.json";
    $.get(henan, function (csJson) {
        echarts.registerMap('henan', csJson);
        myChartRankingMap.setOption(optionRankingMap);
    })
    // myChartRankingMap.setOption(optionRankingMap);
    myChartRankingHistogram.setOption(optionRankingHistogram);
    $(".more").click(function () {
        detaillayer('more', '区县详情-更多', '/admin/index/')
        // window.location.href ='/admin/index/more'
    })
</script>
</body>
</html>