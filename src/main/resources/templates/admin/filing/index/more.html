<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备安装登记流程表"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <table id="table"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";

    $(function () {
        var columns = [
            {
                field: '',
                title: '序号',
                sortable: true,
                align: "center",
                width: 40,
                formatter: function (value, row, index) {
                    return index + 1;
                }
            },
            {
                field: "areaName",
                title: "地市名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "filingApplicationCount",
                title: "备案申请(次)",
                align: 'center',
                valign: 'middle',
            },
            {
                field: "cancellationCount",
                title: "备案注销(次)",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installationFormCount",
                title: "安装申请(次)",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "uninstallationFormCount",
                title: "拆卸申请(次)",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "registrationFormCount",
                title: "使用登记申请(次)",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "useCancellationCount",
                title: "使用登记注销(次)",
                align: 'center',
                valign: 'middle'
            },
        ];
        createBootstrapTable($("#table"), '/admin/index/tableCodeDetail', columns, false, '', 'server', false);
    });
</script>
</body>
</html>
