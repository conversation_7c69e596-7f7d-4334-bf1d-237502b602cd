<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="电子证照数据推送编辑"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="zjtQzjform" class="container-fluid" >

    <form id="zjtQzj-form-edit" class="form-horizontal" autocomplete="off" th:object="${zjtQzj}">
            <input type="hidden" id="id" name="id" th:value="*{id}"/>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="fmcode" class="col-sm-4 control-label require">二维码赋码</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="fmcode" name="fmcode" th:value="*{fmcode}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="certId" class="col-sm-4 control-label require">证照标识</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="certId" name="certId" th:value="*{certId}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="certPreviewUrl" class="col-sm-4 control-label require">证照预览地址</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="certPreviewUrl" name="certPreviewUrl" th:value="*{certPreviewUrl}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="syncFlag" class="col-sm-4 control-label require">同步标识</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="syncFlag" name="syncFlag" th:value="*{syncFlag}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="sysncDate" class="col-sm-4 control-label require">同步时间</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="sysncDate" name="sysncDate" th:value="*{sysncDate}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="certNum" class="col-sm-4 control-label require">证书编号</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="certNum" name="certNum" th:value="*{certNum}"/>
                   </div>
                </div>
            </div>
        <div class="form-group" >
            <button type="button" class="btn btn-info col-sm-offset-6" onclick="formSave()">提交</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>

<script type="text/javascript" th:inline="javascript">
    initValidator();
    function formSave(){
        var bootstrapValidator = $('#zjtQzj-form-edit').data('bootstrapValidator');
        if(bootstrapValidator.isValid()) {
            save('/zjtQzj/update' ,'zjtQzj-form-edit')
        }
    }
    function initValidator() {
        $('#zjtQzj-form-edit').bootstrapValidator({
            message: '输入值不满足要求',
            excluded : [':disabled',':hidden'],
            verbose:false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
        fmcode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/zjtQzj/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        fmcode: $('#fmcode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        certId: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/zjtQzj/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        certId: $('#certId').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        certPreviewUrl: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/zjtQzj/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        certPreviewUrl: $('#certPreviewUrl').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        syncFlag: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/zjtQzj/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        syncFlag: $('#syncFlag').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        sysncDate: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 6,
                    message: '不能超过6位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/zjtQzj/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        sysncDate: $('#sysncDate').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        certNum: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/zjtQzj/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        certNum: $('#certNum').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
    }
    });
        $('#zjtQzj-form-edit').data('bootstrapValidator').validate();
    }
</script>

</body>
</html>
