<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head >
    <title th:text="电子证照数据推送表单"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body >

    <div class="container-fluid">
                <!-- form start -->
                <form id="form" class="form-horizontal">
                    <input type="hidden" id="id" name="id">
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="fmcode" class="col-sm-2 control-label">二维码赋码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="fmcode" name="fmcode" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="certId" class="col-sm-2 control-label">证照标识</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="certId" name="certId" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="certPreviewUrl" class="col-sm-2 control-label">证照预览地址</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="certPreviewUrl" name="certPreviewUrl" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="syncFlag" class="col-sm-2 control-label">同步标识</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="syncFlag" name="syncFlag" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="sysncDate" class="col-sm-2 control-label">同步时间</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="sysncDate" name="sysncDate" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="certNum" class="col-sm-2 control-label">证书编号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="certNum" name="certNum" autocomplete="off">
                                    </div>
                                </div>
                            </div>


                        <div class="form-group">
                            <button type="button" class="btn btn-info col-sm-offset-6" onclick="formSave()">提交</button>
                        </div>
                          <!-- /.box-body -->
                </form>
              <!-- customer form end -->
    </div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";
    var index = parent.layer.getFrameIndex(window.name);
    initValidator();
    function formSave(){
        var bootstrapValidator = $('#form').data('bootstrapValidator').validate();
        if(bootstrapValidator.isValid()) {
            savepop('/admin/zjtQzj/add' ,'form','saveBtn')
        }
    }

    function initValidator() {
        $('#form').bootstrapValidator({
            message: '输入值不满足要求',
            excluded : [':disabled',':hidden'],
            verbose:false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
        fmcode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/zjtQzj/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        fmcode: $('#fmcode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        certId: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/zjtQzj/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        certId: $('#certId').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        certPreviewUrl: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/zjtQzj/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        certPreviewUrl: $('#certPreviewUrl').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        syncFlag: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/zjtQzj/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        syncFlag: $('#syncFlag').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        sysncDate: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 6,
                    message: '不能超过6位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/zjtQzj/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        sysncDate: $('#sysncDate').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        certNum: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/zjtQzj/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        certNum: $('#certNum').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
    }
    });
    }
</script>
</body>
</html>