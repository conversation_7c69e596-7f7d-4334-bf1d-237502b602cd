<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="电子证照数据推送详情"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="zjtQzjDetail" class="container-fluid" >
    <form id="zjtQzj-form-edit" class="form-horizontal" autocomplete="off" th:object="${zjtQzj}">
        <input type="hidden" id="id" name="id" th:value="*{id}"/>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="fmcode" class="col-sm-4 control-label require">二维码赋码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="fmcode" name="fmcode" th:value="*{fmcode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="certId" class="col-sm-4 control-label require">证照标识</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="certId" name="certId" th:value="*{certId}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="certPreviewUrl" class="col-sm-4 control-label require">证照预览地址</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="certPreviewUrl" name="certPreviewUrl" th:value="*{certPreviewUrl}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="syncFlag" class="col-sm-4 control-label require">同步标识</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="syncFlag" name="syncFlag" th:value="*{syncFlag}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="sysncDate" class="col-sm-4 control-label require">同步时间</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="sysncDate" name="sysncDate" th:value="*{sysncDate}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="certNum" class="col-sm-4 control-label require">证书编号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="certNum" name="certNum" th:value="*{certNum}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
        <div class="box-footer col-md-6 col-md-offset-5">
            <button type="button" class="btn btn-info pull-right" onclick="formClose()">关闭</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script type="text/javascript">
    function formClose() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    }
</script>
</body>
</html>
