<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备使用人员表详情"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="filingEquipmentUserDetail" class="container-fluid" >
    <form id="filingEquipmentUser-form-edit" class="form-horizontal" autocomplete="off" th:object="${filingEquipmentUser}">
        <input type="hidden" id="id" name="id" th:value="*{id}"/>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="useRegistrationId" class="col-sm-4 control-label require">设备使用备案id</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="useRegistrationId" name="useRegistrationId" th:value="*{useRegistrationId}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="name" class="col-sm-4 control-label require">姓名</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="name" name="name" th:value="*{name}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="workerType" class="col-sm-4 control-label require">工种</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="workerType" name="workerType" th:value="*{workerType}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="appointmentCertificateCode" class="col-sm-4 control-label require">上岗证号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="appointmentCertificateCode" name="appointmentCertificateCode" th:value="*{appointmentCertificateCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
        <div class="box-footer col-md-6 col-md-offset-5">
            <button type="button" class="btn btn-info pull-right" onclick="formClose()">关闭</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script type="text/javascript">
    function formClose() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    }
</script>
</body>
</html>
