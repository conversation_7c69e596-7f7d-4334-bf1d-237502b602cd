<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备使用人员表"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <table id="filingEquipmentUsertable"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";
    var useRegistrationId = [[${useRegistrationId}]];
    var addFlag = [[${@permission.hasPermi('filingEquipmentUser:add')}]];
    var editFlag = [[${@permission.hasPermi('filingEquipmentUser:edite')}]];
    var delFlag = [[${@permission.hasPermi('filingEquipmentUser:delete')}]];
    var designFlag = [[${@permission.hasPermi('filingEquipmentUser:design')}]];

    $(function () {
        var columns = [
            {
                field: "name",
                title: "姓名",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "workerType",
                title: "工种",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "appointmentCertificateCode",
                title: "上岗证号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: 'id',
                title: '操作',
                align: 'center',
                valign: 'middle',
                cellStyle:{
                    css:{"white-space":"nowrap"}
                },
                formatter: function (value, row, index) {
                    var actions = [];
                    if ("hidden" != editFlag) {
                        actions.push('<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="updatelayer(\'' + value + '\',\'编辑\',\'/admin/filingEquipmentUser/edit/\')"><i class="fa fa-edit"></i>编辑</button>');
                    }
                    if ("hidden" != designFlag) {
                        actions.push('<button id="btn_detail" type="button" class="btn btn-success  btn-xs" onclick="detaillayer(\'' + value + '\',\'详情\',\'/admin/filingEquipmentUser/detail/\')"><i class="fa fa-eye"></i>详情</button>');
                    }
                    if ("hidden" != delFlag){
                        actions.push('<button type="button" class="btn btn-danger  btn-xs" onclick="deletes(\'/admin/filingEquipmentUser/delete\',\'' + value + '\')" ><i class="fa fa-trash-o"></i>删除</button>');
                    }
                    return actions.join('');
                }
            }
        ];
        var toolbar = [];
        toolbar.push('<div id="toolbar" class="btn-group">');
        if("hidden" != addFlag){
            toolbar.push('<button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="detaillayer(\'' + useRegistrationId + '\',\'详情\',\'/admin/filingEquipmentUser/toForm/\')"><i class="fa fa-plus"></i>新增</button>');
        }
        if("hidden" != delFlag){
            toolbar.push('<button id="btn_delete" type="button" class="btn btn-danger btn-xs" onclick="deletes(\'/admin/filingEquipmentUser/delete\')" ><i class="fa fa-trash-o"></i>删除</button>');
        }
        toolbar.push('</div>');
        createBootstrapTable($("#filingEquipmentUsertable"), '/admin/filingEquipmentUser/list?useRegistrationId='+useRegistrationId, columns, false, toolbar.join(''), 'server');
    });
</script>
</body>
</html>