<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备使用人员表表单"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="container-fluid">
    <form id="form" class="form-horizontal">
        <input type="hidden" id="id" name="id">
        <div class="col-md-6" hidden="hidden">
            <div class="form-group">
                <label for="useRegistrationId" class="col-sm-2 control-label">设备使用备案id</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="useRegistrationId" name="useRegistrationId"
                           autocomplete="off">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="name" class="col-sm-2 control-label">姓名</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="name" name="name" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="workerType" class="col-sm-2 control-label">工种</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="workerType" name="workerType" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="appointmentCertificateCode" class="col-sm-2 control-label">上岗证号</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="appointmentCertificateCode"
                           name="appointmentCertificateCode" autocomplete="off">
                </div>
            </div>
        </div>


        <div class="form-group">
            <button type="button" class="btn btn-info col-sm-offset-6" onclick="formSave()">提交</button>
        </div>
        <!-- /.box-body -->
    </form>
    <!-- customer form end -->
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";
    var useRegistrationId = [[${useRegistrationId}]];
    $("#useRegistrationId").val(useRegistrationId);
    var index = parent.layer.getFrameIndex(window.name);
    initValidator();

    function formSave() {
        var bootstrapValidator = $('#form').data('bootstrapValidator').validate();
        if (bootstrapValidator.isValid()) {
            savepop('/admin/filingEquipmentUser/add', 'form', 'saveBtn')
        }
    }

    function initValidator() {
        $('#form').bootstrapValidator({
            message: '输入值不满足要求',
            excluded: [':disabled', ':hidden'],
            verbose: false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
                useRegistrationId: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        },
                        regexp: {
                            regexp: /^[a-zA-Z0-9_\.]+$/,
                            message: '由数字字母下划线和.组成'
                        },
                        remote: {
                            // 验证地址
                            url: '/filingEquipmentUser/checkUnique',
                            //自定义提交数据，默认值提交当前input value
                            data: function (validator) {
                                return {
                                    useRegistrationId: $('#useRegistrationId').val(),
                                    id: $('#id').val()
                                };
                            },
                            message: '已存在',
                            // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                            delay: 1000,
                            //请求方式
                            type: 'POST'
                        }
                    }
                },
                name: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 10,
                            message: '不能超过10位'
                        },
                        regexp: {
                            regexp: /^[a-zA-Z0-9_\.]+$/,
                            message: '由数字字母下划线和.组成'
                        },
                        remote: {
                            // 验证地址
                            url: '/filingEquipmentUser/checkUnique',
                            //自定义提交数据，默认值提交当前input value
                            data: function (validator) {
                                return {
                                    name: $('#name').val(),
                                    id: $('#id').val()
                                };
                            },
                            message: '已存在',
                            // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                            delay: 1000,
                            //请求方式
                            type: 'POST'
                        }
                    }
                },
                workerType: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        },
                        regexp: {
                            regexp: /^[a-zA-Z0-9_\.]+$/,
                            message: '由数字字母下划线和.组成'
                        },
                        remote: {
                            // 验证地址
                            url: '/filingEquipmentUser/checkUnique',
                            //自定义提交数据，默认值提交当前input value
                            data: function (validator) {
                                return {
                                    workerType: $('#workerType').val(),
                                    id: $('#id').val()
                                };
                            },
                            message: '已存在',
                            // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                            delay: 1000,
                            //请求方式
                            type: 'POST'
                        }
                    }
                },
                appointmentCertificateCode: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        },
                        regexp: {
                            regexp: /^[a-zA-Z0-9_\.]+$/,
                            message: '由数字字母下划线和.组成'
                        },
                        remote: {
                            // 验证地址
                            url: '/filingEquipmentUser/checkUnique',
                            //自定义提交数据，默认值提交当前input value
                            data: function (validator) {
                                return {
                                    appointmentCertificateCode: $('#appointmentCertificateCode').val(),
                                    id: $('#id').val()
                                };
                            },
                            message: '已存在',
                            // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                            delay: 1000,
                            //请求方式
                            type: 'POST'
                        }
                    }
                },
            }
        });
    }
</script>
</body>
</html>