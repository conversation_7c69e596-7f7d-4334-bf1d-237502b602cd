<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备使用注销表表单"></title>
    <th:block th:include="admin/include/head :: head"/>
    <link rel="stylesheet" th:href="@{/resources/file-input/css/fileinput.min.css}">
    <style>
        .container-fluid {
            padding-top: 20px;
        }

        .form-control-feedback {
            display: none !important;
        }
        .download {
            padding: 0;
            white-space: nowrap;
        }

        .download a {
            line-height: 36px;
            padding: 8px 6px;
            background-color: #337ab7;
            border-radius: 4px;
            color: #fff;
        }

        .download a:hover {
            background-color: #2e6da4;
            text-decoration: none;
        }
        .fa-download{
            margin-right: 6px;
        }
        .formSave {
            width: 130px;
            height: 40px;
        }
    </style>
</head>
<body>

<div class="container-fluid">
    <!-- form start -->
    <form id="form" class="form-horizontal">
        <input type="hidden" id="id" name="id" th:value="${id}">
        <input type="hidden" id="userId" name="userId" autocomplete="off" th:value="${userId}">
        <input type="hidden" id="engineeringAreaCode" name="engineeringAreaCode" autocomplete="off">
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useFilingCode" class="col-sm-3 control-label require">使用登记编号</label>
                    <div class="col-sm-7">
                        <input type="text" class="form-control" id="useFilingCode" name="useFilingCode"
                               autocomplete="off" placeholder="输入完整的使用登记编号，点击查询获取设备使用信息">
                    </div>
                    <div class="col-sm-2">
                        <button type="button" onclick="searchDecive()" class="btn btn-info" style="width: 100%">查询</button>
                    </div>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useUnit" class="col-sm-3 control-label require">使用单位</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="useUnit" name="useUnit" autocomplete="off"
                               readonly="readonly">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="propertyUnit" class="col-sm-3 control-label require">产权单位</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="propertyUnit" name="propertyUnit" autocomplete="off"
                               readonly="readonly">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="deviceName" class="col-sm-3 control-label require">设备名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="deviceName" name="deviceName" autocomplete="off"
                               readonly="readonly">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="specificationModel" class="col-sm-3 control-label require">规格型号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="specificationModel" name="specificationModel"
                               autocomplete="off" readonly="readonly">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="deviceFilingCode" class="col-sm-3 control-label require">设备备案编号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="deviceFilingCode" name="deviceFilingCode"
                               autocomplete="off" readonly="readonly">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="engineeringName" class="col-sm-3 control-label require">工程名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="engineeringName" name="engineeringName"
                               autocomplete="off" readonly="readonly">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="projectManager" class="col-sm-3 control-label require">项目经理</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="projectManager" name="projectManager" autocomplete="off"
                               readonly="readonly">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentType" class="col-sm-3 control-label require">设备类别</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="equipmentType" autocomplete="off"
                               readonly="readonly">
                        <input type="hidden" name="equipmentType" id="type">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="mcancellationApplicationPath" class="col-sm-3 control-label require">注销申请表</label>
                    <div class="col-sm-7">
                        <input type="file" multiple class="fileinput" name="uploadFile" id="mcancellationApplicationPath" data-type="cancellationApplicationPath">
                        <input type="text" name="cancellationApplicationPath" id="cancellationApplicationPath"
                               hidden="hidden">
                        <table class="table" id="cancellationApplicationPath-table"></table>
                    </div>
                    <div class="col-sm-2 download">
                        <a href="/resources/awi/dist/doc/use_cancel.docx" download="建筑起重机械设备使用登记注销申请表"><i class="fa fa-download"></i>模版下载</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="museRegistrationPath" class="col-sm-3 control-label require">使用备案证</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" name="uploadFile" id="museRegistrationPath" data-type="useRegistrationPath">
                        <input type="text" name="useRegistrationPath" id="useRegistrationPath" hidden="hidden">
                        <table class="table" id="useRegistrationPath-table"></table>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <button type="button" class="btn btn-info formSave col-sm-offset-6" onclick="formSave()">提交</button>
        </div>
        <!-- /.box-body -->
    </form>
    <!-- customer form end -->
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/file-input/js/fileinput.min.js}"></script>
<script th:src="@{/resources/file-input/js/locales/zh.js}"></script>
<script th:inline="javascript">
    "use strict";
    searchDecive()
    initValidator();
    /**控制日期格式
     */
    $('.datepicker').datepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        autoclose: true,
        todayHighlight: true,
        //控制可选的最晚月份，为当前月
        endDate: new Date()
    });

    /**
     *file上传
     */
    $(function () {
        //文件上传
        $.each($(".fileinput"), function (index, item) {
            var type = $(item).data("type");
            $(item).fileinput({
                uploadUrl: '/admin/filingEquipmentUseCancellation/uploadFile',
                required: false,
                overwriteInitial: false,
                maxFileSize: 3072,
                maxFilesNum: 1,
                showPreview: true,
                showUpload: true,
                showCaption: true,
                language: 'zh',
                uploadAsync: true,
                dropZoneEnabled:false
            }).on("fileuploaded", function (event, data) {
                var _data = data.response.data;
                _data.fileType = type;
                addFile(_data, type);
            });
        });
    })

    function addFile(_fileinfo, fileConnect) {
        var fileName = _fileinfo.fileName;
        var fileUrl = _fileinfo.fileUrl;
        var filePath = _fileinfo.filePath;
        var fileSuffix = _fileinfo.fileSuffix;
        var fileType = _fileinfo.fileType;
        var id = $("#id").val().toString();
        var connectId = id + "cancellation" + fileConnect;
        var file = {
            fileName: fileName,
            fileUrl: fileUrl,
            filePath: filePath,
            fileSuffix: fileSuffix,
            fileType: fileType,
            connectId: connectId
        };
        $.ajax({
            url: "/admin/filingFile/fileUpload",
            data: file,
            type: "POST",
            success: function (res) {
                var eld = $("#" + fileConnect).val();
                var fileIdArray;
                if (eld == '') {
                    fileIdArray = [];
                } else {
                    fileIdArray = (eld + "").split(",");
                }
                fileIdArray.push(res.data.toString());
                var ids = fileIdArray.join(",").replace(/undefined\,/, "");
                $("#" + fileConnect).val(ids).change();
                var line = "<tr><td><a href='" + fileUrl + "' download='" + fileName + "'>" + fileName + "</a></td><td><span class='del' onclick=del(this,'" + fileConnect + "','" + res.data + "') style='cursor:pointer; color:#ff101a; text-align: center; line-height: 34px;'>删除</span></td></tr>";
                $("#" + fileConnect + "-table").append(line);
                layer.msg(res.msg);
            }
        })
    }

    function del(a, fileConnect, ed) {
        $.ajax({
            url: "/admin/filingFile/delete",
            data: {ids: ed},
            type: "POST",
            success: function (res) {
                if (res.code == 0) {
                    layer.msg("删除成功");
                    var eld = $("#" + fileConnect).val();
                    var fileIdArray;
                    if (eld == '') {
                        fileIdArray = [];
                    } else {
                        fileIdArray = (eld + "").split(",");
                    }
                    arrayDelete(fileIdArray, ed);
                    var ids = fileIdArray.join(",").replace(/undefined\,/, "");
                    $("#" + fileConnect).val(ids).change();
                    $(a).parents("tr").remove();
                } else {
                    layer.msg("删除失败")
                }
            }
        })
    }

    function arrayDelete(ids, deleteId) {
        var index = ids.indexOf(deleteId);
        if (index > -1) {
            ids.splice(index, 1);
        }
    }

    var index = parent.layer.getFrameIndex(window.name);


    function formSave() {
        var bootstrapValidator = $('#form').data('bootstrapValidator').validate();
        if (bootstrapValidator.isValid()) {
            savepop('/admin/filingEquipmentUseCancellation/add', 'form', 'saveBtn')
        }
    }

    function searchDecive() {
        // var code = $("#useFilingCode").val();
        const code = decodeURI(window.location.href).split('=')[1]
        $("#useFilingCode").val(code)
        $.ajax({
            url: "/admin/filingEquipmentRegistrationForm/search",
            data: {code: code},
            // data: {code: '豫登FAW20040001'},
            type: "POST",
            success: function (res) {
                if (res.code == 0) {
                    $("#engineeringAreaCode").val(res.data.engineeringAreaCode)
                    $("#useUnit").val(res.data.useUnit);// 使用单位
                    $("#propertyUnit").val(res.data.propertyUnit);//产权单位
                    $("#deviceName").val(res.data.deviceName);//设备名称
                    $("#specificationModel").val(res.data.specificationModel);//规格型号
                    $("#deviceFilingCode").val(res.data.deviceFilingCode);//设备备案编号
                    $("#engineeringName").val(res.data.engineeringName);//工程名称
                    $("#projectManager").val(res.data.projectManager);//项目经理
                    // $("#equipmentType").val(res.data.equipmentType);//设备类别
                    var type = res.data.equipmentType;
                    $("#type").val(type);
                    if (type == "T") {
                        type = "塔式起重机";
                    } else if (type == "S") {
                        type = "施工升降机";

                    } else if (type == "W") {
                        type = "物料提升机";

                    } else if (type == "Q") {
                        type = "其他";

                    }
                    $("#equipmentType").val(type);//设备类别
                    layer.msg(res.msg);
                } else {
                    layer.msg("未查询到该设备信息");
                }
            }
        })
    }

    function initValidator() {
        $('#form').bootstrapValidator({
            message: '输入值不满足要求',
            excluded: [':disabled'],
            feedbackIcons: {
                valid: 'glyphicon glyphicon-ok',
                invalid: 'glyphicon glyphicon-remove',
                validating: 'glyphicon glyphicon-refresh'
            },
            verbose: false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
                /* userId: {
                     validators: {
                         notEmpty: {
                             message: '必填'
                         }
                     }
                 },*/
                useUnit: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                propertyUnit: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                deviceName: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 30,
                            message: '不能超过30位'
                        }
                    }
                },
                specificationModel: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 30,
                            message: '不能超过30位'
                        }
                    }
                },
                deviceFilingCode: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                engineeringName: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 50,
                            message: '不能超过50位'
                        }
                    }
                },
                projectManager: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 10,
                            message: '不能超过10位'
                        }
                    }
                },
                equipmentType: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                    }
                },
                useFilingCode: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 30,
                            message: '不能超过30位'
                        }
                    }
                },
                cancellationApplicationPath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 255,
                            message: '不能超过255位'
                        }
                    }
                },
                useRegistrationPath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 255,
                            message: '不能超过255位'
                        }
                    }
                },
                auditRejectReason: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 255,
                            message: '不能超过255位'
                        }
                    }
                },
                auditStatus: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 1,
                            message: '不能超过1位'
                        }
                    }
                },
                auditTime: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 0,
                            message: '不能超过0位'
                        }
                    }
                }
            }
        });
    }
</script>
</body>
</html>