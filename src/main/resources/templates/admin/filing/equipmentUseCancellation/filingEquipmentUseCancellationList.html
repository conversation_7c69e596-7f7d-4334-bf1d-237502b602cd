<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备使用注销表"></title>
    <th:block th:include="admin/include/head :: head"/>
    <style>
        .labelNowrap{
            white-space: nowrap;
            width: auto;
            padding-right: 0;
            padding-left: 0;
        }
        .inputClass{
            width: 228px;
        }
        .message {
            font-size: 13px;
            color: red;
        }
        table{
            word-break: break-all;
        }
    </style>
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group" style="display: flex;padding-left: 8px;">
                    <label class="control-label col-sm-1 labelNowrap" for="engineeringAreaCode">所属区域</label>
                    <div class="col-sm-2 inputClass">
                        <input type="text" class="form-control col-sm-10" id="engineeringAreaCode" name="engineeringAreaCode"
                               placeholder="请输入名称">
                    </div>
                    <label class="control-label col-sm-1 labelNowrap" for="useFilingCode">登记编号</label>
                    <div class="col-sm-2 inputClass">
                        <input type="text" class="form-control col-sm-10" id="useFilingCode" name="useFilingCode"
                               placeholder="请输入名称">
                    </div>
                    <label class="control-label col-sm-1 labelNowrap" for="auditStatus">审核状态</label>
                    <div class="col-sm-2 inputClass">
                        <select class="form-control col-sm-10" id="auditStatus" name="auditStatus">
                            <option value="">请选择</option>
                            <option value="0">未审核</option>
                            <option value="1">已通过</option>
                            <option value="2">已驳回</option>
                        </select>
                    </div>
                    <div class="col-sm-2 inputClass">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="resetSearch()"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="handleSearch()">
                            <i class="fa fa-search"></i>查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <table id="filingEquipmentUseCancellationtable"></table>
</div>

<!--引入js-->
<script th:src="@{/resources/awi/plugins/layui/layui_layui.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/cascader.min.js}"></script>
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>

<script th:inline="javascript">
    "use strict";
    var addFlag = [[${@permission.hasPermi('useCancel:add')}]];
    var editFlag = [[${@permission.hasPermi('useCancel:edit')}]];
    var delFlag = [[${@permission.hasPermi('useCancel:delete')}]];
    var designFlag = [[${@permission.hasPermi('useCancel:design')}]];
    var examiner = [[${@permission.hasPermi('useCancel:examine')}]];
    $(function () {
        layui.use('layCascader', function () {
            // var id = 0;
            var layCascader = layui.layCascader;
            var areaLayCascader = layCascader({
                elem: '#engineeringAreaCode',
                props: {
                    checkStrictly: true,
                    lazy: true,
                    lazyLoad: function (node, resolve) {
                        var level = node.level;
                        if (level == 1) {
                            return
                        } else {
                            $.ajax({
                                url: '/admin/area/getCountyList',
                                type: 'GET', // 请求类型
                                data: {
                                    parentId: node.value || 410000
                                },
                                dataType: 'json',
                                success: function (response) {
                                    var nodes = response.map(function (item) {

                                        return {
                                            value: item.id,
                                            label: item.name,
                                            leaf: level >= 1
                                        };
                                    });
                                    resolve(nodes);
                                },
                                error: function (error) {
                                    console.log('加载子节点数据失败:', error);
                                    resolve([]);

                                }
                            });
                        }
                    }
                }
            });
            areaLayCascader.changeEvent(function (value, node) {
                areaLayCascader.close()
            })
        });
    })
    function handleSearch() {
        formSearch('filingEquipmentUseCancellationtable', '/admin/filingEquipmentUseCancellation/list', 'searchForm')
    }
    function resetSearch() {
        $("#engineeringAreaCode").val('')
        setTimeout(() => {
            $(".el-input__inner").val('')
        }, 100)
        restForm('searchForm')
        formSearch('filingEquipmentUseCancellationtable', '/admin/filingEquipmentUseCancellation/list', 'searchForm')
    }

    function handleCancel(id) {
        layer.open({
            type: 0,
            title: '撤销',
            content: '确认撤销当前申请？',
            btn: ['确认', '取消'],
            yes: function (index, layero) {
                $.ajax({
                    url: '/admin/filingEquipmentUseCancellation/delete',
                    method: 'post',
                    data: {
                        ids: id
                    },
                    success: function (res) {
                        layer.close(index);
                        formSearch('filingEquipmentUseCancellationtable', '/admin/filingEquipmentUseCancellation/list', 'searchForm')
                    }
                })
            }
        })

    }

    $(function () {
        var columns = [
            {
                field: "remarks",
                // field: "engineeringAreaCode",
                title: "工程所属地",
                align: 'center',
                valign: 'middle',
                // formatter: function (value) {
                //     const city = [[${dict}]];
                //     console.log('city',city)
                //     const findValue = city.find((item) => {
                //         return value == item.dictValue
                //     })?.dictLabel
                //     console.log('findValue',findValue)
                //
                //     return findValue
                // }
            },
            {
                field: 'createTime',
                title: '申请日期',
                align: 'center',
                valign: 'middle',
                width:'90',
                formatter: function (value) {
                    return changeDateFormat("yyyy-MM-dd", value);
                }
            },
            {
                field: "useFilingCode",
                title: "使用登记编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "userId",
                title: "申请人id",
                align: 'center',
                valign: 'middle',
                visible: false
            },
            {
                field: "deviceFilingCode",
                title: "设备备案编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "deviceName",
                title: "设备名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "specificationModel",
                title: "规格型号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "propertyUnit",
                title: "产权单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "useUnit",
                title: "使用单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "engineeringName",
                title: "工程名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "projectManager",
                title: "项目经理",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "auditStatus",
                title: "审核状态",
                align: 'center',
                valign: 'middle',
                cellStyle: function (value, row, index) {
                    if (value == 0) {
                        return {css: {'color': '#000'}};
                    } else if (value == 1) {
                        return {css: {'color': '#5cb85c'}};
                    } else if (value == 2) {
                        return {css: {'color': '#f0ad4e'}};
                    } else {
                        return {css: {'color': '#000'}};
                    }
                },
                formatter: function (value, row, index) {
                    if (value == '0') {
                        return "未审核";
                    } else if (value == '1') {
                        return "通过";
                    } else if (value == '2') {
                        return "驳回";
                    }
                    return "";
                }
            },
            {
                field: 'id',
                title: '操作',
                align: 'center',
                valign: 'middle',
                cellStyle: {
                    css: {"white-space": "nowrap"}
                },
                formatter: function (value, row, index) {
                    var actions = [];
                    if ("hidden" != editFlag) {
                        if (row.auditStatus == '2') {
                            actions.push('<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="updatelayer(\'' + value + '\',\'编辑\',\'/admin/filingEquipmentUseCancellation/edit/\')" style="margin-right: 10px;"><i class="fa fa-edit"></i>编辑</button>');
                        }
                    }
                    actions.push('<button id="btn_detail" type="button" class="btn btn-success  btn-xs" onclick="detaillayer(\'' + value + '\',\'详情\',\'/admin/filingEquipmentUseCancellation/detail/\')" style="margin-right: 10px;"><i class="fa fa-eye"></i>详情</button>');
                    if ("hidden" != examiner && row.auditStatus == '0') {
                        actions.push('<button id="btn_detail" type="button" class="btn btn-danger  btn-xs" onclick="detaillayer(\'' + value + '\',\'审核\',\'/admin/filingEquipmentUseCancellation/audit/\')"><i class="fa fa-check-circle"></i>审核</button>');
                    }
                    if (row.auditStatus == '0' && 'hidden' != delFlag) {
                        actions.push('<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="handleCancel(\'' + value + '\')" style="margin-left: 10px;"><i class="fa fa-mail-reply"></i>撤销</button>');
                    }
                    return actions.join('');
                }
            }
        ];

        createBootstrapTable($("#filingEquipmentUseCancellationtable"), '/admin/filingEquipmentUseCancellation/list', columns, false, '', 'server');
    });
</script>
</body>
</html>