<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备使用注销表编辑"></title>
    <th:block th:include="admin/include/head :: head"/>
    <link rel="stylesheet" th:href="@{/resources/file-input/css/fileinput.min.css}">
    <style>
        .container-fluid {
            padding-top: 20px;
        }

        .btn-info {
            width: 130px;
            height: 40px;
        }
    </style>
</head>
<body>
<div id="filingEquipmentUseCancellationform" class="container-fluid">

    <form id="filingEquipmentUseCancellation-form-edit" class="form-horizontal" autocomplete="off"
          th:object="${filingEquipmentUseCancellation}">
        <input type="hidden" id="id" name="id" th:value="*{id}"/>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="deviceFilingCode" class="col-sm-3 control-label require">设备备案编号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="deviceFilingCode" name="deviceFilingCode"
                               th:value="*{deviceFilingCode}" readonly="readonly"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="col-sm-3 control-label">审核驳回原因:</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" th:value="*{auditRejectReason}"
                               readonly="readonly"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useUnit" class="col-sm-3 control-label require">使用单位</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="useUnit" name="useUnit" th:value="*{useUnit}"
                               readonly="readonly"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="propertyUnit" class="col-sm-3 control-label require">产权单位</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="propertyUnit" name="propertyUnit"
                               th:value="*{propertyUnit}" readonly="readonly"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="deviceName" class="col-sm-3 control-label require">设备名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="deviceName" name="deviceName" th:value="*{deviceName}"
                               readonly="readonly"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="specificationModel" class="col-sm-3 control-label require">规格型号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="specificationModel" name="specificationModel"
                               th:value="*{specificationModel}" readonly="readonly"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">

            <div class="col-md-6">
                <div class="form-group">
                    <label for="engineeringName" class="col-sm-3 control-label require">工程名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="engineeringName" name="engineeringName"
                               th:value="*{engineeringName}" readonly="readonly"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="projectManager" class="col-sm-3 control-label require">项目经理</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="projectManager" name="projectManager"
                               th:value="*{projectManager}" readonly="readonly"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentType" class="col-sm-3 control-label require">设备类别</label>
                    <div class="col-sm-9">
                        <!--                        <input type="text" class="form-control" id="equipmentType" name="equipmentType"-->
                        <!--                               th:value="*{equipmentType}" readonly="readonly"/>-->
                        <select class="form-control" id="equipmentType" name="equipmentType" disabled>
                            <option value="T">塔式起重机</option>
                            <option value="S">施工升降机(不含物料提升机)</option>
                            <option value="W">物料提升机</option>
                            <option value="Q">其他起重机械</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="mcancellationApplicationPath" class="col-sm-3 control-label require">注销申请表</label>
                    <div class="col-sm-7">
                        <input type="file" multiple class="fileinput" name="uploadFile" id="mcancellationApplicationPath" data-type="cancellationApplicationPath">
                        <input type="text" name="cancellationApplicationPath" id="cancellationApplicationPath"
                               hidden="hidden">
                        <table class="table" id="cancellationApplicationPath-table"></table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useRegistrationPath" class="col-sm-3 control-label require">使用备案证</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" name="uploadFile" id="museRegistrationPath" data-type="useRegistrationPath">
                        <input type="text" name="useRegistrationPath" id="useRegistrationPath" hidden="hidden">
                        <table class="table" id="useRegistrationPath-table"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <button type="button" class="btn btn-info col-sm-offset-6" onclick="formSave()">提交</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/file-input/js/fileinput.min.js}"></script>
<script th:src="@{/resources/file-input/js/locales/zh.js}"></script>
<script type="text/javascript" th:inline="javascript">

    var files = [[${filepath}]];
    const filingEquipmentUseCancellationValue = [[${filingEquipmentUseCancellation}]]
    $("#equipmentType").val(filingEquipmentUseCancellationValue.equipmentType)
    $(function () {
        var cancellationApplicationPath = "";
        var useRegistrationPath = "";
        for (let i = 0; i < files.length; i++) {
            let file = files[i];
            if (file.connectId.indexOf("cancellationApplicationPath") > 0) {
                cancellationApplicationPath += "<tr><td><a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a></td><td><span class='del' onclick=del(this,'cancellationApplicationPath','" + file.id + "') style='cursor:pointer; color:#ff101a; text-align: center; line-height: 34px;'>删除</span></td></tr>";
            }
            if (file.connectId.indexOf("useRegistrationPath") > 0) {
                useRegistrationPath += "<tr><td><a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a></td><td><span class='del' onclick=del(this,'useRegistrationPath','" + file.id + "') style='cursor:pointer; color:#ff101a; text-align: center; line-height: 34px;'>删除</span></td></tr>";
            }
            let itemPath = $("#" + file.fileType)
            if (itemPath.val()) {
                itemPath.val(itemPath.val() + "," + file.id)
            } else {
                itemPath.val(file.id)
            }
        }
        $("#useRegistrationPath-table").html(useRegistrationPath);
        $("#cancellationApplicationPath-table").html(cancellationApplicationPath);
    })

    $(function () {
        //文件上传
        $.each($(".fileinput"), function (index, item) {
            var type = $(item).data("type");
            $(item).fileinput({
                uploadUrl: '/admin/filingEquipmentUseCancellation/uploadFile',
                required: false,
                overwriteInitial: false,
                maxFileSize: 3072,
                maxFilesNum: 1,
                showPreview: true,
                showUpload: true,
                showCaption: true,
                language: 'zh',
                uploadAsync: true,
                dropZoneEnabled: false
            }).on("fileuploaded", function (event, data) {
                var _data = data.response.data;
                _data.fileType = type;
                addFile(_data, type);
            });
        });
    })

    function addFile(_fileinfo, fileConnect) {
        var fileName = _fileinfo.fileName;
        var fileUrl = _fileinfo.fileUrl;
        var filePath = _fileinfo.filePath;
        var fileSuffix = _fileinfo.fileSuffix;
        var fileType = _fileinfo.fileType;
        var id = $("#id").val().toString();
        var connectId = id + "cancellation" + fileConnect;
        var file = {
            fileName: fileName,
            fileUrl: fileUrl,
            filePath: filePath,
            fileSuffix: fileSuffix,
            fileType: fileType,
            connectId: connectId
        };
        $.ajax({
            url: "/admin/filingFile/fileUpload",
            data: file,
            type: "POST",
            success: function (res) {
                var eld = $("#" + fileConnect).val();
                var fileIdArray;
                if (eld == '') {
                    fileIdArray = [];
                } else {
                    fileIdArray = (eld + "").split(",");
                }
                fileIdArray.push(res.data.toString());
                var ids = fileIdArray.join(",").replace(/undefined\,/, "");
                $("#" + fileConnect).val(ids).change();
                var line = "<tr><td><a href='" + fileUrl + "' download='" + fileName + "'>" + fileName + "</a></td><td><span class='del' onclick=del(this,'" + fileConnect + "','" + res.data + "') style='cursor:pointer; color:#ff101a; text-align: center; line-height: 34px;'>删除</span></td></tr>";
                $("#" + fileConnect + "-table").append(line);
                layer.msg(res.msg);
            }
        })
    }

    function del(a, fileConnect, ed) {
        $.ajax({
            url: "/admin/filingFile/delete",
            data: {ids: ed},
            type: "POST",
            success: function (res) {
                if (res.code == 0) {
                    layer.msg("删除成功");
                    var eld = $("#" + fileConnect).val();
                    var fileIdArray = eld.split(",");
                    var index = fileIdArray.indexOf(ed);
                    if (index > -1) {
                        fileIdArray.splice(index, 1); // 从数组中删除项
                    }
                    var ids = fileIdArray.filter(function (item) {
                        return item != '';
                    }).join(",");
                    $("#" + fileConnect).val(ids).trigger('change');
                    $(a).parents("tr").remove();
                } else {
                    layer.msg("删除失败")
                }
            }
        })
    }

    initValidator();

    function form(url, data, success, error) {
        $.ajax({
            url: url,
            type: 'POST',
            data: data,
            contentType: false,
            processData: false,
            success: function (data) {
                success(data)
            },
            error: function (data) {
                error(data)
            }
        })
    }

    // function formSave(){
    //     var bootstrapValidator = $('#filingEquipmentUseCancellation-form-edit').data('bootstrapValidator');
    //     if(bootstrapValidator.isValid()) {
    //         save('/filingEquipmentUseCancellation/update' ,'filingEquipmentUseCancellation-form-edit')
    //     }
    // }

    var index = parent.layer.getFrameIndex(window.name);

    /**
     * 提交
     * */
    function formSave() {
        $('#filingEquipmentUseCancellation-form-edit').data('bootstrapValidator').validate();

        var formData = new FormData($("#filingEquipmentUseCancellation-form-edit")[0]);
        console.log(formData);
        var bootstrapValidator = $('#filingEquipmentUseCancellation-form-edit').data('bootstrapValidator');
        bootstrapValidator.validate();
        if (bootstrapValidator.isValid()) {
            // form('/admin/filingEquipmentUseCancellation/update', formData, function (res) {
            form('/admin/filingEquipmentUseCancellation/add', formData, function (res) {
                console.log(res);
                if (res.code == 0) {
                    layer.msg("操作成功", {time: 1000}, function () {
                        parent.location.reload();
                        parent.layer.close(index);
                    });
                } else {
                    layer.msg(res.msg, {time: 1000, icon: 5});
                }
            }, function (error) {
                layer.msg("系统异常！");
            })
        }

    }

    /**
     * 检测
     * */

    function initValidator() {
        $('#filingEquipmentUseCancellation-form-edit').bootstrapValidator({
            message: '输入值不满足要求',
            excluded: [':disabled'],
            // feedbackIcons: {
            //     valid: 'glyphicon glyphicon-ok',
            //     invalid: 'glyphicon glyphicon-remove',
            //     validating: 'glyphicon glyphicon-refresh'
            // },
            verbose: false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
                userId: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                useUnit: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                propertyUnit: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                deviceName: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 30,
                            message: '不能超过30位'
                        }
                    }
                },
                specificationModel: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 30,
                            message: '不能超过30位'
                        }
                    }
                },
                deviceFilingCode: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                engineeringName: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 50,
                            message: '不能超过50位'
                        }
                    }
                },
                projectManager: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 10,
                            message: '不能超过10位'
                        }
                    }
                },
                equipmentType: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                useFilingCode: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 30,
                            message: '不能超过30位'
                        }
                    }
                },
                cancellationApplicationPath: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 255,
                            message: '不能超过255位'
                        }
                    }
                },
                useRegistrationPath: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 255,
                            message: '不能超过255位'
                        }
                    }
                },
                auditRejectReason: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 255,
                            message: '不能超过255位'
                        }
                    }
                },
                auditStatus: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
            }
        });
    }
</script>

</body>
</html>
