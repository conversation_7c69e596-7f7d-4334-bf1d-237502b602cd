<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备使用注销表详情"></title>
    <th:block th:include="admin/include/head :: head"/>
    <style>
        .container-fluid{
            padding-top: 15px;
        }
        .box-footer{
            width: max-content;
            margin: 0 auto;
        }
    </style>
</head>
<body>
<div id="filingEquipmentUseCancellationDetail" class="container-fluid">
    <form id="filingEquipmentUseCancellation-form-edit" class="form-horizontal" autocomplete="off"
          th:object="${filingEquipmentUseCancellation}">
        <input type="hidden" id="id" name="id" th:value="*{id}"/>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="deviceFilingCode" class="col-sm-3 control-label require">设备备案编号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="deviceFilingCode" name="deviceFilingCode"
                               th:value="*{deviceFilingCode}" readonly="readonly"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useUnit" class="col-sm-3 control-label require">使用单位</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="useUnit" name="useUnit" th:value="*{useUnit}"
                               readonly="readonly"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">

            <div class="col-md-6">
                <div class="form-group">
                    <label for="propertyUnit" class="col-sm-3 control-label require">产权单位</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="propertyUnit" name="propertyUnit"
                               th:value="*{propertyUnit}" readonly="readonly"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="deviceName" class="col-sm-3 control-label require">设备名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="deviceName" name="deviceName" th:value="*{deviceName}"
                               readonly="readonly"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">

            <div class="col-md-6">
                <div class="form-group">
                    <label for="specificationModel" class="col-sm-3 control-label require">规格型号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="specificationModel" name="specificationModel"
                               th:value="*{specificationModel}" readonly="readonly"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="engineeringName" class="col-sm-3 control-label require">工程名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="engineeringName" name="engineeringName"
                               th:value="*{engineeringName}" readonly="readonly"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="projectManager" class="col-sm-3 control-label require">项目经理</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="projectManager" name="projectManager"
                               th:value="*{projectManager}" readonly="readonly"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentType" class="col-sm-3 control-label require">设备类别</label>
                    <div class="col-sm-9">
                        <!--                        <input type="text" class="form-control" id="equipmentType" name="equipmentType"-->
                        <!--                               th:value="*{equipmentType}" readonly="readonly"/>-->
                        <select class="form-control" id="equipmentType" name="equipmentType" disabled>
                            <option value="T">塔式起重机</option>
                            <option value="S">施工升降机(不含物料提升机)</option>
                            <option value="W">物料提升机</option>
                            <option value="Q">其他起重机械</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="col-sm-3 control-label require">审核状态</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control"
                               name="auditStatus" th:if="*{auditStatus=='0'}" value="未审核" disabled/>
                        <input type="text" class="form-control"
                               name="auditStatus" th:if="*{auditStatus=='1'}" value="审核通过" disabled/>
                        <input type="text" class="form-control"
                               name="auditStatus" th:if="*{auditStatus=='2'}" value="驳回" disabled/>
                    </div>
                </div>
            </div>
        </div>
        <table class="table table-striped table-bordered" th:object="${filingEquipmentUseCancellation}">
            <tbody>
            <tr>
                <th>注销申请表</th>
                <td colspan="3" id="cancellationcancellationApplicationPath">

                </td>
            </tr>
            <tr>
                <th>使用备案证</th>
                <td colspan="3" id="cancellationuseRegistrationPath">
            </tr>
            <!--        <tr>-->
            <!--            <th>审核时间</th>-->
            <!--            <td th:text="*{#dates.format(auditTime,'YYYY-MM-dd')}">Mark</td>-->

            <!--        </tr>-->
            </tbody>
        </table>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/file-input/js/fileinput.min.js}"></script>
<script th:src="@{/resources/file-input/js/locales/zh.js}"></script>
<script th:src="@{/resources/awi/plugins/daterangepicker/moment.min.js}"></script>
<script th:src="@{/resources/awi/plugins/daterangepicker/daterangepicker.js}"></script>
<script type="text/javascript"
        src="https://webapi.amap.com/maps?v=1.4.15&key=6b72d58d224064afe46948c90e24c20f"></script>
<script type="text/javascript" th:inline="javascript">
    var files = [[${filepath}]];
    $("#equipmentType").val([[${filingEquipmentUseCancellation}]].equipmentType)
    $(function () {
        var cancellationcancellationApplicationPath = "";
        var cancellationuseRegistrationPath = "";
        for (let i = 0; i < files.length; i++) {
            let file = files[i];
            if (file.connectId.indexOf("cancellationcancellationApplicationPath") > 0) {
                cancellationcancellationApplicationPath += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("cancellationuseRegistrationPath") > 0) {
                cancellationuseRegistrationPath += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
        }
        $("#cancellationcancellationApplicationPath").html(cancellationcancellationApplicationPath);
        $("#cancellationuseRegistrationPath").html(cancellationuseRegistrationPath);
    })

</script>
</body>
</html>
