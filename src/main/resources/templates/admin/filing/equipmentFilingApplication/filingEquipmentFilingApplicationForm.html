<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="起重机设备备案申请表单"></title>
    <th:block th:include="admin/include/head :: head"/>
    <link rel="stylesheet" th:href="@{/resources/file-input/css/fileinput.min.css}">
    <link rel="stylesheet" th:href="@{/resources/layui/css/layui.css}">
    <style>
        .title {
            font-size: 14px;
            font-weight: bold;
            padding: 10px;
            margin-bottom: 10px;
            margin-right: 12px;
            background: #e1f0f9;
            border-radius: 4px;
            position: relative;

        }

        .title::before {
            content: '';
            width: 4px;
            height: 100%;
            background: #3c8dbc;
            position: absolute;
            left: 0;
            top: 0;
        }

        .form-control-feedback {
            display: none !important;
        }

        .download {
            padding: 0;
            white-space: nowrap;
        }

        .download a {
            line-height: 36px;
            padding: 8px 6px;
            background-color: #337ab7;
            border-radius: 4px;
            color: #fff;
        }

        .download a:hover {
            background-color: #2e6da4;
            text-decoration: none;
        }

        .fa-download {
            margin-right: 6px;
        }

        .btn-info {
            width: 130px;
            height: 40px;
        }
    </style>
</head>
<body>

<div class="container-fluid">
    <!-- form start -->
    <form id="form" class="form-horizontal">
        <input type="hidden" id="userId" name="userId" autocomplete="off" th:value="${userId}">
        <input type="hidden" id="id" name="id" autocomplete="off" th:value="${id}">
        <div class="col-md-12" style="margin-top: 10px">
            <div class="title">设备信息 <span style="color: red;font-size: 14px;font-weight: bold"> 注：请选择正确的设备类型，否则会影响您的审核或生成的备案编号</span>
            </div>
        </div>

        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="applicationDate" class="col-sm-3 control-label require">申请日期</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="applicationDate" autocomplete="off" disabled>
                        <input type="hidden" class="form-control" id="now" name="applicationDate" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentType" class="col-sm-3 control-label require">设备类型</label>
                    <div class="col-sm-9">
                        <select class="form-control" id="equipmentType" name="equipmentType" autocomplete="off">
                            <option value="T">塔式起重机</option>
                            <option value="S">施工升降机(不含物料提升机)</option>
                            <option value="W">物料提升机</option>
                            <option value="Q">其他起重机械</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="oldNumberFlag" class="col-sm-3 control-label require">是否存在旧备案编号</label>
                    <div class="col-sm-9">
                        <select class="form-control" autocomplete="off" id="oldNumberFlag" name="oldNumberFlag">
                            <option value="1">是</option>
                            <option value="0">否</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="oldNumber" class="col-sm-3 control-label require">旧备案编号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="oldNumber" name="oldNumber">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="deviceName" class="col-sm-3 control-label require">设备名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="deviceName" name="deviceName" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="specificationModel" class="col-sm-3 control-label require">规格型号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="specificationModel" name="specificationModel"
                               autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="purchaseTime" class="col-sm-3 control-label require">购买时间</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control datepicker" id="purchaseTime" name="purchaseTime"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="liftingWeight" class="col-sm-3 control-label">起重重量（吨）</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="liftingWeight" name="liftingWeight" autocomplete="off" placeholder="单位：吨">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">产权单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="propertyUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="propertyUnit" name="propertyUnit" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="propertyUnitCode" class="col-sm-3 control-label require">单位社会统一信用代码</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="propertyUnitCode" name="propertyUnitCode" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">

            <div class="col-md-6">
                <div class="form-group">
                    <label for="unitAreaCode" class="col-sm-3 control-label require">单位属地</label>
                    <div class="col-sm-9">
                        <input type="hidden" id="remarks" name="remarks">
                        <input type="text" class="form-control" id="unitAreaCode" name="unitAreaCode" autocomplete="off">
                        <!--                        <select class="form-control" autocomplete="off" id="select" name="select">-->

                        <!--                        </select>-->
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="unitAddress" class="col-sm-3 control-label require">单位详细地址</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="unitAddress" name="unitAddress" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">

            <div class="col-md-6">
                <div class="form-group">
                    <label for="legalRepresentative" class="col-sm-3 control-label require">法定代表人</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="legalRepresentative" name="legalRepresentative"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="legalRepresentativeContactNumber" class="col-sm-3 control-label require">法定代表人联系电话</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="legalRepresentativeContactNumber"
                               name="legalRepresentativeContactNumber" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">

            <div class="col-md-6">
                <div class="form-group">
                    <label for="technicalDirector" class="col-sm-3 control-label require">技术负责人</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="technicalDirector" name="technicalDirector"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="technicalDirectorContactNumber" class="col-sm-3 control-label require">技术负责人联系方式</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="technicalDirectorContactNumber" name="technicalDirectorContactNumber" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">

            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentManager" class="col-sm-3 control-label require">设备管理负责人</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="equipmentManager" name="equipmentManager"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentManagerContactNumber" class="col-sm-3 control-label require">设备管理负责人联系方式</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="equipmentManagerContactNumber" name="equipmentManagerContactNumber" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-12">
            <div class="title">生产单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="manufacturer" class="col-sm-3 control-label require">生产厂家</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="manufacturer" name="manufacturer" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="factoryTime" class="col-sm-3 control-label require">出厂时间</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control datepicker" id="factoryTime" name="factoryTime"
                               autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="factoryLicenseNumber" class="col-sm-3 control-label require">出厂编号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="factoryLicenseNumber" name="factoryLicenseNumber"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="manufacturingLicenseNumber" class="col-sm-3 control-label require">制造许可证编号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="manufacturingLicenseNumber"
                               name="manufacturingLicenseNumber" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">附件信息 <span style="color: red;font-size: 14px;font-weight: bold"> 注：每个文件大小需小于3MB，选择文件后请点击上传完成文件上传操作</span>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="filingApplicationFormPath" class="col-sm-3 control-label require">起重机备案申请表</label>
                    <div class="col-sm-7">
                        <input type="file" class="fileinput" multiple data-type="filingApplicationFormPath"
                               id="filingApplicationForm" name="uploadFile" accept="*/*">
                        <input type="hidden" id="filingApplicationFormPath" name="filingApplicationFormPath"/>
                        <table id="filingApplicationFormPath-table" class="table">
                        </table>

                    </div>
                    <div class="col-sm-2 download">
                        <a href="/resources/awi/dist/doc/application.docx" download="建筑起重机械设备备案申请表"><i class="fa fa-download"></i>模版下载</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="businessLicensePath" class="col-sm-3 control-label require">产权单位法人营业执照</label>
                    <div class="col-sm-9">
                        <input type="file" data-type="businessLicensePath" class="fileinput" multiple id="businessLicense"
                               name="uploadFile" accept="*/*"/>
                        <input type="hidden" id="businessLicensePath" name="businessLicensePath"/>
                        <table id="businessLicensePath-table" class="table">
                        </table>

                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentManufacturingLicensePath" class="col-sm-3 control-label require">起重设备制造许可证</label>
                    <div class="col-sm-9">
                        <input type="file" class="fileinput" multiple data-type="equipmentManufacturingLicensePath" id="equipmentManufacturingLicense" name="uploadFile" accept="*/*">
                        <input type="hidden" id="equipmentManufacturingLicensePath" name="equipmentManufacturingLicensePath">
                        <table id="equipmentManufacturingLicensePath-table" class="table">
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="productCertificationPath" class="col-sm-3 control-label require">产品合格证</label>
                    <div class="col-sm-9">
                        <input type="file" class="fileinput" multiple data-type="productCertificationPath" id="productCertification" name="uploadFile" accept="*/*">
                        <input type="hidden" id="productCertificationPath" name="productCertificationPath"/>

                        <table id="productCertificationPath-table" class="table">

                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="purchaseSaleCertificatePath" class="col-sm-3 control-label require">起重机械设备购销凭证</label>

                    <div class="col-sm-9">
                        <input class="fileinput" multiple type="file" data-type="purchaseSaleCertificatePath"
                               name="uploadFile">
                        <input type="hidden" id="purchaseSaleCertificatePath" name="purchaseSaleCertificatePath"/>
                        <table id="purchaseSaleCertificatePath-table" class="table">
                        </table>

                    </div>
                </div>
            </div>
        </div>

        <div class="form-group col-md-12" style="text-align: center;">
            <button type="button" class="btn btn-info " onclick="formSave()">提交</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/file-input/js/fileinput.min.js}"></script>
<script th:src="@{/resources/file-input/js/locales/zh.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/layui_layui.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/cascader.min.js}"></script>

<script th:inline="javascript">
    "use strict";
    var index = parent.layer.getFrameIndex(window.name);
    //初始化产品合格证文件上传组件
    var dict = [[${dict}]];
    // console.log('dict',dict)
    const userInfo = [[${user}]]
    // console.log('userInfo',userInfo)
    $(function () {
        $("#propertyUnit").val(userInfo.name)
        $("#propertyUnitCode").val(userInfo.loginName)
    })
    $(document).on('change', '#oldNumberFlag', function () {
        $("#form").data("bootstrapValidator").resetField('oldNumber');
        $("#form").data("bootstrapValidator").validateField('oldNumber');
        // $("#form").data("bootstrapValidator").updateStatus("oldNumber", "NOT_VALIDATED", null).validateField('oldNumber');
    })
    $(function () {
        layui.use('layCascader', function () {
            // var id = 0;
            var layCascader = layui.layCascader;
            layCascader({
                elem: '#unitAreaCode',
                placeholder:'请依据营业执照信息选择所属地',
                props: {
                    checkStrictly: true,
                    lazy: true,
                    lazyLoad: function (node, resolve) {
                        $("#unitAreaCode").trigger('change')
                        var level = node.level;
                        if (level == 1) {
                            return
                        } else {
                            $.ajax({
                                url: '/admin/area/getCountyList',
                                type: 'GET', // 请求类型
                                data: {
                                    parentId: node.value || 410000
                                },
                                dataType: 'json',
                                success: function (response) {
                                    var nodes = response.map(function (item) {
                                        return {
                                            value: item.id,
                                            label: item.name,
                                            leaf: level >= 1
                                        };
                                    });
                                    resolve(nodes);
                                },
                                error: function (error) {
                                    console.log('加载子节点数据失败:', error);
                                    resolve([]);

                                }
                            });
                        }
                    }
                }
            });
        });
    })

    // $(document).on('change', '#select', function () {
    //     $('#remarks').val($(this).val())
    //     console.log($('#remarks').val())
    // })
    $('.datepicker').datepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        autoclose: true,
        todayHighlight: true,
        //控制可选的最晚月份，为当前月
        endDate: new Date()
    });

    //获取当前时间
    var date = new Date();
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    if (month < 10) {
        month = "0" + month;
    }

    initValidator();

    function formSave() {
        $("#remarks").val($('.el-input__inner').val())
        // $('#form'). data('bootstrapValidator').resetForm();
        var bootstrapValidator = $('#form').data('bootstrapValidator');
        $('#form').data('bootstrapValidator').validate();
        if (bootstrapValidator.isValid()) {
            savepop('/admin/filingEquipmentFilingApplication/add', 'form', 'saveBtn')
        }
    }


    $(function () {
        $("#applicationDate").val(date.toLocaleDateString());
        $("#now").val(date.toLocaleDateString());


        //文件上传
        $.each($(".fileinput"), function (index, item) {
            var type = $(item).data("type");
            $(item).fileinput({
                uploadUrl: '/admin/filingEquipmentFilingApplication/uploadFile',
                required: false,
                overwriteInitial: false,
                uploadLabel: "上传",                         // 上传按钮内容
                browseLabel: "选择文件",                            // 浏览按钮内容
                showRemove: true,                                       // 显示移除按钮
                maxFileSize: 3072,
                maxFilesNum: 1,
                showPreview: true,
                showUpload: true,
                showCaption: true,
                language: 'zh',
                uploadAsync: true,
                dropZoneEnabled: false,
            }).on("fileuploaded", function (event, data) {
                var _data = data.response.data;
                _data.fileType = type;
                addFile(_data, type);
            });
        });
    })

    function del(a, fileConnect, ed) {
        $.ajax({
            url: "/admin/filingFile/delete",
            data: {ids: ed},
            type: "POST",
            success: function (res) {
                if (res.code == 0) {
                    layer.msg("删除成功");
                    var eld = $("#" + fileConnect).val();
                    var fileIdArray;
                    if (eld == '') {
                        fileIdArray = [];
                    } else {
                        fileIdArray = (eld + "").split(",");
                    }
                    arrayDelete(fileIdArray, ed);
                    var ids = fileIdArray.join(",").replace(/undefined\,/, "");
                    $("#" + fileConnect).val(ids).change();
                    $(a).parents("tr").remove();
                } else {
                    layer.msg("删除失败")
                }
            }
        })
    }

    function arrayDelete(ids, deleteId) {
        var index = ids.indexOf(deleteId);
        if (index > -1) {
            ids.splice(index, 1);
        }
    }

    function addFile(_fileinfo, fileConnect) {
        var fileName = _fileinfo.fileName;
        var fileUrl = _fileinfo.fileUrl;
        var filePath = _fileinfo.filePath;
        var fileSuffix = _fileinfo.fileSuffix;
        var fileType = _fileinfo.fileType;
        var id = $("#id").val().toString();
        var connectId = id + "deviceNew" + fileConnect;
        var file = {
            fileName: fileName,
            fileUrl: fileUrl,
            filePath: filePath,
            fileSuffix: fileSuffix,
            fileType: fileType,
            connectId: connectId
        };
        $.ajax({
            url: "/admin/filingFile/fileUpload",
            data: file,
            type: "POST",
            success: function (res) {
                var eld = $("#" + fileConnect).val();
                var fileIdArray;
                if (eld == '') {
                    fileIdArray = [];
                } else {
                    fileIdArray = (eld + "").split(",");
                }
                fileIdArray.push(res.data.toString());
                var ids = fileIdArray.join(",").replace(/undefined\,/, "");
                $("#" + fileConnect).val(ids).change();
                var line = "<tr><td style='width: 80%;'><a href='" + fileUrl + "' download='" + fileName + "'>" + fileName + "</a></td><td style='text-align: center'><span class='del' onclick=del(this,'" + fileConnect + "','" + res.data + "') style='cursor:pointer; color:#ff101a; line-height: 34px;'>删除</span></td></tr>";
                $("#" + fileConnect + "-table").append(line);
                layer.msg(res.msg);
            }
        })
    }

    function initValidator() {
        $('#form').bootstrapValidator({
            message: '输入值不满足要求',
            excluded: [],
            feedbackIcons: {
                valid: 'glyphicon glyphicon-ok',
                invalid: 'glyphicon glyphicon-remove',
                validating: 'glyphicon glyphicon-refresh'
            },
            verbose: false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
                propertyUnit: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 30,
                            message: '不能超过30位'
                        }
                    },

                },
                select: {
                    validators: {
                        notEmpty: {
                            message: '请选择'
                        },
                    },
                },
                oldNumber: {
                    trigger: "blur",
                    validators: {
                        callback: {
                            message: '必填',
                            callback: function (value, validator) {
                                if ($('#oldNumberFlag').val() == '0') {
                                    //否
                                    $('#oldNumber').val('');
                                    $('#oldNumber').attr('disabled', true);
                                    return true
                                } else {
                                    //是
                                    $('#oldNumber').attr('disabled', false);

                                    return value ? true : false
                                }
                            }
                        }
                    }
                },
                deviceName: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        },
                    }
                },
                specificationModel: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 30,
                            message: '不能超过30位'
                        }
                    }
                },
                manufacturer: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 30,
                            message: '不能超过30位'
                        }
                    }
                },
                factoryTime: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                manufacturingLicenseNumber: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 30,
                            message: '不能超过30位'
                        }
                    }
                },
                factoryLicenseNumber: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 30,
                            message: '不能超过30位'
                        }
                    }
                },
                purchaseTime: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                liftingWeight: {
                    validators: {
                        callback: {
                            message: '请输入合法数字',
                            callback: function (value, validator) {
                                if (value) {
                                    let regex = /^(?!0\d)\d+(\.\d+)?$/;
                                    return regex.test(value)
                                } else {
                                    return true
                                }
                            }
                        }
                    }
                },
                unitAreaCode: {
                    trigger: 'change',
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                unitAddress: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 200,
                            message: '不能超过200位'
                        }
                    }
                },
                legalRepresentative: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                legalRepresentativeContactNumber: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        regexp: {
                            regexp: /^0?(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
                            message: '请输入正确的手机号码'
                        }
                    }
                },
                propertyUnitCode: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 18,
                            min: 18,
                            message: '请输入18位信用代码'
                        }
                    }
                },
                technicalDirector: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                technicalDirectorContactNumber: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        regexp: {
                            regexp: /^0?(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
                            message: '请输入正确的手机号码'
                        }
                    }
                },
                equipmentManager: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                equipmentManagerContactNumber: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        regexp: {
                            regexp: /^0?(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
                            message: '请输入正确的手机号码'
                        }
                    }
                },

                productCertificationPath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                equipmentManufacturingLicensePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                filingApplicationFormPath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                purchaseSaleCertificatePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                businessLicensePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                }
            }
        });
    }

</script>
</body>
</html>
