<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="起重机设备备案申请详情"></title>
    <th:block th:include="admin/include/head :: head"/>
    <link rel="stylesheet" th:href="@{/resources/file-input/css/fileinput.min.css}">

    <style>
        .container-fluid {
            padding: 15px;
        }

        .box-footer {
            width: max-content;
            margin: 0 auto;
        }

        .rejectButton {
            text-align: center;
            margin: 10px 0;
        }

        .title {
            font-size: 14px;
            font-weight: bold;
            padding: 10px;
            margin-bottom: 10px;
            margin-right: 12px;
            background: #e1f0f9;
            border-radius: 4px;
            position: relative;
        }

        .title::before {
            content: '';
            width: 4px;
            height: 100%;
            background: #3c8dbc;
            position: absolute;
            left: 0;
            top: 0;
        }

        .form-control-feedback {
            display: none !important;
        }

        .btn-info {
            width: 130px;
            height: 40px;
        }
    </style>
</head>
<body>
<div id="filingEquipmentFilingApplicationDetail" class="container-fluid">
    <form id="filingEquipmentFilingApplication-form-edit" class="form-horizontal" autocomplete="off"
          th:object="${filingEquipmentFilingApplication}">
        <input type="hidden" id="id" name="id" th:value="*{id}"/>
        <div class="col-md-12" style="margin-top: 10px">
            <div class="title">设备信息
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="applicationDate" class="col-sm-3 control-label">申请日期</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="applicationDate" th:value="*{applicationDate}" autocomplete="off" disabled>
                        <!--                        <input type="hidden" class="form-control" id="now" name="applicationDate" autocomplete="off" th:value="*{applicationDate}">-->
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentType" class="col-sm-3 control-label require">设备类型</label>
                    <!--                    <input type="hidden" th:value="*{equipmentType}" id="deviceType">-->
                    <div class="col-sm-9">
                        <select class="form-control" id="equipmentType" name="equipmentType" disabled>
                            <option value="T">塔式起重机</option>
                            <option value="S">施工升降机(不含物料提升机)</option>
                            <option value="W">物料提升机</option>
                            <option value="Q">其他起重机械</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="oldNumberFlag" class="col-sm-3 control-label require">是否存在旧备案编号</label>
                    <div class="col-sm-9">
                        <!--                        <input type="hidden" id="oldNumberFlag" name="oldNumberFlag">-->
                        <select disabled class="form-control" autocomplete="off" id="oldNumberFlag" name="oldNumberFlag">
                            <option value="1">是</option>
                            <option value="0">否</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="oldNumber" class="col-sm-3 control-label require">旧备案编号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="oldNumber" name="oldNumber" th:value="*{oldNumber}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="deviceName" class="col-sm-3 control-label require">设备名称</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="deviceName" name="deviceName" th:value="*{deviceName}"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="specificationModel" class="col-sm-3 control-label require">规格型号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="specificationModel" name="specificationModel"
                               th:value="*{specificationModel}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="purchaseTime" class="col-sm-3 control-label require">购买时间</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control datepicker" id="purchaseTime" name="purchaseTime"
                               th:value="*{purchaseTime}"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="liftingWeight" class="col-sm-3 control-label">起重重量（吨）</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="liftingWeight" name="liftingWeight"
                               th:value="*{liftingWeight}" placeholder="单位：吨"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">产权单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="propertyUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="propertyUnit" name="propertyUnit"
                               th:value="*{propertyUnit}"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="propertyUnitCode" class="col-sm-3 control-label require">单位社会统一信用代码</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="propertyUnitCode" name="propertyUnitCode"
                               th:value="*{propertyUnitCode}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="unitAreaCode" class="col-sm-3 control-label require">单位属地</label>
                    <div class="col-sm-9">
                        <!--                        <input type="hidden" th:value="*{remarks}" id="remarks" name="remarks">-->
                        <input disabled type="text" class="form-control" id="unitAreaCode" name="unitAreaCode" autocomplete="off" th:value="*{remarks}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="unitAddress" class="col-sm-3 control-label require">单位详细地址</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="unitAddress" name="unitAddress"
                               th:value="*{unitAddress}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="legalRepresentative" class="col-sm-3 control-label require">法定代表人</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="legalRepresentative" name="legalRepresentative"
                               th:value="*{legalRepresentative}"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="legalRepresentativeContactNumber" class="col-sm-3 control-label require">法定代表人联系电话</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="legalRepresentativeContactNumber"
                               name="legalRepresentativeContactNumber" th:value="*{legalRepresentativeContactNumber}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="technicalDirector" class="col-sm-3 control-label require">技术负责人</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="technicalDirector" name="technicalDirector"
                               th:value="*{technicalDirector}"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="technicalDirectorContactNumber" class="col-sm-3 control-label require">技术负责人联系方式</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="technicalDirectorContactNumber" name="technicalDirectorContactNumber"
                               th:value="*{technicalDirectorContactNumber}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentManager" class="col-sm-3 control-label require">设备管理负责人</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="equipmentManager" name="equipmentManager"
                               th:value="*{equipmentManager}"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentManagerContactNumber" class="col-sm-3 control-label require">设备管理负责人联系方式</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="equipmentManagerContactNumber"
                               name="equipmentManagerContactNumber" th:value="*{equipmentManagerContactNumber}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">生产单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="manufacturer" class="col-sm-3 control-label require">生产厂家</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="manufacturer" name="manufacturer"
                               th:value="*{manufacturer}"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="factoryTime" class="col-sm-3 control-label require">出厂时间</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control datepicker" id="factoryTime" name="factoryTime"
                               th:value="*{factoryTime}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="factoryLicenseNumber" class="col-sm-3 control-label require">出厂编号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="factoryLicenseNumber" name="factoryLicenseNumber"
                               th:value="*{factoryLicenseNumber}"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="manufacturingLicenseNumber" class="col-sm-3 control-label require">制造许可证编号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="manufacturingLicenseNumber"
                               name="manufacturingLicenseNumber" th:value="*{manufacturingLicenseNumber}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">审核信息
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="col-sm-3 control-label require">审核状态</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control"
                               name="auditStatus" th:if="*{auditStatus=='0'}" value="未审核"/>
                        <input disabled type="text" class="form-control"
                               name="auditStatus" th:if="*{auditStatus=='1'}" value="审核通过"/>
                        <input disabled type="text" class="form-control"
                               name="auditStatus" th:if="*{auditStatus=='2'}" value="驳回"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">附件信息</div>
        </div>
        <table class="table table-striped table-bordered">
            <tbody>
            <tr>
                <th>产品合格证文件路径</th>
                <td id="productCertificationPath"></td>
                <th>设备制造许可证文件路径</th>
                <td id="equipmentManufacturingLicensePath"></td>
            </tr>
            <tr>
                <th>起重机备案申请表文件路径</th>
                <td id="filingApplicationFormPath"></td>
                <th>购销凭证文件路径</th>
                <td id="purchaseSaleCertificatePath"></td>
            </tr>
            <tr>
                <th>营业执照文件路径</th>
                <td id="businessLicensePath"></td>
            </tr>
            </tbody>
        </table>

    </form>
    <div id="examine">
    </div>
    <div id="reject" hidden>
        <label for="rejectReason">请输入退回原因</label>
        <br/>
        <textarea rows="4" id="rejectReason" name="rejectReason" wrap="hard" style="width: 100%"></textarea>
        <div class="rejectButton">
            <button type="button" class="btn btn-info" onclick="saveReject()">提交</button>
            <button type="button" class="btn btn-info" onclick="reback()">返回</button>
        </div>

    </div>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/file-input/js/fileinput.min.js}"></script>

<script th:inline="javascript">
    let examiner = [[${@permission.hasPermi('equipment:examine')}]];
    let id = [[${filingEquipmentFilingApplication}]].id;
    let index = parent.layer.getFrameIndex(window.name);
    let files = [[${files}]];
    let status = [[${filingEquipmentFilingApplication.auditStatus}]];
    let filingcode = [[${filingEquipmentFilingApplication.filingCode}]];
    $("#equipmentType").val([[${filingEquipmentFilingApplication}]].equipmentType)
    $(function () {
        $("#oldNumberFlag").val([[${filingEquipmentFilingApplication}]].oldNumberFlag)
        if ($("#oldNumberFlag").val() == '0') {
            $("#oldNumber").attr('disabled', true)
        }
    })
    $(function () {
        var productCertification = "";
        var businessLicense = "";
        var filingApplicationForm = "";
        var purchaseSaleCertificate = "";
        var equipmentManufacturingLicense = "";
        for (let i = 0; i < files.length; i++) {
            let file = files[i];
            if (file.connectId.indexOf("productCertification") > 0) {
                productCertification += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("businessLicense") > 0) {
                businessLicense += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("filingApplicationForm") > 0) {
                filingApplicationForm += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("purchaseSaleCertificate") > 0) {
                purchaseSaleCertificate += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("equipmentManufacturingLicense") > 0) {
                equipmentManufacturingLicense += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
        }
        $("#productCertificationPath").html(productCertification);
        $("#businessLicensePath").html(businessLicense);
        $("#filingApplicationFormPath").html(filingApplicationForm);
        $("#purchaseSaleCertificatePath").html(purchaseSaleCertificate);
        $("#equipmentManufacturingLicensePath").html(equipmentManufacturingLicense);
    })

    function popsaveclose() {
        parent.location.reload();
        parent.layer.close(index);
    }

    function reject() {
        $("#examine").hide();
        $("#reject").show();
    }

    function reback() {
        $("#examine").show();
        $("#reject").hide();
    }

    $(function () {
        if ("hidden" !== examiner && status == "0") {
            var html = "";
            html += "<div>" +
                "        <div class=\"box-footer\">" +
                "            <button type=\"button\" class=\"btn btn-info\" onclick=\"examine()\">通过</button>" +
                "            <button type=\"button\" class=\"btn btn-info\" onclick=\"reject()\">驳回</button>" +
                "            <button type=\"button\" class=\"btn btn-info \" onclick=\"popsaveclose()\">关闭</button>" +
                "            </div>" +
                "         </div>"
            $('#examine').html(html);
        }
        if ('1' == status) {
            var html = "";
            html += "<div>" +
                "        <div class=\"box-footer\">" +
                "           <button type=\"button\" class=\"btn btn-info \"> <a target='_parent' href=\"/admin/filingEquipmentFilingApplication/license?code=" + filingcode + "\">打印备案证</a></button>" + "            <button type=\"button\" class=\"btn btn-info \"><a target='_parent' href=\"/admin/filingEquipmentFilingApplication/card?code=" + filingcode + "\">打印备案牌</a></button>" +
                "            <button type=\"button\" class=\"btn btn-info \" onclick=\"popsaveclose()\">关闭</button>" +
                "            </div>" +
                "         </div>"
            $('#examine').html(html);
        }
    });

    function saveReject() {
        let rejectReason = $("#rejectReason").val();
        $.ajax({
            url: "/admin/filingEquipmentFilingApplication/reject",
            data: {id: id, rejectReason: rejectReason},
            type: "POST",
            dataType: "JSON",
            success: function (res) {
                if (res.code == 0) {
                    layer.msg(res.msg, {time: 1000});
                    setTimeout(() => {
                        popsaveclose();
                    }, 1000)
                } else {
                    layer.msg(res.msg);
                }

            }
        })
    }

    function examine() {
        layer.open({
            type: 0,
            title: '审核通过',
            content: '确认通过当前申请？',
            btn: ['确认', '取消'],
            yes: function (index, layero) {
                $.ajax({
                    url: '/admin/filingEquipmentFilingApplication/examine',
                    method: 'post',
                    dataType: "JSON",
                    data: {
                        id: id
                    },
                    success: function (res) {
                        if (res.code == 0) {
                            layer.msg(res.msg, {time: 1000});
                            setTimeout(() => {
                                popsaveclose();
                            }, 1000)
                        } else {
                            layer.msg(res.msg);
                        }
                    }
                })
            }
        })
    }

</script>
</body>
</html>
