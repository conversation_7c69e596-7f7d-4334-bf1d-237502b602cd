<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="相关信息接口"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-sm-1" for="searchName">名称</label>
                    <div class="col-sm-3">
                        <input type="text" class="form-control col-sm-10" id="searchName" name="name"
                               placeholder="请输入名称">
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="restForm('searchForm')"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="formSearch('pushPersonDatatable','/admin/pushPersonData/list' ,'searchForm')">
                            <i class="fa fa-search"></i>查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <table id="pushPersonDatatable"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";

    var addFlag = [[${@permission.hasPermi('pushPersonData:add')}]];
    var editFlag = [[${@permission.hasPermi('pushPersonData:edite')}]];
    var delFlag = [[${@permission.hasPermi('pushPersonData:delete')}]];
    var designFlag = [[${@permission.hasPermi('pushPersonData:design')}]];

    $(function () {
        var columns = [
            {
                field: 'ck',
                checkbox: true,
                align: 'center',
                valign: 'middle'
            },
            {
                field: "businessDataId",
                title: "业务数据ID",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "certNum",
                title: "证照编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "certId",
                title: "证照标识",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "corpCode",
                title: "所属单位统一社会信用代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "corpName",
                title: "所属单位名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "personType",
                title: "人员类型",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "name",
                title: "名字",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "identityCard",
                title: "身份证件号码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "identityCardType",
                title: "身份证件号码类型代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "dataFlag",
                title: "数据有效标识",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "dataRemark",
                title: "数据无效原因",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "pushFlag",
                title: "推送标识",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "pushTime",
                title: "推送时间",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "creatTime",
                title: "创建时间",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "creatAt",
                title: "创建人",
                align: 'center',
                valign: 'middle'
            },
            {
                field: 'createTime',
                title: '创建时间',
                align: 'center',
                valign: 'middle'
            },
            {
                field: 'id',
                title: '操作',
                align: 'center',
                valign: 'middle',
                cellStyle:{
                    css:{"white-space":"nowrap"}
                },
                formatter: function (value, row, index) {
                    var actions = [];
                    if ("hidden" != editFlag) {
                        actions.push('<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="updatelayer(\'' + value + '\',\'编辑\',\'/admin/pushPersonData/edit/\')"><i class="fa fa-edit"></i>编辑</button>');
                    }
                    if ("hidden" != designFlag) {
                        actions.push('<button id="btn_detail" type="button" class="btn btn-success  btn-xs" onclick="detaillayer(\'' + value + '\',\'详情\',\'/admin/pushPersonData/detail/\')"><i class="fa fa-eye"></i>详情</button>');
                    }
                    if ("hidden" != delFlag) {
                        actions.push('<button type="button" class="btn btn-danger  btn-xs" onclick="deletes(\'/admin/pushPersonData/delete\',\'' + value + '\')" ><i class="fa fa-trash-o"></i>删除</button>');
                    }
                    return actions.join('');
                }
            }
        ];
        var toolbar = [];
        toolbar.push('<div id="toolbar" class="btn-group">');
        if("hidden" != addFlag){
            toolbar.push('<button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="poplayer(2,\'新增\',\'/admin/pushPersonData/toForm\')"><i class="fa fa-plus"></i>新增</button>');
        }
        if("hidden" != delFlag){
            toolbar.push('<button id="btn_delete" type="button" class="btn btn-danger btn-xs" onclick="deletes(\'/admin/pushPersonData/delete\')" ><i class="fa fa-trash-o"></i>删除</button>');
        }
        toolbar.push('</div>');
        createBootstrapTable($("#pushPersonDatatable"), '/admin/pushPersonData/list', columns, false, toolbar.join(''), 'server');
    });
</script>
</body>
</html>