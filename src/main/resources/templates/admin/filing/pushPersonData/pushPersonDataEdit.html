<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="相关信息接口编辑"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="pushPersonDataform" class="container-fluid" >

    <form id="pushPersonData-form-edit" class="form-horizontal" autocomplete="off" th:object="${pushPersonData}">
            <input type="hidden" id="id" name="id" th:value="*{id}"/>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="businessDataId" class="col-sm-4 control-label require">业务数据ID</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="businessDataId" name="businessDataId" th:value="*{businessDataId}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="certNum" class="col-sm-4 control-label require">证照编号</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="certNum" name="certNum" th:value="*{certNum}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="certId" class="col-sm-4 control-label require">证照标识</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="certId" name="certId" th:value="*{certId}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="corpCode" class="col-sm-4 control-label require">所属单位统一社会信用代码</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="corpCode" name="corpCode" th:value="*{corpCode}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="corpName" class="col-sm-4 control-label require">所属单位名称</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="corpName" name="corpName" th:value="*{corpName}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="personType" class="col-sm-4 control-label require">人员类型</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="personType" name="personType" th:value="*{personType}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="name" class="col-sm-4 control-label require">名字</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="name" name="name" th:value="*{name}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="identityCard" class="col-sm-4 control-label require">身份证件号码</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="identityCard" name="identityCard" th:value="*{identityCard}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="identityCardType" class="col-sm-4 control-label require">身份证件号码类型代码</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="identityCardType" name="identityCardType" th:value="*{identityCardType}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="dataFlag" class="col-sm-4 control-label require">数据有效标识</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="dataFlag" name="dataFlag" th:value="*{dataFlag}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="dataRemark" class="col-sm-4 control-label require">数据无效原因</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="dataRemark" name="dataRemark" th:value="*{dataRemark}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="pushFlag" class="col-sm-4 control-label require">推送标识</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="pushFlag" name="pushFlag" th:value="*{pushFlag}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="pushTime" class="col-sm-4 control-label require">推送时间</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="pushTime" name="pushTime" th:value="*{pushTime}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="creatTime" class="col-sm-4 control-label require">创建时间</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="creatTime" name="creatTime" th:value="*{creatTime}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="creatAt" class="col-sm-4 control-label require">创建人</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="creatAt" name="creatAt" th:value="*{creatAt}"/>
                   </div>
                </div>
            </div>
        <div class="form-group" >
            <button type="button" class="btn btn-info col-sm-offset-6" onclick="formSave()">提交</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>

<script type="text/javascript" th:inline="javascript">
    initValidator();
    function formSave(){
        var bootstrapValidator = $('#pushPersonData-form-edit').data('bootstrapValidator');
        if(bootstrapValidator.isValid()) {
            save('/pushPersonData/update' ,'pushPersonData-form-edit')
        }
    }
    function initValidator() {
        $('#pushPersonData-form-edit').bootstrapValidator({
            message: '输入值不满足要求',
            excluded : [':disabled',':hidden'],
            verbose:false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
        businessDataId: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushPersonData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        businessDataId: $('#businessDataId').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        certNum: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushPersonData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        certNum: $('#certNum').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        certId: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushPersonData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        certId: $('#certId').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        corpCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushPersonData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        corpCode: $('#corpCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        corpName: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushPersonData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        corpName: $('#corpName').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        personType: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushPersonData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        personType: $('#personType').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        name: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushPersonData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        name: $('#name').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        identityCard: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushPersonData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        identityCard: $('#identityCard').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        identityCardType: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushPersonData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        identityCardType: $('#identityCardType').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        dataFlag: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushPersonData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        dataFlag: $('#dataFlag').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        dataRemark: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushPersonData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        dataRemark: $('#dataRemark').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        pushFlag: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushPersonData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        pushFlag: $('#pushFlag').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        pushTime: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushPersonData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        pushTime: $('#pushTime').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        creatTime: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 6,
                    message: '不能超过6位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushPersonData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        creatTime: $('#creatTime').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        creatAt: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushPersonData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        creatAt: $('#creatAt').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
    }
    });
        $('#pushPersonData-form-edit').data('bootstrapValidator').validate();
    }
</script>

</body>
</html>
