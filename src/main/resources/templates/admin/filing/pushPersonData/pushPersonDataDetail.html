<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="相关信息接口详情"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="pushPersonDataDetail" class="container-fluid" >
    <form id="pushPersonData-form-edit" class="form-horizontal" autocomplete="off" th:object="${pushPersonData}">
        <input type="hidden" id="id" name="id" th:value="*{id}"/>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="businessDataId" class="col-sm-4 control-label require">业务数据ID</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="businessDataId" name="businessDataId" th:value="*{businessDataId}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="certNum" class="col-sm-4 control-label require">证照编号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="certNum" name="certNum" th:value="*{certNum}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="certId" class="col-sm-4 control-label require">证照标识</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="certId" name="certId" th:value="*{certId}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="corpCode" class="col-sm-4 control-label require">所属单位统一社会信用代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="corpCode" name="corpCode" th:value="*{corpCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="corpName" class="col-sm-4 control-label require">所属单位名称</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="corpName" name="corpName" th:value="*{corpName}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="personType" class="col-sm-4 control-label require">人员类型</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="personType" name="personType" th:value="*{personType}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="name" class="col-sm-4 control-label require">名字</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="name" name="name" th:value="*{name}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="identityCard" class="col-sm-4 control-label require">身份证件号码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="identityCard" name="identityCard" th:value="*{identityCard}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="identityCardType" class="col-sm-4 control-label require">身份证件号码类型代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="identityCardType" name="identityCardType" th:value="*{identityCardType}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="dataFlag" class="col-sm-4 control-label require">数据有效标识</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="dataFlag" name="dataFlag" th:value="*{dataFlag}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="dataRemark" class="col-sm-4 control-label require">数据无效原因</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="dataRemark" name="dataRemark" th:value="*{dataRemark}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="pushFlag" class="col-sm-4 control-label require">推送标识</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="pushFlag" name="pushFlag" th:value="*{pushFlag}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="pushTime" class="col-sm-4 control-label require">推送时间</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="pushTime" name="pushTime" th:value="*{pushTime}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="creatTime" class="col-sm-4 control-label require">创建时间</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="creatTime" name="creatTime" th:value="*{creatTime}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="creatAt" class="col-sm-4 control-label require">创建人</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="creatAt" name="creatAt" th:value="*{creatAt}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
        <div class="box-footer col-md-6 col-md-offset-5">
            <button type="button" class="btn btn-info pull-right" onclick="formClose()">关闭</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script type="text/javascript">
    function formClose() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    }
</script>
</body>
</html>
