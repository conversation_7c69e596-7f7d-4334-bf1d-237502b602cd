<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="起重机备案表详情"></title>
    <link rel="stylesheet" th:href="@{/resources/license/css/common.css}">
</head>
<body>
<div class="container">
    <button id="button" onclick="toprint()">打印</button>
</div>
<!--打印开始-->
<div class="container print">
    <table class="table-bordered" th:object="${filingEquipmentRegistration}">
        <tr>
            <td colspan="12"><h1 class="text-center">河南省建筑起重机械设备备案牌</h1>
            </td>
        </tr>
        <tr>
            <td class="text-center" colspan="2">设备名称</td>
            <td colspan="4"><input type="text" class="input-control" disabled th:value="*{deviceName}"></td>
            <td class="text-center" colspan="2">规格型号</td>
            <td colspan="4"><input type="text" class="input-control" disabled th:value="*{specificationModel}"></td>
        </tr>
        <tr>
            <td class="text-center" colspan="2">生产厂家</td>
            <td colspan="4"><input type="text" class="input-control" disabled th:value="*{manufacturer}"></td>
            <td class="text-center" colspan="2">许可证号</td>
            <td colspan="4"><input type="text" class="input-control" disabled th:value="*{manufacturingLicenseNumber}">
            </td>
        </tr>
        <tr>
            <td class="text-center" colspan="2">出厂日期</td>
            <td colspan="4"><input type="text" class="input-control" disabled th:value="*{factoryTime}"></td>
            <td class="text-center" colspan="2">出厂编号</td>
            <td colspan="4"><input type="text" class="input-control" disabled th:value="*{factoryLicenseNumber}"></td>
        </tr>
        <tr>
            <td class="text-center" colspan="2">购买时间</td>
            <td colspan="4"><input type="text" class="input-control" disabled th:value="*{purchaseTime}"></td>
            <td class="text-center" colspan="2">起重重量</td>
            <td colspan="4"><input type="text" class="input-control" disabled th:value="*{liftingWeight}"></td>
        </tr>
        <tr>
            <td class="text-center" colspan="2">产权单位</td>
            <td colspan="4"><input type="text" class="input-control" disabled th:value="*{propertyUnit}"></td>
            <td class="text-center" colspan="2">联系电话</td>
            <td colspan="4"><input type="text" class="input-control" disabled
                                   th:value="*{legalRepresentativeContactNumber}"></td>
        </tr>
        <tr>
            <td class="text-center" colspan="2">备案编号</td>
            <td colspan="4"><input type="text" class="input-control" disabled th:value="*{filingCode}"></td>
            <td class="text-center" colspan="2">备案时间</td>
            <td colspan="4"><input type="text" class="input-control" disabled
                                   th:value="*{#dates.format(filingDate,'YYYY-MM-dd')}"></td>
        </tr>
        <tr>
            <td class="text-center" colspan="2"></td>
            <td colspan="4"></td>
            <td colspan="6"><h3>鹤壁市建设工程安全监督站监制</h3></td>
        </tr>
    </table>
</div>

</body>
<script th:src="@{/resources/license/js/jQuery.print.js}"></script>
<script th:src="@{/resources/license/js/jquery-1.6.1.min.js}"></script>
<script th:inline="javascript">
    let date = [[${filingEquipmentRegistration.filingDate}]];
    $(function () {
        console.log(date);
        let year = date.substr(0, 4);
        let month = date.substr(5, 2);
        let day = date.substr(8, 2);
        $("#year").val(year);
        $("#month").val(month);
        $("#day").val(day);

    })

    function toprint() {
        $("#button").hide();
        print();
    }

</script>
</html>
