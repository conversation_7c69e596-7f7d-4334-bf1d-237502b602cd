<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
	<title th:text="起重机备案表详情"></title>
	<link rel="stylesheet" th:href="@{/resources/license/css/common.css}">
</head>
<body>
<div class="container">
	<button id="button" onclick="toprint()">打印</button>
</div>
<!--打印开始-->
<div class="container print">
	<div class="row">
		<h2 class="text-center">河南省建筑起重机械设备备案证</h2>
	</div>
	<table class="table-bordered" th:object="${filingEquipmentRegistration}">
		<tr>
			<td class="text-center" colspan="4">设备名称</td>
			<td colspan="8"><input type="text" class="input-control" disabled th:value="*{deviceName}"></td>
		</tr>
		<tr>
			<td class="text-center" colspan="4">规格型号</td>
			<td colspan="8"><input type="text" class="input-control" disabled th:value="*{specificationModel}"></td>
		</tr>
		<tr>
			<td class="text-center" colspan="4">生产厂家</td>
			<td colspan="8"><input type="text" class="input-control" disabled th:value="*{manufacturer}"></td>
		</tr>
		<tr>
			<td class="text-center" colspan="4">制造许可证号</td>
			<td colspan="8"><input type="text" class="input-control" disabled th:value="*{manufacturingLicenseNumber}"></td>
		</tr>
		<tr>
			<td class="text-center" colspan="4">出厂日期</td>
			<td colspan="8"><input type="text" class="input-control" disabled th:value="*{factoryTime}"></td>
		</tr>
		<tr>
			<td class="text-center" colspan="4">出厂编号</td>
			<td colspan="8"><input type="text" class="input-control" disabled th:value="*{factoryLicenseNumber}"></td>
		</tr>
		<tr>
			<td class="text-center" colspan="4">购买时间</td>
			<td colspan="8"><input type="text" class="input-control" disabled th:value="*{purchaseTime}"></td>
		</tr>
		<tr>
			<td class="text-center" colspan="4">设备产权单位</td>
			<td colspan="8"><input type="text" class="input-control" disabled  th:value="*{propertyUnit}"></td>
		</tr>
		<tr>
			<td class="text-center" colspan="4">社会统一信用代码</td>
			<td colspan="8"><input type="text" class="input-control" disabled  th:value="*{propertyUnitCode}"></td>
		</tr>
		<tr>
			<td class="text-center" colspan="4">法定代表人</td>
			<td colspan="8"><input type="text" class="input-control" disabled th:value="*{legalRepresentative}"></td>
		</tr>
		<tr>
			<td class="text-center" colspan="4">单位技术负责人</td>
			<td colspan="8"><input type="text" class="input-control" disabled th:value="*{technicalDirector}"></td>
		</tr>
		<tr>
			<td class="text-center" colspan="4">设备管理负责人</td>
			<td colspan="8"><input type="text" class="input-control" disabled th:value="*{equipmentManager}"></td>
		</tr>
		<tr>
			<td class="text-center" colspan="4">设备备案编号</td>
			<td colspan="8"><input type="text" class="input-control" disabled th:value="*{filingCode}"></td>
		</tr>
		<tr>
			<td class="text-center" colspan="4">设备旧备案编号</td>
			<td colspan="8" th:if="*{oldNumber}"><input  type="text" class="input-control" disabled th:value="*{oldNumber}"></td>
			<td colspan="8" th:unless="*{oldNumber}"><input  type="text" class="input-control" value="/" disabled></td>
		</tr>
		<tr>
			<td colspan="12">
				<table>
					<tr><td colspan="12"></td></tr>
					<tr><td colspan="12"></td></tr>
					<tr><td colspan="12"></td></tr>
					<tr><td colspan="12"></td></tr>
					<tr><td colspan="12"></td></tr>
					<tr><td colspan="12"></td></tr>
					<tr><td colspan="12"></td></tr>
					<tr><td colspan="12"></td></tr>
					<tr><td colspan="12"></td></tr>
					<tr><td colspan="12"></td></tr>
					<tr><td colspan="12"></td></tr>
					<tr><td colspan="12"></td></tr>
					<tr><td colspan="12"></td></tr>
					<tr>
						<td colspan="6"></td>
						<td colspan="3" align="right">备案机关：（公章）</td>
						<td colspan="3"></td>
					</tr>
					<tr>
						<td colspan="9"></td>
						<td colspan="3">
							<table>
								<tr>
									<td width="50px"><input id="year" class="input-control" type="text" name="baseName" disabled></td>
									<td>年</td>
									<td><input id="month" class="input-control" type="text" disabled></td><td>月</td>
									<td><input id="day" class="input-control" type="text" disabled></td><td>日</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
</div>

</body>
<script th:src="@{/resources/license/js/jQuery.print.js}"></script>
<script th:src="@{/resources/license/js/jquery-1.6.1.min.js}"></script>
<script th:inline="javascript">
	let date =  [[${filingEquipmentRegistration.filingDate}]];
	console.log([[${filingEquipmentRegistration}]])
	$(function () {
		console.log(date);
		let year = date.substr(0,4);
		let month = date.substr(5,2);
		let day = date.substr(8,2);
		$("#year").val(year);
		$("#month").val(month);
		$("#day").val(day);

	})

	function toprint() {
		// $("#button").hide();
		print();
	}

</script>
</html>
