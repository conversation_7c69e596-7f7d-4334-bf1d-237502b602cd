<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
	<title th:text="起重机安装告知书详情"></title>
	<link rel="stylesheet" th:href="@{/resources/license/css/common.css}">
</head>
<body>
<div class="container">
	<button id="button" onclick="toprint()">打印</button>
</div>
<!--打印开始-->
<div class="container print">
	<div class="row">
		<h2 class="text-center">建筑起重机械设备安装告知书</h2>
	</div>
	<table>
		<tr>
			<td colspan="4"><input type="text" name="baseName" placeholder="（备案管理机关名称）" class="form-control"></td>
			<td colspan="7" align="left">:</td>
		</tr>
		<tr>
			<td colspan="2" align="right">我单位拟于</td>
			<td><input type="text"  class="form-control"></td>
			<td>年</td>
			<td><input type="text" class="form-control"></td>
			<td>月</td>
			<td><input type="text" class="form-control"></td>
			<td>日在</td>
			<td colspan="3">
				<input type="text"   class="form-control">
			</td>

		</tr>
		<tr>
			<td colspan="2">对一台</td>
			<td colspan="5"><input type="text"   class="form-control"></td>
			<td colspan="7">进行安装，其</td>
		</tr>
		<tr>
			<td colspan="12">安装相关资料已经施工总承包单位、监理单位审核，</td>

		</tr><tr>
		<td colspan="12">特此告知。</td>
		</tr>
		<tr>
			<td></td>
			<td colspan="11">附件：材料目录</td>
		</tr>
		<tr><td colspan="12" height="100"></td></tr>

		<tr>
			<td colspan="6"></td>
			<td colspan="3" align="right">单位（公章）：</td>
			<td colspan="3"></td>
		</tr>
		<tr>
			<td colspan="9"></td>
			<td colspan="3">
				<table>
					<tr>
						<td><input class="form-control" type="text" name="baseName" ></td>
						<td>年</td>
						<td><input class="form-control" type="text" ></td><td>月</td>
						<td><input class="form-control" type="text" ></td><td>日</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
</div>
</body>
<script th:src="@{/resources/license/js/jQuery.print.js}"></script>
<script th:src="@{/resources/license/js/jquery-1.6.1.min.js}"></script>
<script th:inline="javascript">
	let date =  [[${filingEquipmentRegistration.installationTime}]];
	$(function () {
		console.log(date);
		let year = date.substr(0,4);
		let month = date.substr(5,2);
		let day = date.substr(8,2);
		$("#year").val(year);
		$("#month").val(month);
		$("#day").val(day);

	})

	function toprint() {
		$("#button").hide();
		print();
	}

</script>
</html>
