<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
	<title th:text="起重机备案表详情"></title>
	<link rel="stylesheet" th:href="@{/resources/license/css/equipmentuse.css}">
</head>
<body>
<div class="container" id="button">
	<button onclick="toprint()">打印</button>
</div>
<!--打印开始-->
<div class="container print">
	<div class="row">
		<h2 class="text-center">建筑起重机械设备使用登记证</h2>
	</div>
	<table class="table-bordered" th:object="${filingUse}">
		<tr>
			<td class="text-center" colspan="2">设备名称:</td>
			<td colspan="4"><input type="text" class="input-control" th:value="*{deviceName}" disabled></td>
			<td class="text-center" colspan="2">规格型号:</td>
			<td colspan="4"><input type="text" class="input-control" th:value="*{specificationModel}"disabled></td>
		</tr>
		<tr>
			<td class="text-center" colspan="2">工程名称:</td>
			<td colspan="4"><input type="text" class="input-control" th:value="*{engineeringName}"disabled></td>
			<td class="text-center" colspan="2">安装高度:</td>
			<td colspan="4"><input type="text" class="input-control" th:value="*{firstInstallationHeight}"disabled></td>
		</tr>
		<tr>
			<td class="text-center" colspan="2">使用单位:</td>
			<td colspan="4"><input type="text" class="input-control" th:value="*{useUnit}"disabled></td>
			<td class="text-center" colspan="2">项目经理：</td>
			<td colspan="4"><input type="text" class="input-control" th:value="*{projectManager}"disabled></td>
		</tr>
		<tr>
			<td class="text-center" colspan="2">安装单位:</td>
			<td colspan="4"><input type="text" class="input-control" th:value="*{installationUnit}"disabled></td>
			<td class="text-center" colspan="2">安装日期:</td>
			<td colspan="4"><input type="text" class="input-control" th:value="*{#dates.format(installationEndTime,'YYYY年MM月dd日')}"disabled></td>
		</tr>
		<tr>
			<td class="text-center" colspan="2">检测单位:</td>
			<td colspan="4"><input type="text" class="input-control" th:value="*{testingUnit}"disabled></td>
			<td class="text-center" colspan="2">检测日期:</td>
			<td colspan="4"><input type="text" class="input-control" th:value="*{#dates.format(testingDate,'YYYY年MM月dd日')}"disabled></td>
		</tr>
		<tr>
			<td class="text-center" colspan="2">备案编号：</td>
			<td colspan="4"><input type="text" class="input-control" th:value="*{deviceFilingCode}"disabled></td>
			<td class="text-center" colspan="2">登记编号:</td>
			<td colspan="4"><input type="text" class="input-control" th:value="*{useFilingCode}"disabled></td>
		</tr>
		<tr>
			<td colspan="12">
				<table>
					<tr>
						<td colspan="6"></td>
						<td colspan="3" align="right">登记机关（公章）</td>
						<td colspan="3"></td>
					</tr>
					<tr>
						<td colspan="9" align="right">登记日期</td>
						<td colspan="3">
							<table>
								<tr>
									<td width="30px"><input id="year" class="input-control" type="text" name="baseName" disabled></td>
									<td>年</td>
									<td><input id="month" class="input-control" type="text" disabled></td><td>月</td>
									<td><input id="day" class="input-control" type="text" disabled></td><td>日</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>

</div>
</body>
<script th:src="@{/resources/license/js/jQuery.print.js}"></script>
<script th:src="@{/resources/license/js/jquery-1.6.1.min.js}"></script>
<script th:inline="javascript">
	let date =  [[${filingUse.registrationDate}]];
	$(function () {
		console.log(date);
		let year = date.substr(0,4);
		let month = date.substr(5,2);
		let day = date.substr(8,2);
		$("#year").val(year);
		$("#month").val(month);
		$("#day").val(day);

	})

	function toprint() {
		$("#button").hide();
		print();
	}

</script>
</html>
