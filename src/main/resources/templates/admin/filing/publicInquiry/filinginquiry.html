<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="起重机设备备案申请"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-sm-1" for="searchName">名称</label>
                    <div class="col-sm-3">
                        <input type="text" class="form-control col-sm-10" id="searchName" name="name"
                               placeholder="请输入名称">
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="restForm('searchForm')"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="formSearch('filingEquipmentFilingApplicationtable','/admin/filingEquipmentFilingApplication/list' ,'searchForm')">
                            <i class="fa fa-search"></i>查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <table id="filingEquipmentFilingApplicationtable"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";

    $(function () {
        var columns = [
            {
                field: "propertyUnit",
                title: "产权单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "deviceName",
                title: "设备名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "specificationModel",
                title: "规格型号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "applicationDate",
                title: "申请日期",
                align: 'center',
                valign: 'middle',

            },
            {
                field: "equipmentType",
                title: "设备类型",
                align: 'center',
                valign: 'middle',
                formatter:function(value,row,index){
                    if(value=='T'){
                        return "塔式起重机"
                    } else if(value=='S'){
                        return "施工升降机(不含物料提升机)"
                    }else if (value=="W"){
                        return "物料提升机"
                    }else if (value=='Q'){
                        return "其他起重机械"
                    }
                }
            },
            {
                field: "auditStatus",
                title: "确认状态",
                align: 'center',
                valign: 'middle',
                formatter: function (value, row, index) {
                    if (value == 0) {
                        return "未审核";
                    } else if (value == 1) {
                        return "审核通过";
                    }else if (value ==2){
                        return "驳回";
                    }
                    return value;
                    //return selectDictLabel(dataStatus, value)
                }
            },
            {
                field: "auditReject",
                title: "审核退回原因",
                align: 'center',
                valign: 'middle',
                formatter: function (value, row, index) {
                    if(value==null){
                        return "审核通过，无驳回"
                    }else{
                        return value
                    }
                }
            },
            {
                field: "auditTime",
                title: "审核时间",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "filingCode",
                title: "备案编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: 'id',
                title: '操作',
                align: 'center',
                valign: 'middle',
                cellStyle: {
                    css: {"white-space": "nowrap"}
                },
                formatter: function (value, row, index) {
                    var actions = [];
                    actions.push('<button id="btn_detail" type="button" class="btn btn-success  btn-xs" onclick="detaillayer(\'' + value + '\',\'详情\',\'/admin/filingEquipmentFilingApplication/seeDetail/\')"><i class="fa fa-eye"></i>详情</button>');
                    return actions.join('');
                }
            }
        ];
        createBootstrapTable($("#filingEquipmentFilingApplicationtable"), '/admin/filingEquipmentFilingApplication/seeList', columns, false);
    // , toolbar.join(''), 'server'
    });
</script>
</body>
</html>
