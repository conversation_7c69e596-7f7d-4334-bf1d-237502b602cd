<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备使用登记表"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-sm-1" for="searchName">名称</label>
                    <div class="col-sm-3">
                        <input type="text" class="form-control col-sm-10" id="searchName" name="name"
                               placeholder="请输入名称">
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="restForm('searchForm')"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="formSearch('filingEquipmentRegistrationFormtable','/admin/filingEquipmentRegistrationForm/list' ,'searchForm')">
                            <i class="fa fa-search"></i>查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <table id="filingEquipmentRegistrationFormtable"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";
    $(function () {
        var columns = [
            {
                field: "useFilingCode",
                title: "使用登记编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "applicationDate",
                title: "申请日期",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "useUnit",
                title: "使用单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "propertyUnit",
                title: "产权单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "deviceName",
                title: "设备名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "deviceFilingCode",
                title: "设备备案编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "engineeringName",
                title: "工程名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installationUnit",
                title: "安装单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "testingUnit",
                title: "检验检测单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "testingDate",
                title: "检测日期",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "auditStatus",
                title: "确认状态",
                align: 'center',
                valign: 'middle',
                formatter: function (value, row, index) {
                    if (value == 0) {
                        return "未审核";
                    } else if (value == 1) {
                        return "审核通过";
                    }else if (value ==2){
                        return "驳回";
                    }
                    return value;
                }
            },
            {
                field: "auditTime",
                title: "审核时间",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "auditRejectReason",
                title: "审核退回原因",
                align: 'center',
                valign: 'middle',
                formatter: function (value, row, index) {
                    if(value==null){
                        return "审核通过或未审核"
                    }else{
                        return value
                    }
                }
            },

            {
                field: 'id',
                title: '操作',
                align: 'center',
                valign: 'middle',
                cellStyle:{
                    css:{"white-space":"nowrap"}
                },
                formatter: function (value, row, index) {
                    var actions = [];
                        actions.push('<button id="btn_detail" type="button" class="btn btn-success  btn-xs" onclick="detaillayer(\'' + value + '\',\'详情\',\'/admin/filingEquipmentRegistrationForm/seeDetail/\')"><i class="fa fa-eye"></i>详情</button>');
                    return actions.join('');
                }
            }
        ];
        createBootstrapTable($("#filingEquipmentRegistrationFormtable"), '/admin/filingEquipmentRegistrationForm/seeList', columns, false);
    });
</script>
</body>
</html>