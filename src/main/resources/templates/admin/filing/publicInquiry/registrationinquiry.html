<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备使用备案表"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-sm-1" for="searchName">名称</label>
                    <div class="col-sm-3">
                        <input type="text" class="form-control col-sm-10" id="searchName" name="name"
                               placeholder="请输入名称">
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="restForm('searchForm')"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="formSearch('filingEquipmentUseRegistrationtable','/admin/filingEquipmentUseRegistration/list' ,'searchForm')">
                            <i class="fa fa-search"></i>查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <table id="filingEquipmentUseRegistrationtable"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";
    $(function () {
        var columns = [
            {
                field: 'ck',
                checkbox: true,
                align: 'center',
                valign: 'middle'
            },
            {
                field: "useUnit",
                title: "使用单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "userContact",
                title: "使用单位联系人",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "userContactNumber",
                title: "使用单位联系电话",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "propertyUnit",
                title: "产权单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "deviceName",
                title: "设备名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "specificationModel",
                title: "规格型号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "factoryTime",
                title: "出厂日期",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "deviceFilingCode",
                title: "设备备案编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "engineeringName",
                title: "工程名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "projectManager",
                title: "项目经理",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installationUnit",
                title: "安装单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installationUnitQualificationLevel",
                title: "安装单位资质等级",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "siteInstallationLeader",
                title: "现场安装负责人",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installationUnitQualificationCertificateNumber",
                title: "安装单位资质证号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installationUnitLicenseSafetyPermitNumber",
                title: "安装单位安全许可证号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installationStartTime",
                title: "安装开始时间",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installationEndTime",
                title: "安装终止时间",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "testingUnit",
                title: "检验检测单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "testingDate",
                title: "检测日期",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "testingLeadere",
                title: "检验检测负责人",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "jointInspectionDate",
                title: "联合验收日期",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "firstInstallationHeight",
                title: "首次安装高度",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "finalUseHeight",
                title: "最终使用高度",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "equipmentType",
                title: "设备类别",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "auditRejectReason",
                title: "审核驳回意见",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "auditStatus",
                title: "确认状态",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "auditTime",
                title: "审核时间",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "useFilingCode",
                title: "备案编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: 'createTime',
                title: '创建时间',
                align: 'center',
                valign: 'middle'
            },

        ];
        createBootstrapTable($("#filingEquipmentUseRegistrationtable"), '/admin/filingEquipmentUseRegistration/list', columns, false);
    });
</script>
</body>
</html>