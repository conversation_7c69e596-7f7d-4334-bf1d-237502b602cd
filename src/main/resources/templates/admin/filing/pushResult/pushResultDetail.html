<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="推送结果表详情"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="pushResultDetail" class="container-fluid" >
    <form id="pushResult-form-edit" class="form-horizontal" autocomplete="off" th:object="${pushResult}">
        <input type="hidden" id="id" name="id" th:value="*{id}"/>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="associationId" class="col-sm-4 control-label require">关联id</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="associationId" name="associationId" th:value="*{associationId}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="returnCode" class="col-sm-4 control-label require">返回代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="returnCode" name="returnCode" th:value="*{returnCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="returnMsg" class="col-sm-4 control-label require">返回信息</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="returnMsg" name="returnMsg" th:value="*{returnMsg}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="returnData" class="col-sm-4 control-label require">返回数据结果集</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="returnData" name="returnData" th:value="*{returnData}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="createDate" class="col-sm-4 control-label require">创建时间</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="createDate" name="createDate" th:value="*{createDate}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="creatAt" class="col-sm-4 control-label require">创建人</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="creatAt" name="creatAt" th:value="*{creatAt}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
        <div class="box-footer col-md-6 col-md-offset-5">
            <button type="button" class="btn btn-info pull-right" onclick="formClose()">关闭</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script type="text/javascript">
    function formClose() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    }
</script>
</body>
</html>
