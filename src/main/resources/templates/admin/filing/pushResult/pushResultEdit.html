<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="推送结果表编辑"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="pushResultform" class="container-fluid" >

    <form id="pushResult-form-edit" class="form-horizontal" autocomplete="off" th:object="${pushResult}">
            <input type="hidden" id="id" name="id" th:value="*{id}"/>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="associationId" class="col-sm-4 control-label require">关联id</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="associationId" name="associationId" th:value="*{associationId}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="returnCode" class="col-sm-4 control-label require">返回代码</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="returnCode" name="returnCode" th:value="*{returnCode}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="returnMsg" class="col-sm-4 control-label require">返回信息</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="returnMsg" name="returnMsg" th:value="*{returnMsg}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="returnData" class="col-sm-4 control-label require">返回数据结果集</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="returnData" name="returnData" th:value="*{returnData}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="createDate" class="col-sm-4 control-label require">创建时间</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="createDate" name="createDate" th:value="*{createDate}"/>
                   </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group" >
                   <label for="creatAt" class="col-sm-4 control-label require">创建人</label>
                   <div class="col-sm-6">
                        <input type="text" class="form-control" id="creatAt" name="creatAt" th:value="*{creatAt}"/>
                   </div>
                </div>
            </div>
        <div class="form-group" >
            <button type="button" class="btn btn-info col-sm-offset-6" onclick="formSave()">提交</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>

<script type="text/javascript" th:inline="javascript">
    initValidator();
    function formSave(){
        var bootstrapValidator = $('#pushResult-form-edit').data('bootstrapValidator');
        if(bootstrapValidator.isValid()) {
            save('/pushResult/update' ,'pushResult-form-edit')
        }
    }
    function initValidator() {
        $('#pushResult-form-edit').bootstrapValidator({
            message: '输入值不满足要求',
            excluded : [':disabled',':hidden'],
            verbose:false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
        associationId: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushResult/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        associationId: $('#associationId').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        returnCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushResult/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        returnCode: $('#returnCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        returnMsg: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushResult/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        returnMsg: $('#returnMsg').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        returnData: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 0,
                    message: '不能超过0位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushResult/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        returnData: $('#returnData').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        createDate: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushResult/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        createDate: $('#createDate').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        creatAt: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/pushResult/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        creatAt: $('#creatAt').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
    }
    });
        $('#pushResult-form-edit').data('bootstrapValidator').validate();
    }
</script>

</body>
</html>
