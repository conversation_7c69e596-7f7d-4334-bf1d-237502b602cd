<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="起重机备案表"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-sm-1" for="manufacturingLicenseNumber">制造许可证编号</label>
                    <div class="col-sm-2">
                        <input type="text" class="form-control col-sm-10" id="manufacturingLicenseNumber" name="manufacturingLicenseNumber"
                               placeholder="请输入名称">
                    </div>
                    <label class="control-label col-sm-1" for="cancel">申请类型</label>
                    <div class="col-sm-2">
                        <select  class="form-control col-sm-10" id="cancel" name="cancel"
                                 placeholder="请输入名称">
                            <option value="" disabled selected>全部</option>
                            <option value="false">申请备案</option>
                            <option value="true">注销备案</option>
                        </select>
                    </div>
                        <label class="control-label col-sm-1" for="equipmentType">设备类型</label>
                    <div class="col-sm-2">
                        <select class="form-control col-sm-10" id="equipmentType" name="equipmentType">
                            <option value="">请选择</option>
                            <option value="T">塔式起重机</option>
                            <option value="S">施工升降机(不含物料提升机)</option>
                            <option value="W">物料提升机</option>
                            <option value="Q">其他起重机械</option>
                        </select>
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="restForm('searchForm')"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="formSearch('filingEquipmentFilingtable','/checkMessage/filingList' ,'searchForm')">
                            <i class="fa fa-search"></i>查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <table id="filingEquipmentFilingtable"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";

    $(function () {
        var columns = [
            {
                field: "filingCode",
                title: "备案编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "filingDate",
                title: "备案日期",
                align: 'center',
                valign: 'middle',
                formatter:function (value) {
                    return changeDateFormat("yyyy-MM-dd", value);
                }
            },
            {
                field: "deviceName",
                title: "设备名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "specificationModel",
                title: "规格型号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "manufacturer",
                title: "生产厂家",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "manufacturingLicenseNumber",
                title: "制造许可证编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "propertyUnit",
                title: "产权单位",
                align: 'center',
                valign: 'middle'
            },{
                field: "cancellationStatus",
                title: "注销状态",
                align: 'center',
                valign: 'middle',
                formatter:function (value,raw,index) {
                    if (value==1){
                        return "已注销"
                    } else if(value==0){
                        return "未注销"
                    }
                }
            },
        ];
        var toolbar = [];
        createBootstrapTable($("#filingEquipmentFilingtable"), '/checkMessage/filingList', columns, false, toolbar.join(''), 'server');
    });
</script>
</body>
</html>
