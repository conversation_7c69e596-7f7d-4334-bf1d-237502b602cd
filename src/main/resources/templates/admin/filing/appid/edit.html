<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="起重机设备备案申请表单"></title>
    <th:block th:include="admin/include/head :: head"/>
    <link rel="stylesheet" th:href="@{/resources/file-input/css/fileinput.min.css}">
    <link rel="stylesheet" th:href="@{/resources/layui/css/layui.css}">
    <style>
        .container-fluid {
            padding: 20px;
        }

        .btn-info {
            width: 130px;
            height: 40px;
        }
    </style>
</head>
<body>

<div class="container-fluid">
    <form id="form" class="form-horizontal" th:object="${accessKey}">
        <input type="hidden" class="form-control" id="accessId" name="accessId" autocomplete="off" th:value="*{accessId}">

        <div class="form-group">
            <label for="systemName" class="col-sm-2 control-label require">平台名称</label>
            <div class="col-sm-10">
                <input class="form-control" id="systemName" name="systemName" autocomplete="off" th:value="*{systemName}">
            </div>
        </div>
        <div class="form-group">
            <label for="area" class="col-sm-2 control-label require">区域</label>
            <div class="col-sm-10">
                <input class="form-control" id="area" name="area" autocomplete="off" th:value="*{area}">
            </div>
        </div>
        <div class="form-group">
            <label for="areaCode" class="col-sm-2 control-label require">区域编码</label>
            <div class="col-sm-10">
                <input class="form-control" id="areaCode" name="areaCode" autocomplete="off" th:value="*{areaCode}">
            </div>
        </div>
        <div class="form-group col-md-12" style="text-align: center;margin-top: 100px">
            <button type="button" class="btn btn-info " onclick="formSave()">提交</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/file-input/js/fileinput.min.js}"></script>
<script th:src="@{/resources/file-input/js/locales/zh.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/layui_layui.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/cascader.min.js}"></script>

<script th:inline="javascript">
    "use strict";
    initValidator();
    const accessKey = [[${accessKey}]]
    console.log('accessKey', accessKey)
    $(function () {
        setTimeout(() => {
            $('.el-input__inner').val(accessKey.area)
        }, 500)
        layui.use('layCascader', function () {
            // var id = 0;
            var layCascader = layui.layCascader;
            var AreaLayCascader = layCascader({
                elem: '#area',
                placeholder: '请选择所属地',

                props: {
                    checkStrictly: true,
                    lazy: true,
                    lazyLoad: function (node, resolve) {
                        $("#area").trigger('change')
                        var level = node.level;

                        if (level == 1) {
                            return
                        } else {
                            $.ajax({
                                url: '/admin/area/getCountyList',
                                type: 'GET', // 请求类型
                                data: {
                                    parentId: node.value || 410000
                                },
                                dataType: 'json',
                                success: function (response) {
                                    var nodes = response.map(function (item) {
                                        return {
                                            value: item.id,
                                            label: item.name,
                                            leaf: level >= 1
                                        };
                                    });
                                    resolve(nodes);
                                },
                                error: function (error) {
                                    console.log('加载子节点数据失败:', error);
                                    resolve([]);
                                }
                            });
                        }
                    }
                },

            });
            AreaLayCascader.changeEvent(function (value, Node) {
                $("#areaCode").val(value)
                console.log(Node)
                $("#area").val(Node.data.label)

            })
        });

    })

    function formSave() {
        var bootstrapValidator = $('#form').data('bootstrapValidator');
        $('#form').data('bootstrapValidator').validate();
        if (bootstrapValidator.isValid()) {
            save('/admin/access/edit', 'form')
        }
    }


    function initValidator() {
        $('#form').bootstrapValidator({
            message: '输入值不满足要求',
            excluded: [],
            verbose: false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
                systemName: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                    },

                },
                area: {
                    validators: {
                        notEmpty: {
                            message: '请选择'
                        },
                    },
                },
                areaCode: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                    },
                },
            }
        });
    }

</script>
</body>
</html>
