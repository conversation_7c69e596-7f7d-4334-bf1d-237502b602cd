<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="起重机设备备案申请"></title>
    <th:block th:include="admin/include/head :: head"/>
    <style>
        .labelNowrap {
            white-space: nowrap;
        }

        table {
            word-break: break-all;
        }
    </style>
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-sm-1 labelNowrap" for="areaCode">地区</label>
                    <div class="col-sm-2">
                        <input type="text" class="form-control col-sm-10" id="areaCode" name="areaCode"
                               placeholder="请输入名称">
                    </div>
                    <label class="control-label col-sm-1 labelNowrap" for="systemName">平台名称</label>
                    <div class="col-sm-2">
                        <input type="text" class="form-control col-sm-10" id="systemName" name="systemName"
                               placeholder="请输入平台名称">
                    </div>

                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="resetSearch()"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="formSearch('appIdTable','/admin/access/list' ,'searchForm')">
                            <i class="fa fa-search"></i>查询
                        </button>
                        <!--                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"-->
                        <!--                                id="btn_query"-->
                        <!--                                onclick="handleSearch()">-->
                        <!--                            <i class="fa fa-search"></i>查询-->
                        <!--                        </button>-->
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div id="toolbar" class="btn-group">
        <button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="handleAdd()"><i class="fa fa-plus"></i>分配</button>
    </div>
    <table id="appIdTable"></table>
</div>

<script th:src="@{/resources/awi/plugins/layui/layui_layui.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/cascader.min.js}"></script>
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";

    $(function () {
        layui.use('layCascader', function () {
            // var id = 0;
            var layCascader = layui.layCascader;
             var areaLayCascader = layCascader({
                elem: '#areaCode',
                props: {
                    checkStrictly: true,
                    lazy: true,
                    lazyLoad: function (node, resolve) {
                        var level = node.level;
                        if (level == 1) {
                            // return
                        } else {
                            $.ajax({
                                url: '/admin/area/getCountyList',
                                type: 'GET', // 请求类型
                                data: {
                                    parentId: node.value || 410000
                                },
                                dataType: 'json',
                                success: function (response) {
                                    var nodes = response.map(function (item) {
                                        return {
                                            value: item.id,
                                            label: item.name,
                                            leaf: level >= 1
                                        };
                                    });
                                    resolve(nodes);
                                },
                                error: function (error) {
                                    console.log('加载子节点数据失败:', error);
                                    resolve([]);
                                }
                            });
                        }
                    }
                }
            });
            areaLayCascader.changeEvent(function (value, node) {
                areaLayCascader.close()
            })
        });
    })

    function handleAdd() {
        layer.open({
            type: 2,
            title: '新增',
            content: '/admin/access/toAdd',
            area: ['800px', '400px'],
            shadeClose: true, //点击遮罩关闭层
        })

    }

    function resetSearch() {
        $("#areaCode").val('')
        setTimeout(() => {
            $(".el-input__inner").val('')
        }, 100)
        restForm('searchForm')
        formSearch('appIdTable', '/admin/access/list', 'searchForm')
    }

    // function handleSearch() {
    //     formSearch('appIdTable', '/admin/access/list', 'searchForm')
    //
    // }

    function handleEdit(accessId) {
        layer.open({
            type: 2,
            title: '编辑',
            content: '/admin/access/edit/' + accessId,
            area: ['800px', '400px'],
            shadeClose: true, //点击遮罩关闭层
        })
    }

    function handleDelete(accessId) {
        layer.open({
            type: 0,
            title: '删除',
            content: '确认删除当前数据？',
            btn: ['确认', '取消'],
            yes: function (index, layero) {
                $.ajax({
                    url: '/admin/access/remove',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        ids: accessId
                    },
                    success: function (res) {
                        layer.msg(res.msg)
                        formSearch('appIdTable', '/admin/access/list', 'searchForm')
                    },
                    error:function () {
                    }
                })
            }
        })

    }

    $(function () {
        var columns = [
            {
                field: "area",
                title: "地区",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "systemName",
                title: "平台名称",
                align: 'center',
                valign: 'middle',
            },
            {
                field: "appId",
                title: "appId",
                align: 'center',
                valign: 'middle',
            },
            {
                field: 'accessId',
                title: '操作',
                align: 'center',
                valign: 'middle',
                cellStyle: {
                    css: {"white-space": "nowrap"}
                },
                formatter: function (value, row, index) {
                    var actions = [];
                    actions.push('<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="handleEdit(\'' + value + '\')" style="margin-right: 10px"><i class="fa fa-edit"></i>编辑</button>');
                    actions.push('<button id="btn_edit" type="button" class="btn btn-danger  btn-xs" onclick="handleDelete(\'' + value + '\')"><i class="fa fa-trash-o"></i>删除</button>');
                    return actions.join('');
                }
            }
        ];

        createBootstrapTable($("#appIdTable"), '/admin/access/list', columns, false, '#toolbar', 'server');

    })


</script>
</body>
</html>
