<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备使用登记表详情"></title>
    <th:block th:include="admin/include/head :: head"/>
    <style>
        .container-fluid {
            padding-top: 15px;
        }

        .box-footer {
            width: max-content;
            margin: 0 auto;
        }

        .userTable th, td {
            text-align: center;
        }

        .rejectButton {
            text-align: center;
            margin: 10px 0;
        }

        .title {
            font-size: 14px;
            font-weight: bold;
            padding: 10px;
            margin-bottom: 10px;
            margin-right: 12px;
            background: #e1f0f9;
            border-radius: 4px;
            position: relative;

        }

        .title::before {
            content: '';
            width: 4px;
            height: 100%;
            background: #3c8dbc;
            position: absolute;
            left: 0;
            top: 0;
        }

        table th, td {
            text-align: center;
            border: none !important;
        }

        .del {
            cursor: pointer;
            text-align: center;
            line-height: 34px;
        }

        .save {
            cursor: pointer;
            background-color: #5bc0de;
            color: #fff;
            text-align: center;
            line-height: 34px;
            padding: 8px 10px;
            border-radius: 4px;
        }

        .btn-info {
            width: 130px;
            height: 40px;
        }

        /*.print{*/
        /*    width: 160px;*/
        /*}*/
    </style>
</head>
<body>
<div id="filingEquipmentRegistrationFormDetail" class="container-fluid">
    <form id="filingEquipmentRegistrationForm-form-edit" class="form-horizontal"
          th:object="${filingEquipmentRegistrationForm}">
        <input type="hidden" id="userId" name="userId" autocomplete="off" th:value="*{userId}">
        <input type="hidden" id="id" name="id" th:value="*{id}">
        <div class="col-md-12" style="margin-top: 10px">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="applicationDate" class="col-sm-3 control-label require">申请日期</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control " name="applicationDate" id="applicationDate" th:value="*{#dates.format(applicationDate,'YYYY-MM-dd')}" disabled>
                        <input type="hidden" class="form-control " id="now" name="applicationDate" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">设备信息
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentType" class="col-sm-3 control-label require">设备类型</label>
                    <div class="col-sm-9">
                        <!--                        <input id="types" th:value="*{equipmentType}" type="hidden">-->
                        <select disabled class="form-control" id="equipmentType" name="equipmentType" autocomplete="off">
                            <option value="T">塔式起重机</option>
                            <option value="S">施工升降机(不含物料提升机)</option>
                            <option value="W">物料提升机</option>
                            <option value="Q">其他起重机械</option>
                        </select>
                    </div>
                </div>
            </div>
            <!--            <div class="col-md-6">-->
            <!--                <div class="form-group">-->
            <!--                    <label for="deviceName" class="col-sm-3 control-label require">设备名称</label>-->
            <!--                    <div class="col-sm-9">-->
            <!--                        <input disabled type="text" class="form-control" id="deviceName" name="deviceName" autocomplete="off"-->
            <!--                               th:value="*{deviceName}">-->
            <!--                    </div>-->
            <!--                </div>-->
            <!--            </div>-->
        </div>
        <div class="col-md-12 tashi">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="armLength" class="col-sm-3 control-label require">起重臂长度</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="armLength" name="armLength" placeholder="单位：米" th:value="*{armLength}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="maxWeight" class="col-sm-3 control-label require">最大起重量</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="maxWeight" name="maxWeight" placeholder="单位：吨" th:value="*{maxWeight}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 tashi">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="maxHeight" class="col-sm-3 control-label require">拟安装最大高度</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="maxHeight" name="maxHeight" placeholder="单位：米" th:value="*{maxHeight}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="maximumCapacity" class="col-sm-3 control-label require">最大幅度起重量</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="maximumCapacity" name="maximumCapacity" placeholder="单位：吨" th:value="*{maximumCapacity}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 shigong">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="sRegularWeight" class="col-sm-3 control-label require">额定载重量</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="sRegularWeight" name="sRegularWeight" placeholder="单位：千克" th:value="*{sRegularWeight}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="sRegularSpeed" class="col-sm-3 control-label require">额定速度</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="sRegularSpeed" name="sRegularSpeed" placeholder="单位：米/分钟" th:value="*{sRegularSpeed}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 wuliao">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="wRegularWeight" class="col-sm-3 control-label require">额定载重量</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="wRegularWeight" name="wRegularWeight" placeholder="单位：千克" th:value="*{wRegularWeight}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="wRegularSpeed" class="col-sm-3 control-label require">额定速度</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="wRegularSpeed" name="wRegularSpeed" placeholder="单位：米/分钟" th:value="*{wRegularSpeed}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="specificationModel" class="col-sm-3 control-label require">规格型号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="specificationModel" name="specificationModel"
                               th:value="*{specificationModel}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="factoryTime" class="col-sm-3 control-label require">出厂日期</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control datepicker" id="factoryTime" name="factoryTime"
                               autocomplete="off" th:value="*{factoryTime}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="factoryNumber" class="col-sm-3 control-label require">出厂编号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="factoryNumber" name="factoryNumber"
                               th:value="*{factoryNumber}"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="deviceFilingCode" class="col-sm-3 control-label require">设备备案编号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="deviceFilingCode" name="deviceFilingCode"
                               th:value="*{deviceFilingCode}"
                               autocomplete="off"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="testingDate" class="col-sm-3 control-label require">检测日期</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control datepicker" id="testingDate" name="testingDate"
                               th:value="*{#dates.format(testingDate,'YYYY-MM-dd')}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationAddress" class="col-sm-3 control-label require">安装位置</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="installationAddress" name="installationAddress"
                               th:value="*{installationAddress}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="firstInstallationHeight" class="col-sm-3 control-label">首次安装高度</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="firstInstallationHeight" name="firstInstallationHeight"
                               th:value="*{firstInstallationHeight}"
                               placeholder="单位：米">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="finalUseHeight" class="col-sm-3 control-label">最终使用高度</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="finalUseHeight" name="finalUseHeight"
                               th:value="*{finalUseHeight}"
                               placeholder="单位：米">
                    </div>
                </div>
            </div>
        </div>
        <!--        <div class="col-md-12">-->
        <!--            <div class="title">设备使用人员信息</div>-->
        <!--        </div>-->
        <!--        <div class="col-md-12">-->
        <!--            <table id="person" class="table">-->
        <!--                <tr>-->
        <!--                    <th>姓名</th>-->
        <!--                    <th>工种</th>-->
        <!--                    <th>上岗证号</th>-->
        <!--                    <th width="80">-->
        <!--                    </th>-->
        <!--                </tr>-->
        <!--                &lt;!&ndash;                <tr>&ndash;&gt;-->
        <!--                &lt;!&ndash;                    <td><input type="text" id="userName" class="form-control temporary" autocomplete="off"></td>&ndash;&gt;-->
        <!--                &lt;!&ndash;                    <td><input type="text" id="userType" class="form-control temporary" autocomplete="off"></td>&ndash;&gt;-->
        <!--                &lt;!&ndash;                    <td><input type="text" id="userCode" class="form-control temporary" autocomplete="off"></td>&ndash;&gt;-->
        <!--                &lt;!&ndash;                </tr>&ndash;&gt;-->
        <!--            </table>-->
        <!--        </div>-->
        <div class="col-md-12">
            <div class="title">产权单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="propertyUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="propertyUnit" name="propertyUnit" autocomplete="off"
                               th:value="*{propertyUnit}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="propertyUnitCode" class="col-sm-3 control-label require">单位社会统一信用代码</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="propertyUnitCode" name="propertyUnitCode" th:value="*{propertyUnitCode}"
                        >
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">使用单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="useUnit" name="useUnit" autocomplete="off"
                               th:value="*{useUnit}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useUnitCode" class="col-sm-3 control-label require">单位统一社会信用代码</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="useUnitCode" name="useUnitCode" th:value="*{useUnitCode}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="userContact" class="col-sm-3 control-label require">项目负责人</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="userContact" name="userContact" th:value="*{userContact}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="userContactCard" class="col-sm-3 control-label require">项目负责人身份证号码</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="userContactCard" name="userContactCard"
                               autocomplete="off"
                               th:value="*{userContactCard}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useProduceSafeManager" class="col-sm-3 control-label require">专职安全生产管理人员</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="useProduceSafeManager" name="useProduceSafeManager" th:value="*{useProduceSafeManager}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useProduceSafeManagerCard" class="col-sm-3 control-label require">专职安全生产管理人员身份证号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="useProduceSafeManagerCard" name="useProduceSafeManagerCard"
                               autocomplete="off"
                               th:value="*{useProduceSafeManagerCard}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useProduceDeviceManager" class="col-sm-3 control-label require">建筑起重机械管理人员</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="useProduceDeviceManager" name="useProduceDeviceManager" th:value="*{useProduceDeviceManager}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useProduceDeviceManagerCard" class="col-sm-3 control-label require">建筑起重机械管理人员身份证号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="useProduceDeviceManagerCard" name="useProduceDeviceManagerCard"
                               autocomplete="off"
                               th:value="*{useProduceDeviceManagerCard}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useSignalWorker" class="col-sm-3 control-label require">建筑起重司索信号工</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="useSignalWorker" name="useSignalWorker" th:value="*{useSignalWorker}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useSignalWorkerCard" class="col-sm-3 control-label require"> 建筑起重司索信号工身份证号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="useSignalWorkerCard" name="useSignalWorkerCard"
                               autocomplete="off"
                               th:value="*{useSignalWorkerCard}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useDeviceDriver" class="col-sm-3 control-label require">建筑起重机械司机</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="useDeviceDriver" name="useDeviceDriver" th:value="*{useDeviceDriver}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useDeviceDriverCard" class="col-sm-3 control-label require">建筑起重机械司机身份证号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="useDeviceDriverCard" name="useDeviceDriverCard"
                               autocomplete="off"
                               th:value="*{useDeviceDriverCard}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">制造单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="produceUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="produceUnit" name="produceUnit" th:value="*{produceUnit}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="produceUnitCode" class="col-sm-3 control-label require">单位统一社会信用代码</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="produceUnitCode" name="produceUnitCode" th:value="*{produceUnitCode}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">维保单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="upkeepUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="upkeepUnit" name="upkeepUnit" th:value="*{upkeepUnit}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="upkeepUnitCode" class="col-sm-3 control-label require">单位统一社会信用代码</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="upkeepUnitCode" name="upkeepUnitCode" th:value="*{upkeepUnitCode}">
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-12">
            <div class="title">安装单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="installationUnit" name="installationUnit"
                               th:value="*{installationUnit}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationUnitCode" class="col-sm-3 control-label require">单位统一社会信用代码</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="installationUnitCode" name="installationUnitCode"
                               th:value="*{installationUnitCode}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationUnitQualificationCertificateNumber"
                           class="col-sm-3 control-label">单位资质证号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="installationUnitQualificationCertificateNumber"
                               th:value="*{installationUnitQualificationCertificateNumber}"
                               name="installationUnitQualificationCertificateNumber" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationUnitLicenseSafetyPermitNumber" class="col-sm-3 control-label">单位安全许可证号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="installationUnitLicenseSafetyPermitNumber"
                               th:value="*{installationUnitLicenseSafetyPermitNumber}"
                               name="installationUnitLicenseSafetyPermitNumber" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="siteInstallationLeader" class="col-sm-3 control-label">现场安装负责人</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="siteInstallationLeader" name="siteInstallationLeader"
                               th:value="*{siteInstallationLeader}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="siteInstallationLeaderCard" class="col-sm-3 control-label">现场安装负责人身份证号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="siteInstallationLeaderCard" name="siteInstallationLeaderCard"
                               th:value="*{siteInstallationLeaderCard}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">检测单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="testingUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="testingUnit" name="testingUnit" autocomplete="off"
                               th:value="*{testingUnit}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="testingUnitCode" class="col-sm-3 control-label require">单位统一社会信用代码</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="testingUnitCode" name="testingUnitCode" th:value="*{testingUnitCode}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="testingLeadere" class="col-sm-3 control-label">检测负责人</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="testingLeadere" name="testingLeadere"
                               th:value="*{testingLeadere}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="testingLeadereCard" class="col-sm-3 control-label ">检测负责人身份证号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="testingLeadereCard" name="testingLeadereCard"
                               th:value="*{testingLeadereCard}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="testingContent" class="col-sm-3 control-label ">检测内容</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="testingContent" name="testingContent"
                               autocomplete="off" th:value="*{testingContent}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">工程信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="engineeringName" class="col-sm-3 control-label require">工程名称</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="engineeringName" name="engineeringName"
                               autocomplete="off" th:value="*{engineeringName}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="engineeringAreaCode" class="col-sm-3 control-label require">工程项目属地</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="engineeringAreaCode" name="engineeringAreaCode" autocomplete="off" th:value="*{remarks}">
                        <!--                        <input type="hidden" id="remarks" name="remarks" th:value="*{remarks}">-->
                        <!--                        <select class="form-control" id="engineeringAreaCode" name="engineeringAreaCode" autocomplete="off">-->
                        <!--                        </select>-->
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="projectManager" class="col-sm-3 control-label">工程所在地项目项目经理</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="projectManager" name="projectManager"
                               th:value="*{projectManager}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="engineeringAddressDetail" class="col-sm-3 control-label require">工程项目详细地址</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="engineeringAddressDetail" name="engineeringAddressDetail"
                               th:value="*{engineeringAddressDetail}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="isBuildLicense" class="col-sm-3 control-label require">是否办理施工许可证</label>
                    <div class="col-sm-9">
                        <!--                        <input type="hidden" id="isBuildLicense" name="isBuildLicense">-->
                        <select disabled class="form-control" autocomplete="off" id="isBuildLicense" name="isBuildLicense">
                            <option value="1">是</option>
                            <option value="0">否</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="buildLicenseCode" class="col-sm-3 control-label require">建筑工程施工许可证编号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="buildLicenseCode" name="buildLicenseCode" th:value="*{buildLicenseCode}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationStartTime" class="col-sm-3 control-label">安装开始日期</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control datepicker" id="installationStartTime"
                               th:value="*{#dates.format(installationStartTime,'YYYY-MM-dd')}"
                               name="installationStartTime"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationEndTime" class="col-sm-3 control-label require">安装结束日期</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control datepicker" id="installationEndTime"
                               th:value="*{#dates.format(installationEndTime,'YYYY-MM-dd')}"
                               name="installationEndTime"
                               autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">审核信息
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="col-sm-3 control-label require">审核状态</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control"
                               name="auditStatus" th:if="*{auditStatus=='0'}" value="未审核"/>
                        <input disabled type="text" class="form-control"
                               name="auditStatus" th:if="*{auditStatus=='1'}" value="审核通过"/>
                        <input disabled type="text" class="form-control"
                               name="auditStatus" th:if="*{auditStatus=='2'}" value="驳回"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">附件信息</div>
        </div>
        <!--            <div class="col-md-12">-->
        <table class="table table-striped table-bordered" th:object="${filingEquipmentRegistrationForm}">
            <tbody>
            <tr>
                <th>使用备案申请表路径</th>
                <td id="filingApplicationFormPath"></td>
                <th>起重机备案证文件路径</th>
                <td id="equipmentRecordCertificate"></td>

            </tr>
            <tr>
                <th>设备租赁合同文件路径</th>
                <td id="equipmentLeasingContract"></td>
                <th>定期检验及安装检验检测报告文件路径</th>
                <td id="inspectionReportPath"></td>

            </tr>
            <tr>
                <th>安装验收报告文件路径</th>
                <td id="installationAcceptanceReport"></td>
                <th>现场特种作业人员资格证书文件路径</th>
                <td id="operatorsQualificationCertificate"></td>

            </tr>
            <tr>
                <th>设备保养制度文件路径</th>
                <td id="equipmentMaintenanceManagementSystemPath"></td>
                <th>安全事故应急预案文件路径</th>
                <td id="safetyAccidentsEmergencyPlanPath"></td>
            </tr>
            </tbody>
        </table>
        <!--            </div>-->
    </form>

    <div id="examine">
    </div>
    <div id="reject" hidden>
        <label for="rejectReason">请输入退回原因</label><br>
        <textarea rows="4" id="rejectReason" name="rejectReason" wrap="hard" style="width: 100%"></textarea>
        <div class="rejectButton">
            <button type="button" class="btn btn-info" onclick="saveReject()">提交</button>
            <button type="button" class="btn btn-info" onclick="reback()">返回</button>

        </div>
    </div>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>

<script th:inline="javascript">
    let examiner = [[${@permission.hasPermi('use:examine')}]];
    let id = [[${filingEquipmentRegistrationForm}]].id;
    let index = parent.layer.getFrameIndex(window.name);
    let files = [[${files}]];
    let users = [[${users}]];
    // users = [{name: '000',workerType:'001',appointmentCertificateCode:'003'}]
    let status = [[${filingEquipmentRegistrationForm.auditStatus}]];
    let filingcode = [[${filingEquipmentRegistrationForm.useFilingCode}]];
    equipmentTypeCharge()

    $("#isBuildLicense").val([[${filingEquipmentRegistrationForm}]].isBuildLicense)

    // $(function () {
    //     let html = "<table class='userTable table'><thead><tr><th>姓名</th><th>工种</th><th>上岗证号</th></tr></thead>";
    //     for (let i = 0; i < users.length; i++) {
    //         let user = users[i];
    //         console.log('user', user)
    //         html += "<tr><td>" + user.name + "</td><td>" + user.workerType + "</td><td>" + user.appointmentCertificateCode + "</td></tr>";
    //     }
    //     html += "</tabl>";
    //     $("#user").html(html);
    // })

    // $(function () {
    //     for (var i = 0; i < users.length; i++) {
    //         var user = users[i];
    //         toAddList(user.name, user.workerType, user.appointmentCertificateCode, user.id);
    //     }
    // })
    //
    // function toAddList(name, type, code, id) {
    //     var trLine = '<tr>\n' +
    //         '                <td>' + name + '</td><td>' + type + '</td><td>' + code + '</td><td></td></tr>';
    //     $("#person").append(trLine);
    // }

    function equipmentTypeCharge() {
        $("#equipmentType").val([[${filingEquipmentRegistrationForm}]].equipmentType)
        const equipmentTypeValue = $("#equipmentType").val()
        if (equipmentTypeValue == "T") {
            $(".tashi").show()
            $(".shigong").hide()
            $(".wuliao").hide()
        } else if (equipmentTypeValue == "S") {
            $(".tashi").hide()
            $(".shigong").show()
            $(".wuliao").hide()
        } else if (equipmentTypeValue == "W") {
            $(".tashi").hide()
            $(".shigong").hide()
            $(".wuliao").show()
        } else if (equipmentTypeValue == "Q") {
            $(".tashi").hide()
            $(".shigong").hide()
            $(".wuliao").hide()
        } else {
            $(".tashi").hide()
            $(".shigong").hide()
            $(".wuliao").hide()
        }
    }

    $(function () {
        var filingApplicationFormPath = "";
        var equipmentRecordCertificatePath = "";
        var equipmentLeasingContractPath = "";
        var inspectionReportPath = "";
        var installationAcceptanceReportPath = "";
        var operatorsQualificationCertificatePath = "";
        var equipmentMaintenanceManagementSystemPath = "";
        var safetyAccidentsEmergencyPlanPath = "";
        for (let i = 0; i < files.length; i++) {
            let file = files[i];
            if (file.connectId.indexOf("filingApplicationFormPath") > 0) {
                filingApplicationFormPath += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("equipmentRecordCertificate") > 0) {
                equipmentRecordCertificatePath += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("equipmentLeasingContract") > 0) {
                equipmentLeasingContractPath += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("inspectionReport") > 0) {
                inspectionReportPath += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("installationAcceptanceReport") > 0) {
                installationAcceptanceReportPath += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("operatorsQualificationCertificate") > 0) {
                operatorsQualificationCertificatePath += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("equipmentMaintenanceManagementSystemPath") > 0) {
                equipmentMaintenanceManagementSystemPath += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("safetyAccidentsEmergencyPlanPath") > 0) {
                safetyAccidentsEmergencyPlanPath += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
        }
        $("#filingApplicationFormPath").html(filingApplicationFormPath);
        $("#equipmentRecordCertificate").html(equipmentRecordCertificatePath);
        $("#equipmentLeasingContract").html(equipmentLeasingContractPath);
        $("#inspectionReportPath").html(inspectionReportPath);
        $("#installationAcceptanceReport").html(installationAcceptanceReportPath);
        $("#operatorsQualificationCertificate").html(operatorsQualificationCertificatePath);
        $("#equipmentMaintenanceManagementSystemPath").html(equipmentMaintenanceManagementSystemPath);
        $("#safetyAccidentsEmergencyPlanPath").html(safetyAccidentsEmergencyPlanPath);
    })

    function popsaveclose() {
        parent.location.reload();
        parent.layer.close(index);
    }

    function reject() {
        $("#examine").hide();
        $("#reject").show();
    }

    function reback() {
        $("#examine").show();
        $("#reject").hide();
    }

    $(function () {
        if ("hidden" !== examiner && '0' == status) {
            var html = "";
            html += "<div>" +
                "        <div class=\"box-footer\">" +
                "            <button type=\"button\" class=\"btn btn-info\" onclick=\"examine()\">通过</button>" +
                "            <button type=\"button\" class=\"btn btn-info\" onclick=\"reject()\">驳回</button>" +
                "            <button type=\"button\" class=\"btn btn-info \" onclick=\"popsaveclose()\">关闭</button>" +
                "            </div>" +
                "         </div>"
            $('#examine').html(html);
        }
        if ('1' == status) {
            var html = "";
            html += "<div>" +
                "        <div class=\"box-footer\">" +
                "            <button type=\"button\" class=\"btn btn-info \"><a target='_parent' href=\"/admin/filingEquipmentUseRegistration/card?code=" + filingcode + "\">打印登记证</a></button>" +
                "            <button type=\"button\" class=\"btn btn-info \" onclick=\"popsaveclose()\">关闭</button>" +
                "            </div>" +
                "         </div>"
            $('#examine').html(html);
        }
    });


    function saveReject() {
        let rejectReason = $("#rejectReason").val();
        $.ajax({
            url: "/admin/filingEquipmentRegistrationForm/reject",
            data: {id: id, rejectReason: rejectReason},
            type: "POST",
            dataType: "JSON",
            success: function (res) {
                if (res.code == 0) {
                    layer.msg(res.msg, {time: 1000});
                    setTimeout(() => {
                        popsaveclose();
                    }, 1000)
                } else {
                    layer.msg(res.msg);
                }

            }
        })
    }

    function examine() {
        layer.open({
            type: 0,
            title: '审核通过',
            content: '确认通过当前申请？',
            btn: ['确认', '取消'],
            yes: function (index, layero) {
                var loadingIndex = layer.load(1, {
                    shade: [0.3, '#000'],
                    content: '数据校验中...',
                    success: function (layero) {
                        $('.layui-layer-loading').css({
                            'padding': '10px 20px',
                            'background': '#fff',
                            'border-radius':'4px'
                        });
                        layero.find('.layui-layer-content').css({
                            'padding-left': '40px',
                            'width': '138px',
                            'display':'flex',
                            'align-items':'center',
                            'font-size':'15px',

                        });
                    }

                });
                $.ajax({
                    url: '/admin/filingEquipmentRegistrationForm/examine',
                    method: 'post',
                    dataType: "JSON",
                    data: {
                        id: id
                    },
                    success: function (res) {
                        if (res.code == 0) {
                            layer.msg(res.msg, {time: 1000}, function () {
                                popsaveclose();

                            });
                        } else {
                            layer.msg(res.msg,{time: 3000});
                        }
                    },
                    complete: function (res) {
                        layer.close(loadingIndex);
                    }
                })
            }
        })
        // $.ajax({
        //     url: "/admin/filingEquipmentRegistrationForm/examine",
        //     data: {id: id},
        //     type: "POST",
        //     dataType: "JSON",
        //     success: function (res) {
        //         if (res.code == 0) {
        //             layer.msg(res.msg, {time: 1000});
        //             popsaveclose();
        //         } else {
        //             layer.msg(res.msg);
        //         }
        //     }
        // })
    }

</script>
</body>
</html>
