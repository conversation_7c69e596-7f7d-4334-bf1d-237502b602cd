<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备使用登记表编辑"></title>
    <th:block th:include="admin/include/head :: head"/>
    <link rel="stylesheet" th:href="@{/resources/file-input/css/fileinput.min.css}">
    <style>
        .title {
            font-size: 14px;
            font-weight: bold;
            padding: 10px;
            margin-bottom: 10px;
            margin-right: 12px;
            background: #e1f0f9;
            border-radius: 4px;
            position: relative;

        }

        .title::before {
            content: '';
            width: 4px;
            height: 100%;
            background: #3c8dbc;
            position: absolute;
            left: 0;
            top: 0;
        }

        table th, td {
            text-align: center;
            border: none !important;
        }

        .del {
            cursor: pointer;
            text-align: center;
            line-height: 34px;
        }

        .save {
            cursor: pointer;
            background-color: #5bc0de;
            color: #fff;
            text-align: center;
            line-height: 34px;
            padding: 8px 10px;
            border-radius: 4px;
        }
        .btn-info {
            width: 130px;
            height: 40px;
        }
    </style>
</head>
<body>
<div id="filingEquipmentRegistrationFormform" class="container-fluid">

    <form id="filingEquipmentRegistrationForm-form-edit" class="form-horizontal"
          th:object="${filingEquipmentRegistrationForm}">
        <input type="hidden" id="userId" name="userId" autocomplete="off" th:value="*{userId}">
        <input type="hidden" id="id" name="id" th:value="*{id}">
        <div class="col-md-12" style="margin-top: 10px">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="applicationDate" class="col-sm-3 control-label require">申请日期</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control " name="applicationDate" id="applicationDate" th:value="*{applicationDate}" disabled>
                        <input type="hidden" class="form-control " id="now" name="applicationDate" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">设备信息 <span style="color: red;font-size: 14px;font-weight: bold"> 注：请选择正确的设备类型，否则会影响您的审核或生成的备案编号</span>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentType" class="col-sm-3 control-label require">设备类型</label>
                    <div class="col-sm-9">
                        <!--                        <input id="types" th:value="*{equipmentType}" type="hidden">-->
                        <select class="form-control" id="equipmentType" name="equipmentType" autocomplete="off">
                            <option value="T">塔式起重机</option>
                            <option value="S">施工升降机(不含物料提升机)</option>
                            <option value="W">物料提升机</option>
                            <option value="Q">其他起重机械</option>
                        </select>
                    </div>
                </div>
            </div>
            <!--            <div class="col-md-6">-->
            <!--                <div class="form-group">-->
            <!--                    <label for="deviceName" class="col-sm-3 control-label require">设备名称</label>-->
            <!--                    <div class="col-sm-9">-->
            <!--                        <input type="text" class="form-control" id="deviceName" name="deviceName" autocomplete="off"-->
            <!--                               th:value="*{deviceName}">-->
            <!--                    </div>-->
            <!--                </div>-->
            <!--            </div>-->
        </div>
        <div class="col-md-12 tashi">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="armLength" class="col-sm-3 control-label require">起重臂长度（米）</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="armLength" name="armLength" placeholder="单位：米" th:value="*{armLength}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="maxWeight" class="col-sm-3 control-label require">最大起重量（吨）</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="maxWeight" name="maxWeight" placeholder="单位：吨" th:value="*{maxWeight}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 tashi">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="maxHeight" class="col-sm-3 control-label require">拟安装最大高度（米）</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="maxHeight" name="maxHeight" placeholder="单位：米" th:value="*{maxHeight}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="maximumCapacity" class="col-sm-3 control-label require">最大幅度起重量（吨）</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="maximumCapacity" name="maximumCapacity" placeholder="单位：吨" th:value="*{maximumCapacity}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 shigong">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="sRegularWeight" class="col-sm-3 control-label require">额定载重量（千克）</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="sRegularWeight" name="sRegularWeight" placeholder="单位：千克" th:value="*{sRegularWeight}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="sRegularSpeed" class="col-sm-3 control-label require">额定速度（米/分钟）</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="sRegularSpeed" name="sRegularSpeed" placeholder="单位：米/分钟" th:value="*{sRegularSpeed}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 wuliao">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="wRegularWeight" class="col-sm-3 control-label require">额定载重量（千克）</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="wRegularWeight" name="wRegularWeight" placeholder="单位：千克" th:value="*{wRegularWeight}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="wRegularSpeed" class="col-sm-3 control-label require">额定速度（米/分钟）</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="wRegularSpeed" name="wRegularSpeed" placeholder="单位：米/分钟" th:value="*{wRegularSpeed}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="specificationModel" class="col-sm-3 control-label require">规格型号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="specificationModel" name="specificationModel"
                               th:value="*{specificationModel}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="factoryTime" class="col-sm-3 control-label require">出厂日期</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control datepicker" id="factoryTime" name="factoryTime"
                               autocomplete="off" th:value="*{factoryTime}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="factoryNumber" class="col-sm-3 control-label require">出厂编号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="factoryNumber" name="factoryNumber"
                               th:value="*{factoryNumber}"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="deviceFilingCode" class="col-sm-3 control-label require">设备备案编号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="deviceFilingCode" name="deviceFilingCode"
                               th:value="*{deviceFilingCode}"
                               autocomplete="off"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="testingDate" class="col-sm-3 control-label require">检测日期</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control datepicker" id="testingDate" name="testingDate"
                               th:value="*{#dates.format(testingDate,'YYYY-MM-dd')}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationAddress" class="col-sm-3 control-label require">安装位置</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="installationAddress" name="installationAddress"
                               th:value="*{installationAddress}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="firstInstallationHeight" class="col-sm-3 control-label">首次安装高度（米）</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="firstInstallationHeight" name="firstInstallationHeight"
                               th:value="*{firstInstallationHeight}"
                               placeholder="单位：米">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="finalUseHeight" class="col-sm-3 control-label">最终使用高度（米）</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="finalUseHeight" name="finalUseHeight"
                               th:value="*{finalUseHeight}"
                               placeholder="单位：米">
                    </div>
                </div>
            </div>
        </div>
        <!--        <div class="col-md-12">-->
        <!--            <div class="title">设备使用人员信息<span style="color: red;font-size: 14px;font-weight: bold"> 注：输入使用人员信息后，请点击“保存”上传人员信息</span></div>-->
        <!--        </div>-->
        <!--        <div class="col-md-12">-->
        <!--            <table id="person" class="table">-->
        <!--                <tr>-->
        <!--                    <th>姓名</th>-->
        <!--                    <th>工种</th>-->
        <!--                    <th>上岗证号</th>-->
        <!--                    <th width="80">-->
        <!--                    </th>-->
        <!--                </tr>-->
        <!--                <tr>-->
        <!--                    <td><input type="text" id="userName" class="form-control temporary" autocomplete="off"></td>-->
        <!--&lt;!&ndash;                    <td>&ndash;&gt;-->
        <!--&lt;!&ndash;                        <select class="form-control" id="userType" name="userType" autocomplete="off">&ndash;&gt;-->
        <!--&lt;!&ndash;                            <option value="">请选择</option>&ndash;&gt;-->
        <!--&lt;!&ndash;                            <option value="1106">使用单位专职安全生产管理员</option>&ndash;&gt;-->
        <!--&lt;!&ndash;                            <option value="1107">使用单位建筑起重机械管理人员</option>&ndash;&gt;-->
        <!--&lt;!&ndash;                            <option value="1108">建筑起重司索信号工</option>&ndash;&gt;-->
        <!--&lt;!&ndash;                            <option value="1109">建筑起重机械司机</option>&ndash;&gt;-->
        <!--&lt;!&ndash;                        </select>&ndash;&gt;-->
        <!--&lt;!&ndash;                    </td>&ndash;&gt;-->
        <!--                    <td><input type="text" id="userType" class="form-control temporary" autocomplete="off"></td>-->
        <!--                    <td><input type="text" id="userCode" class="form-control temporary" autocomplete="off"></td>-->
        <!--                    <td><span class="save" onclick="saveUser(this)">保存</span></td>-->
        <!--                </tr>-->
        <!--            </table>-->
        <!--        </div>-->
        <div class="col-md-12">
            <div class="title">产权单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="propertyUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="propertyUnit" name="propertyUnit" autocomplete="off"
                               th:value="*{propertyUnit}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="propertyUnitCode" class="col-sm-3 control-label require">单位社会统一信用代码</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="propertyUnitCode" name="propertyUnitCode" th:value="*{propertyUnitCode}"
                        >
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">使用单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="useUnit" name="useUnit" autocomplete="off"
                               th:value="*{useUnit}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useUnitCode" class="col-sm-3 control-label require">单位统一社会信用代码</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="useUnitCode" name="useUnitCode" th:value="*{useUnitCode}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="userContact" class="col-sm-3 control-label require">项目负责人</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="userContact" name="userContact" th:value="*{userContact}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="userContactCard" class="col-sm-3 control-label require">项目负责人身份证号码</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="userContactCard" name="userContactCard"
                               autocomplete="off"
                               th:value="*{userContactCard}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useProduceSafeManager" class="col-sm-3 control-label require">专职安全生产管理人员</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="useProduceSafeManager" name="useProduceSafeManager" th:value="*{useProduceSafeManager}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useProduceSafeManagerCard" class="col-sm-3 control-label require">专职安全生产管理人员身份证号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="useProduceSafeManagerCard" name="useProduceSafeManagerCard"
                               autocomplete="off"
                               th:value="*{useProduceSafeManagerCard}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useProduceDeviceManager" class="col-sm-3 control-label require">建筑起重机械管理人员</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="useProduceDeviceManager" name="useProduceDeviceManager" th:value="*{useProduceDeviceManager}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useProduceDeviceManagerCard" class="col-sm-3 control-label require">建筑起重机械管理人员身份证号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="useProduceDeviceManagerCard" name="useProduceDeviceManagerCard"
                               autocomplete="off"
                               th:value="*{useProduceDeviceManagerCard}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useSignalWorker" class="col-sm-3 control-label require">建筑起重司索信号工</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="useSignalWorker" name="useSignalWorker" th:value="*{useSignalWorker}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useSignalWorkerCard" class="col-sm-3 control-label require"> 建筑起重司索信号工身份证号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="useSignalWorkerCard" name="useSignalWorkerCard"
                               autocomplete="off"
                               th:value="*{useSignalWorkerCard}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useDeviceDriver" class="col-sm-3 control-label require">建筑起重机械司机</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="useDeviceDriver" name="useDeviceDriver" th:value="*{useDeviceDriver}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="useDeviceDriverCard" class="col-sm-3 control-label require">建筑起重机械司机身份证号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="useDeviceDriverCard" name="useDeviceDriverCard"
                               autocomplete="off"
                               th:value="*{useDeviceDriverCard}">
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-12">
            <div class="title">制造单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="produceUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="produceUnit" name="produceUnit" th:value="*{produceUnit}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="produceUnitCode" class="col-sm-3 control-label require">单位统一社会信用代码</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="produceUnitCode" name="produceUnitCode" th:value="*{produceUnitCode}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">维保单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="upkeepUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="upkeepUnit" name="upkeepUnit" th:value="*{upkeepUnit}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="upkeepUnitCode" class="col-sm-3 control-label require">单位统一社会信用代码</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="upkeepUnitCode" name="upkeepUnitCode" th:value="*{upkeepUnitCode}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">安装单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="installationUnit" name="installationUnit"
                               th:value="*{installationUnit}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationUnitCode" class="col-sm-3 control-label require">统一社会信用代码</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="installationUnitCode" name="installationUnitCode"
                               th:value="*{installationUnitCode}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationUnitQualificationCertificateNumber"
                           class="col-sm-3 control-label">单位资质证号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="installationUnitQualificationCertificateNumber"
                               th:value="*{installationUnitQualificationCertificateNumber}"
                               name="installationUnitQualificationCertificateNumber" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationUnitLicenseSafetyPermitNumber" class="col-sm-3 control-label">安装单位安全许可证号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="installationUnitLicenseSafetyPermitNumber"
                               th:value="*{installationUnitLicenseSafetyPermitNumber}"
                               name="installationUnitLicenseSafetyPermitNumber" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="siteInstallationLeader" class="col-sm-3 control-label require">主要负责人</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="siteInstallationLeader" name="siteInstallationLeader"
                               th:value="*{siteInstallationLeader}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="siteInstallationLeaderCard" class="col-sm-3 control-label require">主要负责人身份证号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="siteInstallationLeaderCard" name="siteInstallationLeaderCard"
                               th:value="*{siteInstallationLeaderCard}">
                    </div>
                </div>
            </div>
        </div>


        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationTechnician" class="col-sm-3 control-label require">技术负责人</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="installationTechnician" name="installationTechnician"
                               th:value="*{installationTechnician}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationTechnicianCard" class="col-sm-3 control-label require">技术负责人身份证号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="installationTechnicianCard" name="installationTechnicianCard"
                               th:value="*{installationTechnicianCard}">
                    </div>
                </div>
            </div>
        </div><div class="col-md-12">
        <div class="col-md-6">
            <div class="form-group">
                <label for="installationManager" class="col-sm-3 control-label require">现场管理人员</label>
                <div class="col-sm-9">
                    <input type="text" class="form-control" id="installationManager" name="installationManager"
                           th:value="*{installationManager}"
                           autocomplete="off">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="installationManagerCard" class="col-sm-3 control-label require">现场管理人员身份证号</label>
                <div class="col-sm-9">
                    <input type="text" class="form-control" id="installationManagerCard" name="installationManagerCard"
                           th:value="*{installationManagerCard}">
                </div>
            </div>
        </div>
    </div>
        <div class="col-md-12">
            <div class="title">检测单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="testingUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="testingUnit" name="testingUnit" autocomplete="off"
                               th:value="*{testingUnit}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="testingUnitCode" class="col-sm-3 control-label require">单位统一社会信用代码</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="testingUnitCode" name="testingUnitCode" th:value="*{testingUnitCode}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="testingLeadere" class="col-sm-3 control-label">检测负责人</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="testingLeadere" name="testingLeadere"
                               th:value="*{testingLeadere}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="testingLeadereCard" class="col-sm-3 control-label ">检测负责人身份证号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="testingLeadereCard" name="testingLeadereCard"
                               th:value="*{testingLeadereCard}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="testingContent" class="col-sm-3 control-label ">检测内容</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="testingContent" name="testingContent"
                               autocomplete="off" th:value="*{testingContent}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">工程信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="engineeringName" class="col-sm-3 control-label require">工程名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="engineeringName" name="engineeringName"
                               autocomplete="off" th:value="*{engineeringName}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="engineeringAreaCode" class="col-sm-3 control-label require">工程项目属地</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="engineeringAreaCode" name="engineeringAreaCode" autocomplete="off" th:value="*{engineeringAreaCode}">
                        <input type="hidden" id="remarks" name="remarks" th:value="*{remarks}">
                        <!--                        <select class="form-control" id="engineeringAreaCode" name="engineeringAreaCode" autocomplete="off">-->
                        <!--                        </select>-->
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="projectManager" class="col-sm-3 control-label">工程所在地项目项目经理</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="projectManager" name="projectManager"
                               th:value="*{projectManager}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="engineeringAddressDetail" class="col-sm-3 control-label require">工程项目详细地址</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="engineeringAddressDetail" name="engineeringAddressDetail"
                               th:value="*{engineeringAddressDetail}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="isBuildLicense" class="col-sm-3 control-label require">是否办理施工许可证</label>
                    <div class="col-sm-9">
                        <!--                        <input type="hidden" id="isBuildLicense" name="isBuildLicense">-->
                        <select class="form-control" autocomplete="off" id="isBuildLicense" name="isBuildLicense">
                            <option value="1">是</option>
                            <option value="0">否</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="buildLicenseCode" class="col-sm-3 control-label require">建筑工程施工许可证编号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="buildLicenseCode" name="buildLicenseCode" th:value="*{buildLicenseCode}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationStartTime" class="col-sm-3 control-label">安装开始日期</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control datepicker" id="installationStartTime"
                               th:value="*{#dates.format(installationStartTime,'YYYY-MM-dd')}"
                               name="installationStartTime"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationEndTime" class="col-sm-3 control-label require">安装结束日期</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control datepicker" id="installationEndTime"
                               th:value="*{#dates.format(installationEndTime,'YYYY-MM-dd')}"
                               name="installationEndTime"
                               autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">附件信息 <span style="color: red;font-size: 14px;font-weight: bold"> 注：每个文件大小需小于3MB，选择文件后请点击上传完成文件上传操作</span>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="filingApplicationFormPath" class="col-sm-3 control-label require">使用备案申请表</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="filingApplicationFormPath"
                               id="filingApplicationForm" name="uploadFile">
                        <input type="hidden" class="form-control" id="filingApplicationFormPath" name="filingApplicationFormPath" autocomplete="off">
                        <table id="filingApplicationFormPath-table" class="table"></table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentRecordCertificatePath" class="col-sm-3 control-label require">起重机备案证文件</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="equipmentRecordCertificatePath"
                               name="uploadFile">
                        <input type="hidden" class="form-control" id="equipmentRecordCertificatePath" name="equipmentRecordCertificatePath" autocomplete="off">
                        <table id="equipmentRecordCertificatePath-table" class="table"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentLeasingContractPath" class="col-sm-3 control-label require">设备租赁合同文件</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="equipmentLeasingContractPath"
                               name="uploadFile">
                        <input type="hidden" class="form-control" id="equipmentLeasingContractPath" name="equipmentLeasingContractPath" autocomplete="off">
                        <table id="equipmentLeasingContractPath-table" class="table"></table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="inspectionReportPath" class="col-sm-3 control-label require">定期检验及安装检验检测报告文件</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="inspectionReportPath"
                               name="uploadFile">
                        <input type="hidden" class="form-control" id="inspectionReportPath" name="inspectionReportPath" autocomplete="off">
                        <table id="inspectionReportPath-table" class="table"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationAcceptanceReportPath" class="col-sm-3 control-label require">安装验收报告文件</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="installationAcceptanceReportPath"
                               name="uploadFile">
                        <input type="hidden" class="form-control" id="installationAcceptanceReportPath" name="installationAcceptanceReportPath" autocomplete="off">
                        <table id="installationAcceptanceReportPath-table" class="table"></table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">

                    <label for="operatorsQualificationCertificatePath"
                           class="col-sm-3 control-label require">现场特种作业人员资格证书文件</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="operatorsQualificationCertificatePath"
                               name="uploadFile">
                        <input type="hidden" class="form-control" id="operatorsQualificationCertificatePath" name="operatorsQualificationCertificatePath" autocomplete="off">
                        <table id="operatorsQualificationCertificatePath-table" class="table"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentMaintenanceManagementSystemPath" class="col-sm-3 control-label require">设备保养制度文件</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput"
                               data-type="equipmentMaintenanceManagementSystemPath"
                               name="uploadFile">
                        <input type="hidden" class="form-control" id="equipmentMaintenanceManagementSystemPath" name="equipmentMaintenanceManagementSystemPath" autocomplete="off">
                        <table id="equipmentMaintenanceManagementSystemPath-table" class="table"></table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="safetyAccidentsEmergencyPlanPath" class="col-sm-3 control-label require">安全事故应急预案文件</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="safetyAccidentsEmergencyPlanPath"
                               name="uploadFile">
                        <input type="hidden" class="form-control" id="safetyAccidentsEmergencyPlanPath" name="safetyAccidentsEmergencyPlanPath" autocomplete="off">
                        <table id="safetyAccidentsEmergencyPlanPath-table" class="table"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group" style="text-align: center;">
            <button type="button" class="btn btn-info " onclick="formSave()">提交</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/file-input/js/fileinput.min.js}"></script>
<script th:src="@{/resources/file-input/js/locales/zh.js}"></script>
<script th:src="@{/resources/awi/plugins/bootstrapvalidator/js/bootstrapValidator.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/layui_layui.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/cascader.min.js}"></script>


<script type="text/javascript" th:inline="javascript">
    "use strict";
    var id = [[${id}]];
    var users = [[${users}]];
    $("#id").val(id);
    var dict = [[${dict}]];
    let files = [[${files}]]
    const filingEquipmentRegistrationFormValue = [[${filingEquipmentRegistrationForm}]]
    // console.log('filingEquipmentRegistrationForm', [[${filingEquipmentRegistrationForm}]])
    // console.log('users', users)
    // console.log('files', files)
    $(document).on('change', '#isBuildLicense', function () {
        $("#filingEquipmentRegistrationForm-form-edit").data("bootstrapValidator").resetField('buildLicenseCode');
        $("#filingEquipmentRegistrationForm-form-edit").data("bootstrapValidator").validateField('buildLicenseCode');
    })
    $(document).on('change', '#installationStartTime', function () {
        $("#filingEquipmentRegistrationForm-form-edit").data("bootstrapValidator").resetField('installationEndTime');
        $("#filingEquipmentRegistrationForm-form-edit").data("bootstrapValidator").validateField('installationEndTime');

    })
    $(document).on('change', '#installationEndTime', function () {
        $("#filingEquipmentRegistrationForm-form-edit").data("bootstrapValidator").resetField('installationStartTime');
        $("#filingEquipmentRegistrationForm-form-edit").data("bootstrapValidator").validateField('installationStartTime');

    })

    function equipmentTypeCharge() {
        const equipmentTypeValue = $("#equipmentType").val()
        if (equipmentTypeValue == "T") {
            $(".tashi").show()
            $(".shigong").hide()
            $(".wuliao").hide()
        } else if (equipmentTypeValue == "S") {
            $(".tashi").hide()
            $(".shigong").show()
            $(".wuliao").hide()
        } else if (equipmentTypeValue == "W") {
            $(".tashi").hide()
            $(".shigong").hide()
            $(".wuliao").show()
        } else if (equipmentTypeValue == "Q") {
            $(".tashi").hide()
            $(".shigong").hide()
            $(".wuliao").hide()
        } else {
            $(".tashi").hide()
            $(".shigong").hide()
            $(".wuliao").hide()
        }
        $("#filingEquipmentRegistrationForm-form-edit").data("bootstrapValidator").resetField('useSignalWorker');
        $("#filingEquipmentRegistrationForm-form-edit").data("bootstrapValidator").resetField('useSignalWorkerCard');
        $("#filingEquipmentRegistrationForm-form-edit").data("bootstrapValidator").validateField('useSignalWorker');
        $("#filingEquipmentRegistrationForm-form-edit").data("bootstrapValidator").validateField('useSignalWorkerCard');
    }

    $("#equipmentType").change(function () {
        equipmentTypeCharge()
    })
    $(function () {
        $("#equipmentType").val(filingEquipmentRegistrationFormValue.equipmentType)
        equipmentTypeCharge()
        $("#isBuildLicense").val(filingEquipmentRegistrationFormValue.isBuildLicense)
        if ($("#isBuildLicense").val() == '0'){
            $('#buildLicenseCode').attr('disabled',true);
        }
    })


    $(function () {
        var filingApplicationFormPath = "";
        var equipmentRecordCertificatePath = "";
        var equipmentLeasingContractPath = "";
        var inspectionReportPath = "";
        var installationAcceptanceReportPath = "";
        var operatorsQualificationCertificatePath = "";
        var equipmentMaintenanceManagementSystemPath = "";
        var safetyAccidentsEmergencyPlanPath = "";
        for (let i = 0; i < files.length; i++) {
            let file = files[i];
            if (file.connectId.indexOf("filingApplicationFormPath") > 0) {
                filingApplicationFormPath += "<tr><td style='width: 80%;'><a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a></td><td style='text-align: center'><span class='del' onclick=delFile(this,'filingApplicationFormPath','" + file.id + "') style='cursor:pointer; color:#ff101a; line-height: 34px;'>删除</span></td></tr>";
            }
            if (file.connectId.indexOf("equipmentRecordCertificatePath") > 0) {
                equipmentRecordCertificatePath += "<tr><td style='width: 80%;'><a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a></td><td style='text-align: center'><span class='del' onclick=delFile(this,'equipmentRecordCertificatePath','" + file.id + "') style='cursor:pointer; color:#ff101a; line-height: 34px;'>删除</span></td></tr>";
            }
            if (file.connectId.indexOf("equipmentLeasingContractPath") > 0) {
                equipmentLeasingContractPath += "<tr><td style='width: 80%;'><a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a></td><td style='text-align: center'><span class='del' onclick=delFile(this,'equipmentLeasingContractPath','" + file.id + "') style='cursor:pointer; color:#ff101a; line-height: 34px;'>删除</span></td></tr>";
            }
            if (file.connectId.indexOf("inspectionReportPath") > 0) {
                inspectionReportPath += "<tr><td style='width: 80%;'><a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a></td><td style='text-align: center'><span class='del' onclick=delFile(this,'inspectionReportPath','" + file.id + "') style='cursor:pointer; color:#ff101a; line-height: 34px;'>删除</span></td></tr>";
            }
            if (file.connectId.indexOf("installationAcceptanceReportPath") > 0) {
                installationAcceptanceReportPath += "<tr><td style='width: 80%;'><a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a></td><td style='text-align: center'><span class='del' onclick=delFile(this,'installationAcceptanceReportPath','" + file.id + "') style='cursor:pointer; color:#ff101a; line-height: 34px;'>删除</span></td></tr>";
            }
            if (file.connectId.indexOf("operatorsQualificationCertificatePath") > 0) {
                operatorsQualificationCertificatePath += "<tr><td style='width: 80%;'><a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a></td><td style='text-align: center'><span class='del' onclick=delFile(this,'operatorsQualificationCertificatePath','" + file.id + "') style='cursor:pointer; color:#ff101a; line-height: 34px;'>删除</span></td></tr>";
            }
            if (file.connectId.indexOf("equipmentMaintenanceManagementSystemPath") > 0) {
                equipmentMaintenanceManagementSystemPath += "<tr><td style='width: 80%;'><a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a></td><td style='text-align: center'><span class='del' onclick=delFile(this,'equipmentMaintenanceManagementSystemPath','" + file.id + "') style='cursor:pointer; color:#ff101a; line-height: 34px;'>删除</span></td></tr>";
            }
            if (file.connectId.indexOf("safetyAccidentsEmergencyPlanPath") > 0) {
                safetyAccidentsEmergencyPlanPath += "<tr><td style='width: 80%;'><a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a></td><td style='text-align: center'><span class='del' onclick=delFile(this,'safetyAccidentsEmergencyPlanPath','" + file.id + "') style='cursor:pointer; color:#ff101a; line-height: 34px;'>删除</span></td></tr>";
            }
            let itemPath = $("#" + file.fileType)
            if (itemPath.val()) {
                itemPath.val(itemPath.val() + "," + file.id)
            } else {
                itemPath.val(file.id)
            }
        }
        $("#filingApplicationFormPath-table").html(filingApplicationFormPath);
        $("#equipmentRecordCertificatePath-table").html(equipmentRecordCertificatePath);
        $("#equipmentLeasingContractPath-table").html(equipmentLeasingContractPath);
        $("#inspectionReportPath-table").html(inspectionReportPath);
        $("#installationAcceptanceReportPath-table").html(installationAcceptanceReportPath);
        $("#operatorsQualificationCertificatePath-table").html(operatorsQualificationCertificatePath);
        $("#equipmentMaintenanceManagementSystemPath-table").html(equipmentMaintenanceManagementSystemPath);
        $("#safetyAccidentsEmergencyPlanPath-table").html(safetyAccidentsEmergencyPlanPath);
    })

    function delFile(a, fileConnect, ed) {
        $.ajax({
            url: "/admin/filingFile/delete",
            data: {ids: ed},
            type: "POST",
            success: function (res) {
                if (res.code == 0) {
                    layer.msg("删除成功");
                    var eld = $("#" + fileConnect).val();
                    var fileIdArray = eld.split(",");
                    var index = fileIdArray.indexOf(ed);
                    if (index > -1) {
                        fileIdArray.splice(index, 1); // 从数组中删除项
                    }
                    var ids = fileIdArray.filter(function (item) {
                        return item != '';
                    }).join(",");
                    $("#" + fileConnect).val(ids).trigger('change');
                    $(a).parents("tr").remove();
                } else {
                    layer.msg("删除失败")
                }
            }
        })
    }

    $(function () {
        setTimeout(() => {
            $('.el-input__inner').val(filingEquipmentRegistrationFormValue.remarks)
        },500)
        layui.use('layCascader', function () {
            // var id = 0;
            var layCascader = layui.layCascader;
            layCascader({
                elem: '#engineeringAreaCode',
                placeholder:'请依据施工许可证信息选择所属地',
                props: {
                    checkStrictly: true,
                    lazy: true,
                    lazyLoad: function (node, resolve) {
                        $("#engineeringAreaCode").trigger('change')
                        var level = node.level;
                        if (level == 1) {
                            return
                        } else {
                            $.ajax({
                                url: '/admin/area/getCountyList',
                                type: 'GET', // 请求类型
                                data: {
                                    parentId: node.value || 410000
                                },
                                dataType: 'json',
                                success: function (response) {
                                    var nodes = response.map(function (item) {

                                        return {
                                            value: item.id,
                                            label: item.name,
                                            leaf: level >= 1
                                        };
                                    });
                                    resolve(nodes);
                                },
                                error: function (error) {
                                    console.log('加载子节点数据失败:', error);
                                    resolve([]);

                                }
                            });
                        }
                    }
                }
            });
        });
    })

    $(function () {
        for (var i = 0; i < users.length; i++) {
            var user = users[i];
            toAddList(user.name, user.workerType, user.appointmentCertificateCode, user.id);
        }
    })

    var index = parent.layer.getFrameIndex(window.name);
    $('.datepicker').datepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        autoclose: true,
        todayHighlight: true,
        //控制可选的最晚月份，为当前月
        endDate: new Date()
    });
    //获取当前时间
    var date = new Date();
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    if (month < 10) {
        month = "0" + month;
    }
    $("#applicationDate").val(date.toLocaleDateString());
    $("#now").val(date.toLocaleDateString());


    $(function () {
        //文件上传
        $.each($(".fileinput"), function (index, item) {
            var type = $(item).data("type");
            $(item).fileinput({
                uploadUrl: '/admin/filingEquipmentRegistrationForm/uploadFile',
                required: false,
                // allowedFileExtensions : ['jpg', 'png','gif','docx','doc','pdf',],
                overwriteInitial: false,
                maxFileSize: 3072,
                maxFilesNum: 1,
                showPreview: true,
                showUpload: true,
                showCaption: true,
                language: 'zh',
                uploadAsync: true,
                dropZoneEnabled: false,
            }).on("fileuploaded", function (event, data, previewed, index) {
                var _data = data.response.data;
                _data.fileType = type;
                // $("#" + type).val(_data.fileUrl);
                // $("#" + type).change();
                addFile(_data, type);
            });
        });
    })

    //todo 文件信息保存
    function addFile(_fileinfo, fileConnect) {
        var fileName = _fileinfo.fileName;
        var fileUrl = _fileinfo.fileUrl;
        var filePath = _fileinfo.filePath;
        var fileSuffix = _fileinfo.fileSuffix;
        var fileType = _fileinfo.fileType;
        var id = $("#id").val().toString();
        var connectId = id + "deviceNew" + fileConnect;
        var file = {
            fileName: fileName,
            fileUrl: fileUrl,
            filePath: filePath,
            fileSuffix: fileSuffix,
            fileType: fileType,
            connectId: connectId
        };
        $.ajax({
            url: "/admin/filingFile/fileUpload",
            data: file,
            type: "POST",
            success: function (res) {
                var eld = $("#" + fileConnect).val();
                var fileIdArray;
                if (eld == '') {
                    fileIdArray = [];
                } else {
                    fileIdArray = (eld + "").split(",");
                }
                fileIdArray.push(res.data.toString());
                var ids = fileIdArray.join(",").replace(/undefined\,/, "");
                $("#" + fileConnect).val(ids).change();
                var line = "<tr><td style='width: 80%;'><a href='" + fileUrl + "' download='" + fileName + "'>" + fileName + "</a></td><td style='text-align: center'><span class='del' onclick=delFile(this,'" + fileConnect + "','" + res.data + "') style='cursor:pointer; color:#ff101a; text-align: center; line-height: 34px;'>删除</span></td></tr>";
                $("#" + fileConnect + "-table").append(line);
                layer.msg(res.msg);
            }
        })
    }

    function toAddList(name, type, code, id) {
        var trLine = '<tr>\n' +
            '                <td>' + name + '</td><td>' + type + '</td><td>' + code + '</td><td><span class="del" onclick="del(this,\'' + id + '\')" style="cursor:pointer; color:#ff101a; text-align: center; line-height: 34px;">删除</span></td></tr>';
        $("#person").append(trLine);
    }

    //deluser
    function del(a, ed) {
        $.ajax({
            url: "/admin/filingEquipmentUser/del",
            data: {id: ed},
            type: "POST",
            success: function (res) {
                if (res.code == 0) {
                    $(a).parents("tr").remove();
                } else {
                    layer.msg("删除失败")
                }
            }
        })
    }

    // function saveUser() {
    //     let userName = $("#userName").val();
    //     let type = $("#userType").val();
    //     let code = $("#userCode").val();
    //     if (userName != "" && type != "" && code != "") {
    //         $.ajax({
    //             url: "/admin/filingEquipmentUser/add",
    //             type: "POST",
    //             data: {name: userName, workerType: type, appointmentCertificateCode: code, useRegistrationId: id},
    //             success: function (res) {
    //                 if (res.code == 0) {
    //                     $(".temporary").val("");
    //                     var delId = res.data.id + '';
    //                     toAddList(res.data.name, res.data.workerType, res.data.appointmentCertificateCode, delId);
    //                 } else {
    //                     layer.msg("添加使用人员失败");
    //                 }
    //             },
    //             error: function () {
    //                 layer.msg("系统错误");
    //             }
    //         })
    //     } else {
    //         layer.msg("请将使用人员信息填写完整");
    //     }
    // }


    initValidator();


    function formSave() {
        $("#remarks").val($('.el-input__inner').val())

        $('#filingEquipmentRegistrationForm-form-edit').data('bootstrapValidator').validate();

        var bootstrapValidator = $('#filingEquipmentRegistrationForm-form-edit').data('bootstrapValidator');
        if (bootstrapValidator.isValid()) {
            save('/admin/filingEquipmentRegistrationForm/update', 'filingEquipmentRegistrationForm-form-edit')
        }
    }

    function initValidator() {
        $('#filingEquipmentRegistrationForm-form-edit').bootstrapValidator({
            message: '输入值不满足要求',
            excluded: [],
            // feedbackIcons: {
            //     valid: 'glyphicon glyphicon-ok',
            //     invalid: 'glyphicon glyphicon-remove',
            //     validating: 'glyphicon glyphicon-refresh'
            // },
            verbose: false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
                armLength: {
                    validators: {
                        // notEmpty: {
                        //     message: '必填'
                        // },
                        callback: {
                            message: '请输入合法数字',
                            callback: function (value, validator) {
                                let isVisible = $("#armLength").is(":visible")
                                if (isVisible) {
                                    if (value) {
                                        let regex = /^(?!0\d)\d+(\.\d+)?$/;
                                        return regex.test(value)
                                    } else {
                                        return false
                                    }
                                } else {
                                    return true
                                }
                            }
                        }
                    }
                },
                maxWeight: {
                    validators: {
                        // notEmpty: {
                        //     message: '必填'
                        // },
                        callback: {
                            message: '请输入合法数字',
                            callback: function (value, validator) {
                                let isVisible = $("#maxWeight").is(":visible")
                                if (isVisible) {
                                    if (value) {
                                        let regex = /^(?!0\d)\d+(\.\d+)?$/;
                                        return regex.test(value)
                                    } else {
                                        return false
                                    }
                                } else {
                                    return true
                                }
                            }
                        }
                    }
                },
                maxHeight: {
                    validators: {
                        // notEmpty: {
                        //     message: '必填'
                        // },
                        callback: {
                            message: '请输入合法数字',
                            callback: function (value, validator) {
                                let isVisible = $("#maxHeight").is(":visible")
                                if (isVisible) {
                                    if (value) {
                                        let regex = /^(?!0\d)\d+(\.\d+)?$/;
                                        return regex.test(value)
                                    } else {
                                        return false
                                    }
                                } else {
                                    return true
                                }
                            }
                        }
                    }
                },
                maximumCapacity: {
                    validators: {
                        // notEmpty: {
                        //     message: '必填'
                        // },
                        callback: {
                            message: '请输入合法数字',
                            callback: function (value, validator) {
                                let isVisible = $("#maximumCapacity").is(":visible")
                                if (isVisible) {
                                    if (value) {
                                        let regex = /^(?!0\d)\d+(\.\d+)?$/;
                                        return regex.test(value)
                                    } else {
                                        return false
                                    }
                                } else {
                                    return true
                                }
                            }
                        }
                    }
                },
                sRegularWeight: {
                    validators: {
                        // notEmpty: {
                        //     message: '必填'
                        // },
                        callback: {
                            message: '请输入合法数字',
                            callback: function (value, validator) {
                                let isVisible = $("#sRegularWeight").is(":visible")
                                if (isVisible) {
                                    if (value) {
                                        let regex = /^(?!0\d)\d+(\.\d+)?$/;
                                        return regex.test(value)
                                    } else {
                                        return false
                                    }
                                } else {
                                    return true
                                }
                            }
                        }
                    }
                },
                sRegularSpeed: {
                    validators: {
                        // notEmpty: {
                        //     message: '必填'
                        // },
                        callback: {
                            message: '请输入合法数字',
                            callback: function (value, validator) {
                                let isVisible = $("#sRegularSpeed").is(":visible")
                                if (isVisible) {
                                    if (value) {
                                        let regex = /^(?!0\d)\d+(\.\d+)?$/;
                                        return regex.test(value)
                                    } else {
                                        return false
                                    }
                                } else {
                                    return true
                                }
                            }
                        }
                    }
                },
                wRegularWeight: {
                    validators: {
                        // notEmpty: {
                        //     message: '必填'
                        // },
                        callback: {
                            message: '请输入合法数字',
                            callback: function (value, validator) {
                                let isVisible = $("#wRegularWeight").is(":visible")
                                if (isVisible) {
                                    if (value) {
                                        let regex = /^(?!0\d)\d+(\.\d+)?$/;
                                        return regex.test(value)
                                    } else {
                                        return false
                                    }
                                } else {
                                    return true
                                }
                            }
                        }
                    }
                },
                wRegularSpeed: {
                    validators: {
                        // notEmpty: {
                        //     message: '必填'
                        // },
                        callback: {
                            message: '请输入合法数字',
                            callback: function (value, validator) {
                                let isVisible = $("#wRegularSpeed").is(":visible")
                                if (isVisible) {
                                    if (value) {
                                        let regex = /^(?!0\d)\d+(\.\d+)?$/;
                                        return regex.test(value)
                                    } else {
                                        return false
                                    }
                                } else {
                                    return true
                                }
                            }
                        }
                    }
                },
                specificationModel: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                factoryTime: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                factoryNumber: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                installationAddress: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                userContact: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                userContactCard: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            min: 15,
                            max: 18,
                            message: '身份证号码长度应在15至18位之间'
                        },
                        regexp: {
                            regexp: /^(^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}(\d|X|x)$)|([1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)$/,
                            message: '请输入正确的身份证号码'
                        }
                    }
                },
                useProduceSafeManager:{
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                useProduceSafeManagerCard: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            min: 15,
                            max: 18,
                            message: '身份证号码长度应在15至18位之间'
                        },
                        regexp: {
                            regexp: /^(^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}(\d|X|x)$)|([1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)$/,
                            message: '请输入正确的身份证号码'
                        }
                    }
                },
                useProduceDeviceManager:{
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                useProduceDeviceManagerCard: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            min: 15,
                            max: 18,
                            message: '身份证号码长度应在15至18位之间'
                        },
                        regexp: {
                            regexp: /^(^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}(\d|X|x)$)|([1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)$/,
                            message: '请输入正确的身份证号码'
                        }
                    }
                },
                useSignalWorker:{
                    validators: {
                        // notEmpty: {
                        //     message: '必填'
                        // }
                        callback:{
                            message:'必填',
                            callback:function (value) {
                                let equipmentType = $("#equipmentType").val()
                                if(equipmentType == 'T'){
                                    $("#useSignalWorker").attr('disabled',false)
                                    if(value){
                                        return true
                                    }else{
                                        return false
                                    }
                                }else{
                                    $("#useSignalWorker").val('')
                                    $("#useSignalWorker").attr('disabled',true)
                                    return true
                                }
                            }
                        }
                    }
                },
                useSignalWorkerCard: {
                    validators: {
                        // notEmpty: {
                        //     message: '必填'
                        // },
                        stringLength: {
                            min: 15,
                            max: 18,
                            message: '身份证号码长度应在15至18位之间'
                        },
                        regexp: {
                            regexp: /^(^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}(\d|X|x)$)|([1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)$/,
                            message: '请输入正确的身份证号码'
                        },
                        callback:{
                            message:'必填',
                            callback:function (value) {
                                let equipmentType = $("#equipmentType").val()
                                if(equipmentType == 'T'){
                                    $("#useSignalWorkerCard").attr('disabled',false)

                                    if(value){
                                        return true
                                    }else{
                                        return false
                                    }
                                }else{
                                    $("#useSignalWorkerCard").val('')
                                    $("#useSignalWorkerCard").attr('disabled',true)
                                    return true
                                }
                            }
                        }
                    }
                },
                useDeviceDriver:{
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                useDeviceDriverCard: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            min: 15,
                            max: 18,
                            message: '身份证号码长度应在15至18位之间'
                        },
                        regexp: {
                            regexp: /^(^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}(\d|X|x)$)|([1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)$/,
                            message: '请输入正确的身份证号码'
                        }
                    }
                },
                deviceFilingCode: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                engineeringName: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 50,
                            message: '不能超过50位'
                        }
                    }
                },
                installationUnit: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                siteInstallationLeader: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                    }
                },
                siteInstallationLeaderCard: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            min: 15,
                            max: 18,
                            message: '身份证号码长度应在15至18位之间'
                        },
                        regexp: {
                            regexp: /^(^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}(\d|X|x)$)|([1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)$/,
                            message: '请输入正确的身份证号码'
                        }
                    }
                },
                installationTechnician: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                    }
                },
                installationTechnicianCard: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            min: 15,
                            max: 18,
                            message: '身份证号码长度应在15至18位之间'
                        },
                        regexp: {
                            regexp: /^(^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}(\d|X|x)$)|([1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)$/,
                            message: '请输入正确的身份证号码'
                        }
                    }
                },
                installationManager: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                    }
                },
                installationManagerCard: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            min: 15,
                            max: 18,
                            message: '身份证号码长度应在15至18位之间'
                        },
                        regexp: {
                            regexp: /^(^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}(\d|X|x)$)|([1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)$/,
                            message: '请输入正确的身份证号码'
                        }
                    }
                },
                testingUnit: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                testingUnitCode: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 18,
                            min: 18,
                            message: "请输入18位信用代码"
                        }
                    }
                },
                testingDate: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 0,
                            message: '不能超过0位'
                        }
                    }
                },
                testingLeadereCard: {
                    validators: {
                        stringLength: {
                            min: 15,
                            max: 18,
                            message: '身份证号码长度应在15至18位之间'
                        },
                        regexp: {
                            regexp: /^(^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}(\d|X|x)$)|([1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)$/,
                            message: '请输入正确的身份证号码'
                        }
                    }
                },
                engineeringAreaCode: {
                    trigger:'change',
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                engineeringAddressDetail: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                buildLicenseCode: {
                    trigger: "blur",
                    validators: {
                        callback: {
                            message: '必填',
                            callback: function (value, validator) {
                                if ($('#isBuildLicense').val() == '0') {
                                    //否
                                    $('#buildLicenseCode').val('');
                                    $('#buildLicenseCode').attr('disabled',true);
                                    return true
                                } else {
                                    //是
                                    $('#buildLicenseCode').attr('disabled',false);
                                    return value ? true : false
                                }
                            }
                        }
                    }
                },
                installationStartTime: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        callback: {
                            message: '开始时间不能大于结束时间',
                            callback: function (value, validator) {
                                let installationEndTime = $('#installationEndTime').val()
                                let installationStartTime = new Date(value)
                                if (installationEndTime) {
                                    installationEndTime = new Date(installationEndTime)

                                    if (isNaN(installationStartTime.getTime()) || isNaN(installationEndTime.getTime())) {
                                        return false;
                                    }
                                    return installationEndTime >= installationStartTime;
                                } else {
                                    return true
                                }
                            }
                        }

                    }
                },
                installationEndTime: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        callback: {
                            message: '结束时间不能小于开始时间',
                            callback: function (value, validator) {
                                let installationStartTime = $('#installationStartTime').val()
                                let installationEndTime = new Date(value)
                                if (installationStartTime) {
                                    installationStartTime = new Date(installationStartTime)

                                    if (isNaN(installationStartTime.getTime()) || isNaN(installationEndTime.getTime())) {
                                        return false;
                                    }
                                    return installationEndTime >= installationStartTime;
                                } else {
                                    return true
                                }
                            }
                        }
                    }
                },
                jointInspectionDate: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 0,
                            message: '不能超过0位'
                        }
                    }
                },
                firstInstallationHeight: {
                    validators: {
                        callback: {
                            message: '请输入合法数字',
                            callback: function (value, validator) {
                                if (value) {
                                    let regex = /^(?!0\d)\d+(\.\d+)?$/;
                                    return regex.test(value)
                                } else {
                                    return true
                                }
                            }
                        }
                    }
                },
                finalUseHeight: {
                    validators: {
                        callback: {
                            message: '请输入合法数字',
                            callback: function (value, validator) {
                                if (value) {
                                    let regex = /^(?!0\d)\d+(\.\d+)?$/;
                                    return regex.test(value)
                                } else {
                                    return true
                                }
                            }
                        }
                    }
                },
                propertyUnit: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                propertyUnitCode: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 18,
                            min: 18,
                            message: "请输入18位信用代码"
                        }
                    }
                },
                useUnit: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                useUnitCode: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 18,
                            min: 18,
                            message: "请输入18位信用代码"
                        }
                    }
                },
                produceUnit: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                produceUnitCode: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 18,
                            min: 18,
                            message: "请输入18位信用代码"
                        }
                    }
                },
                upkeepUnit: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        }
                    }
                },
                upkeepUnitCode: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 18,
                            min: 18,
                            message: "请输入18位信用代码"
                        }
                    }
                },
                installationUnitCode: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 18,
                            min: 18,
                            message: "请输入18位信用代码"
                        }
                    }
                },
                filingApplicationFormPath: {
                    trigger: 'change',
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                equipmentRecordCertificatePath: {
                    trigger: 'change',
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                equipmentLeasingContractPath: {
                    trigger: 'change',
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                inspectionReportPath: {
                    trigger: 'change',
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                installationAcceptanceReportPath: {
                    trigger: 'change',
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                operatorsQualificationCertificatePath: {
                    trigger: 'change',
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                equipmentMaintenanceManagementSystemPath: {
                    trigger: 'change',
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                safetyAccidentsEmergencyPlanPath: {
                    trigger: 'change',
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                }
            }
        });
    }
</script>

</body>
</html>
