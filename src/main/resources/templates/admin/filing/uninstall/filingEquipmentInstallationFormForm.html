<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备拆卸登记流程表表单"></title>
    <th:block th:include="admin/include/head :: head"/>
    <link rel="stylesheet" th:href="@{/resources/file-input/css/fileinput.min.css}">
    <style>
        .title {
            font-size: 14px;
            font-weight: bold;
            padding: 10px;
            margin-bottom: 10px;
            margin-right: 12px;
            background: #e1f0f9;
            border-radius: 4px;
            position: relative;

        }

        .title::before {
            content: '';
            width: 4px;
            height: 100%;
            background: #3c8dbc;
            position: absolute;
            left: 0;
            top: 0;
        }

        .form-control-feedback {
            display: none !important;
        }

        .download {
            padding: 0;
            white-space: nowrap;
        }

        .download a {
            line-height: 36px;
            padding: 8px 6px;
            background-color: #337ab7;
            border-radius: 4px;
            color: #fff;
        }

        .download a:hover {
            background-color: #2e6da4;
            text-decoration: none;
        }

        .fa-download {
            margin-right: 6px;
        }

        .btn-info {
            width: 130px;
            height: 40px;
        }
    </style>
</head>
<body>

<div class="container-fluid">
    <!-- form start -->
    <form id="form" class="form-horizontal">
        <input type="hidden" id="id" name="id">
        <div class="col-md-12" style="margin-top: 10px">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="applicationDate" class="col-sm-3 control-label require">申请日期</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="applicationDate" autocomplete="off" disabled>
                        <input type="hidden" class="form-control" id="now" name="applicationDate" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <input type="hidden" class="form-control" id="userId" name="userId" autocomplete="off">
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">设备信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentFilingCode" class="col-sm-3 control-label require">设备备案编号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="equipmentFilingCode" name="equipmentFilingCode"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentType" class="col-sm-3 control-label require">设备类型</label>
                    <div class="col-sm-9">
                        <!--                        <input id="types" th:value="*{equipmentType}" type="hidden">-->
                        <select class="form-control" id="equipmentType" name="equipmentType" autocomplete="off">
                            <option value="T">塔式起重机</option>
                            <option value="S">施工升降机(不含物料提升机)</option>
                            <option value="W">物料提升机</option>
                            <option value="Q">其他起重机械</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="specificationModel" class="col-sm-3 control-label require">规格型号</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="specificationModel" name="specificationModel"
                               autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">工程信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="engineeringName" class="col-sm-3 control-label require">工程名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="engineeringName" name="engineeringName"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="constructionSite" class="col-sm-3 control-label require">工地名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="constructionSite" name="constructionSite"
                               autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="engineeringAreaCode" class="col-sm-3 control-label require">工程信息属地</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="engineeringAreaCode" name="engineeringAreaCode" autocomplete="off">
                        <input type="hidden" id="remarks" name="remarks">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="engineeringAddressDetail" class="col-sm-3 control-label require">工程信息详细地址</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="engineeringAddressDetail" name="engineeringAddressDetail"
                               autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="jobContent" class="col-sm-3 control-label require">工作内容</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="jobContent" name="jobContent" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">拆卸单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="installationUnit" name="installationUnit"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationTime" class="col-sm-3 control-label require">拆卸时间</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control datepicker" id="installationTime" name="installationTime"
                               autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationHeight" class="col-sm-3 control-label require">拆卸高度（米）</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="installationHeight" name="installationHeight"
                               autocomplete="off" placeholder="单位：米">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationLeader" class="col-sm-3 control-label require">拆卸负责人</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="installationLeader" name="installationLeader"
                               autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationLeaderContactNumber" class="col-sm-3 control-label require">拆卸负责人联系电话</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="installationLeaderContactNumber" name="installationLeaderContactNumber"
                               autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="techLeader" class="col-sm-3 control-label require">技术负责人</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="techLeader" name="techLeader" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="techLeaderContactNumber" class="col-sm-3 control-label require">技术负责人联系电话</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="techLeaderContactNumber" name="techLeaderContactNumber" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="siteLeader" class="col-sm-3 control-label require">现场负责人</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="siteLeader" name="siteLeader" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="siteLeaderContactNumber" class="col-sm-3 control-label require">现场负责人联系电话</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="siteLeaderContactNumber" name="siteLeaderContactNumber" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">附件信息 <span style="color: red;font-size: 14px;font-weight: bold"> 注：每个文件大小需小于3MB，选择文件后请点击上传完成文件上传操作</span></div>
        </div>

        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="notificationBookFilePath" class="col-sm-3 control-label require">起重机拆卸告知书</label>
                    <div class="col-sm-7">
                        <input type="file" multiple class="fileinput" data-type="notificationBookFilePath" name="uploadFile">
                        <input type="hidden" class="form-control" id="notificationBookFilePath"
                               name="notificationBookFilePath" autocomplete="off">
                        <table class="table" id="notificationBookFilePath-table"></table>
                    </div>
                    <div class="col-sm-2 download">
                        <a href="/resources/awi/dist/doc/install_gaozhishu.doc" download="建筑起重机械设备安装（拆卸）告知书"><i class="fa fa-download"></i>模版下载</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentFilingLicenseFilePath" class="col-sm-3 control-label require">起重机备案证</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="equipmentFilingLicenseFilePath" name="uploadFile">
                        <input type="hidden" class="form-control" id="equipmentFilingLicenseFilePath"
                               name="equipmentFilingLicenseFilePath" autocomplete="off">
                        <table class="table" id="equipmentFilingLicenseFilePath-table"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="auditFormFilePath" class="col-sm-3 control-label require">起重机拆卸审核表</label>
                    <div class="col-sm-7">
                        <input type="file" multiple class="fileinput" data-type="auditFormFilePath" name="uploadFile">
                        <input type="hidden" class="form-control" id="auditFormFilePath" name="auditFormFilePath"
                               autocomplete="off">
                        <table class="table" id="auditFormFilePath-table"></table>
                    </div>
                    <div class="col-sm-2 download">
                        <a href="/resources/awi/dist/doc/install_shenhebiao.doc" download="建筑起重机械设备安装（拆卸）审核表"><i class="fa fa-download"></i>模版下载</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="qualifiLicenseFilePath" class="col-sm-3 control-label require">企业资质证书</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="qualifiLicenseFilePath" name="uploadFile">
                        <input type="hidden" class="form-control" id="qualifiLicenseFilePath" name="qualifiLicenseFilePath"
                               autocomplete="off">
                        <table class="table" id="qualifiLicenseFilePath-table"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="licenseSafetyPermitFilePath" class="col-sm-3 control-label require">安全生产许可证副本</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="licenseSafetyPermitFilePath" name="uploadFile">
                        <input type="hidden" class="form-control" id="licenseSafetyPermitFilePath"
                               name="licenseSafetyPermitFilePath" autocomplete="off">
                        <table class="table" id="licenseSafetyPermitFilePath-table"></table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="operatorsListFilePath" class="col-sm-3 control-label require">拆卸单位 特种作业人员名单</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="operatorsListFilePath" name="uploadFile">
                        <input type="hidden" class="form-control" id="operatorsListFilePath" name="operatorsListFilePath"
                               autocomplete="off">
                        <table class="table" id="operatorsListFilePath-table"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="operatorsLicenseFilePath" class="col-sm-3 control-label require">拆卸单位特种作业人员证书</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="operatorsLicenseFilePath" name="uploadFile">
                        <input type="hidden" class="form-control" id="operatorsLicenseFilePath"
                               name="operatorsLicenseFilePath" autocomplete="off">
                        <table class="table" id="operatorsLicenseFilePath-table"></table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="specialConstructionSchemeFilePath" class="col-sm-3 control-label require">设备拆卸专项施工方案</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="specialConstructionSchemeFilePath"
                               name="uploadFile">
                        <input type="hidden" class="form-control" id="specialConstructionSchemeFilePath"
                               name="specialConstructionSchemeFilePath" autocomplete="off">
                        <table class="table" id="specialConstructionSchemeFilePath-table"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationContractFilePath" class="col-sm-3 control-label require">拆卸合同</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="installationContractFilePath" name="uploadFile">
                        <input type="hidden" class="form-control" id="installationContractFilePath"
                               name="installationContractFilePath" autocomplete="off">
                        <table class="table" id="installationContractFilePath-table"></table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="securityProtocolFilePath" class="col-sm-3 control-label require">安全协议</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="securityProtocolFilePath" name="uploadFile">
                        <input type="hidden" class="form-control" id="securityProtocolFilePath"
                               name="securityProtocolFilePath" autocomplete="off">
                        <table class="table" id="securityProtocolFilePath-table"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="emergencyRescuePlanFilePath" class="col-sm-3 control-label require">应急救援预案</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="emergencyRescuePlanFilePath" name="uploadFile">
                        <input type="hidden" class="form-control" id="emergencyRescuePlanFilePath"
                               name="emergencyRescuePlanFilePath" autocomplete="off">
                        <table class="table" id="emergencyRescuePlanFilePath-table"></table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentQualificationCertificateFilePath" class="col-sm-3 control-label require">设备产品合格证明</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="equipmentQualificationCertificateFilePath"
                               name="uploadFile">
                        <input type="hidden" class="form-control" id="equipmentQualificationCertificateFilePath"
                               name="equipmentQualificationCertificateFilePath" autocomplete="off">
                        <table class="table" id="equipmentQualificationCertificateFilePath-table"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="operatorQualificationCertificateFilePath" class="col-sm-3 control-label require">操作人员资格证书</label>
                    <div class="col-sm-9">
                        <input type="file" multiple class="fileinput" data-type="operatorQualificationCertificateFilePath"
                               name="uploadFile">
                        <input type="hidden" class="form-control" id="operatorQualificationCertificateFilePath"
                               name="operatorQualificationCertificateFilePath" autocomplete="off">
                        <table class="table" id="operatorQualificationCertificateFilePath-table"></table>
                    </div>
                </div>
            </div>
        </div>


        <div class="form-group" style="text-align: center;">
            <button type="button" class="btn btn-info " onclick="formSave()">提交</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/file-input/js/fileinput.min.js}"></script>
<script th:src="@{/resources/file-input/js/locales/zh.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/layui_layui.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/cascader.min.js}"></script>

<script th:inline="javascript">
    "use strict";
    var id = "[[${id}]]";
    var userId = [[${userId}]];
    var index = parent.layer.getFrameIndex(window.name);
    const userInfo = [[${user}]]
    $("#id").val(id);
    $("#userId").val(userId);
    initValidator();
    $(function () {
        $("#installationUnit").val(userInfo.name)
    })
    $(function () {
        layui.use('layCascader', function () {
            // var id = 0;
            var layCascader = layui.layCascader;
            layCascader({
                elem: '#engineeringAreaCode',
                placeholder: '请依据施工许可证信息选择所属地',
                props: {
                    checkStrictly: true,
                    lazy: true,
                    lazyLoad: function (node, resolve) {
                        $("#engineeringAreaCode").trigger('change')
                        var level = node.level;
                        if (level == 1) {
                            return
                        } else {
                            $.ajax({
                                url: '/admin/area/getCountyList',
                                type: 'GET', // 请求类型
                                data: {
                                    parentId: node.value || 410000
                                },
                                dataType: 'json',
                                success: function (response) {
                                    var nodes = response.map(function (item) {

                                        return {
                                            value: item.id,
                                            label: item.name,
                                            leaf: level >= 1
                                        };
                                    });
                                    resolve(nodes);
                                },
                                error: function (error) {
                                    console.log('加载子节点数据失败:', error);
                                    resolve([]);

                                }
                            });
                        }
                    }
                }
            });
        });
    })

    function formSave() {
        $("#remarks").val($('.el-input__inner').val())

        var bootstrapValidator = $('#form').data('bootstrapValidator').validate();
        if (bootstrapValidator.isValid()) {
            savepop('/admin/filingEquipmentUninstallationForm/add', 'form', 'saveBtn')
        }
    }

    $('.datepicker').datepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        autoclose: true,
        todayHighlight: true,
        //控制可选的最晚月份，为当前月
    });
    //获取当前时间
    var date = new Date();
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    if (month < 10) {
        month = "0" + month;
    }
    $("#applicationDate").val(date.toLocaleDateString());
    $("#now").val(date.toLocaleDateString());
    $(function () {
        //文件上传
        $.each($(".fileinput"), function (index, item) {
            var type = $(item).data("type");
            $(item).fileinput({
                uploadUrl: '/admin/filingEquipmentFilingApplication/uploadFile',
                required: false,
                overwriteInitial: false,
                maxFileSize: 3072,
                maxFilesNum: 1,
                showPreview: true,
                showUpload: true,
                showCaption: true,
                language: 'zh',
                uploadAsync: true,
                dropZoneEnabled: false,
            }).on("fileuploaded", function (event, data) {
                var _data = data.response.data;
                _data.fileType = type;
                addFile(_data, type);
            });
        });
    })

    function del(a, fileConnect, ed) {
        $.ajax({
            url: "/admin/filingFile/delete",
            data: {ids: ed},
            type: "POST",
            success: function (res) {
                if (res.code == 0) {
                    layer.msg("删除成功");
                    var eld = $("#" + fileConnect).val();
                    var fileIdArray;
                    if (eld == '') {
                        fileIdArray = [];
                    } else {
                        fileIdArray = (eld + "").split(",");
                    }
                    arrayDelete(fileIdArray, ed);
                    var ids = fileIdArray.join(",").replace(/undefined\,/, "");
                    $("#" + fileConnect).val(ids).change();
                    $(a).parents("tr").remove();
                } else {
                    layer.msg("删除失败")
                }
            }
        })
    }

    function arrayDelete(ids, deleteId) {
        var index = ids.indexOf(deleteId);
        if (index > -1) {
            ids.splice(index, 1);
        }
    }

    function addFile(_fileinfo, fileConnect) {
        var fileName = _fileinfo.fileName;
        var fileUrl = _fileinfo.fileUrl;
        var filePath = _fileinfo.filePath;
        var fileSuffix = _fileinfo.fileSuffix;
        var fileType = _fileinfo.fileType;
        var id = $("#id").val().toString();
        var connectId = id + "install" + fileConnect;
        var file = {
            fileName: fileName,
            fileUrl: fileUrl,
            filePath: filePath,
            fileSuffix: fileSuffix,
            fileType: fileType,
            connectId: connectId
        };
        $.ajax({
            url: "/admin/filingFile/fileUpload",
            data: file,
            type: "POST",
            success: function (res) {
                var eld = $("#" + fileConnect).val();
                var fileIdArray;
                if (eld == '') {
                    fileIdArray = [];
                } else {
                    fileIdArray = (eld + "").split(",");
                }
                fileIdArray.push(res.data.toString());
                var ids = fileIdArray.join(",").replace(/undefined\,/, "");
                $("#" + fileConnect).val(ids).change();
                var line = "<tr><td style='width: 80%'><a href='" + fileUrl + "' download='" + fileName + "'>" + fileName + "</a></td><td style='text-align: center'><span class='del' onclick=del(this,'" + fileConnect + "','" + res.data + "') style='cursor:pointer; color:#ff101a; text-align: center; line-height: 34px;'>删除</span></td></tr>";
                $("#" + fileConnect + "-table").append(line);
                layer.msg(res.msg);
            }
        })
    }

    function initValidator() {
        $('#form').bootstrapValidator({
            message: '输入值不满足要求',
            excluded: [':disabled'],
            verbose: false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
                userId: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        },
                    }
                },
                applicationDate: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 0,
                            message: '不能超过0位'
                        },
                    }
                },
                equipmentFilingCode: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        },
                    }
                },
                specificationModel: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 30,
                            message: '不能超过30位'
                        },
                    }
                },
                engineeringName: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 50,
                            message: '不能超过50位'
                        },
                    }
                },
                installationUnit: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 20,
                            message: '不能超过20位'
                        },
                    }
                },
                siteLeader: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 10,
                            message: '不能超过10位'
                        },
                    }
                },
                siteLeaderContactNumber: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        regexp: {
                            regexp: /^0?(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
                            message: '请输入正确的手机号码'
                        }
                    }
                },
                techLeaderContactNumber: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        regexp: {
                            regexp: /^0?(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
                            message: '请输入正确的手机号码'
                        }
                    }
                },
                installationLeaderContactNumber: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        regexp: {
                            regexp: /^0?(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
                            message: '请输入正确的手机号码'
                        }
                    }
                },
                installationTime: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 0,
                            message: '不能超过0位'
                        },
                    }
                },
                installationHeight: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 10,
                            message: '不能超过10位'
                        },
                        callback: {
                            message: '请输入合法数字',
                            callback: function (value, validator) {
                                let regex = /^(?!0\d)\d+(\.\d+)?$/;
                                return regex.test(value)
                            }
                        }
                    }
                },
                constructionSite: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 255,
                            message: '不能超过255位'
                        },
                    }
                },
                engineeringAreaCode: {
                    trigger: 'change',
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                engineeringAddressDetail: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                jobContent: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 30,
                            message: '不能超过30位'
                        },

                    }
                },
                installationLeader: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 10,
                            message: '不能超过10位'
                        },

                    }
                },
                techLeader: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 10,
                            message: '不能超过10位'
                        },
                    }
                },
                notificationBookFilePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                equipmentFilingLicenseFilePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                auditFormFilePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                qualifiLicenseFilePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                licenseSafetyPermitFilePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                operatorsListFilePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                operatorsLicenseFilePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                specialConstructionSchemeFilePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                installationContractFilePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                securityProtocolFilePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                emergencyRescuePlanFilePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                equipmentQualificationCertificateFilePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                operatorQualificationCertificateFilePath: {
                    trigger: "change",
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
            }
        });
    }
</script>
</body>
</html>