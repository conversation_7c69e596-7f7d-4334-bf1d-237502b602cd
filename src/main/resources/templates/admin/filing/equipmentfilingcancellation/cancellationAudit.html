<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="起重机设备注销申请详情"></title>
    <th:block th:include="admin/include/head :: head"/>
    <style>
        .container {
            padding: 15px;
        }
        .btn {
            width: 130px;
            height: 40px;
        }
    </style>
</head>
<body>
<div id="filingEquipmentFilingCancellationDetail" class="container">
    <form id="filingEquipmentFilingCancellation-form-audit" class="form-horizontal" autocomplete="off"
          th:object="${filingEquipmentFilingCancellation}">
        <div class="form-group">
            <label for="filingCode" class="col-xs-2 control-label">备案编号</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="filingCode" th:value="*{filingCode}" name="filingCode" autocomplete="off">
            </div>
            <label for="applicationDate" class="col-xs-2 control-label">审核状态</label>
            <div class="col-xs-4">
                <input type="text" class="form-control"
                       name="auditStatus" disabled="disabled" th:if="*{auditStatus=='0'}" value="未审核"/>
                <input type="text" class="form-control"
                       name="auditStatus" disabled="disabled" th:if="*{auditStatus=='1'}" value="审核通过"/>
                <input type="text" class="form-control"
                       name="auditStatus" disabled="disabled" th:if="*{auditStatus=='2'}" value="驳回"/>
            </div>
        </div>
        <div class="form-group">
            <label for="deviceName" class="col-xs-2 control-label">设备名称</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="deviceName" name="deviceName" th:value="*{deviceName}" autocomplete="off">
            </div>
            <label for="specificationModel" class="col-xs-2 control-label">规格型号</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="specificationModel" th:value="*{specificationModel}" name="specificationModel" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="manufacturer" class="col-xs-2 control-label">生产厂家</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="manufacturer" th:value="*{manufacturer}" name="manufacturer" autocomplete="off">
            </div>
            <label for="factoryTime" class="col-xs-2 control-label">出厂时间</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="factoryTime" th:value="*{factoryTime}" name="factoryTime" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="manufacturingLicenseNumber" class="col-xs-2 control-label">制造许可证编号</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="manufacturingLicenseNumber" name="manufacturingLicenseNumber" th:value="*{manufacturingLicenseNumber}" autocomplete="off">
            </div>
            <label for="factoryLicenseNumber" class="col-xs-2 control-label">出厂编号</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="factoryLicenseNumber" name="factoryLicenseNumber" th:value="*{factoryLicenseNumber}" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="purchaseTime" class="col-xs-2 control-label">购买时间</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="purchaseTime" name="purchaseTime" th:value="*{purchaseTime}" autocomplete="off">
            </div>
            <label for="propertyUnit" class="col-xs-2 control-label">产权单位</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="propertyUnit" th:value="*{propertyUnit}" name="propertyUnit" autocomplete="off">
            </div>

        </div>
        <div class="form-group">
            <label for="legalRepresentative" class="col-xs-2 control-label">法定代表人</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="legalRepresentative" name="legalRepresentative" th:value="*{legalRepresentative}" autocomplete="off">
            </div>
            <label for="legalRepresentativeContactNumber" class="col-xs-2 control-label">联系电话</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="legalRepresentativeContactNumber" name="legalRepresentativeContactNumber" th:value="*{legalRepresentativeContactNumber}" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="unitAddress" class="col-xs-2 control-label">产权单位地址</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="unitAddress" th:value="*{unitAddress}" name="unitAddress" autocomplete="off">
            </div>
            <label for="applicationDate" class="col-xs-2 control-label">申请日期</label>
            <div class="col-xs-4">
                <input disabled type="text" class="form-control" id="applicationDate" th:value="*{applicationDate}" name="applicationDate" autocomplete="off">
            </div>
        </div>

        <div class="form-group">
            <label for="applicationDate" class="col-xs-2 control-label">注销原因</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" th:value="${#strings.replace(filingEquipmentFilingCancellation.cancellationReason,',','<br/>')}" name="applicationDate" autocomplete="off">
            </div>
        </div>
        <div class="form-group" style="display: none;">
            <label for="applicationDate" class="col-xs-2 control-label">审核驳回原因</label>
            <div class="col-xs-4">
                <input type="text" class="form-control"
                       name="auditStatus" disabled="disabled" th:if="*{auditStatus=='2'}" th:value="*{auditReject}"/>
            </div>
        </div>
        <table class="table table-striped table-bordered" th:object="${filingEquipmentFilingCancellation}">
            <tbody>
            <tr>
                <th>注销申请表</th>
                <td colspan="3" id="cancellationcancellationApplicationFormPath">
                </td>
            </tr>
            <tr>
                <th>备案证</th>
                <td colspan="3" id="cancellationfilingPaper">
            </tr>
            <tr>
                <th>备案牌</th>
                <td colspan="3" id="cancellationfilingCard">
            </tr>

            </tbody>
        </table>
    </form>
    <div style="text-align: center">
        <form id="form">
            <input id="id" name="id" hidden="hidden" th:value="*{id}"/>
            <textarea id="auditReject" name="auditReject" placeholder="请填写驳回原因" style="width: 100%;min-height: 100px;"></textarea>
        </form>
        <button type="button" onclick="auditPass()" class="btn btn-success">审核通过</button>
        <button type="button" onclick="rejectAudit()" class="btn btn-warning">驳回审核</button>
    </div>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>

<script th:inline="javascript">
    var files = [[${filepath}]];
    // console.log('filingEquipmentFilingCancellation',[[${filingEquipmentFilingCancellation}]])
    $(function () {
        var cancellationfilingCard = "";
        var cancellationfilingPaper = "";
        var cancellationcancellationApplicationFormPath = "";
        for (let i = 0; i < files.length; i++) {
            let file = files[i];
            if (file.connectId.indexOf("cancellationfilingCard") > 0) {
                cancellationfilingCard += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("cancellationfilingPaper") > 0) {
                cancellationfilingPaper += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("cancellationcancellationApplicationFormPath") > 0) {
                cancellationcancellationApplicationFormPath += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
        }
        $("#cancellationfilingCard").html(cancellationfilingCard);
        $("#cancellationfilingPaper").html(cancellationfilingPaper);
        $("#cancellationcancellationApplicationFormPath").html(cancellationcancellationApplicationFormPath);
    })
    var index = parent.layer.getFrameIndex(window.name);

    function auditPass() {
        layer.open({
            type:0,
            title:'审核通过',
            content:'确认通过当前申请？',
            btn: ['确认', '取消'],
            yes:function (index, layero) {
                var id = $("#id").val();
                var filingCode = $("#filingCode").val();
                $.ajax({
                    url: '/admin/equipmentFilingCancellation/auditpass',
                    method: 'post',
                    dataType: "JSON",
                    data: {id: id, filingCode: filingCode},
                    success: function (res) {
                        if (res.code == 0) {
                            layer.msg('操作成功', {time: 1000}, function () {
                                parent.location.reload();
                                parent.layer.close(index);
                            });
                        } else {
                            layer.msg(res.msg);
                        }
                    }
                })
            }
        })
        // var id = $("#id").val();
        // var filingCode = $("#filingCode").html();
        // $.ajax({
        //     url: "/admin/equipmentFilingCancellation/auditpass",
        //     data: {id: id, filingCode: filingCode},
        //     type: "POST",
        //     type: "POST",
        //     dataType: "JSON",
        //     success: function (res) {
        //         if (res.code == 0) {
        //             layer.msg("审核通过提交成功", {time: 1000}, function () {
        //                 parent.location.reload();
        //                 parent.layer.close(index);
        //             });
        //         } else {
        //             layer.msg(res.msg);
        //         }
        //
        //     }
        // })
    }

    function rejectAudit() {
        var formData = new FormData($("#form")[0]);
        var bootstrapValidator = $('#form').data('bootstrapValidator');
        bootstrapValidator.validate();
        if (bootstrapValidator.isValid()) {
            $.ajax({
                url: "/admin/equipmentFilingCancellation/auditupdate",
                data: formData,
                type: "POST",
                contentType: false,
                processData: false,
                success: function (res) {
                    if (res.code == 0) {
                        layer.msg("驳回审核提交成功", {time: 1000}, function () {
                            parent.location.reload();
                            parent.layer.close(index);
                        });
                    } else {
                        layer.msg(res.msg);
                    }

                }
            })
        }
    }

    $('#form').bootstrapValidator({
        message: '输入值不满足要求',
        excluded: [':disabled', ':hidden'],
        feedbackIcons: {
            valid: 'glyphicon glyphicon-ok',
            invalid: 'glyphicon glyphicon-remove',
            validating: 'glyphicon glyphicon-refresh'
        },
        verbose: false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
        fields: {
            auditReject: {
                group: 'form',
                validators: {
                    notEmpty: {
                        message: '请输入驳回原因'
                    }
                }
            }
        }
    });
</script>
</body>
</html>
