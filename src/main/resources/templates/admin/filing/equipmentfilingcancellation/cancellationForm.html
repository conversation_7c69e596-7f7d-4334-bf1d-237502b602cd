<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="起重机设备注销申请表单"></title>
    <th:block th:include="admin/include/head :: head"/>
    <link rel="stylesheet" th:href="@{/resources/file-input/css/fileinput.min.css}">
    <style>
        .container {
            padding-top: 20px;
        }

        .download {
            padding: 0;
            white-space: nowrap;
        }

        .download a {
            line-height: 36px;
            padding: 8px 6px;
            background-color: #337ab7;
            border-radius: 4px;
            color: #fff;
        }

        .download a:hover {
            background-color: #2e6da4;
            text-decoration: none;
        }

        .fa-download {
            margin-right: 6px;
        }

        .checkbox {
            display: flex;
        }

        .checkbox > label {
            width: 50%;
        }

        @media (min-width: 768px) {
            .control-label {
                text-align: right !important;
            }
        }
        .formSave {
            width: 130px;
            height: 40px;
        }
    </style>
</head>
<body>
<div class="container">
    <!-- form start -->
    <form id="form" class="form-horizontal" enctype="multipart/form-data">
        <input type="hidden" th:value="*{id}" id="id" name="id">
        <input type="hidden" th:value="*{userId}" class="form-control" id="userId" name="userId" autocomplete="off">
        <input type="hidden" class="form-control" id="unitAreaCode" name="unitAreaCode" autocomplete="off">
        <div class="form-group">
            <label for="filingCode" class="col-xs-2 control-label require ">备案编号</label>
            <div class="col-xs-4">
                <input readonly="readonly" type="text" class="form-control" id="filingCode" name="filingCode" autocomplete="off">
            </div>
            <!--            <div class="col-sm-1">-->
            <!--                <button type="button" onclick="handleSearch()" class="btn btn-info" style="width: 100%">查询</button>-->
            <!--            </div>-->
        </div>
        <div class="form-group">
            <label for="deviceName" class="col-xs-2 control-label require">设备名称</label>
            <div class="col-xs-4">
                <input type="text" readonly="readonly" class="form-control" id="deviceName" name="deviceName"
                       autocomplete="off">
            </div>
            <label for="unitAddress" class="col-xs-2 control-label require">产权单位地址</label>
            <div class="col-xs-4">
                <input type="text" readonly="readonly" class="form-control" id="unitAddress" name="unitAddress"
                       autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="specificationModel" class="col-xs-2 control-label require">规格型号</label>
            <div class="col-xs-4">
                <input type="text" readonly="readonly" class="form-control" id="specificationModel"
                       name="specificationModel" autocomplete="off">
            </div>
            <label for="manufacturer" class="col-xs-2 control-label require">生产厂家</label>
            <div class="col-xs-4">
                <input type="text" readonly="readonly" class="form-control" id="manufacturer" name="manufacturer" autocomplete="off">
            </div>
        </div>


        <div class="form-group">
            <label for="factoryTime" class="col-xs-2 control-label require">出厂时间</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" id="factoryTime" name="factoryTime" autocomplete="off" readonly="readonly">
            </div>
            <label for="manufacturingLicenseNumber" class="col-xs-2 control-label require">制造许可证编号</label>
            <div class="col-xs-4">
                <input type="text" readonly="readonly" class="form-control" id="manufacturingLicenseNumber"
                       name="manufacturingLicenseNumber" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="factoryLicenseNumber" class="col-xs-2 control-label require">出厂编号</label>
            <div class="col-xs-4">
                <input type="text" readonly="readonly" class="form-control" id="factoryLicenseNumber"
                       name="factoryLicenseNumber" autocomplete="off">
            </div>
            <label for="purchaseTime" class="col-xs-2 control-label require">购买时间</label>
            <div class="col-xs-4">
                <input type="text" readonly="readonly" class="form-control" id="purchaseTime" name="purchaseTime"
                       autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="propertyUnit" class="col-xs-2 control-label require">产权单位</label>
            <div class="col-xs-4">
                <input type="text" readonly="readonly" class="form-control" id="propertyUnit" name="propertyUnit"
                       autocomplete="off">
            </div>
            <label for="legalRepresentative" class="col-xs-2 control-label require">法定代表人</label>
            <div class="col-xs-4">
                <input type="text" readonly="readonly" class="form-control" id="legalRepresentative"
                       name="legalRepresentative" autocomplete="off">
            </div>
        </div>


        <div class="form-group">
            <label for="legalRepresentativeContactNumber" class="col-xs-2 control-label require">联系电话</label>
            <div class="col-xs-4">
                <input type="text" readonly="readonly" class="form-control" id="legalRepresentativeContactNumber"
                       name="legalRepresentativeContactNumber" autocomplete="off">
            </div>

        </div>

        <div class="form-group">
            <label class="col-xs-2 control-label require ">注销原因</label>
            <div class="col-xs-10">
                <div class="checkbox">
                    <label>
                        <input type="checkbox" value="1.产权发生变更的" name="cancellationReason">
                        产权发生变更的；
                    </label>
                    <label>
                        <input type="checkbox" value="2.属国家明令淘汰或者禁止使用的" name="cancellationReason">
                        属国家明令淘汰或者禁止使用的；
                    </label>
                </div>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" value="3.超过安全技术标准或者制造厂家规定的使用年限的" name="cancellationReason">
                        超过安全技术标准或者制造厂家规定的使用年限的；
                    </label>
                    <label>
                        <input type="checkbox" value="4.经检验达不到安全技术标准规定的" name="cancellationReason">
                        经检验达不到安全技术标准规定的；
                    </label>
                </div>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" value="5.没有完整安全技术档案的" name="cancellationReason">
                        没有完整安全技术档案的；
                    </label>
                    <label>
                        <input type="checkbox" value="6.没有齐全有效的安全保护装置的" name="cancellationReason">
                        没有齐全有效的安全保护装置的；
                    </label>
                </div>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" id="clickOther" value="其他原因" name="cancellationReason">
                        其他原因；
                    </label>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label id="otherReasonLabel" for="otherReason" class="col-xs-2 control-label " style="text-align: right;padding: 0"></label>
            <div class="col-xs-10">
                <input type="text" class="form-control" disabled id="otherReason" name="otherReason"
                       autocomplete="off">
            </div>
        </div>

        <div class="form-group" style="display:none;">
            <label for="applicationDate" class="col-xs-2 control-label require">申请日期</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" id="applicationDate" name="applicationDate" autocomplete="off">
            </div>
        </div>

        <div class="col-md-12">
            <label class="col-sm-2"></label>
            <label class="control-label require" style="color: red;">注：每个文件大小需小于3MB，选择文件后请点击上传完成文件上传操作</label>
        </div>

        <div class="form-group">
            <label for="mcancellationApplicationFormPath" class="col-xs-2 control-label require ">注销申请表</label>
            <div class="col-xs-4">
                <input class="fileinput" multiple type="file" name="uploadFile" id="mcancellationApplicationFormPath"
                       data-type="cancellationApplicationFormPath">
                <input type="text" name="cancellationApplicationFormPath" id="cancellationApplicationFormPath"
                       hidden="hidden">
                <table id="cancellationApplicationFormPath-table" class="table"></table>
            </div>
            <div class="col-sm-2 download">
                <a href="/resources/awi/dist/doc/application_cancel.docx" download="建筑起重机械设备备案注销申请表"><i class="fa fa-download"></i>模版下载</a>
            </div>
        </div>

        <div class="form-group">
            <label for="mfilingPaper" class="col-xs-2 control-label require ">备案证</label>
            <div class="col-xs-4">
                <input class="fileinput" multiple type="file" name="uploadFile" id="mfilingPaper"
                       data-type="filingPaper">
                <input type="text" name="filingPaper" id="filingPaper" hidden="hidden">
                <table id="filingPaper-table" class="table"></table>
            </div>
            <label for="mfilingCard" class="col-xs-2 control-label require ">备案牌</label>
            <div class="col-xs-4">
                <input class="fileinput" multiple type="file" name="uploadFile" id="mfilingCard"
                       data-type="filingCard">
                <input type="text" name="filingCard" id="filingCard" hidden="hidden">
                <table id="filingCard-table" class="table"></table>
            </div>
        </div>

        <div class="form-group">
            <button type="button" class="btn btn-info formSave col-xs-offset-6" onclick="formSave()">提交</button>
        </div>
    </form>
    <!-- customer form end -->
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/file-input/js/fileinput.min.js}"></script>
<script th:src="@{/resources/file-input/js/locales/zh.js}"></script>
<script th:inline="javascript">
    "use strict";

    handleSearch()
    console.log(decodeURI(window.location.href))
    /**
     *file上传
     */
    function addFile(_fileinfo, fileConnect) {
        var fileName = _fileinfo.fileName;
        var fileUrl = _fileinfo.fileUrl;
        var filePath = _fileinfo.filePath;
        var fileSuffix = _fileinfo.fileSuffix;
        var fileType = _fileinfo.fileType;
        var id = $("#id").val().toString();
        var connectId = id + "cancellation" + fileConnect;
        var file = {
            fileName: fileName,
            fileUrl: fileUrl,
            filePath: filePath,
            fileSuffix: fileSuffix,
            fileType: fileType,
            connectId: connectId
        };
        $.ajax({
            url: "/admin/filingFile/fileUpload",
            data: file,
            type: "POST",
            success: function (res) {
                var eld = $("#" + fileConnect).val();
                var fileIdArray;
                if (eld == '') {
                    fileIdArray = [];
                } else {
                    fileIdArray = (eld + "").split(",");
                }
                fileIdArray.push(res.data.toString());
                var ids = fileIdArray.join(",").replace(/undefined\,/, "");
                $("#" + fileConnect).val(ids).change();
                var line = "<tr><td style='width: 80%'><a href='" + fileUrl + "' download='" + fileName + "'>" + fileName + "</a></td><td style='text-align: center'><span class='del' onclick=del(this,'" + fileConnect + "','" + res.data + "') style='cursor:pointer; color:#ff101a; text-align: center; line-height: 34px;'>删除</span></td></tr>";
                $("#" + fileConnect + "-table").append(line);
                layer.msg(res.msg);
            }
        })
    }    //文件上传
    $.each($(".fileinput"), function (index, item) {
        var type = $(item).data("type");
        $(item).fileinput({
            uploadUrl: '/admin/equipmentFilingCancellation/uploadFile',
            required: false,
            overwriteInitial: false,
            maxFileSize: 3072,
            maxFilesNum: 1,
            showPreview: true,
            showUpload: true,
            showCaption: true,
            language: 'zh',
            uploadAsync: true,
            dropZoneEnabled: false,

        }).on("fileuploaded", function (event, data) {
            var _data = data.response.data;
            _data.fileType = type;
            addFile(_data, type);
        });
    })

    function del(a, fileConnect, ed) {
        $.ajax({
            url: "/admin/filingFile/delete",
            data: {ids: ed},
            type: "POST",
            success: function (res) {
                if (res.code == 0) {
                    layer.msg("删除成功");
                    var eld = $("#" + fileConnect).val();
                    var fileIdArray;
                    if (eld == '') {
                        fileIdArray = [];
                    } else {
                        fileIdArray = (eld + "").split(",");
                    }
                    arrayDelete(fileIdArray, ed);
                    var ids = fileIdArray.join(",").replace(/undefined\,/, "");
                    $("#" + fileConnect).val(ids).change();
                    $(a).parents("tr").remove();
                } else {
                    layer.msg("删除失败")
                }
            }
        })
    }


    function arrayDelete(ids, id) {
        var index = ids.indexOf(id);
        if (index > -1) {
            ids.splice(index, 1);
        }
    }

    $("#clickOther").click(function () {
        if ($("#clickOther").prop("checked")) {
            $("#otherReason").prop("disabled", false);
            $("#otherReasonLabel").addClass('require')
            $('#form').data('bootstrapValidator').enableFieldValidators('otherReason', true);
        } else {
            $('#form').data('bootstrapValidator').enableFieldValidators('otherReason', false);
            $("#otherReason").prop("disabled", true);
            $(this).val("其他原因");
            $("#otherReason").val("");
            $("#otherReasonLabel").removeClass('require')

        }
    })
    $("#otherReason").blur(function () {
        if ($("#clickOther").prop("checked")) {
            $("#clickOther").val("7." + $(this).val());
        } else {
            return;
        }
    })
    var nowTime = new Date();
    var year = nowTime.getFullYear();
    var month = nowTime.getMonth() + 1;
    var day = nowTime.getDate();
    var time = year + '-' + month + '-' + day;
    $("#applicationDate").val(time);

    /**
     * form 表单提交
     * @param url
     * @param data
     * @param success
     * @param error
     */
    function form(url, data, success, error) {
        $.ajax({
            url: url,
            type: 'POST',
            data: data,
            contentType: false,
            processData: false,
            success: function (data) {
                success(data)
            },
            error: function (data) {
                error(data)
            }
        })
    }

    var index = parent.layer.getFrameIndex(window.name);

    function formSave() {
        var formData = new FormData($("#form")[0]);
        var bootstrapValidator = $('#form').data('bootstrapValidator');
        bootstrapValidator.validate();
        if (bootstrapValidator.isValid()) {
            form('/admin/equipmentFilingCancellation/add', formData, function (res) {
                if (res.code == 0) {
                    layer.msg("注销申请提交成功", {time: 1000}, function () {
                        parent.location.reload();
                        parent.layer.close(index);
                    });
                } else {
                    layer.msg(res.msg, {time: 1000, icon: 5});
                }
            }, function (error) {
                layer.msg("系统异常！");
            })
        }
    }

    $('#form').bootstrapValidator({
        message: '输入值不满足要求',
        excluded: [':disabled'],
        // feedbackIcons: {
        //     valid: 'glyphicon glyphicon-ok',
        //     invalid: 'glyphicon glyphicon-remove',
        //     validating: 'glyphicon glyphicon-refresh'
        // },
        verbose: false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
        fields: {
            filingCode: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    },
                    remote: {
                        url: "/admin/equipmentFilingCancellation/checkUnique",
                        message: "设备已提交注销申请,不能重复提交",
                        delay: 2000,
                        type: "POST",
                    }
                }
            },
            deviceName: {
                trigger: "change",
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            specificationModel: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            manufacturer: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            factoryTime: {
                trigger: "change",
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            manufacturingLicenseNumber: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            factoryLicenseNumber: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            purchaseTime: {
                trigger: "change",
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            propertyUnit: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            unitAddress: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            legalRepresentative: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            legalRepresentativeContactNumber: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    },
                    regexp: {
                        regexp: /^1\d{10}$/,
                        message: '手机号格式错误'
                    }
                }
            },
            cancellationReason: {
                group: '.col-xs-4    ',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            otherReason: {
                enabled: false,
                group: '.col-xs-4    ',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            cancellationApplicationFormPath: {
                trigger: "change",
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            filingPaper: {
                trigger: "change",
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            filingCard: {
                trigger: "change",
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
        }
    });

    // $("#filingCode").blur(function () {
    function handleSearch() {
        const code = decodeURI(window.location.href).split('=')[1]
        $("#filingCode").val(code)
        $.ajax({
            url: "/admin/filingEquipmentFiling/search",
            type: 'POST',
            data: {code:  code},
            dataTpye: 'json',
            success: function (res) {
                if (res.code == 0) {
                    $("#unitAreaCode").val(res.data.unitAreaCode)
                    $("#deviceName").val(res.data.deviceName);
                    $("#manufacturer").val(res.data.manufacturer);
                    $("#manufacturingLicenseNumber").val(res.data.manufacturingLicenseNumber);
                    $("#purchaseTime").val(res.data.purchaseTime);
                    $("#unitAddress").val(res.data.unitAddress);
                    $("#legalRepresentative").val(res.data.legalRepresentative);
                    $("#specificationModel").val(res.data.specificationModel);
                    $("#factoryTime").val(res.data.factoryTime);
                    $("#factoryLicenseNumber").val(res.data.factoryLicenseNumber);
                    $("#propertyUnit").val(res.data.propertyUnit);
                    $("#legalRepresentativeContactNumber").val(res.data.legalRepresentativeContactNumber);
                    $("#form").data('bootstrapValidator').updateStatus("deviceName", "NOT_VALIDATED", null);
                    $("#form").data('bootstrapValidator').validateField('deviceName');
                    $("#form").data('bootstrapValidator').updateStatus("manufacturer", "NOT_VALIDATED", null);
                    $("#form").data('bootstrapValidator').validateField('manufacturer');
                    $("#form").data('bootstrapValidator').updateStatus("manufacturingLicenseNumber", "NOT_VALIDATED", null);
                    $("#form").data('bootstrapValidator').validateField('manufacturingLicenseNumber');
                    $("#form").data('bootstrapValidator').updateStatus("purchaseTime", "NOT_VALIDATED", null);
                    $("#form").data('bootstrapValidator').validateField('purchaseTime');
                    $("#form").data('bootstrapValidator').updateStatus("unitAddress", "NOT_VALIDATED", null);
                    $("#form").data('bootstrapValidator').validateField('unitAddress');
                    $("#form").data('bootstrapValidator').updateStatus("legalRepresentative", "NOT_VALIDATED", null);
                    $("#form").data('bootstrapValidator').validateField('legalRepresentative');
                    $("#form").data('bootstrapValidator').updateStatus("specificationModel", "NOT_VALIDATED", null);
                    $("#form").data('bootstrapValidator').validateField('specificationModel');
                    $("#form").data('bootstrapValidator').updateStatus("factoryTime", "NOT_VALIDATED", null);
                    $("#form").data('bootstrapValidator').validateField('factoryTime');
                    $("#form").data('bootstrapValidator').updateStatus("factoryLicenseNumber", "NOT_VALIDATED", null);
                    $("#form").data('bootstrapValidator').validateField('factoryLicenseNumber');
                    $("#form").data('bootstrapValidator').updateStatus("propertyUnit", "NOT_VALIDATED", null);
                    $("#form").data('bootstrapValidator').validateField('propertyUnit');
                    $("#form").data('bootstrapValidator').updateStatus("legalRepresentativeContactNumber", "NOT_VALIDATED", null);
                    $("#form").data('bootstrapValidator').validateField('legalRepresentativeContactNumber');
                } else {
                    layer.msg("未查询到该设备信息");
                }
            }
        })
    }


</script>
</body>
</html>