<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="起重机设备注销申请详情"></title>
    <th:block th:include="admin/include/head :: head"/>
    <style>
        .container-fluid {
            padding-top: 15px;
        }

        .box-footer {
            width: max-content;
            margin: 0 auto;
        }

        .container {
            padding: 15px;
        }

    </style>
</head>
<body>
<div id="filingEquipmentFilingCancellationDetail" class="container">

    <form id="filingEquipmentFilingApplication-form-edit" class="form-horizontal" autocomplete="off"
          th:object="${filingEquipmentFilingCancellation}">
        <div class="form-group">
            <label for="filingCode" class="col-xs-2 control-label">备案编号</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="filingCode" th:value="*{filingCode}" name="filingCode" autocomplete="off">
            </div>
            <label for="applicationDate" class="col-xs-2 control-label">审核状态</label>
            <div class="col-xs-4">
                <input type="text" class="form-control"
                       name="auditStatus" disabled="disabled" th:if="*{auditStatus=='0'}" value="未审核"/>
                <input type="text" class="form-control"
                       name="auditStatus" disabled="disabled" th:if="*{auditStatus=='1'}" value="审核通过"/>
                <input type="text" class="form-control"
                       name="auditStatus" disabled="disabled" th:if="*{auditStatus=='2'}" value="驳回"/>
            </div>
        </div>
        <div class="form-group">
            <label for="deviceName" class="col-xs-2 control-label">设备名称</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="deviceName" name="deviceName" th:value="*{deviceName}" autocomplete="off">
            </div>
            <label for="specificationModel" class="col-xs-2 control-label">规格型号</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="specificationModel" th:value="*{specificationModel}" name="specificationModel" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="manufacturer" class="col-xs-2 control-label">生产厂家</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="manufacturer" th:value="*{manufacturer}" name="manufacturer" autocomplete="off">
            </div>
            <label for="factoryTime" class="col-xs-2 control-label">出厂时间</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="factoryTime" th:value="*{factoryTime}" name="factoryTime" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="manufacturingLicenseNumber" class="col-xs-2 control-label">制造许可证编号</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="manufacturingLicenseNumber" name="manufacturingLicenseNumber" th:value="*{manufacturingLicenseNumber}" autocomplete="off">
            </div>
            <label for="factoryLicenseNumber" class="col-xs-2 control-label">出厂编号</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="factoryLicenseNumber" name="factoryLicenseNumber" th:value="*{factoryLicenseNumber}" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="purchaseTime" class="col-xs-2 control-label">购买时间</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="purchaseTime" name="purchaseTime" th:value="*{purchaseTime}" autocomplete="off">
            </div>
            <label for="propertyUnit" class="col-xs-2 control-label">产权单位</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="propertyUnit" th:value="*{propertyUnit}" name="propertyUnit" autocomplete="off">
            </div>

        </div>
        <div class="form-group">
            <label for="legalRepresentative" class="col-xs-2 control-label">法定代表人</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="legalRepresentative" name="legalRepresentative" th:value="*{legalRepresentative}" autocomplete="off">
            </div>
            <label for="legalRepresentativeContactNumber" class="col-xs-2 control-label">联系电话</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="legalRepresentativeContactNumber" name="legalRepresentativeContactNumber" th:value="*{legalRepresentativeContactNumber}" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="unitAddress" class="col-xs-2 control-label">产权单位地址</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="unitAddress" th:value="*{unitAddress}" name="unitAddress" autocomplete="off">
            </div>
            <label for="applicationDate" class="col-xs-2 control-label">申请日期</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="applicationDate" th:value="*{applicationDate}" name="applicationDate" autocomplete="off">
            </div>
        </div>

        <div class="form-group">
            <label for="applicationDate" class="col-xs-2 control-label">注销原因</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" th:value="${#strings.replace(filingEquipmentFilingCancellation.cancellationReason,',','<br/>')}" name="applicationDate" autocomplete="off">
            </div>

        </div>
        <div class="form-group">

        </div>
        <div class="form-group" style="display: none;">
            <label for="applicationDate" class="col-xs-2 control-label">审核驳回原因</label>
            <div class="col-xs-4">
                <input type="text" class="form-control"
                       name="auditStatus" th:if="*{auditStatus=='2'}" th:value="*{auditReject}"/>
            </div>
        </div>
        <table class="table table-striped table-bordered" th:object="${filingEquipmentFilingCancellation}">
            <tbody>
            <tr>
                <th>注销申请表</th>
                <td colspan="3" id="cancellationcancellationApplicationFormPath">
                </td>
            </tr>
            <tr>
                <th>备案证</th>
                <td colspan="3" id="cancellationfilingPaper">
            </tr>
            <tr>
                <th>备案牌</th>
                <td colspan="3" id="cancellationfilingCard">
            </tr>

            </tbody>
        </table>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    var files = [[${filepath}]];
    console.log([[${filingEquipmentFilingCancellation}]])
    $(function () {
        var cancellationfilingCard = "";
        var cancellationfilingPaper = "";
        var cancellationcancellationApplicationFormPath = "";
        for (let i = 0; i < files.length; i++) {
            let file = files[i];
            if (file.connectId.indexOf("cancellationfilingCard") > 0) {
                cancellationfilingCard += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("cancellationfilingPaper") > 0) {
                cancellationfilingPaper += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
            if (file.connectId.indexOf("cancellationcancellationApplicationFormPath") > 0) {
                cancellationcancellationApplicationFormPath += "<a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a><br>";
            }
        }
        $("#cancellationfilingCard").html(cancellationfilingCard);
        $("#cancellationfilingPaper").html(cancellationfilingPaper);
        $("#cancellationcancellationApplicationFormPath").html(cancellationcancellationApplicationFormPath);
    })
</script>
</body>
</html>
