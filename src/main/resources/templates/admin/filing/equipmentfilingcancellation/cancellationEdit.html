<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="起重机设备注销申请编辑"></title>
    <th:block th:include="admin/include/head :: head"/>
    <link rel="stylesheet" th:href="@{/resources/file-input/css/fileinput.min.css}">
    <style>
        .container {
            padding: 15px;
        }

        .checkbox {
            display: flex;
        }

        .checkbox > label {
            width: 50%;
        }
        .btn-info {
            width: 130px;
            height: 40px;
        }
    </style>
</head>
<body>
<div id="filingEquipmentFilingCancellationform" class="container">
    <form id="form-edit" enctype="multipart/form-data" class="form-horizontal" autocomplete="off" th:object="${filingEquipmentFilingCancellation}">
        <input type="hidden" id="id" name="id" th:value="*{id}">
        <input type="hidden" th:value="*{userId}" class="form-control" id="userId" name="userId" autocomplete="off">
        <div class="form-group">
            <label for="filingCode" class="col-xs-2 control-label">备案编号</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="filingCode" th:value="*{filingCode}" name="filingCode" autocomplete="off">
            </div>
            <label class="col-xs-2 control-label">审核驳回原因</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled th:if="*{auditStatus=='2'}" th:value="*{auditReject}" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="deviceName" class="col-xs-2 control-label">设备名称</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="deviceName" name="deviceName" th:value="*{deviceName}" autocomplete="off">
            </div>
            <label for="specificationModel" class="col-xs-2 control-label">规格型号</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="specificationModel" th:value="*{specificationModel}" name="specificationModel" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="manufacturer" class="col-xs-2 control-label">生产厂家</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="manufacturer" th:value="*{manufacturer}" name="manufacturer" autocomplete="off">
            </div>
            <label for="factoryTime" class="col-xs-2 control-label">出厂时间</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="factoryTime" th:value="*{factoryTime}" name="factoryTime" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="manufacturingLicenseNumber" class="col-xs-2 control-label">制造许可证编号</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="manufacturingLicenseNumber" name="manufacturingLicenseNumber" th:value="*{manufacturingLicenseNumber}" autocomplete="off">
            </div>
            <label for="factoryLicenseNumber" class="col-xs-2 control-label">出厂编号</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="factoryLicenseNumber" name="factoryLicenseNumber" th:value="*{factoryLicenseNumber}" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="purchaseTime" class="col-xs-2 control-label">购买时间</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="purchaseTime" name="purchaseTime" th:value="*{purchaseTime}" autocomplete="off">
            </div>
            <label for="propertyUnit" class="col-xs-2 control-label">产权单位</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="propertyUnit" th:value="*{propertyUnit}" name="propertyUnit" autocomplete="off">
            </div>

        </div>
        <div class="form-group">
            <label for="legalRepresentative" class="col-xs-2 control-label">法定代表人</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="legalRepresentative" name="legalRepresentative" th:value="*{legalRepresentative}" autocomplete="off">
            </div>
            <label for="legalRepresentativeContactNumber" class="col-xs-2 control-label">联系电话</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" disabled="disabled" id="legalRepresentativeContactNumber" name="legalRepresentativeContactNumber" th:value="*{legalRepresentativeContactNumber}" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label for="unitAddress" class="col-xs-2 control-label">产权单位地址</label>
            <div class="col-xs-10">
                <input type="text" class="form-control" disabled="disabled" id="unitAddress" th:value="*{unitAddress}" name="unitAddress" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-2 control-label">注销原因</label>
            <div class="col-xs-10">
                <div class="checkbox">
                    <label>
                        <input type="checkbox" th:checked="${#strings.contains(filingEquipmentFilingCancellation.cancellationReason,'1')}" value="1.产权发生变更的" name="cancellationReason">
                        产权发生变更的；
                    </label>
                    <label>
                        <input type="checkbox" th:checked="${#strings.contains(filingEquipmentFilingCancellation.cancellationReason,'2')}" value="2.属国家明令淘汰或者禁止使用的" name="cancellationReason">
                        属国家明令淘汰或者禁止使用的；
                    </label>
                </div>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" th:checked="${#strings.contains(filingEquipmentFilingCancellation.cancellationReason,'3')}" value="3.超过安全技术标准或者制造厂家规定的使用年限的" name="cancellationReason">
                        超过安全技术标准或者制造厂家规定的使用年限的；
                    </label>
                    <label>
                        <input type="checkbox" th:checked="${#strings.contains(filingEquipmentFilingCancellation.cancellationReason,'4')}" value="4.经检验达不到安全技术标准规定的" name="cancellationReason">
                        经检验达不到安全技术标准规定的；
                    </label>
                </div>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" th:checked="${#strings.contains(filingEquipmentFilingCancellation.cancellationReason,'5')}" value="5.没有完整安全技术档案的" name="cancellationReason">
                        没有完整安全技术档案的；
                    </label>
                    <label>
                        <input type="checkbox" th:checked="${#strings.contains(filingEquipmentFilingCancellation.cancellationReason,'6')}" value="6.没有齐全有效的安全保护装置的" name="cancellationReason">
                        没有齐全有效的安全保护装置的；
                    </label>
                </div>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" th:checked="${#strings.contains(filingEquipmentFilingCancellation.cancellationReason,'7')}" id="clickOther" th:value="'7.'+${#strings.substringAfter(filingEquipmentFilingCancellation.cancellationReason,'7.')}" name="cancellationReason">
                        其他原因；
                    </label>

                </div>

            </div>
        </div>
        <div class="form-group">
            <label id="otherReasonLabel" for="otherReason" class="col-xs-2 control-label" style="text-align: right;padding: 0"></label>
            <div class="col-xs-10">
                <input type="text" class="form-control" disabled id="otherReason" name="otherReason" autocomplete="off">
            </div>

        </div>
        <div class="form-group" style="display: none;">
            <label for="applicationDate" class="col-xs-2 control-label">申请日期</label>
            <div class="col-xs-4">
                <input type="text" class="form-control" id="applicationDate" th:value="*{applicationDate}" name="applicationDate" autocomplete="off">
            </div>
        </div>
        <div class="col-md-12">
            <label class="col-sm-2 control-label"></label>
            <label class="control-label" style="color: red;">注：每个文件大小需小于3MB，选择文件后请点击上传完成文件上传操作</label>
        </div>


        <div class="form-group">
            <label for="mcancellationApplicationFormPath" class="col-xs-2 control-label require">注销申请表</label>
            <div class="col-xs-4">
                <input class="fileinput" multiple type="file" name="uploadFile" id="mcancellationApplicationFormPath"
                       data-type="cancellationApplicationFormPath">
                <input type="text" name="cancellationApplicationFormPath" id="cancellationApplicationFormPath"
                       hidden="hidden" th:value="*{cancellationApplicationFormPath}">
                <table id="cancellationApplicationFormPath-table" class="table"></table>
            </div>
            <label for="mfilingPaper" class="col-xs-2 control-label require">备案证</label>
            <div class="col-xs-4">
                <input class="fileinput" multiple type="file" name="uploadFile" id="mfilingPaper"
                       data-type="filingPaper">
                <input type="text" name="filingPaper" id="filingPaper" hidden="hidden" th:value="*{filingPaper}">
                <table id="filingPaper-table" class="table"></table>

            </div>
        </div>

        <div class="form-group">
            <label for="mfilingCard" class="col-xs-2 control-label require">备案牌</label>
            <div class="col-xs-4">
                <input class="fileinput" multiple type="file" name="uploadFile" id="mfilingCard"
                       data-type="filingCard">
                <input type="text" name="filingCard" id="filingCard" hidden="hidden" th:value="*{filingCard}">
                <table id="filingCard-table" class="table"></table>
            </div>
        </div>

        <div style="display:none;" class="col-xs-6">
            <div class="form-group">
                <label for="technicalDirector" class="col-xs-4 control-label require">技术负责人</label>
                <div class="col-xs-6">
                    <input type="text" class="form-control" id="technicalDirector" name="technicalDirector" th:value="*{technicalDirector}"/>
                </div>
            </div>
        </div>
        <div style="display:none;" class="col-xs-6">
            <div class="form-group">
                <label for="technicalDirectorContactNumber" class="col-xs-4 control-label require">技术负责人联系方式</label>
                <div class="col-xs-6">
                    <input type="text" class="form-control" id="technicalDirectorContactNumber" name="technicalDirectorContactNumber" th:value="*{technicalDirectorContactNumber}"/>
                </div>
            </div>
        </div>
        <div style="display:none;" class="col-xs-6">
            <div class="form-group">
                <label for="equipmentManager" class="col-xs-4 control-label require">设备管理负责人</label>
                <div class="col-xs-6">
                    <input type="text" class="form-control" id="equipmentManager" name="equipmentManager" th:value="*{equipmentManager}"/>
                </div>
            </div>
        </div>
        <div style="display:none;" class="col-xs-6">
            <div class="form-group">
                <label for="equipmentManagerContactNumber" class="col-xs-4 control-label require">设备管理负责人联系方式</label>
                <div class="col-xs-6">
                    <input type="text" class="form-control" id="equipmentManagerContactNumber" name="equipmentManagerContactNumber" th:value="*{equipmentManagerContactNumber}"/>
                </div>
            </div>
        </div>
        <div style="display:none;" class="col-xs-6">
            <div class="form-group">
                <label for="liftingWeight" class="col-xs-4 control-label require">起重重量</label>
                <div class="col-xs-6">
                    <input type="text" class="form-control" id="liftingWeight" name="liftingWeight" th:value="*{liftingWeight}"/>
                </div>
            </div>
        </div>
        <div style="display:none;" class="col-xs-6">
            <div class="form-group">
                <label for="auditStatus" class="col-xs-4 control-label require">审核状态</label>
                <div class="col-xs-6">
                    <input type="text" class="form-control" id="auditStatus" name="auditStatus" th:value="*{auditStatus}"/>
                </div>
            </div>
        </div>
        <div style="display:none;" class="col-xs-6">
            <div class="form-group">
                <label for="auditReject" class="col-xs-4 control-label require">审核退回原因</label>
                <div class="col-xs-6">
                    <input type="text" class="form-control" id="auditReject" name="auditReject" th:value="*{auditReject}"/>
                </div>
            </div>
        </div>
        <div class="form-group">
            <button type="button" class="btn btn-info col-xs-offset-6" onclick="formSave()">提交审核</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/file-input/js/fileinput.min.js}"></script>
<script th:src="@{/resources/file-input/js/locales/zh.js}"></script>
<script type="text/javascript" th:inline="javascript">
    /**
     *file上传
     */
    var files = [[${filepath}]];
    $(function () {
        var cancellationfilingCard = "";
        var cancellationfilingPaper = "";
        var cancellationcancellationApplicationFormPath = "";
        for (let i = 0; i < files.length; i++) {
            let file = files[i];
            if (file.connectId.indexOf("cancellationfilingCard") > 0) {
                cancellationfilingCard += "<tr><td><a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a></td><td><span class='del' onclick=del(this,'productCertification','" + file.id + "') style='cursor:pointer; color:#ff101a; text-align: center; line-height: 34px;'>删除</span></td></tr>";
            }
            if (file.connectId.indexOf("cancellationfilingPaper") > 0) {
                cancellationfilingPaper += "<tr><td><a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a></td><td><span class='del' onclick=del(this,'productCertification','" + file.id + "') style='cursor:pointer; color:#ff101a; text-align: center; line-height: 34px;'>删除</span></td></tr>";
            }
            if (file.connectId.indexOf("cancellationcancellationApplicationFormPath") > 0) {
                cancellationcancellationApplicationFormPath += "<tr><td><a href='" + file.fileUrl + "' download='" + file.fileName + "'>" + file.fileName + "</a></td><td><span class='del' onclick=del(this,'productCertification','" + file.id + "') style='cursor:pointer; color:#ff101a; text-align: center; line-height: 34px;'>删除</span></td></tr>";
            }
        }
        $("#filingCard-table").html(cancellationfilingCard);
        $("#filingPaper-table").html(cancellationfilingPaper);
        $("#cancellationApplicationFormPath-table").html(cancellationcancellationApplicationFormPath);
    })

    function addFile(_fileinfo, fileConnect) {
        var fileName = _fileinfo.fileName;
        var fileUrl = _fileinfo.fileUrl;
        var filePath = _fileinfo.filePath;
        var fileSuffix = _fileinfo.fileSuffix;
        var fileType = _fileinfo.fileType;
        var id = $("#id").val().toString();
        var connectId = id + "cancellation" + fileConnect;
        var file = {
            fileName: fileName,
            fileUrl: fileUrl,
            filePath: filePath,
            fileSuffix: fileSuffix,
            fileType: fileType,
            connectId: connectId
        };
        $.ajax({
            url: "/admin/filingFile/fileUpload",
            data: file,
            type: "POST",
            success: function (res) {
                var eld = $("#" + fileConnect).val();
                var fileIdArray;
                if (eld == '') {
                    fileIdArray = [];
                } else {
                    fileIdArray = (eld + "").split(",");
                }
                fileIdArray.push(res.data.toString());
                var ids = fileIdArray.join(",").replace(/undefined\,/, "");
                $("#" + fileConnect).val(ids).change();
                var line = "<tr><td style='width: 80%'><a href='" + fileUrl + "' download='" + fileName + "'>" + fileName + "</a></td><td style='text-align: center'><span class='del' onclick=del(this,'" + fileConnect + "','" + res.data + "') style='cursor:pointer; color:#ff101a; text-align: center; line-height: 34px;'>删除</span></td></tr>";
                $("#" + fileConnect + "-table").append(line);
                layer.msg(res.msg);
            }
        })
    }

    //文件上传
    $.each($(".fileinput"), function (index, item) {
        var type = $(item).data("type");
        $(item).fileinput({
            uploadUrl: '/admin/equipmentFilingCancellation/uploadFile',
            required: false,
            overwriteInitial: false,
            maxFileSize: 3072,
            maxFilesNum: 1,
            showPreview: false,
            showUpload: true,
            showCaption: true,
            language: 'zh',
            uploadAsync: true
        }).on("fileuploaded", function (event, data) {
            var _data = data.response.data;
            _data.fileType = type;
            addFile(_data, type);
        });
    })

    function del(a, fileConnect, ed) {
        $.ajax({
            url: "/admin/filingFile/delete",
            data: {ids: ed},
            type: "POST",
            success: function (res) {
                if (res.code == 0) {
                    layer.msg("删除成功");
                    var eld = $("#" + fileConnect).val();
                    var fileIdArray;
                    if (eld == '') {
                        fileIdArray = [];
                    } else {
                        fileIdArray = (eld + "").split(",");
                    }
                    arrayDelete(fileIdArray, ed);
                    var ids = fileIdArray.join(",").replace(/undefined\,/, "");
                    $("#" + fileConnect).val(ids).change();
                    $(a).parents("tr").remove();
                } else {
                    layer.msg("删除失败")
                }
            }
        })
    }


    function arrayDelete(ids, id) {
        var index = ids.indexOf(id);
        if (index > -1) {
            ids.splice(index, 1);
        }
    }

    if ($("#clickOther").prop("checked")) {
        $("#otherReason").prop("disabled", false);
        $("#otherReasonLabel").addClass('require')
        $("#otherReason").val($("#clickOther").val().substring($("#clickOther").val().indexOf("7.") + 2, $("#clickOther").val().length));
    }
    $("#clickOther").click(function () {
        if ($("#clickOther").prop("checked")) {
            $("#otherReason").prop("disabled", false);
            $("#otherReasonLabel").addClass('require')

            $('#form-edit').data('bootstrapValidator').enableFieldValidators('otherReason', true);
        } else {
            $('#form-edit').data('bootstrapValidator').enableFieldValidators('otherReason', false);
            $("#otherReason").prop("disabled", true);
            $(this).val("其他原因");
            $("#otherReason").val("");
            $("#otherReasonLabel").removeClass('require')
        }
    })
    $("#otherReason").blur(function () {
        if ($("#clickOther").prop("checked")) {
            $("#clickOther").val("7." + $(this).val());
        } else {
            return;
        }
    })

    /**
     * form 表单提交
     * @param url
     * @param data
     * @param success
     * @param error
     */
    function form(url, data, success, error) {
        $.ajax({
            url: url,
            type: 'POST',
            data: data,
            contentType: false,
            processData: false,
            success: function (data) {
                success(data)
            },
            error: function (data) {
                error(data)
            }
        })
    }

    var index = parent.layer.getFrameIndex(window.name);

    function formSave() {
        var formData = new FormData($("#form-edit")[0]);
        var bootstrapValidator = $('#form-edit').data('bootstrapValidator');
        bootstrapValidator.validate();
        if (bootstrapValidator.isValid()) {
            form('/admin/equipmentFilingCancellation/update', formData, function (res) {
                if (res.code == 0) {
                    layer.msg("注销申请提交成功", {time: 1000}, function () {
                        parent.location.reload();
                        parent.layer.close(index);
                    });
                } else {
                    layer.msg(res.msg, {time: 1000, icon: 5});
                }
            }, function (error) {
                layer.msg("系统异常！");
            })
        }

    }

    $('#form-edit').bootstrapValidator({
        message: '输入值不满足要求',
        excluded: [':disabled', ':hidden'],
        //校验图标
        // feedbackIcons: {
        //     valid: 'glyphicon glyphicon-ok',
        //     invalid: 'glyphicon glyphicon-remove',
        //     validating: 'glyphicon glyphicon-refresh'
        // },
        verbose: false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
        fields: {
            filingCode: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    },
                    remote: {
                        url: "/admin/equipmentFilingCancellation/checkUnique",
                        data: function (validator) {
                            return {
                                filingCode: $('#filingCode').val(),
                                id: $('#id').val()
                            }
                        },
                        message: "备案编号已存在,不能重复提交",
                        delay: 2000,
                        type: "POST",
                    }
                }
            },
            deviceName: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            specificationModel: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            manufacturer: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            factoryTime: {
                trigger: "change",
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            manufacturingLicenseNumber: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            factoryLicenseNumber: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            purchaseTime: {
                trigger: "change",
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            propertyUnit: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            unitAddress: {
                group: '.col-xs-10',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            legalRepresentative: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            legalRepresentativeContactNumber: {
                group: '.col-xs-4',
                validators: {
                    notEmpty: {
                        message: '必填'
                    },
                    regexp: {
                        regexp: /^1\d{10}$/,
                        message: '手机号格式错误'
                    }
                }
            },
            cancellationReason: {
                group: '.col-xs-10    ',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            },
            otherReason: {
                enabled: false,
                group: '.col-xs-10    ',
                validators: {
                    notEmpty: {
                        message: '必填'
                    }
                }
            }
        }
    });
</script>

</body>
</html>
