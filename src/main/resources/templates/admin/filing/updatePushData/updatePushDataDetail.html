<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="更新全国数据详情"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="updatePushDataDetail" class="container-fluid" >
    <form id="updatePushData-form-edit" class="form-horizontal" autocomplete="off" th:object="${updatePushData}">
        <input type="hidden" id="id" name="id" th:value="*{id}"/>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="businessDataId" class="col-sm-4 control-label require">业务数据ID</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="businessDataId" name="businessDataId" th:value="*{businessDataId}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="certId" class="col-sm-4 control-label require">证照标识</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="certId" name="certId" th:value="*{certId}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="areaCode" class="col-sm-4 control-label require">行政区划代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="areaCode" name="areaCode" th:value="*{areaCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="certNum" class="col-sm-4 control-label require">证照编号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="certNum" name="certNum" th:value="*{certNum}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="deviceCategoryCode" class="col-sm-4 control-label require">类别代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="deviceCategoryCode" name="deviceCategoryCode" th:value="*{deviceCategoryCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="deviceModel" class="col-sm-4 control-label require">规格型号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="deviceModel" name="deviceModel" th:value="*{deviceModel}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="factoryNum" class="col-sm-4 control-label require">出厂编号</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="factoryNum" name="factoryNum" th:value="*{factoryNum}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="manufactureCorpCode" class="col-sm-4 control-label require">制造单位统一社会信用代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="manufactureCorpCode" name="manufactureCorpCode" th:value="*{manufactureCorpCode}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="certStatus" class="col-sm-4 control-label require">证书状态代码</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="certStatus" name="certStatus" th:value="*{certStatus}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="certStatusDescription" class="col-sm-4 control-label require">证书状态描述</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="certStatusDescription" name="certStatusDescription" th:value="*{certStatusDescription}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="operateType" class="col-sm-4 control-label require">操作类型</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="operateType" name="operateType" th:value="*{operateType}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="creatDate" class="col-sm-4 control-label require">创建时间</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="creatDate" name="creatDate" th:value="*{creatDate}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="creatAt" class="col-sm-4 control-label require">创建人</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="creatAt" name="creatAt" th:value="*{creatAt}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="pushFlag" class="col-sm-4 control-label require">推送标识</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="pushFlag" name="pushFlag" th:value="*{pushFlag}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="pushDate" class="col-sm-4 control-label require">push_date</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="pushDate" name="pushDate" th:value="*{pushDate}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
        <div class="box-footer col-md-6 col-md-offset-5">
            <button type="button" class="btn btn-info pull-right" onclick="formClose()">关闭</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script type="text/javascript">
    function formClose() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    }
</script>
</body>
</html>
