<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head >
    <title th:text="更新全国数据表单"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body >

    <div class="container-fluid">
                <!-- form start -->
                <form id="form" class="form-horizontal">
                    <input type="hidden" id="id" name="id">
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="businessDataId" class="col-sm-2 control-label">业务数据ID</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="businessDataId" name="businessDataId" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="certId" class="col-sm-2 control-label">证照标识</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="certId" name="certId" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="areaCode" class="col-sm-2 control-label">行政区划代码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="areaCode" name="areaCode" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="certNum" class="col-sm-2 control-label">证照编号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="certNum" name="certNum" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="deviceCategoryCode" class="col-sm-2 control-label">类别代码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="deviceCategoryCode" name="deviceCategoryCode" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="deviceModel" class="col-sm-2 control-label">规格型号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="deviceModel" name="deviceModel" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="factoryNum" class="col-sm-2 control-label">出厂编号</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="factoryNum" name="factoryNum" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="manufactureCorpCode" class="col-sm-2 control-label">制造单位统一社会信用代码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="manufactureCorpCode" name="manufactureCorpCode" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="certStatus" class="col-sm-2 control-label">证书状态代码</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="certStatus" name="certStatus" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="certStatusDescription" class="col-sm-2 control-label">证书状态描述</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="certStatusDescription" name="certStatusDescription" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="operateType" class="col-sm-2 control-label">操作类型</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="operateType" name="operateType" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="creatDate" class="col-sm-2 control-label">创建时间</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="creatDate" name="creatDate" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="creatAt" class="col-sm-2 control-label">创建人</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="creatAt" name="creatAt" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="pushFlag" class="col-sm-2 control-label">推送标识</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="pushFlag" name="pushFlag" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="pushDate" class="col-sm-2 control-label">push_date</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="pushDate" name="pushDate" autocomplete="off">
                                    </div>
                                </div>
                            </div>


                        <div class="form-group">
                            <button type="button" class="btn btn-info col-sm-offset-6" onclick="formSave()">提交</button>
                        </div>
                          <!-- /.box-body -->
                </form>
              <!-- customer form end -->
    </div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";
    var index = parent.layer.getFrameIndex(window.name);
    initValidator();
    function formSave(){
        var bootstrapValidator = $('#form').data('bootstrapValidator').validate();
        if(bootstrapValidator.isValid()) {
            savepop('/admin/updatePushData/add' ,'form','saveBtn')
        }
    }

    function initValidator() {
        $('#form').bootstrapValidator({
            message: '输入值不满足要求',
            excluded : [':disabled',':hidden'],
            verbose:false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
        businessDataId: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/updatePushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        businessDataId: $('#businessDataId').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        certId: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/updatePushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        certId: $('#certId').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        areaCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/updatePushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        areaCode: $('#areaCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        certNum: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/updatePushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        certNum: $('#certNum').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        deviceCategoryCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/updatePushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        deviceCategoryCode: $('#deviceCategoryCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        deviceModel: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/updatePushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        deviceModel: $('#deviceModel').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        factoryNum: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/updatePushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        factoryNum: $('#factoryNum').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        manufactureCorpCode: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/updatePushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        manufactureCorpCode: $('#manufactureCorpCode').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        certStatus: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/updatePushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        certStatus: $('#certStatus').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        certStatusDescription: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/updatePushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        certStatusDescription: $('#certStatusDescription').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        operateType: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/updatePushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        operateType: $('#operateType').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        creatDate: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 6,
                    message: '不能超过6位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/updatePushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        creatDate: $('#creatDate').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        creatAt: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/updatePushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        creatAt: $('#creatAt').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        pushFlag: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 100,
                    message: '不能超过100位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/updatePushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        pushFlag: $('#pushFlag').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
        pushDate: {
            validators: {
                notEmpty: {
                    message: '必填'
                },
                stringLength: {
                    max: 6,
                    message: '不能超过6位'
                },
                regexp: {
                    regexp: /^[a-zA-Z0-9_\.]+$/,
                        message: '由数字字母下划线和.组成'
                },
                remote: {
                    // 验证地址
                    url: '/updatePushData/checkUnique',
                        //自定义提交数据，默认值提交当前input value
                        data: function(validator) {
                        return {
                        pushDate: $('#pushDate').val(),
                            id: $('#id').val()
                    };
                    },
                    message: '已存在',
                        // 每输入一个字符，就发ajax请求，服务器压力还是太大，设置1秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
                        delay: 1000,
                        //请求方式
                        type: 'POST'
                }
            }
        },
    }
    });
    }
</script>
</body>
</html>