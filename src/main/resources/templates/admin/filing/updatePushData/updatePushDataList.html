<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="更新全国数据"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-sm-1" for="searchName">名称</label>
                    <div class="col-sm-3">
                        <input type="text" class="form-control col-sm-10" id="searchName" name="name"
                               placeholder="请输入名称">
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="restForm('searchForm')"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="formSearch('updatePushDatatable','/admin/updatePushData/list' ,'searchForm')">
                            <i class="fa fa-search"></i>查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <table id="updatePushDatatable"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";

    var addFlag = [[${@permission.hasPermi('updatePushData:add')}]];
    var editFlag = [[${@permission.hasPermi('updatePushData:edite')}]];
    var delFlag = [[${@permission.hasPermi('updatePushData:delete')}]];
    var designFlag = [[${@permission.hasPermi('updatePushData:design')}]];

    $(function () {
        var columns = [
            {
                field: 'ck',
                checkbox: true,
                align: 'center',
                valign: 'middle'
            },
            {
                field: "businessDataId",
                title: "业务数据ID",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "certId",
                title: "证照标识",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "areaCode",
                title: "行政区划代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "certNum",
                title: "证照编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "deviceCategoryCode",
                title: "类别代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "deviceModel",
                title: "规格型号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "factoryNum",
                title: "出厂编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "manufactureCorpCode",
                title: "制造单位统一社会信用代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "certStatus",
                title: "证书状态代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "certStatusDescription",
                title: "证书状态描述",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "operateType",
                title: "操作类型",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "creatDate",
                title: "创建时间",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "creatAt",
                title: "创建人",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "pushFlag",
                title: "推送标识",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "pushDate",
                title: "push_date",
                align: 'center',
                valign: 'middle'
            },
            {
                field: 'createTime',
                title: '创建时间',
                align: 'center',
                valign: 'middle'
            },
            {
                field: 'id',
                title: '操作',
                align: 'center',
                valign: 'middle',
                cellStyle:{
                    css:{"white-space":"nowrap"}
                },
                formatter: function (value, row, index) {
                    var actions = [];
                    if ("hidden" != editFlag) {
                        actions.push('<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="updatelayer(\'' + value + '\',\'编辑\',\'/admin/updatePushData/edit/\')"><i class="fa fa-edit"></i>编辑</button>');
                    }
                    if ("hidden" != designFlag) {
                        actions.push('<button id="btn_detail" type="button" class="btn btn-success  btn-xs" onclick="detaillayer(\'' + value + '\',\'详情\',\'/admin/updatePushData/detail/\')"><i class="fa fa-eye"></i>详情</button>');
                    }
                    if ("hidden" != delFlag) {
                        actions.push('<button type="button" class="btn btn-danger  btn-xs" onclick="deletes(\'/admin/updatePushData/delete\',\'' + value + '\')" ><i class="fa fa-trash-o"></i>删除</button>');
                    }
                    return actions.join('');
                }
            }
        ];
        var toolbar = [];
        toolbar.push('<div id="toolbar" class="btn-group">');
        if("hidden" != addFlag){
            toolbar.push('<button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="poplayer(2,\'新增\',\'/admin/updatePushData/toForm\')"><i class="fa fa-plus"></i>新增</button>');
        }
        if("hidden" != delFlag){
            toolbar.push('<button id="btn_delete" type="button" class="btn btn-danger btn-xs" onclick="deletes(\'/admin/updatePushData/delete\')" ><i class="fa fa-trash-o"></i>删除</button>');
        }
        toolbar.push('</div>');
        createBootstrapTable($("#updatePushDatatable"), '/admin/updatePushData/list', columns, false, toolbar.join(''), 'server');
    });
</script>
</body>
</html>