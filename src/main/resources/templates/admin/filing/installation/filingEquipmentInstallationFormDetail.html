<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备安装登记流程表详情"></title>
    <th:block th:include="admin/include/head :: head"/>
    <style>
        .container-fluid {
            padding-top: 15px;
        }

        .box-footer {
            width: max-content;
            margin: 0 auto;
        }

        .rejectButton {
            text-align: center;
            margin: 10px 0;
        }

        .title {
            font-size: 14px;
            font-weight: bold;
            padding: 10px;
            margin-bottom: 10px;
            margin-right: 12px;
            background: #e1f0f9;
            border-radius: 4px;
            position: relative;

        }

        .title::before {
            content: '';
            width: 4px;
            height: 100%;
            background: #3c8dbc;
            position: absolute;
            left: 0;
            top: 0;
        }

        .btn-info {
            width: 130px;
            height: 40px;
        }

        .print {
            width: 160px;
        }
    </style>
</head>
<body>
<div id="filingEquipmentInstallationFormDetail" class="container-fluid">
    <form id="filingEquipmentInstallationForm-form-edit" class="form-horizontal" autocomplete="off"
          th:object="${filingEquipmentInstallationForm}">
        <input type="hidden" id="id" name="id" th:value="*{id}"/>
        <!--                <input type="hidden" class="form-control" id="userId" name="userId" th:value="*{userId}"/>-->
        <div class="col-md-12" style="margin-top: 10px">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="applicationDate" class="col-sm-3 control-label require">申请日期</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="applicationDate" name="applicationDate" autocomplete="off" disabled th:value="*{#dates.format(applicationDate,'YYYY-MM-dd')}">
                    </div>
                    <!--                    <div class="col-sm-9">-->
                    <!--                        <input type="text" class="form-control " name="applicationDate" id="applicationDate" th:value="*{applicationDate}" disabled>-->
                    <!--                        <input type="hidden" class="form-control " id="now" name="applicationDate" autocomplete="off">-->
                    <!--                    </div>-->
                </div>
            </div>
            <div class="col-md-6">
                <input type="hidden" class="form-control" id="userId" name="userId" th:value="*{userId}"/>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">设备信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentFilingCode" class="col-sm-3 control-label require">设备备案编号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="equipmentFilingCode" name="equipmentFilingCode"
                               th:value="*{equipmentFilingCode}"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="equipmentType" class="col-sm-3 control-label require">设备类型</label>
                    <div class="col-sm-9">
                        <select disabled class="form-control"  th:switch="*{equipmentType}" id="equipmentType" name="equipmentType" autocomplete="off">
                            <option value="T" th:case="T">塔式起重机</option>
                            <option value="S" th:case="S">施工升降机(不含物料提升机)</option>
                            <option value="W" th:case="W">物料提升机</option>
                            <option value="Q" th:case="Q">其他起重机械</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="specificationModel" class="col-sm-3 control-label require">规格型号</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="specificationModel" name="specificationModel"
                               th:value="*{specificationModel}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">工程信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="engineeringName" class="col-sm-3 control-label require">工程名称</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="engineeringName" name="engineeringName"
                               th:value="*{engineeringName}"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="constructionSite" class="col-sm-3 control-label require">工地名称</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="constructionSite" name="constructionSite"
                               th:value="*{constructionSite}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="engineeringAreaCode" class="col-sm-3 control-label require">工程信息属地</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="engineeringAreaCode" name="engineeringAreaCode" autocomplete="off" th:value="*{remarks}">
                        <!--                        <input type="hidden" id="remarks" name="remarks" th:value="*{remarks}">-->
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="engineeringAddressDetail" class="col-sm-3 control-label require">工程信息详细地址</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="engineeringAddressDetail" name="engineeringAddressDetail" th:value="*{engineeringAddressDetail}"
                               autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="jobContent" class="col-sm-3 control-label require">工作内容</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="jobContent" name="jobContent" th:value="*{jobContent}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">安装单位信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationUnit" class="col-sm-3 control-label require">单位名称</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="installationUnit" name="installationUnit"
                               th:value="*{installationUnit}"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationTime" class="col-sm-3 control-label require">安装时间</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control datepicker" id="installationTime" name="installationTime"
                               th:value="*{#dates.format(installationTime,'YYYY-MM-dd')}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationHeight" class="col-sm-3 control-label require">安装高度（米）</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="installationHeight" name="installationHeight"
                               th:value="*{installationHeight}" placeholder="单位：米"/>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationLeader" class="col-sm-3 control-label require">安装负责人</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="installationLeader" name="installationLeader"
                               th:value="*{installationLeader}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="installationLeaderContactNumber" class="col-sm-3 control-label require">安装负责人联系电话</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="installationLeaderContactNumber" name="installationLeaderContactNumber"
                               th:value="*{installationLeaderContactNumber}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="techLeader" class="col-sm-3 control-label require">技术负责人</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="techLeader" name="techLeader" th:value="*{techLeader}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="techLeaderContactNumber" class="col-sm-3 control-label require">技术负责人联系电话</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="techLeaderContactNumber" name="techLeaderContactNumber" th:value="*{techLeaderContactNumber}">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="siteLeader" class="col-sm-3 control-label require">现场负责人</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="siteLeader" name="siteLeader" th:value="*{siteLeader}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="siteLeaderContactNumber" class="col-sm-3 control-label require">现场负责人联系电话</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control" id="siteLeaderContactNumber" name="siteLeaderContactNumber" th:value="*{siteLeaderContactNumber}">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">审核信息</div>
        </div>
        <div class="col-md-12">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="col-sm-3 control-label require">审核状态</label>
                    <div class="col-sm-9">
                        <input disabled type="text" class="form-control"
                               name="auditStatus" th:if="*{auditStatus == 0}" value="未审核"/>
                        <input disabled type="text" class="form-control"
                               name="auditStatus" th:if="*{auditStatus == 1}" value="审核通过"/>
                        <input disabled type="text" class="form-control"
                               name="auditStatus" th:if="*{auditStatus == 2}" value="驳回"/>
                        <input disabled type="text" class="form-control"
                               name="auditStatus" th:unless="*{auditStatus == 0 || auditStatus == 1 || auditStatus == 2}" value="未知"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="title">附件信息</div>
        </div>
        <table class="table table-striped table-bordered" th:object="${filingEquipmentInstallationForm}">
            <tbody>
            <tr>
                <th>起重机安装告知书</th>
                <td class="files" id="notificationBookFilePath"></td>
                <th>起重机备案证</th>
                <td class="files" id="equipmentFilingLicenseFilePath"></td>
            </tr>
            <tr>
                <th>起重机安装确认表</th>
                <td class="files" id="auditFormFilePath"></td>
                <th>企业资质证书</th>
                <td class="files" id="qualifiLicenseFilePath"></td>
            </tr>
            <tr>
                <th>安全生产许可证副本</th>
                <td class="files" id="licenseSafetyPermitFilePath"></td>
                <th>安装单位特种作业人员名单</th>
                <td class="files" id="operatorsListFilePath"></td>
            </tr>
            <tr>
                <th>安装单位特种作业人员证书</th>
                <td class="files" id="operatorsLicenseFilePath"></td>
                <th>设备安装专项施工方案</th>
                <td class="files" id="specialConstructionSchemeFilePath"></td>
            </tr>
            <tr>
                <th>安装合同</th>
                <td class="files" id="installationContractFilePath"></td>
                <th>安全协议</th>
                <td class="files" id="securityProtocolFilePath"></td>
            </tr>
            <tr>
                <th>应急救援预案</th>
                <td class="files" id="emergencyRescuePlanFilePath"></td>
                <th>设备产品合格证明</th>
                <td class="files" id="equipmentQualificationCertificateFilePath"></td>
            </tr>
            <tr>
                <th>操作人员资格证书</th>
                <td class="files" id="operatorQualificationCertificateFilePath"></td>
            </tr>
            </tbody>
        </table>
    </form>

    <div id="examine">
    </div>
    <div id="reject" hidden>
        <label for="rejectReason">请输入退回原因</label><br>
        <textarea rows="4" id="rejectReason" name="rejectReason" wrap="hard" style="width: 100%"></textarea>
        <div class="rejectButton">
            <button type="button" class="btn btn-info" onclick="saveReject()">提交</button>
            <button type="button" class="btn btn-info" onclick="reback()">返回</button>

        </div>
    </div>
    <div class="box-footer col-md-6 col-md-offset-5">
    </div>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>

<script th:inline="javascript" type="text/javascript">
    let a = [[${equipmentType}]]
    console.log('a',a)
    let examiner = [[${@permission.hasPermi('install:examine')}]];
    let index = parent.layer.getFrameIndex(window.name);
    var audit = [[${filingEquipmentInstallationForm.auditStatus}]];
    // console.log('audit',audit)
    let id = [[${filingEquipmentInstallationForm}]].id;
    let files = [[${files}]];
    $("#equipmentType").val([[${filingEquipmentInstallationForm}]].equipmentType)

    function formClose() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    }

    $(function () {
        $.each($(".files"), function (index, item) {
            var cons = item.getAttribute("id");
            var html = "";
            for (var i = 0; i < files.length; i++) {
                var filePath = files[i];
                if (filePath.connectId.indexOf(cons) > 0) {
                    html += "<a href='" + filePath.fileUrl + "' download='" + filePath.fileName + "'>" + filePath.fileName + "</a><br>";
                }
            }
            $("#" + cons).html(html);
        });
    })
    $(function () {
        if (audit == 0) {
            $("#audit").val("未审核");
        }
        if (audit == 1) {
            $("#audit").val("审核通过");
        }
        if (audit == 2) {
            $("#audit").val("驳回");
        }
    })
    $(function () {
        if (examiner != "hidden" && audit == "0") {
            var html = "";
            html += "<div>" +
                "        <div class=\"box-footer\">" +
                "            <button type=\"button\" class=\"btn btn-info\" onclick=\"examine()\">通过</button>" +
                "            <button type=\"button\" class=\"btn btn-info\" onclick=\"reject()\">驳回</button>" +
                "            <button type=\"button\" class=\"btn btn-info \" onclick=\"popsaveclose()\">关闭</button>" +
                "            </div>" +
                "         </div>"
            $('#examine').html(html);
        }
        if ('1' == audit) {
            var html = "";
            html += "<div>" +
                "        <div class=\"box-footer\">" +
                "            <button type=\"button\" class=\"btn btn-info print \"><a target='_parent' href=\"/admin/filingEquipmentInstallationForm/license?id=" + id + "\">下载告知书和回复书</a></button>" +
                "            <button type=\"button\" class=\"btn btn-info \" onclick=\"popsaveclose()\">关闭</button>" +
                "            </div>" +
                "         </div>"
            $('#examine').html(html);
        }
    });

    function reject() {
        $("#examine").hide();
        $("#reject").show();
    }

    function popsaveclose() {
        parent.location.reload();
        parent.layer.close(index);
    }

    function reback() {
        $("#examine").show();
        $("#reject").hide();
    }

    function saveReject() {
        let rejectReason = $("#rejectReason").val();
        $.ajax({
            url: "/admin/filingEquipmentInstallationForm/reject",
            data: {id: id, rejectReason: rejectReason},
            type: "POST",
            dataType: "JSON",
            success: function (res) {
                if (res.code == 0) {
                    layer.msg(res.msg, {time: 1000});
                    setTimeout(() => {
                        popsaveclose();
                    }, 1000)
                } else {
                    layer.msg(res.msg);
                }

            }
        })
    }

    function examine() {
        layer.open({
            type: 0,
            title: '审核通过',
            content: '确认通过当前申请？',
            btn: ['确认', '取消'],
            yes: function (index, layero) {
                $.ajax({
                    url: '/admin/filingEquipmentInstallationForm/examine',
                    method: 'post',
                    dataType: "JSON",
                    data: {
                        id: id
                    },
                    success: function (res) {
                        if (res.code == 0) {
                            layer.msg(res.msg, {time: 1000}, function () {
                                popsaveclose();

                            });
                        } else {
                            layer.msg(res.msg);
                        }
                    }
                })
            }
        })
        // $.ajax({
        //     url: "/admin/filingEquipmentInstallationForm/examine",
        //     data: {id: id},
        //     type: "POST",
        //     dataType: "JSON",
        //     success: function (res) {
        //         if (res.code == 0) {
        //             layer.msg(res.msg, {time: 1000});
        //             popsaveclose();
        //         } else {
        //             layer.msg(res.msg);
        //         }
        //     }
        // })
    }
</script>
</body>
</html>
