<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="设备安装登记流程表"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-sm-1" for="searchName">设备名称</label>
                    <div class="col-sm-2">
                        <input type="text" class="form-control col-sm-10" id="searchName" name="deviceName"
                               placeholder="请输入名称">
                    </div>
                    <label class="control-label col-sm-1" for="searchName">登记类型</label>
                    <div class="col-sm-2">
                        <select class="form-control col-sm-10" id="workType" name="workType">
                            <option value="0">安装</option>
                            <option value="1">拆卸</option>
                        </select>
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="restForm('searchForm')"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="formSearch('InstallationFormtable','/admin/filingEquipmentInstallationForm/allList' ,'searchForm')">
                            <i class="fa fa-search"></i>查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <table id="InstallationFormtable"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";
    var addFlag = [[${@permission.hasPermi('install:add')}]];
    var editFlag = [[${@permission.hasPermi('install:edite')}]];
    var delFlag = [[${@permission.hasPermi('install:delete')}]];
    var designFlag = [[${@permission.hasPermi('install:view')}]];
    $(function () {
        var columns = [
            {
                field: "userId",
                title: "申请企业社会统一信用代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "applicationDate",
                title: "申请日期",
                align: 'center',
                valign: 'middle',
                formatter:function (value) {
                    return value;
                }
            },
            {
                field: "equipmentFilingCode",
                title: "设备备案编号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "deviceName",
                title: "设备名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "specificationModel",
                title: "规格型号",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "engineeringName",
                title: "工程名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installationUnit",
                title: "作业单位",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "siteLeader",
                title: "现场负责人",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "installationTime",
                title: "作业时间",
                align: 'center',
                valign: 'middle',
                formatter:function (value) {   return value;
                }
            },
            {
                field: "jobContent",
                title: "工作内容",
                align: 'center',
                valign: 'middle'
            }
        ];
        var toolbar = [];
        toolbar.push('<div id="toolbar" class="btn-group">');
        toolbar.push('</div>');
        createBootstrapTable($("#InstallationFormtable"), '/admin/filingEquipmentInstallationForm/allList', columns, false, toolbar.join(''), 'server');
    });
</script>
</body>
</html>