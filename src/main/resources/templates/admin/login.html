<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>建筑起重机械设备使用登记和备案系统</title>
    <meta name=renderer content=webkit>
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/iCheck/all.css}">
    <link rel="stylesheet" th:href="@{/resources/bootstrap/css/bootstrap.min.css}">
    <link rel="stylesheet" th:href="@{/resources/sys/login.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/layer/theme/default/layer.css}">
    <script language="javascript">
        if (top.location != self.location) {
            top.location = self.location;
        }
    </script>
    <style>
        .footer{
            background:rgba(0,0,0,0.2);
            position: fixed;
            bottom:0;
            width: 100%;
            display: flex;
            justify-content: center;
            gap: 30px;
        }
        .messageContainer{
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>
<body>
<div class="header">
    <div class="con">
        <h2>河南省建筑起重机械管理信息系统</h2>
    </div>
</div>
<div class="main">
    <div class="con">
        <div class="left" style="display: none">
            <div class="title">
                <!-- Nav tabs -->
                <ul class="nav nav-tabs">
                    <li class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab">注销设备公示</a></li>
                    <li><a href="#profile" aria-controls="profile" role="tab" data-toggle="tab">注销设备查询</a></li>
                </ul>
            </div>
            <div class="tab-content">
                <div role="tabpanel" class="tab-pane active" id="home">
                    <div class="caption">
                        <ul>
                            <li><p>注销日期</p>
                                <p>备案编号</p>
                                <p>设备类型</p>
                                <p>规格型号</p></li>
                        </ul>
                    </div>
                    <div id="demo2" class="scroll-text">
                        <ul id="cancel">
                        </ul>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane active" id="tech">
                    <div class="caption">
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane" id="profile" style="padding:20px; height: 418px;">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <div class="col-sm-5">
                                <input type="text" class="form-control" placeholder="请输入完整的备案编号">
                            </div>
                            <label class="col-sm-5 control-label"
                                   style="text-align: left; color:red;">请输入完整的备案编号</label>
                        </div>
                    </form>
                    <div class="details">
                        <p>注销日期：<span id="cancelDate"></span></p>
                        <p>备案编号：<span id="filingCode"></span></p>
                        <p>设备类型：<span id="equipmentType"></span></p>
                        <p>规格型号：<span id="specificationModel"></span></p>
                        <p>产权单位：<span id="unit"></span></p>
                    </div>
                </div>
            </div>
        </div>
        <div class="right">
            <div class="form">
                <h3>用户登录</h3>
                <form class="form-group">
                    <ul>
                        <li><input placeholder="用户名" id="name" type="text" name="name" required></li>
                        <li><input placeholder="登陆密码" id="password" type="password" name="password" required></li>
                        <li>
                            <input type="text" id="vcode" name="vcode" class="form-control" placeholder="请输入验证码">
                            <img th:src="@{/captcha/captchaImage(type=math)}" class="imgcode"/>
                        </li>
                        <li>
                            <div style="color:#008eff;">
                                <input id="checkbox" type="checkbox" name="">&nbsp;&nbsp;记住密码
                            </div>
                            <a href="/admin/toRegister" style="color:#008eff;">注册账号</a>
                        </li>
                        <li>
                            <button id="login" type="button" class="btn btn-block btn-flat login-btn">登录</button>
                        </li>
                    </ul>
                    <li class="link"><a target="_blank" href="/checkMessage/filingInquiry_bf" style="color: #0004ff;">备案信息查询</a>
                        <a target="_blank" href="/checkMessage/registrationInquiry_bf"
                           style="color: #0004ff;">使用登记信息查询</a>
                        <a target="_blank" href="/checkMessage/installationInquiry_bf"
                           style="color: #0004ff;">安拆登记信息查询</a>
                    </li>

                    </ul>
                </form>
            </div>
        </div>
    </div>
</div>
<div class="footer" >
    <div class="messageContainer">
        <span>
            技术支持：天筑科技股份有限公司
        </span>
        <span>
            联系我们：0371-63284897；0371-55673016
        </span>
        <span>
            温馨提示：建议使用<a href="http://down.360safe.com/cse/360cse_9.5.0.138.exe" style="color: #FF0000;text-decoration: underline ">360极速浏览器</a>或谷歌浏览器!
        </span>
    </div>
<!--    <div class="imageContaienr">-->
<!--        <img style="width: 80px" th:src="@{/common/img/weixinImage.png}"/>-->
<!--    </div>-->
</div>
<div id="changePassword" class="col-md-12 box" style="display: none;">
    <form id="form" class="form-horizontal">
        <input type="hidden" id="id" name="id">
        <div class="box-body">
            <div class="col-sm-12">
                <div class="form-group">
                    <label for="oldPassword" class="col-sm-2 control-label">旧密码</label>
                    <div class="col-sm-10">
                        <input type="hidden" class="form-control" id="oldPassword" name="oldPassword" placeholder="填写旧密码" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-sm-12">
                <div class="form-group">
                    <label for="newPassword" class="col-sm-2 control-label" style="color: #00a65a">新密码</label>
                    <div class="col-sm-10">
                        <input type="password" class="form-control" id="newPassword" name="password" placeholder="填写新密码" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-sm-12">
                <div class="form-group">
                    <label for="newPassword2" class="col-sm-2 control-label" style="color: #00a65a">确认密码</label>
                    <div class="col-sm-10">
                        <input type="password" class="form-control" id="newPassword2" placeholder="再次填写新密码" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
</body>
<script th:src="@{/resources/awi/plugins/jQuery/jquery-2.2.3.min.js}"></script>
<script th:src="@{/resources/bootstrap/js/bootstrap.min.js}"></script>
<script th:src="@{/resources/awi/plugins/bootstrapvalidator/js/bootstrapValidator.js}"></script>
<script th:src="@{/resources/awi/plugins/bootstrapvalidator/js/language/zh_CN.js}"></script>
<script th:src="@{/resources/awi/plugins/iCheck/icheck.min.js}"></script>
<script th:src="@{/resources/awi/plugins/layer/layer.js}"></script>
<script th:src="@{/resources/common/common.js}"></script>
<script th:src="@{/resources/sys/login.js}"></script>
<script th:src="@{/resources/sys/jquery.scrollbox.js}"></script>
<script th:inline="javascript">
    $(function () {
        $.ajax({
            url: "/admin/filingEquipmentFiling/indexList",
            type: "POST",
            success: function (res) {
                html = "";
                var list = res;
                for (var i = 0; i < list.length; i++) {
                    var data = list[i];
                    console.log(data);
                    html += "<li><p>" + data.updateTime + "</p><p>" + data.filingCode + "</p><p>" + data.equipmentType + "</p><p>" + data.specificationModel + "</p></li>\n";
                }
                $("#cancel").html(html);
            }
        })
    })

    $('#demo2').scrollbox({
        linear: true,
        step: 1,
        delay: 0,
        speed: 100
    });
    var ctx = [[@{/}]]; var captchaType = [[math]];
</script>

</html>

