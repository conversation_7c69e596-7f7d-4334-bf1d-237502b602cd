<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head >
    <title>岗位管理</title>
    <link th:replace="admin/include/head :: head">
</head>
<body>
<section class="content">
    <div class="box">
        <div class="box-body">
            <form class="form-inline" id="searchForm">
                <div class="form-group">
                    <label for="searchName"> 岗位名称： </label>
                    <input type="text" class="form-control" id="searchName" name="postName" placeholder="请输入岗位名称"
                           autocomplete="off">
                </div>
                <button class="btn  btn-default  btn-sm" type="button" onclick="restForm()">重置</button>
                <button class="btn  btn-success btn-sm" type="button" onclick="formSearch()">搜索</button>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <table id="table"></table>
        </div>
    </div>
</section>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/sys/sys_post.js}"></script>
</body>

<div id="post_form" class="col-md-12 box" style="display: none">

    <input type="hidden" id="postId">
    <form id="form" class="form-horizontal">
        <div class="box-body">
            <div class="form-group">
                <label for="postName" class="control-label col-md-2">岗位名称</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="postName" name="postName" placeholder="岗位名称" autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="postCode" class="control-label col-md-2">岗位编码</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="postCode" name="postCode" placeholder="岗位编码" autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="postSort" class="control-label col-md-2">排序</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="postSort"  autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="status" class="control-label col-md-2">岗位状态</label>
                <div class="col-sm-10" id="status">
                    <input type="radio" name="status" value="0" class="flat-red" checked> 启用
                    <input type="radio" name="status" value="1" class="flat-red"> 停用
                </div>
            </div>
            <div class="form-group">
                <label for="remarks" class="col-md-2 control-label">备注</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="remarks" placeholder="备注" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="box-footer">
            <button type="button" class="btn btn-info pull-right" onclick="save()">提交</button>
        </div>
    </form>
</div>



</html>