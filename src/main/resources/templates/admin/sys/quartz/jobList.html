<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head >
    <title>定时任务</title>
    <link th:replace="admin/include/head :: head">
</head>
<body>
<section class="content">
    <div class="box">
        <div class="box-body">
            <form class="form-inline" id="searchForm">
                <div class="form-group">
                    <label for="jobName"> 任务名称： </label>
                    <input type="text" name="jobName" placeholder="请输入任务名称" autocomplete="off">
                </div>
                <button class="btn  btn-default  btn-sm" type="button" onclick="restForm('searchForm')">重置</button>
                <button class="btn  btn-success btn-sm" type="button"
                        onclick="formSearch('jobtable','/admin/job/list' ,'searchForm')">搜索
                </button>
                <button class="btn  btn-warning  btn-sm" type="button"
                        onclick="showSub('allJobListTable','/admin/job/queryAllJob','allJobList')">所有任务
                </button>
                <button class="btn  btn-danger btn-sm" type="button"
                        onclick="showSub('runJobListTable','/admin/job/queryRunJob','runJobList')">执行中任务
                </button>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <table id="jobtable">
            </table>
        </div>
    </div>

</section>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
</body>
<div class="col-md-12  box" id="allJobList" style="display: none">
    <table id="allJobListTable">
    </table>
</div>

<div class="col-md-12 box" id="runJobList" style="display: none">
    <table id="runJobListTable">
    </table>
</div>

<div id="jobform" class="col-md-12  box" style="display: none">

    <form id="form" class="form-horizontal required-validate">
        <div class="box-body">
            <div class="form-group">
                <label for="jobClassPath" class="col-sm-2 control-label">任务类</label>
                <div class="col-sm-10">
                    <input type="hidden" id="jobId" name="jobId">
                    <input type="text" class="form-control" id="jobClassPath"
                           name="jobClassPath" autocomplete="off"
                    >
                </div>
            </div>
            <div class="form-group">
                <label for="jobName" class="col-sm-2 control-label">任务名称</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="jobName"
                           name="jobName" autocomplete="off"
                    >
                </div>
            </div>

            <div class="form-group">
                <label for="jobGroup" class="col-sm-2 control-label">任务组名</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="jobGroup"
                           name="jobGroup" autocomplete="off">
                </div>
            </div>

            <!--
                        <div class="form-group">
                            <label for="methodName" class="col-sm-2 control-label">任务方法</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" id="methodName"
                                       name="methodName" autocomplete="off">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="methodParams" class="col-sm-2 control-label">方法参数</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" id="methodParams"
                                       name="methodParams" autocomplete="off">
                            </div>
                        </div>-->
            <div class="form-group">
                <label for="cronExpression" class="col-sm-2 control-label">执行表达式</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="cronExpression"
                           name="cronExpression" autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="misfirePolicy" class="col-sm-2 control-label">计划执行错误策略</label>
                <div class="col-sm-10">
                    <select id="misfirePolicy" name="misfirePolicy" class="select2">
                        <!--<option value="0">默认</option>-->
                        <!--<option value="1">继续</option>-->
                        <!--<option value="2">等待</option>-->
                        <!--<option value="3">放弃</option>-->
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="status" class="col-sm-2 control-label">状态</label>
                <div class="col-sm-10" id="status">
                    <!--<select id="status" name="status" class="select2">-->
                    <!--&lt;!&ndash;<option value="0">正常</option>&ndash;&gt;-->
                    <!--&lt;!&ndash;<option value="1">暂停</option>&ndash;&gt;-->
                    <!--</select>-->
                </div>
            </div>


            <div class="form-group">
                <label for="remarks" class="col-sm-2 control-label">备注信息</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="remarks"
                           name="remarks" autocomplete="off">
                </div>
            </div>

        </div>
        <div class="box-footer">
            <button type="button" class="btn btn-info pull-right" onclick="save('/admin/job/save' ,'form')">提交</button>
        </div>
    </form>
</div>

<script type="text/javascript">
    var select2;
    $(function () {
        select2 = $('.select2').select2()
        var columns = [
            {
                field: 'ck',
                checkbox: true
            },
            {
                field: 'jobId',
                title: '任务id',
                visible: false
            },
            {
                field: 'jobClassPath',
                title: '任务类'
            }, {
                field: 'jobName',
                title: '任务名称'
            }, {
                field: 'jobGroup',
                title: '任务组'
            },
            // {
            //     field: 'methodName',
            //     title: '方法名称'
            // },
            // {
            //     field: 'methodParams',
            //     title: '方法参数'
            // },
            {
                field: 'cronExpression',
                title: '执行批次'
            }, {
                field: 'misfirePolicy',
                title: '执行错误策略',
                formatter: function (value, row, index) {
                    return selectDictLabel(dataPolicy, value)
                }
            },
            {
                field: 'status',
                title: '状态',
                formatter: function (value, row, index) {
            return selectDictLabel(dataStatus, value)
        }
            },
            {
                field: 'createTime',
                title: '创建时间'
            }, {
                field: 'jobId',
                title: '操作',
                formatter: function (value, row, index) {
                    var actions = [];
                    actions.push('<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="updateJob(\'/admin/job/get\',\'' + value + '\',\'jobform\')"><i class="fa fa-edit"></i> 修改</button>');
                    actions.push('<button type="button" class="btn btn-success  btn-xs" onclick="execute(\'/admin/job/executePlan\',\'' + value + '\')" style="margin-left: 10px;"><i class="fa  fa-check"></i> 修改生效</button>');
                    actions.push('<button type="button" class="btn btn-danger  btn-xs" onclick="execute(\'/admin/job/runAJobNow\',\'' + value + '\')" style="margin-left: 10px;"><i class="fa fa-forward"></i> 立即执行一次</button>');
                    if(0==row.status){
                        actions.push('<button type="button" class="btn btn-warning  btn-xs" onclick="execute(\'/admin/job/pauseJob\',\'' + value + '\')" style="margin-left: 10px;"><i class="fa fa-pause-circle"></i> 暂停</button>');
                    }else{
                        actions.push('<button type="button" class="btn btn-info  btn-xs" onclick="execute(\'/admin/job/resumeJob\',\'' + value + '\')" style="margin-left: 10px;"><i class="fa fa-play-circle"></i> 回复暂停</button>');
                    }

                    return actions.join('');
                }
            }
        ]

        //初始化数据
        var dataStatus = [];

        initStatus = function (data, defaultValue) {
            htmlStatus = radioInitDict(data, "status", defaultValue);
            //console.log(htmlStatus);
            $("#status").html(htmlStatus);
        }

        dataStatus = getDictData("job_status", dataStatus, initStatus, null);

        statusFormatter = function (value, row, index) {
            return selectDictLabel(dataStatus, value)
        }

        var dataPolicy = [];

        initPolicy = function (data, defaultValue) {
            htmlPolicy = selectOptionInitDict(data, defaultValue);
            $("#misfirePolicy").html(htmlPolicy);
        }

        dataPolicy = getDictData("job_policy", dataPolicy, initPolicy, null);

        function policyFormatter(value, row, index) {
            return selectDictLabel(dataPolicy, value)
        }

        var toolbar1 =

            '<div id="toolbar" class="btn-group">\n' +
            '        <button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="add(\'jobform\',\'form\')"><i class="fa  fa-plus"></i>新增</button>' +
            //  '        <button id="btn_delete" type="button" class="btn btn-danger btn-xs" onclick="deletes(\'/job/delete\')"><i class="fa fa-trash-o"></i>删除</button>' +
            ' </div>'

        createBootstrapTable($("#jobtable"), '/admin/job/list', columns, true, toolbar1, 'server');


        //表单验证
        $('#form').bootstrapValidator({
            message: '这个值无效',
            feedbackIcons: {
                valid: 'glyphicon glyphicon-ok',
                invalid: 'glyphicon glyphicon-remove',
                validating: 'glyphicon glyphicon-refresh'
            },
            fields: {
                jobClassPath: {
                    message: '任务类无效',
                    validators: {
                        notEmpty: {
                            message: '任务类不能为空'
                        },
                        stringLength: {
                            min: 3,
                            max: 200,
                            message: '任务类必须在3到200之间'
                        }
                    }
                },
                jobName: {
                    message: '任务名称无效',
                    validators: {
                        notEmpty: {
                            message: '任务名称不能为空'
                        },
                        stringLength: {
                            min: 3,
                            max: 30,
                            message: '任务名称必须在3到30之间'
                        }
                    }
                },
                jobGroup: {
                    message: '任务组名称无效',
                    validators: {
                        notEmpty: {
                            message: '任务组名不能为空'
                        },
                        stringLength: {
                            min: 3,
                            max: 30,
                            message: '任务组名必须在3到30之间'
                        }
                    }
                },
                status: {
                    message: '状态无效',
                    validators: {
                        notEmpty: {
                            message: '状态不能为空'
                        }
                    }
                }
            }
        })
            .on('error.field.bv', function (e, data) {
                //  console.log(data.field, data.element, '-->error');
            })
            .on('success.field.bv', function (e, data) {
                // console.log(data.field, data.element, '-->success');
            });


        var jobcolumns = [
            {
                field: 'jobName',
                title: '任务名称'
            },
            {
                field: 'jobGroupName',
                title: '任务组'
            },
            {
                field: 'description',
                title: '执行批次'
            },
            {
                field: 'jobTime',
                title: '执行时间'
            },
            {
                field: 'jobStatus',
                title: '状态'
            }
        ]
        createBootstrapTable($("#allJobListTable"), "/admin/job/queryAllJob", jobcolumns, false, "<span/>", "client");
        createBootstrapTable($("#runJobListTable"), "/admin/job/queryRunJob", jobcolumns, false, "<span/>", "client");

    })

    //更新job
    function updateJob(url, id, contentId) {
        var data = {id: id};
        request(url, 0, data, function (data) {
            $("#jobId").val(data.data.jobId);
            $("#jobClassPath").val(data.data.jobClassPath);
            $("#jobName").val(data.data.jobName);
            $("#jobGroup").val(data.data.jobGroup);
            $("#cronExpression").val(data.data.cronExpression);
            $("#misfirePolicy").val(data.data.misfirePolicy).trigger("change");
            $("input[name='status'][value=" + data.data.status + "]").attr("checked", true);
            popForm = layer.open({
                type: 1,
                area: '650px',
                title: '修改',
                shade: 0.6,
                shadeClose: true, //点击遮罩关闭层
                maxmin: false,
                anim: 1,
                content: $("#" + contentId),
                success: function (layero, index) {
                    layer.iframeAuto(index);
                },end:function () {
                    $("#"+contentId).hide();
                }
            });
        }, function (error) {
            layer.msg("系统异常！")
        })

    }

    // 查询
    function showSub(tableId, url, contentId) {

        request(url, 0, null, function (res) {
            $("#" + tableId).bootstrapTable('load', res.data);
        }, function (error) {
            layer.msg(error.msg)
        })

        popForm = layer.open({
            type: 1,
            area: '650px',
            title: '查看任务',
            shade: 0.6,
            shadeClose: true, //点击遮罩关闭层
            maxmin: false,
            anim: 1,
            content: $("#" + contentId),
            success: function (layero, index) {
                layer.iframeAuto(index);
            },end:function () {
                $("#"+contentId).hide();
            }
        });
    }
</script>
</html>