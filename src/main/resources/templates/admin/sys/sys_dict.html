<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title>字典管理</title>
    <link th:replace="admin/include/head :: head">
    <style>
        #btn_add {
            margin-right: 10px;
        }
    </style>
</head>
<body>
<section class="content">

    <div class="box">
        <div class="box-body">
            <form class="form-inline" id="searchForm">
                <div class="form-group">
                    <label> 字典名称： </label>
                    <input type="text" class="form-control" id="searchDictName" name="dictName" placeholder="请输入字典名称"
                           autocomplete="off">
                </div>
                <div class="form-group">
                    <label> 字典标签： </label>
                    <input type="text" class="form-control" id="searchDictLabel" name="dictLabel" placeholder="请输入字典标签"
                           autocomplete="off">
                </div>
                <button class="btn  btn-default  btn-sm" type="button" onclick="restForm()">重置</button>
                <button class="btn  btn-success btn-sm" type="button" onclick="formSearch()">搜索</button>
                <button class="btn  btn-success btn-sm" type="button" onclick="updateDataDictCache()">更新缓存</button>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <table id="table"></table>
        </div>
    </div>
</section>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/sys/sys_dict.js}"></script>
</body>

<div id="dict_form" class="col-md-12 box" style="display: none">
    <input type="hidden" id="dictId">
    <form id="form" class="form-horizontal">
        <div class="box-body">
            <div class="form-group">
                <label for="dictType" class="col-sm-2 control-label">字典类型</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="dictType" name="dictType" placeholder="字典类型"
                           autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="dictName" class="col-sm-2 control-label">字典名称</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="dictName" name="dictName" placeholder="字典名称"
                           autocomplete="off">

                </div>
            </div>
            <div class="form-group">
                <label for="dictValue" class="col-sm-2 control-label">字典值</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="dictValue" name="dictValue" placeholder="字典值"
                           autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="dictLabel" class="col-sm-2 control-label">字典标签</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="dictLabel" name="dictLabel" placeholder="字典标签"
                           autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="sort" class="col-sm-2 control-label">排序</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="sort" name="sort" placeholder="排序"
                           autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="status" class="col-sm-2 control-label">字典状态</label>
                <div class="col-sm-10" id="status">
                    <input type="radio" name="status" value="0" class="flat-red" checked> 启用
                    <input type="radio" name="status" value="1" class="flat-red"> 停用
                </div>
            </div>
            <div class="form-group">
                <label for="remarks" class="col-sm-2 control-label">备注</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="remarks" placeholder="备注" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="box-footer">
            <button type="button" class="btn btn-info pull-right" onclick="save()">提交</button>
        </div>
    </form>
</div>
</html>