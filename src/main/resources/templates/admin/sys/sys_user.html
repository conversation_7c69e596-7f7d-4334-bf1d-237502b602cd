<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head >
    <title>用户管理</title>
    <link th:replace="admin/include/head :: head">
</head>
<link rel="stylesheet" th:href="@{/resources/awi/plugins/z-tree3.5/css/metro/zTreeStyle.css}">
<!--<style>-->
<!--@media (min-width: 768px){-->
<!--#user_form .control-label {-->
<!--text-align: left;-->
<!--}-->
<!--}-->
<!--</style>-->
<body>
<section class="content">

    <div class="box">
        <div class="box-body">
            <form class="form-inline" id="searchForm">
                <div class="form-group">
                    <label for="userName"> 用户名： </label>
                    <input type="text" class="form-control" id="userName" name="name" placeholder="请输入用户名"
                           autocomplete="off">
                </div>
                <div class="form-group">
                    <label for="loginName1"> 账号： </label>
                    <input type="email" class="form-control" id="loginName1" name="loginName" placeholder="请输入登陆账号"
                           autocomplete="off">
                </div>
                <button class="btn  btn-default  btn-sm" type="button" onclick="restForm()">重置</button>
                <button class="btn  btn-success btn-sm" type="button" onclick="formSearch()">搜索</button>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <table id="table"></table>
        </div>
    </div>
</section>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/sys/sys_user.js?version=1.1}"></script>
<script th:src="@{/resources/awi/plugins/z-tree3.5/js/jquery.ztree.all-3.5.js}"></script>

</body>

<div id="user_form" class="col-md-12 box" style="display: none">

    <form id="form" class="form-horizontal">
        <input type="hidden" id="userId" name="id">
        <div class="box-body">
            <div class="form-group">
                <label for="name" class="col-sm-12 col-md-2 control-label">姓名</label>
                <div class="col-sm-12 col-md-10">
                    <input type="text" class="form-control" id="name" name="name" placeholder="姓名"
                           autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="mobile" class="col-sm-12 col-md-2 control-label">联系方式</label>
                <div class="col-sm-12  col-md-10">
                    <input type="text" class="form-control" id="mobile" name="mobile" placeholder="联系方式"
                           autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="loginName" class="col-sm-12 col-md-2 control-label">登录名</label>
                <div class="col-sm-12  col-md-10">
                    <input type="text" class="form-control" id="loginName" name="loginName" placeholder="登录名"
                           autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="password" class="col-sm-12 col-md-2 control-label">密码</label>
                <div class="col-sm-12 col-md-10">
                    <input type="password" class="form-control" id="password" name="password" placeholder="密码"
                           autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="parentName" class="col-sm-12 col-md-2 control-label">归属部门</label>
                <div class="col-sm-12 col-md-10">
                    <input type="hidden" id="parentId">
                    <input type="text" class="form-control" id="parentName" onfocus="openDeptTree()" autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="postId" class="col-sm-12 col-md-2 control-label">岗位</label>
                <div class="col-sm-12 col-md-10">
                    <select id="postId" class=" select2" style="width: 100%;" name="postId">

                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="postId" class="col-sm-12 col-md-2 control-label">职位</label>
                <div class="col-sm-12 col-md-10">
                    <select id="positionId" class=" select2" style="width: 100%;" name="positionId">

                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="typeck" class="col-sm-12 col-md-2 control-label">角色权限</label>
                <div class="col-sm-12 col-md-10" id="typeck">

                </div>
            </div>
            <div class="form-group">
                <label for="parentName" class="col-sm-12 col-md-2 control-label">排序</label>
                <div class="col-sm-12 col-md-10">
                    <input type="text" class="form-control" id="sort" name="sort" value="100" autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="remarks" class="col-sm-12 col-md-2 control-label">备注</label>
                <div class="col-sm-12 col-md-10">
                    <input type="text" class="form-control" id="remarks" placeholder="备注" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="box-footer">
            <button type="button" class="btn btn-info pull-right" onclick="save()">提交</button>
        </div>
    </form>
</div>
<div id="zTree" style="display: none;" class="box">
    <div style="padding: 10px; height:90%;">
        <ul id="typeTree" class="ztree"></ul>
    </div>
</div>
<script th:inline="javascript">

</script>
</html>