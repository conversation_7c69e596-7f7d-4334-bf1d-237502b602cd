<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="角色表详情"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="sysRoleDetail" class="container-fluid" >
    <form id="sysRole-form-edit" class="form-horizontal" autocomplete="off" th:object="${sysRole}">
        <input type="hidden" id="id" name="id" th:value="*{id}"/>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="name" class="col-sm-4 control-label require">角色名称</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="name" name="name" th:value="*{name}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="roleType" class="col-sm-4 control-label require">角色类型</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="roleType" name="roleType" th:value="*{roleType}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group" >
                        <label for="useable" class="col-sm-4 control-label require">是否可用</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="useable" name="useable" th:value="*{useable}" readonly="readonly"/>
                        </div>
                    </div>
                </div>
        <div class="box-footer col-md-6 col-md-offset-5">
            <button type="button" class="btn btn-info pull-right" onclick="formClose()">关闭</button>
        </div>
    </form>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script type="text/javascript">
    function formClose() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    }
</script>
</body>
</html>
