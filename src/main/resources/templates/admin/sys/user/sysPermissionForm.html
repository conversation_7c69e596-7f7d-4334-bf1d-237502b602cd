<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head >
    <title th:text="系统权限表表单">欢迎页</title>
    <link th:replace="admin/include/head :: head">
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/z-tree3.5/css/metro/zTreeStyle.css}">
</head>
<body class="skin-blue sidebar-mini  pace-done" style="height: auto;">
<section class="content">
    <div class="row box">
        <div class="col-md-12">
                <!-- form start -->
                <form id="form" class="form-horizontal">
                    <input type="hidden" id="id" name="id">
                    <div class="box-body">
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="name" class="col-sm-2 control-label">名称</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="name"   name="name" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="parentId" class="col-sm-2 control-label">上级</label>
                                    <div class="col-sm-10">
                                        <input type="hidden" class="form-control" id="parentId"   name="parentId" >
                                        <input type="text" class="form-control" id="parentName"   name="parentName" onfocus="openPermissionTree()" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="permission" class="col-sm-2 control-label">权限标识</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="permission"   name="permission" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                    <label for="sort" class="col-sm-2 control-label">排序</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" id="sort"   name="sort" value="1">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <button type="button" class="btn btn-info pull-right" onclick="savepop('/admin/sysPermission/save' ,'form')">提交</button>
                        </div>
                          <!-- /.box-body -->
                </form>
              <!-- customer form end -->
        </div>
    </div>
</section>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/awi/plugins/z-tree3.5/js/jquery.ztree.all-3.5.js}"></script>
</body>
<div id="zTree" style="display: none;" class="box">
    <div style="padding: 10px; height:90%;" class="box">
        <ul id="typeTree" class="ztree"></ul>
    </div>
</div>
<script th:inline="javascript">
    var index = parent.layer.getFrameIndex(window.name) ;
    var id=[[${id!=null?id:''}]];
    $(function () {
        popInit('/admin/sysPermission/get', "form", id);
    });

    initTree('/admin/sysPermission/zTreeNodes','typeTree',0,"parentName","parentId");

    function openPermissionTree() {
        openTree("zTree");
    }
</script>
</html>