<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head >
    <title>职位信息表</title>
    <link th:replace="admin/include/head :: head">
</head>
<body>
<section class="content">
    <div class="box">
        <div class="box-body">
            <form class="form-inline" id="searchForm">
                <div class="form-group">
                    <label for="positionName"> 职位名称： </label>
                    <input type="text" class="form-control" id="searchPositionName" placeholder="请输入职位名称"
                           name="positionName"
                           autocomplete="off">
                </div>
                <button class="btn  btn-default  btn-sm" type="button" onclick="restForm('searchForm')">重置</button>
                <button class="btn  btn-success btn-sm" type="button"
                        onclick="formSearch('sysPositiontable','/sysPosition/list' ,'searchForm')">搜索
                </button>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <table id="sysPositiontable"></table>
        </div>
    </div>
</section>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
</body>

<div id="sysPositionform" class="col-md-12 box" style="display: none">

    <form id="form" class="form-horizontal">
        <div class="box-body">

            <input type="hidden" id="id" name="id">

            <div class="form-group">
                <label for="positionCode" class="col-sm-2 control-label">职位编码</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="positionCode"
                           name="positionCode" autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <label for="positionName" class="col-sm-2 control-label">职位名称</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="positionName"
                           name="positionName" autocomplete="off">
                </div>
            </div>


            <div class="form-group">
                <label for="sort" class="col-sm-2 control-label">显示顺序</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="sort"
                           name="sort" autocomplete="off">
                </div>
            </div>


            <div class="form-group">
                <label for="status" class="col-sm-2 control-label">状态</label>
                <div class="col-sm-10" id="status">
                </div>
            </div>
        </div>
        <div class="box-footer">
            <button type="button" class="btn btn-info pull-right" onclick="save('/sysPosition/save' ,'form')">提交
            </button>
        </div>
    </form>
</div>
<script type="text/javascript">
    $(function () {
        var columns = [
            {
                field: 'ck',
                checkbox: true
            },
            {
                field: "positionCode",
                title: "职位编码"
            },
            {
                field: "positionName",
                title: "职位名称"
            },

            {
                field: "status",
                title: "状态",
                formatter: function (value, row, index) {
                    return selectDictLabel(dataStatus, value)
                }
            },
            {
                field: 'createTime',
                title: '创建时间'
            }, {
                field: 'id',
                title: '操作',
                formatter: function (value, row, index) {
                    return '<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="update(\'/sysPosition/get\',\'' + value + '\',\'sysPositionform\')"><i class="fa fa-edit"></i> 修改</button>' +
                        '<button type="button" class="btn btn-danger  btn-xs" onclick="deletes(\'/sysPosition/delete\',\'' + value + '\')" style="margin-left: 10px;"><i class="fa fa-trash-o"></i> 删除</button>';
                }
            }
        ]
        var toolbar1 =

            '<div id="toolbar" class="btn-group">' +
            '        <button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="add(\'sysPositionform\',\'form\')"><i class="fa  fa-plus"></i>新增</button>' +
            '        <button id="btn_delete" type="button" class="btn btn-danger btn-xs" onclick="deletes(\'/sysPosition/delete\')"><i class="fa fa-trash-o"></i>删除</button>' +
            ' </div>'

        createBootstrapTable($("#sysPositiontable"), '/sysPosition/list', columns, false, toolbar1, 'server');
    })
    //初始化数据
    var dataStatus = [];

    initStatus = function (data, defaultValue) {
        htmlStatus = radioInitDict(data, "status", defaultValue);
        //console.log(htmlStatus);
        $("#status").html(htmlStatus);
    }

    dataStatus = getDictData("job_status", dataStatus, initStatus, null);

    statusFormatter = function (value, row, index) {
        return selectDictLabel(dataStatus, value)
    }
</script>
</html>