<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="用户表编辑"></title>
    <th:block th:include="admin/include/head :: head"/>
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/z-tree3.5/css/metro/zTreeStyle.css}">
</head>
<body>
<div id="sysUserform" class="container-fluid">

    <form id="sysUser-form-edit" class="form-horizontal" autocomplete="off" th:object="${userMap}">
        <input type="hidden" id="id" name="id" th:value="*{user.id}"/>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="name" class="col-sm-4 control-label require">姓名</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="name" name="name" th:value="*{user.name}"/>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="loginName" class="col-sm-4 control-label require">登录名</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="loginName" name="loginName" th:value="*{user.loginName}"/>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="mobile" class="col-sm-4 control-label require">社会统一信用代码</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="mobile" name="mobile" th:value="*{user.mobile}"/>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="deptName" class="col-sm-4 control-label">归属组织</label>
                <div class="col-sm-6">
                    <input type="hidden" id="deptId" name="deptId" th:value="*{user?.dept?.id}">
                    <input type="text" class="form-control" id="deptName" th:value="*{user?.dept?.deptName}" onfocus="openDeptTree()" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="postId" class="col-sm-4 control-label">岗位</label>
                <div class="col-sm-6">
                    <select id="postId" class="select2 form-control" style="width: 100%;" name="postId">
                    </select>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="positionId" class="col-sm-4 control-label">职位</label>
                <div class="col-sm-6">
                    <select id="positionId" class="select2 form-control" style="width: 100%;" name="positionId">
                    </select>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="tyPeck" class="col-sm-4 control-label">角色</label>
                <div class="col-sm-6" id="tyPeck" th:value="*{roleIds}">
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="userType" class="col-sm-4 control-label require">用户类型</label>
                <div class="col-sm-6">
                    <select id="userType" class="form-control" th:value="*{user.userType}" name="userType">
                        <option th:selected="*{user.userType == '0'}"  value="0">企业用户</option>
                        <option th:selected="*{user.userType == '1'}"  value="1">审查单位</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="loginFlag" class="col-sm-4 control-label require">是否可登录</label>
                <div class="col-sm-6">
                    <select th:value="*{user.loginFlag}" name="loginFlag" id="loginFlag" class="form-control">
                        <option th:selected="*{user.loginFlag == 0}" value="0">正常</option>
                        <option th:selected="*{user.loginFlag == 1}" value="1">禁用</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="sort" class="col-sm-4 control-label require">排序</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="sort" name="sort" th:value="*{user.sort}"/>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="remarks" class="col-sm-4 control-label">备注</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="remarks" th:value="*{user.remarks}" placeholder="备注" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="form-group col-md-11">
            <button type="button" class="btn btn-info pull-right" onclick="formSave()">提交</button>
        </div>
    </form>
</div>
<!-- ztree -->
<div id="zTree" style="display: none;" class="box">
    <div style="padding: 10px; height:90%;" class="box">
        <ul id="typeTree" class="ztree"></ul>
    </div>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/awi/plugins/z-tree3.5/js/jquery.ztree.all-3.5.js}"></script>
<script th:src="@{/resources/common/crud.js}"></script>

<script type="text/javascript" th:inline="javascript">
    var index = parent.layer.getFrameIndex(window.name);
    var postId = [[${userMap?.user?.postId}]];
    var positionId = [[${userMap?.user?.positionId}]];
    var roleIds = [[${userMap?.roleIds}]];
    console.log(roleIds);
    initTree('/admin/sysDept/zTreeNodes', "typeTree", 0, "deptName", "deptId", null);
    loadRole(roleIds);
    getPost(postId);
    getPosition(positionId);
    initValidator();
    function openDeptTree() {
        openTree("zTree");
    }

    var select2 = $('.select2').select2({
        theme: "bootstrap"
    });

    //获取岗位信息
    function getPost(res) {
        var data = {
            status: 0
        };
        request('/admin/sysPost/info', 1, data, function (data) {
            $("#postId").empty();
            $("#postId").append(' <option value="">' + '' + '</option>');
            for (var i = 0; i < data.length; i++) {
                $("#postId").append(' <option value="' + data[i].id + '">' + data[i].postName + '</option>')
            }
            if (res !== undefined && res !== null) {
                $("#postId").val(res).trigger('change');
            }
        }, function (error) {
            layer.msg("获取岗位信息异常！")
        })
    }

    //获取职位信息
    function getPosition(res) {
        var data = {
            status: 0
        };
        request('/admin/sysPosition/info', 1, data, function (data) {
            $("#positionId").empty();
            $("#positionId").append(' <option value="">' + '' + '</option>');
            for (var i = 0; i < data.length; i++) {
                $("#positionId").append(' <option value="' + data[i].id + '">' + data[i].positionName + '</option>')
            }
            if (res !== undefined && res !== null) {
                $("#positionId").val(res).trigger('change');
            }
        }, function (error) {
            layer.msg("获取职位信息异常！")
        })
    }

    //获取角色列表
    function loadRole(res) {
        request('/admin/sysRole/roleList', 1, null, function (data) {
            $("#tyPeck").empty();
            for (var i = 0; i < data.length; i++) {
                $("#tyPeck").append('<input type="checkbox" name="ids" class="minimal" value=' + data[i].id + '><span>' + data[i].name + '')
            }
            if (res != undefined) {
                $.each($('input[name=ids]'), function () {
                    for (var j = 0; j < res.length; j++) {
                        if (res[j] == $(this).val()) {
                            $(this).prop("checked", true);
                        }
                    }
                });
            }
        }, function (error) {
            layer.msg("系统异常！")
        })
    }

    function formSave() {
        var bootstrapValidator = $('#sysUser-form-edit').data('bootstrapValidator').validate();
        if (bootstrapValidator.isValid()) {
            savepop('/admin/sysUser/save', 'sysUser-form-edit')
        }else {
            layer.msg("请填写所有必填项");
        }
    }

    function initValidator() {
        $('#sysUser-form-edit').bootstrapValidator({
            message: '输入值不满足要求',
            excluded: [':disabled', ':hidden'],
            verbose: false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
                loginName: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                mobile: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 18,
                            message: '不能超过18位'
                        },
                        regexp: {
                            regexp: /^[0-9A-Z]{18}$/,
                            message: '请输入正确的社会统一信用代码'
                        }
                    }
                },
                name: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                postId: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                // positionId: {
                //     validators: {
                //         notEmpty: {
                //             message: '必填'
                //         }
                //     }
                // },
                tyPeck: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                sort: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                userType: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                loginDate: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                loginFlag: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                }
            }
        });
    }
</script>

</body>
</html>
