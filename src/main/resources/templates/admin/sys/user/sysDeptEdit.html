<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="用户部门编辑"></title>
    <th:block th:include="admin/include/head :: head"/>
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/z-tree3.5/css/metro/zTreeStyle.css}">
</head>
<body>
<div id="sysDeptform" class="container-fluid">

    <form id="sysDept-form-edit" class="form-horizontal" autocomplete="off" th:object="${sysDept}">
        <input type="hidden" id="id" name="id" th:value="*{id}"/>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="deptName" class="col-sm-4 control-label require">部门名称</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="deptName" name="deptName" th:value="*{deptName}"/>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="leader" class="col-sm-4 control-label require">负责人</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="leader" name="leader" th:value="*{leader}"/>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="orderNum" class="col-sm-4 control-label require">排序</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="orderNum" name="orderNum" th:value="*{orderNum}"/>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="parentId" class="col-sm-4 control-label require">上级组织</label>
                <div class="col-sm-6">
                    <input type="hidden" id="parentId" name="parentId" th:value="*{parentId}"/>
                    <input type="text" class="form-control" id="parentName" name="parentName" th:value="*{parentName}" onfocus="openTree('zTree')"/>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="phone" class="col-sm-4 control-label require">联系方式（+86）</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="phone" name="phone" th:value="*{phone}"/>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="status" class="col-sm-4 control-label require">部门状态</label>
                <div class="col-sm-6">
                    <select th:field="*{status}" th:value="*{status}" name="status" id="status" class="form-control">
                        <option th:selected="*{status == 0}" value="0">正常</option>
                        <option th:selected="*{status == 1}" value="1">禁用</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="projectAreaCode" class="col-sm-2 control-label require">所属地</label>
                <div class="col-sm-10">
                    <input type="text" class="form-control" id="projectAreaCode" name="projectAreaCode" autocomplete="off" th:value="*{projectAreaCode}">
                    <input type="hidden" id="remarks" name="remarks" th:value="*{remarks}">
                </div>
            </div>
        </div>

        <div class="form-group col-md-11">
            <button type="button" class="btn btn-info pull-right" onclick="formSave()">提交</button>
        </div>
    </form>
</div>
<!-- ztree -->
<div id="zTree" style="display: none;" class="box">
    <div style="padding: 10px; height:90%;" class="box">
        <ul id="typeTree" class="ztree"></ul>
    </div>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/awi/plugins/z-tree3.5/js/jquery.ztree.all-3.5.js}"></script>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/layui_layui.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/cascader.min.js}"></script>
<script type="text/javascript" th:inline="javascript">
    initTree("/admin/sysDept/zTreeNodes", "typeTree", 0, "parentName", "parentId", null);
    initValidator();
    let sysDeptValue = [[${sysDept}]]
    $(function () {
        setTimeout(() => {
            $('.el-input__inner').val(sysDeptValue.remarks)
        },500)
        layui.use('layCascader', function () {
            // var id = 0;
            var layCascader = layui.layCascader;
            layCascader({
                elem: '#projectAreaCode',
                props: {
                    checkStrictly: true,
                    lazy: true,
                    lazyLoad: function (node, resolve) {
                        $("#projectAreaCode").trigger('change')
                        var level = node.level;
                        if (level == 1) {
                            return
                        } else {
                            $.ajax({
                                url: '/admin/area/getCountyList',
                                type: 'GET', // 请求类型
                                data: {
                                    parentId: node.value || 410000
                                },
                                dataType: 'json',
                                success: function (response) {
                                    var nodes = response.map(function (item) {

                                        return {
                                            value: item.id,
                                            label: item.name,
                                            leaf: level >= 1
                                        };
                                    });
                                    resolve(nodes);
                                },
                                error: function (error) {
                                    console.log('加载子节点数据失败:', error);
                                    resolve([]);

                                }
                            });
                        }
                    }
                }
            });
        });
    })
    function formSave() {
        $("#remarks").val($('.el-input__inner').val())

        var bootstrapValidator = $('#sysDept-form-edit').data('bootstrapValidator').validate();
        if (bootstrapValidator.isValid()) {
            save('/admin/sysDept/update', 'sysDept-form-edit')
        } else {
            layer.msg("请填写所有必填项");
        }
    }

    function initValidator() {
        $('#sysDept-form-edit').bootstrapValidator({
            message: '输入值不满足要求',
            excluded: [':disabled', ':hidden'],
            verbose: false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
                deptName: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                leader: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                orderNum: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                parentId: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                projectAreaCode: {
                    trigger:'change',
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                phone: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 11,
                            message: '不能超过11位'
                        },
                        regexp: {
                            regexp: /^0?(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
                            message: '请输入正确的手机号'
                        }
                    }
                },
                status: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                }
            }
        });
    }
</script>

</body>
</html>
