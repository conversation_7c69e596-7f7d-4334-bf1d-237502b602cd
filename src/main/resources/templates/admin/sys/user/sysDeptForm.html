<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="用户部门表单"></title>
    <th:block th:include="admin/include/head :: head"/>
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/z-tree3.5/css/metro/zTreeStyle.css}">
</head>
<body>

<div class="container-fluid">
    <!-- form start -->
    <form id="form" class="form-horizontal">
        <input type="hidden" id="id" name="id">
        <div class="col-md-6">
            <div class="form-group">
                <label for="deptName" class="col-sm-4 control-label">部门名称</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="deptName" name="deptName" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="leader" class="col-sm-4 control-label">负责人</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="leader" name="leader" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="orderNum" class="col-sm-4 control-label">排序</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="orderNum" name="orderNum" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="parentId" class="col-sm-4 control-label">上级组织</label>
                <div class="col-sm-6">
                    <input type="hidden" class="form-control" id="parentId" name="parentId" autocomplete="off">
                    <input type="text" class="form-control" id="parentName" name="parentName" onfocus="openTree('zTree')" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="phone" class="col-sm-4 control-label">联系方式(+86)</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="phone" name="phone" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="status" class="col-sm-4 control-label">部门状态</label>
                <div class="col-sm-6">
                    <!--<input type="text" class="form-control" id="status" name="status" autocomplete="off">-->
                    <select class="form-control" id="status" name="status">
                        <option value="0" selected>正常</option>
                        <option value="1">禁用</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="projectAreaCode" class="col-sm-4 control-label">所属地</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="projectAreaCode" name="projectAreaCode" autocomplete="off">
                    <input type="hidden" id="remarks" name="remarks">
                </div>
            </div>
        </div>

        <div class="form-group col-md-11">
            <button type="button" class="btn btn-info pull-right" onclick="formSave()">提交</button>
        </div>
        <!-- /.box-body -->
    </form>
    <!-- customer form end -->
</div>
<!-- ztree -->
<div id="zTree" style="display: none;" class="box">
    <div style="padding: 10px; height:90%;" class="box">
        <ul id="typeTree" class="ztree"></ul>
    </div>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/awi/plugins/z-tree3.5/js/jquery.ztree.all-3.5.js}"></script>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/layui_layui.js}"></script>
<script th:src="@{/resources/awi/plugins/layui/cascader.min.js}"></script>
<script th:inline="javascript">
    "use strict";
    initTree("/admin/sysDept/zTreeNodes", "typeTree", 0, "parentName", "parentId", null);
    var index = parent.layer.getFrameIndex(window.name);
    initValidator();
    $(function () {
        layui.use('layCascader', function () {
            // var id = 0;
            var layCascader = layui.layCascader;
            layCascader({
                elem: '#projectAreaCode',
                props: {
                    checkStrictly: true,
                    lazy: true,
                    lazyLoad: function (node, resolve) {
                        $("#projectAreaCode").trigger('change')
                        var level = node.level;
                        if (level == 1) {
                            return
                        } else {
                            $.ajax({
                                url: '/admin/area/getCountyList',
                                type: 'GET', // 请求类型
                                data: {
                                    parentId: node.value || 410000
                                },
                                dataType: 'json',
                                success: function (response) {
                                    var nodes = response.map(function (item) {

                                        return {
                                            value: item.id,
                                            label: item.name,
                                            leaf: level >= 1
                                        };
                                    });
                                    resolve(nodes);
                                },
                                error: function (error) {
                                    console.log('加载子节点数据失败:', error);
                                    resolve([]);

                                }
                            });
                        }
                    }
                }
            });
        });
    })

    function formSave() {
        $("#remarks").val($('.el-input__inner').val())

        var bootstrapValidator = $('#form').data('bootstrapValidator').validate();
        if (bootstrapValidator.isValid()) {
            savepop('/admin/sysDept/add', 'form');
        } else {
            layer.msg("请填写所有必填项");
        }
    }

    function initValidator() {
        $('#form').bootstrapValidator({
            message: '输入值不满足要求',
            excluded: [':disabled', ':hidden'],
            verbose: false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
                deptName: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                leader: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                orderNum: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                parentId: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                phone: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 11,
                            message: '不能超过11位'
                        },
                        regexp: {
                            regexp: /^0?(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
                            message: '请输入正确的手机号'
                        }
                    }
                },
                status: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                projectAreaCode: {
                    trigger: 'change',
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
            }
        });
    }
</script>
</body>
</html>
