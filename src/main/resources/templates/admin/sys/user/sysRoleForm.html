<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="角色表表单"></title>
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/z-tree3.5/css/metro/zTreeStyle.css}">
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>

<div class="container-fluid">
    <!-- form start -->
    <form id="form" class="form-horizontal">
        <input type="hidden" id="id" name="id">
        <input type="hidden" id="permissionIds" name="permissionIds">
        <div class="col-md-6">
            <div class="form-group">
                <label for="name" class="col-sm-4 control-label">角色名称</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="name" name="name" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="typeTree" class="col-sm-4 control-label">权限功能</label>
                <div class="col-sm-6">
                    <div id="zTree" class="box">
                        <div style="padding: 10px; height:90%;">
                            <ul id="typeTree" class="ztree"></ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="remarks" class="col-sm-4 control-label">备注</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="remarks" autocomplete="off">
                </div>
            </div>
        </div>

        <div class="form-group col-md-11">
            <button type="button" class="btn btn-info pull-right" onclick="formSave()">提交</button>
        </div>
        <!-- /.box-body -->
    </form>
    <!-- customer form end -->

</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/awi/plugins/z-tree3.5/js/jquery.ztree.all-3.5.js}"></script>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";
    initTree("/admin/sysPermission/zTreeNodes", "typeTree", 1, "parentName", "permissionIds", null);
    var index = parent.layer.getFrameIndex(window.name);
    initValidator();

    function formSave() {
        var bootstrapValidator = $('#form').data('bootstrapValidator').validate();
        if (bootstrapValidator.isValid()) {
            savepop('/admin/sysRole/add', 'form', 'saveBtn')
        }
    }

    function initValidator() {
        $('#form').bootstrapValidator({
            message: '输入值不满足要求',
            excluded: [':disabled', ':hidden'],
            verbose: false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
                name: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 100,
                            message: '不能超过100位'
                        }
                    }
                }
            }
        });
    }
</script>
</body>
</html>