<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head >
    <title th:text="系统权限表">标题</title>
    <link th:replace="admin/include/head :: head">
    <link rel="stylesheet" th:href="@{/resources/bootstrap/css/bootstrap-treetable.css}">
</head>
<body >
<div class="panel-body" style="padding-bottom:0px;">
   <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group" >
                    <label class="control-label col-sm-1  " for="searchName">名称</label>
                    <div class="col-sm-3">
                        <input type="text" class="form-control col-sm-10" id="searchName" name="name" placeholder="请输入名称">
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button" onclick="restForm('searchForm')"><i class="fa fa-refresh"></i>重置</button>
                        <button  class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px" id="btn_query"  onclick="formSearch('sysPermissiontable','/admin/sysPermission/list' ,'searchForm')"><i class="fa fa-search"></i>查询</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <table id="sysPermissiontable"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/bootstrap/js/bootstrap-treetable.js}"></script>

</body>

<script type="text/javascript">
    $(function () {
        var columns = [
            {
                field: 'ck',
                checkbox: true,
                align:'center',
                valign:'middle'
            },
            {
                field: "name",
                title: "名称",
                align:'center',
                valign:'middle'
            },

            {
                field: "permission",
                title: "权限标识",
                align:'center',
                valign:'middle'
            },
            {
                field: "sort",
                title: "排序",
                align:'center',
                valign:'middle'
            },
            {
                field: 'createTime',
                title: '创建时间',
                align:'center',
                valign:'middle'
            }, {
                field: 'id',
                title: '操作',
                align:'center',
                valign:'middle',
                formatter: function (value, row, index) {
                    return '<div class="btn-group"><button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="poplayer(2,\'新增\',\'/admin/sysPermission/toForm?id='+value+'\')" style="margin-right: 10px;"><span class="glyphicon glyphicon-pencil" aria-hidden="true"></span>修改</button>' +
                            '<button type="button" class="btn btn-danger  btn-xs" onclick="deletes(\'/admin/sysPermission/delete\',\'' + value + '\')"><span class="glyphicon glyphicon-remove" aria-hidden="true"></span> 删除</button></div>';
                }
            }
        ]
        var toolbar1 =
            '<div id="toolbar" class="btn-group">' +
            '        <button id="btn_add" type="button" class="btn btn-success" onclick="poplayer(2,\'新增\',\'/admin/sysPermission/toForm\')"><span class="glyphicon glyphicon-plus" aria-hidden="true"></span>新增</button>' +
            ' </div>'
        $("#sysPermissiontable").bootstrapTreeTable({
            code: 'id',
            parentCode: 'parentId',
            type: 'get',
            url: '/admin/sysPermission/list',
            striped: false,
            expandColumn: '0',
            bordered: true,
            columns: columns,
            toolbar: toolbar1
        });
    })
</script>
</html>