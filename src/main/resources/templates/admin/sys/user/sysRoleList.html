<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="角色表"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-sm-1" for="searchName">角色名称</label>
                    <div class="col-sm-3">
                        <input type="text" class="form-control col-sm-10" id="searchName" name="name"
                               placeholder="请输入名称">
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="restForm('searchForm')"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="formSearch('sysRoletable','/admin/sysRole/list' ,'searchForm')">
                            <i class="fa fa-search"></i>查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <table id="sysRoletable"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";

    var addFlag = [[${@permission.hasPermi('sysRole:add')}]];
    var editFlag = [[${@permission.hasPermi('sysRole:edite')}]];
    var delFlag = [[${@permission.hasPermi('sysRole:delete')}]];
    var designFlag = [[${@permission.hasPermi('sysRole:design')}]];

    $(function () {
        var columns = [
            {
                field: 'ck',
                checkbox: true,
                align: 'center',
                valign: 'middle'
            },
            {
                field: "name",
                title: "角色名称",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "useable",
                title: "是否可用",
                align: 'center',
                valign: 'middle',
                formatter: function (value, row, index) {
                    if (value == null || value === '1') {
                        return '<button id="btn_edit" type="button" class="btn btn-warning disabled btn-xs">停用</button>'
                    } else {
                        return '<button id="btn_edit" type="button" class="btn btn-success  disabled btn-xs">正常</button>'
                    }
                }
            },
            {
                field: 'createTime',
                title: '创建时间',
                align: 'center',
                valign: 'middle'
            },
            {
                field: 'id',
                title: '操作',
                align: 'center',
                valign: 'middle',
                cellStyle: {
                    css: {"white-space": "nowrap"}
                },
                formatter: function (value, row, index) {
                    var actions = [];
                    if ("hidden" != editFlag) {
                        actions.push('<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="updatelayer(\'' + value + '\',\'编辑\',\'/admin/sysRole/edit/\')" style="margin-right: 10px;"><i class="fa fa-edit"></i>编辑</button>');
                    }
                    /*if ("hidden" != designFlag) {
                        actions.push('<button id="btn_detail" type="button" class="btn btn-success  btn-xs" onclick="detaillayer(\'' + value + '\',\'详情\',\'/admin/sysRole/detail/\')"><i class="fa fa-eye"></i>详情</button>');
                    }*/
                    if ("hidden" != delFlag) {
                        actions.push('<button type="button" class="btn btn-danger  btn-xs" onclick="deleteOne(\'/admin/sysRole/deleteOne\',\'' + value + '\')" ><i class="fa fa-trash-o"></i>删除</button>');
                    }
                    return actions.join('');
                }
            }
        ];
        var toolbar = [];
        toolbar.push('<div id="toolbar" class="btn-group">');
        if ("hidden" != addFlag) {
            toolbar.push('<button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="poplayer(2,\'新增\',\'/admin/sysRole/toForm\')"><i class="fa fa-plus"></i>新增</button>');
        }
        toolbar.push('</div>');
        createBootstrapTable($("#sysRoletable"), '/admin/sysRole/list', columns, false, toolbar.join(''), 'server');
    });
</script>
</body>
</html>