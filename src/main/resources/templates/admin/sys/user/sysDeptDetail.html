<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="用户部门详情"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="sysDeptDetail" class="container-fluid form-horizontal" th:object="${sysDept}">
    <div class="box-body">
        <input type="hidden" id="id" name="id" th:value="*{id}"/>
        <div class="col-md-6">
            <div class="form-group">
                <label for="deptName"
                       class="col-sm-4 control-label require">部门名称</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="deptName" name="deptName" th:value="*{deptName}" readonly="readonly"/>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="leader"
                       class="col-sm-4 control-label require">负责人</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="leader" name="leader" th:value="*{leader}" readonly="readonly"/>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="orderNum"
                       class="col-sm-4 control-label require">排序</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="orderNum" name="orderNum" th:value="*{orderNum}" readonly="readonly"/>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="parentId"
                       class="col-sm-4 control-label require">上级组织</label>
                <div class="col-sm-6">
                    <input type="hidden" id="parentId" name="parentId" th:value="*{parentId}"/>
                    <input type="text" class="form-control" id="parentName" name="parentName" th:value="*{parentName}" readonly="readonly"/>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="phone"
                       class="col-sm-4 control-label require">联系方式</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="phone" name="phone" th:value="*{phone}" readonly="readonly"/>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="status"
                       class="col-sm-4 control-label require">部门状态</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="status" name="status" th:value="*{status} == '0'?'正常':'停用'" readonly="readonly"/>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="projectAreaCode" class="col-sm-2 control-label require">所属地</label>
            <div class="col-sm-10">
                <input disabled type="text" class="form-control" id="projectAreaCode" name="projectAreaCode" autocomplete="off" th:value="*{remarks}">
<!--                <input type="hidden" id="remarks" name="remarks" th:value="*{remarks}">-->
            </div>
        </div>
    </div>
    <div class="box-footer col-md-6 col-md-offset-5">
        <button type="button" class="btn btn-info pull-right" onclick="formClose()">关闭</button>
    </div>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>

<script type="text/javascript">
    function formClose() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    }
</script>
</body>
</html>
