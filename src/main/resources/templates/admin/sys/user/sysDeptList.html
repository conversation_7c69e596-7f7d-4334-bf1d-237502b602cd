<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="用户部门"></title>
    <th:block th:include="admin/include/head :: head"/>
    <link rel="stylesheet" th:href="@{/resources/bootstrap/css/bootstrap-treetable.css}">
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-sm-1" for="searchName">名称</label>
                    <div class="col-sm-3">
                        <input type="text" class="form-control col-sm-10" id="searchName" name="name"
                               placeholder="请输入名称">
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="restForm('searchForm')"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="formSearch('sysDepttable','/admin/sysDept/list' ,'searchForm')">
                            <i class="fa fa-search"></i>查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<section class="content">
    <div class="row">
        <div class="col-xs-12">
            <table id="sysDepttable"></table>
        </div>
    </div>
</section>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:src="@{/resources/bootstrap/js/bootstrap-treetable.js}"></script>
<script th:inline="javascript">
    "use strict";
    var addFlag = [[${@permission.hasPermi('sysDept:add')}]];
    var editFlag = [[${@permission.hasPermi('sysDept:edite')}]];
    var delFlag = [[${@permission.hasPermi('sysDept:delete')}]];
    var designFlag = [[${@permission.hasPermi('sysDept:design')}]];

    $(function () {
        var columns = [
            /*{
                field: 'ck',
                checkbox: true,
                align: 'center',
                valign: 'middle'
            },*/
            {
                field: "deptName",
                title: "部门名称",
                align: 'left',
                width: 100,
                valign: 'middle'
            },
            {
                field: "leader",
                title: "负责人",
                align: 'center',
                width: 80,
                valign: 'middle'
            },
            {
                field: "parentId",
                title: "上级组织",
                align: 'center',
                width: 80,
                valign: 'middle',
                formatter: function (value, row, index) {
                    return getDataName(deptData, value);
                }
            },
            {
                field: "phone",
                title: "联系方式",
                align: 'center',
                width: 80,
                valign: 'middle'
            },
            {
                field: "status",
                title: "部门状态",
                align: 'center',
                width: 70,
                valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.status == null || row.status == '1') {
                        return '<button id="btn_edit" type="button" class="btn btn-warning disabled btn-xs" >停用</button>'
                    } else {
                        return '<button id="btn_edit" type="button" class="btn btn-success  disabled btn-xs" >正常</button>'
                    }
                }
            },
            {
                field: 'id',
                title: '操作',
                align: 'center',
                valign: 'middle',
                width: 300,
                cellStyle: {
                    css: {"white-space": "nowrap"}
                },
                formatter: function (value, row, index) {
                    var actions = [];
                    if ("hidden" != editFlag) {
                        actions.push('<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="updatelayer(\'' + value + '\',\'编辑\',\'/admin/sysDept/edit/\')" style="margin-right: 10px;"><i class="fa fa-edit"></i>编辑</button>');
                    }
                    if ("hidden" != designFlag) {
                        actions.push('<button id="btn_detail" type="button" class="btn btn-success  btn-xs" onclick="detaillayer(\'' + value + '\',\'详情\',\'/admin/sysDept/detail/\')" style="margin-right: 10px;"><i class="fa fa-eye"></i>详情</button>');
                    }
                    if ("hidden" != delFlag) {
                        actions.push('<button type="button" class="btn btn-danger  btn-xs" onclick="deleteDept(\'/admin/sysDept/deletes\',\'' + value + '\')" ><i class="fa fa-trash-o"></i>删除</button>');
                    }
                    return actions.join('');
                }
            }
        ];
        var toolbar = [];
        toolbar.push('<div id="toolbar" class="btn-group">');
        if ("hidden" != addFlag) {
            toolbar.push('<button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="poplayer(2,\'新增\',\'/admin/sysDept/toForm\')"><i class="fa fa-plus"></i>新增</button>');
        }
        toolbar.push('</div>');

        $("#sysDepttable").bootstrapTreeTable({
            code: 'id',
            parentCode: 'parentId',
            type: 'get',
            url: '/admin/sysDept/list',
            striped: false,
            expandColumn: '0',
            bordered: true,
            columns: columns,
            toolbar: toolbar.join(''),
            sidePagination: 'server'
        });
    });

    var deptData = [];
    deptData = getData("/admin/sysDept/getDataForSelect", deptData);

    //删除操作
    function deleteDept(url, id) {
        var data = {
            id: id
        };
        request("/admin/sysDept/checkDeptNode", 1, data, function (data) {
            if (data.valid === true) {
                deletes(url, id);
            } else {
                layer.msg("此部门下还有员工，无法删除", {icon: 2});
            }
        });
    }

    /**
     * 弹出层 编辑
     * @param id
     * @param title 标题
     * @param url
     */
    function updatelayer(id,title,url) {
        if(undefined == id || null == id || '' == id ){
            return ;
        }
        if(undefined == url || null == url || '' == url ){
            return ;
        }
        url = url+id;
        var printForm = layer.open({
            type: 2,
            area : ['40%', '80%'],
            title: title,
            shade: 0.6,
            maxmin: false,
            shadeClose: true, //点击遮罩关闭层
            closeBtn: 1,
            content: url,
            success: function (layero, index) {
                // layer.iframeAuto(index);
            }
        });
    }
</script>
</body>
</html>