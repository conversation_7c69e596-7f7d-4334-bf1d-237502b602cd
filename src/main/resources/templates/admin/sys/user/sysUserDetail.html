<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="用户表详情"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="sysUserDetail" class="container-fluid form-horizontal" th:object="${userMap}">
    <div class="box-body">
        <div class="col-sm-6">
            <div class="form-group">
                <label for="name" class="col-sm-4 control-label require">姓名</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="name" name="name" th:value="*{user.name}"
                           readonly="readonly"/>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="loginName" class="col-sm-4 control-label require">登录名</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="loginName" name="loginName" th:value="*{user.loginName}"
                           readonly="readonly"/>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="mobile" class="col-sm-4 control-label require">社会统一信用代码</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="mobile" name="mobile" th:value="*{user.mobile}"
                           readonly="readonly"/>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="deptName" class="col-sm-4 control-label">归属部门</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="deptName" th:value="*{user?.dept?.deptName}"
                           autocomplete="off" readonly="readonly">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="postName" class="col-sm-4 control-label">岗位</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="postName" th:value="*{user?.post?.postName}"
                           autocomplete="off" readonly="readonly">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="positionName" class="col-sm-4 control-label">职位</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="positionName" th:value="*{user?.position?.positionName}"
                           autocomplete="off" readonly="readonly">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="tyPeck" class="col-sm-4 control-label">角色</label>
                <div class="col-sm-6" id="tyPeck" th:value="*{roleIds}">
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="userType" class="col-sm-4 control-label require">用户类型</label>
                <div class="col-sm-6">
                    <select id="userType" th:switch="*{user.userType}" class="form-control" name="userType"
                            readonly="readonly">
                        <option th:selected="*{user.userType == '0'}" value="3">超级管理员</option>
                        <option th:selected="*{user.userType == '1'}" value="1">管理员</option>
                        <option th:selected="*{user.userType == '2'}" value="2">普通用户</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="loginFlag" class="col-sm-4 control-label require">是否可登录</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="loginFlag" name="loginFlag"
                           th:value="*{user.loginFlag} == '0'?'正常':'禁用'" readonly="readonly"/>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label for="sort" class="col-sm-4 control-label require">排序</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="sort" name="sort" th:value="*{user.sort}"
                           readonly="readonly"/>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="loginDate"
                       class="col-sm-4 control-label require">最后登陆时间</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="loginDate" name="loginDate"
                           th:value="*{#dates.format(user.loginDate,'yyyy-mm-dd HH:mm:ss')}" readonly="readonly"/>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="loginIp"
                       class="col-sm-4 control-label require">最后登陆IP</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="loginIp" name="loginIp" th:value="*{user.loginIp}"
                           readonly="readonly"/>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="remarks" class="col-sm-4 control-label">备注</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="remarks" th:value="*{user.remarks}" autocomplete="off"
                           readonly="readonly">
                </div>
            </div>
        </div>
    </div>

    <div class="box-footer col-md-6 col-md-offset-5">
        <button type="button" class="btn btn-info pull-right" onclick="formClose()">关闭</button>
    </div>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>

<script type="text/javascript">
    "use strict";
    var roleIds = [[${userMap?.roleIds}]];
    loadRole(roleIds);

    //获取角色列表
    function loadRole(res) {
        request('/admin/sysRole/roleList', 1, null, function (data) {
            $("#tyPeck").empty();
            for (var i = 0; i < data.length; i++) {
                $("#tyPeck").append('<input type="checkbox" name="role" class="minimal" value=' + data[i].id + '><span>' + data[i].name + '')
            }
            if (res != undefined) {
                $.each($('input[name=role]'), function () {
                    for (var j = 0; j < res.length; j++) {
                        if (res[j] == $(this).val()) {
                            $(this).prop("checked", true);
                        }
                    }
                });
            }
        }, function (error) {
            layer.msg("系统异常！")
        })
    }

    function formClose() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    }
</script>
</body>
</html>
