<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="用户表"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="control-label col-sm-2" for="searchName">姓名</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control col-sm-8" id="searchName" name="name"
                                   placeholder="请输入姓名">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="control-label col-sm-3" for="searchMobile">社会统一信用代码</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control col-sm-8" id="searchMobile" name="mobile"
                                   placeholder="请输入社会统一信用代码">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <button class="btn btn-warning btn-rounded btn-sm " type="button" onclick="restForm('searchForm')">
                        <i class="fa fa-refresh"></i>重置
                    </button>
                    <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                            id="btn_query" onclick="formSearch('sysUsertable','/admin/sysUser/list' ,'searchForm')">
                        <i class="fa fa-search"></i>查询
                    </button>
                </div>
            </form>
        </div>
    </div>
    <table id="sysUsertable"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";

    var addFlag = [[${@permission.hasPermi('sysUser:add')}]];
        var editFlag = [[${@permission.hasPermi('sysUser:edite')}]];
    var delFlag = [[${@permission.hasPermi('sysUser:delete')}]];
    var designFlag = [[${@permission.hasPermi('sysUser:design')}]];

    $(function () {
        var columns = [
            {
                field: 'ck',
                checkbox: true,
                align: 'center',
                valign: 'middle'
            },
            {
                title: '序号',
                align: "center",
                width: 40,
                formatter: function (value, row, index) {
                    //获取每页显示的数量
                    var pageSize = $('#sysUsertable').bootstrapTable('getOptions').pageSize;
                    //获取当前是第几页
                    var pageNumber = $('#sysUsertable').bootstrapTable('getOptions').pageNumber;
                    //返回序号，注意index是从0开始的，所以要加上1
                    return pageSize * (pageNumber - 1) + index + 1;
                }
            },
            {
                field: "name",
                title: "姓名",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "loginName",
                title: "登录名",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "mobile",
                title: "社会统一信用代码",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "userType",
                title: "用户类型",
                align: 'center',
                valign: 'middle',
                formatter: function (value, row, index) {
                    if (value === '0') {
                        return '<button id="btn_edit" type="button" class="btn btn-primary disabled btn-xs" >企业用户</button>';
                    } else if (value === '1') {
                        return '<button id="btn_edit" type="button" class="btn btn-success disabled btn-xs" >审核单位</button>';
                    }
                }
            },
            {
                field: "loginDate",
                title: "最后登陆时间",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "loginFlag",
                title: "是否可登录",
                align: 'center',
                valign: 'middle',
                formatter: function (value, row, index) {
                    if (value == null || value === '1') {
                        return '<button id="btn_edit" type="button" class="btn btn-warning disabled btn-xs" >停用</button>'
                    } else {
                        return '<button id="btn_edit" type="button" class="btn btn-success  disabled btn-xs" >正常</button>'
                    }
                }
            },
            {
                field: 'id',
                title: '操作',
                align: 'center',
                valign: 'middle',
                cellStyle: {
                    css: {"white-space": "nowrap"}
                },
                formatter: function (value, row, index) {
                    var actions = [];
                    if ("hidden" != editFlag) {
                        actions.push('<button id="btn_reset" type="button" class="btn btn-warning  btn-xs" onclick="resetPass(\'/admin/sysUser/resetPassword/\',\'' + value + '\')" style="margin-right: 10px;"><i class="fa fa-edit"></i>重置密码</button>');
                    }
                    if ("hidden" != designFlag) {
                        actions.push('<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="updatelayer(\'' + value + '\',\'编辑\',\'/admin/sysUser/edit/\')" style="margin-right: 10px;"><i class="fa fa-edit"></i>编辑</button>');
                    }
                    if ("hidden" != designFlag) {
                        actions.push('<button id="btn_detail" type="button" class="btn btn-success  btn-xs" onclick="detaillayer(\'' + value + '\',\'详情\',\'/admin/sysUser/detail/\')" style="margin-right: 10px;"><i class="fa fa-eye"></i>详情</button>');
                    }
                    if ("hidden" != delFlag) {
                        actions.push('<button type="button" class="btn btn-danger  btn-xs" onclick="deletes(\'/admin/sysUser/delete\',\'' + value + '\')" ><i class="fa fa-trash-o"></i>删除</button>');
                    }
                    return actions.join('');
                }
            }
        ];
        var toolbar = [];
        toolbar.push('<div id="toolbar" class="btn-group">');
        if ("hidden" != addFlag) {
            toolbar.push('<button id="btn_add" type="button" class="btn btn-info btn-xs" onclick="poplayer(2,\'新增\',\'/admin/sysUser/toForm\')" style="margin-right: 10px;"><i class="fa fa-plus"></i>新增</button>');
        }
        if ("hidden" != delFlag) {
            toolbar.push('<button id="btn_delete" type="button" class="btn btn-danger btn-xs" onclick="deletes(\'/admin/sysUser/delete\')" ><i class="fa fa-trash-o"></i>删除</button>');
        }
        toolbar.push('</div>');
        createBootstrapTable($("#sysUsertable"), '/admin/sysUser/list', columns, false, toolbar.join(''), 'server');
    });

    function resetPass(url, id) {
        var data = {id: id};
        layer.confirm('确定执行吗？', {
            title: '提示', btn: ['确定', '取消'] //按钮
        }, function () {
            request(url, 1, data, function (data) {
                if (data.code == 0) {
                    layer.confirm(data.msg, {
                        title: '注意', btn: ['确定']
                    });
                } else {
                    layer.msg(data.msg, {time: 3000, icon: 5})
                }
            }, function (error) {
                layer.msg("系统异常！")
            })
        });
    }
</script>
</body>
</html>