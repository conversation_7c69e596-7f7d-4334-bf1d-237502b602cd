<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head >
    <title>操作日志</title>
    <link th:replace="admin/include/head :: head">
</head>
<body>
<section class="content">
    <div class="box">
        <div class="box-body">
            <form class="form-inline" id="searchForm">
                <div class="form-group">
                    <label for="requestUri"> 请求路径： </label>
                    <input type="text" class="form-control" id="requestUri"  name="requestUri" placeholder="请求路径"
                           autocomplete="off">
                    <label for="createByName"> 用户账号： </label>
                    <input type="text" class="form-control" id="createByName"  name="createByName" placeholder="用户账号"
                           autocomplete="off">
                </div>
                <button class="btn  btn-default  btn-sm" type="button" onclick="restForm('searchForm')">重置</button>
                <button class="btn  btn-success btn-sm" type="button" onclick="formSearch()">搜索  </button>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <table id="sysOperLogtable"></table>
        </div>
    </div>
</section>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
</body>
<script type="text/javascript">
    $(function () {
        var columns = [

            // {
            //     field: "logType",
            //     title: "日志类型"
            // },
            {
                field: "logTitle",
                title: "日志标题"
            },
            {
                field: "createByName",
                title: "用户名称"
            },
            {
                field: "requestUri",
                title: "请求URI"
            },
            {
                field: "requestMethod",
                title: "操作方式"
            },
            {
                field: "requestParams",
                title: "操作提交的数据"
            },
            // {
            //     field: "diffModifyData",
            //     title: "新旧数据比较结果"
            // },
            // {
            //     field: "bizKey",
            //     title: "业务主键"
            // },
            // {
            //     field: "bizType",
            //     title: "业务类型"
            // },
            {
                field: "remoteAddr",
                title: "操作IP地址"
            },
            // {
            //     field: "serverAddr",
            //     title: "请求服务器地址"
            // },
            {
                field: "isException",
                title: "是否异常",
                formatter: function (value, row, index) {
                    if(value==0){
                        return "无"
                    }else{
                        return "有"
                    }

                }
            },
            // {
            //     field: "exceptionInfo",
            //     title: "异常信息"
            // },
            {
                field: "userAgent",
                title: "用户代理"
            },
            {
                field: "deviceName",
                title: "设备名称/操作系统"
            },
            {
                field: "browserName",
                title: "浏览器名称"
            },
            {
                field: "executeTime",
                title: "执行时间"
            },
            {
                field: 'createTime',
                title: '创建时间'
            }
        ]
        createBootstrapTable($("#sysOperLogtable"), '/admin/sysOperLog/list', columns, false, null, 'server');
    })

    //form表单查询
    function formSearch() {
        var data = {
            requestUri:$("#requestUri").val() ,
            createByName: $("#createByName").val()
        }
        var opt = {
            url: '/admin/sysOperLog/list',
            silent: true,
            query: data
        };

        $("#sysOperLogtable").bootstrapTable('refresh',opt);
    }

    function restForm() {
        document.getElementById("searchForm").reset()
        formSearch();
    }
</script>
</html>