<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head >
    <title>菜单管理</title>
    <link th:replace="admin/include/head :: head">
    <link rel="stylesheet" th:href="@{/resources/bootstrap/css/bootstrap-treetable.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/z-tree3.5/css/metro/zTreeStyle.css}">
    <link rel="stylesheet" th:href="@{/resources/awi/plugins/iCheck/all.css}">
</head>
<body>

<section class="content">
    <div class="row">
        <div class="col-xs-12">
            <table id="table"></table>
        </div>
    </div>
</section>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/bootstrap/js/bootstrap-treetable.js}"></script>
<script th:src="@{/resources/awi/plugins/z-tree3.5/js/jquery.ztree.all-3.5.js}"></script>
<script th:src="@{/resources/awi/plugins/iCheck/icheck.js}"></script>
<script th:src="@{/resources/sys/sys_menu.js}"></script>
</body>

<div id="menu_form" class="col-md-12 box" style="display: none">

        <input type="hidden" id="menuId">
        <form id="form" class="form-horizontal">
            <div class="box-body">
                <div class="form-group" id="show_tree">
                    <label for="parentName" class="col-sm-2 control-label">上级菜单</label>
                    <div class="col-sm-10">
                        <input type="hidden" id="parentId">
                        <input type="text" class="form-control" id="parentName" onfocus="openMenuTree()" autocomplete="off">
                    </div>
                </div>
                <div class="form-group">
                    <label for="menuType" class="col-sm-2 control-label">类型</label>
                    <div class="col-sm-10">
                    <select id="menuType" class="select2" style="width: 100%;" onchange="changeForm()">
                        <option value="0" >目录</option>
                        <option value="1" selected="selected">菜单</option>
                        <option value="2">按钮</option>
                    </select>
                </div>
                </div>
                <div class="form-group" id="show_name">
                    <label for="name" class="col-sm-2 control-label">菜单名称</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="name" name="name" autocomplete="off">
                    </div>
                </div>
                <div class="form-group" id="show_href">
                    <label for="hrefs" class="col-sm-2 control-label">请求地址</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="hrefs" name="hrefs" autocomplete="off">
                    </div>
                </div>
                <div class="form-group" id="show_perm">
                    <label for="permission" class="col-sm-2 control-label">权限标识</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="permission" name="permission" autocomplete="off">
                    </div>
                </div>
                <div class="form-group" id="show_sort">
                    <label for="sort" class="col-sm-2 control-label">排序</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="sort" name="sort" value="1">
                    </div>
                </div>

                <div class="form-group" id="show_icon">
                    <label for="icon" class="col-sm-2 control-label">图标</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="icon" name="icon" autocomplete="off" value="fa fa-folder">
                    </div>
                </div>
                <div class="form-group" id="show_status">
                    <label for="isShow" class="col-sm-2 control-label">菜单状态</label>
                    <div class="col-sm-10" id="isShow">
                        <input type="radio" name="isShow" value="0" class="flat-red" id="show" checked> <label for="show">显示</label>
                        <input type="radio" name="isShow" value="1" class="flat-red" id="hidden"> <label for="hidden">隐藏</label>
                    </div>
                </div>
            </div>
            <div class="box-footer">
                <button type="button" class="btn btn-info pull-right" onclick="save()">提交</button>
            </div>
        </form>

</div>
<div id="zTree" style="display: none;" class="box">
    <div style="padding: 10px; height:90%;" class="box">
        <ul id="typeTree" class="ztree"></ul>
    </div>
</div>
</html>