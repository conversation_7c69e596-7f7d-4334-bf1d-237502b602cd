<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head >
    <title th:text="新闻资讯表表单"></title>
    <th:block th:include="admin/include/head :: head"/>
    <link rel="stylesheet" th:href="@{/resources/file-input/css/fileinput.css}"/>
</head>
<body >

<div class="container-fluid">
    <!-- form start -->
    <div class="row">
        <div class="col-xs-12">
            <form id="form" class="form-horizontal">
                <div class="box-body">

                    <div class="form-group">
                        <label for="title" class="col-sm-2 col-md-2 control-label">标题</label>
                        <div class="col-sm-10 col-md-10">
                            <input type="text" class="form-control" id="title" name="title" placeholder="请输入标题，25字以内"
                                   autocomplete="off">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="author" class="col-sm-2 col-md-2 control-label">作者</label>
                        <div class="col-sm-10 col-md-10">
                            <input type="text" class="form-control" id="author" name="author"
                                   placeholder="填写作者名称，不填写默认登录用户"
                                   autocomplete="off">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="description" class="col-sm-2 col-md-2 control-label">内容描述</label>
                        <div class="col-sm-10  col-md-10">
                            <input type="text" class="form-control" id="description" name="description"
                                   placeholder="请填写内容描述，200字以内" autocomplete="off">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="category" class="col-sm-2 col-md-2 control-label">内容类型</label>
                        <div class="col-sm-10 col-md-10">
                            <input type="text" class="form-control" id="category" name="category" autocomplete="off"  value="通知公告" readonly>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="details" class="col-sm-2 col-md-2 control-label">内容详情</label>
                        <div class="col-sm-10  col-md-10">
                            <textarea id="details" rowspan=""></textarea>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="state" class="col-sm-2 col-md-2 control-label">状态</label>
                        <div class="col-sm-10 col-md-10">
                            <select id="state" name="state" class=" select2" style="width: 100%;">
                                <option value="0">保存</option>
                                <option value="1">正常</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="roofPlacement" class="col-sm-2 col-md-2 control-label">置顶</label>
                        <div class="col-sm-10 col-md-10">
                            <select id="roofPlacement" name="roofPlacement" class=" select2" style="width: 100%;">
                                <option value="0">否</option>
                                <option value="1">是</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="remarks" class="col-sm-2 col-md-2 control-label">备注</label>
                        <div class="col-sm-10  col-md-10">
                            <input type="text" class="form-control" id="remarks" name="remarks" placeholder="备注"
                                   autocomplete="off">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="remarks" class="col-sm-2 col-md-2 control-label">附件上传</label>
                        <div class="col-sm-10  col-md-10">
                            <input type="file"  name="uploadFile" id="uploadFile"  class="form-control">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <button type="button" class="btn btn-info col-sm-offset-6" onclick="formSave()">提交</button>
                </div>
            </form>
        </div>
    </div>
    <!-- customer form end -->
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/ckeditor/ckeditor.js}"></script>
<script th:src="@{/resources/file-input/js/fileinput.js}"></script>
<script th:src="@{/resources/file-input/js/locales/zh.js}"></script>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";
    var index = parent.layer.getFrameIndex(window.name);
    initValidator();
    function formSave(){
        var bootstrapValidator = $('#form').data('bootstrapValidator');
        if(bootstrapValidator.isValid()) {
            save()
        }

    }

    /**
     * 保存栏目数据
     */
    function save() {
        var details = CKEDITOR.instances.details.getData();

        var formData = new FormData($("#form")[0])
        formData.append("details", details)
        form('/admin/sysNews/add', formData, function (res) {
            if (res.code == 0) {
                layer.msg(res.msg, {time: 1000, icon: 1}, function () {
                    parent.location.reload();
                    parent.layer.close(index);
                })

            } else {
                layer.msg(res.msg, {time: 1000, icon: 5})
            }
        })
    }


    $("#uploadFile").fileinput({
        'language':"zh",
        'showPreview':false,
        'showUpload':false,
        'showRemove':true,
        'uploadAsync': true,
        'minFileCount': 1,
        'maxFileCount': 1,
        'maxFileSize': 30720,
        'autoReplace':true
    });

    $(function () {
        $('.select2').select2()
        CKEDITOR.replace('details',
            {
                toolbar:
                    [
                        //加粗     斜体，     下划线      穿过线      下标字        上标字
                        ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript'],
                        // 数字列表          实体列表            减小缩进    增大缩进
                        ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent'],
                        //左对 齐             居中对齐          右对齐          两端对齐
                        ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
                        ['Image'],
                        // 样式       格式      字体    字体大小
                        ['Styles', 'Format', 'Font', 'FontSize'],
                        //全屏           显示区块
                        ['Maximize']
                    ]
            }
        );

    })

    function initValidator() {
        $('#form').bootstrapValidator({
            message: '输入值不满足要求',
            excluded : [':disabled',':hidden'],
            verbose:false,//verbose为false表示一个字段的多个验证规则中，如果有一个验证不通过则继续去验证其他的字段 在0.5.2版本生效
            fields: {
                title: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 255,
                            message: '不能超过255位'
                        }
                    }
                },
                author: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        },
                        stringLength: {
                            max: 36,
                            message: '不能超过36位'
                        }
                    }
                },
                category: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                description: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                state: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                },
                roofPlacement: {
                    validators: {
                        notEmpty: {
                            message: '必填'
                        }
                    }
                }
            }
        });
        $('#form').data('bootstrapValidator').validate();
    }
</script>
</body>
</html>