<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="新闻资讯详情"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div id="sysNewsDetail" class="container-fluid" >
    <div class="row">
        <div class="col-xs-12">
            <form id="form" class="form-horizontal">
                <input type="hidden" id="id" name="id">
                <div class="box-body">
                    <div class="form-group">
                        <label for="title" class="col-sm-2 col-md-2 control-label">标题</label>
                        <div class="col-sm-10 col-md-10">
                            <input type="text" class="form-control" id="title" name="title" placeholder="请输入标题，25字以内"
                                   autocomplete="off" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="author" class="col-sm-2 col-md-2 control-label">作者</label>
                        <div class="col-sm-10 col-md-10">
                            <input type="text" class="form-control" id="author" name="author"
                                   placeholder="填写作者名称，不填写默认登录用户"
                                   autocomplete="off" readonly>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="category" class="col-sm-2 col-md-2 control-label">内容类型</label>
                        <div class="col-sm-10 col-md-10">
                            <input type="text" class="form-control" id="category" name="category" autocomplete="off" readonly>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="details" class="col-sm-2 col-md-2 control-label">内容详情</label>
                        <div class="col-sm-10  col-md-10">
                            <textarea id="details" rowspan="" readonly></textarea>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="state" class="col-sm-2 col-md-2 control-label">状态</label>
                        <div class="col-sm-10 col-md-10">
                            <input type="text" class="form-control" id="state" name="state" autocomplete="off" readonly>
                        </div>
                    </div>
                    <!--<div class="form-group">-->
                        <!--<label for="roofPlacement" class="col-sm-2 col-md-2 control-label">置顶</label>-->
                        <!--<div class="col-sm-10 col-md-10">-->
                            <!--<input type="text" class="form-control" id="roofPlacement" name="roofPlacement" autocomplete="off" readonly>-->
                        <!--</div>-->
                    </div>
                    <div class="form-group">
                        <label for="remarks" class="col-sm-2 col-md-2 control-label">备注</label>
                        <div class="col-sm-10  col-md-10">
                            <input type="text" class="form-control" id="remarks" name="remarks" placeholder="备注"
                                   autocomplete="off" readonly>
                        </div>
                    </div>
                <div class="form-group">
                    <label  class="col-sm-2 col-md-2 control-label">附件</label>
                    <div class="col-sm-10  col-md-10">
                          <span th:if="${fileUrl !=null}">
                        <a th:href="${fileUrl}" th:text="${sysNews.description}"></a>
                          </span>
                        <span th:if="${fileUrl==null}">暂无</span>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/ckeditor/ckeditor.js}"></script>
<script th:src="@{/resources/common/crud.js}"></script>
<script type="text/javascript" th:inline="javascript">
    var sysNews = [[${sysNews}]]
    $(function () {

        $('.select2').select2()

        CKEDITOR.replace('details',
            {
                toolbar:
                    [
                        //加粗     斜体，     下划线      穿过线      下标字        上标字
                        ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript'],
                        // 数字列表          实体列表            减小缩进    增大缩进
                        ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent'],
                        //左对 齐             居中对齐          右对齐          两端对齐
                        ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
                        ['Image'],
                        // 样式       格式      字体    字体大小
                        ['Styles', 'Format', 'Font', 'FontSize'],
                        //全屏           显示区块
                        ['Maximize']
                    ]
            }
        );
        //加载表单数据
        loadInfo()
    })
    function loadInfo() {

        $('#id').val(sysNews.id);

        CKEDITOR.instances.details.setData(sysNews.details);

        if (sysNews.category == '0'){
            $('#category').val('新闻资讯');
        } else {
            $('#category').val('通知公告');
        }

        $('#title').val(sysNews.title);
        if (sysNews.description != null && sysNews.description != '' && sysNews.description != undefined) {
            $('#description').val(sysNews.description)
        }

        if (sysNews.author != null && sysNews.author != '' && sysNews.author != undefined) {
            $('#author').val(sysNews.author)
        }
        if(sysNews.state=='0'){
            return $('#state').val('保存');
        }else {
            return $('#state').val('正常');
        }
        if (sysNews.remarks != null && sysNews.remarks != '' && sysNews.remarks != undefined) {
            $('#remarks').val(sysNews.remarks)
        }

        if(sysNews.roofPlacement !='0'){
            return  $('#roofPlacement').val('否');
        }else {
            return  $('#roofPlacement').val('是');
        }


    }
</script>
</body>
</html>
