<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--引入css-->
<head>
    <title th:text="新闻资讯"></title>
    <th:block th:include="admin/include/head :: head"/>
</head>
<body>
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">查询条件</div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-sm-1" for="searchName">名称</label>
                    <div class="col-sm-3">
                        <input type="text" class="form-control col-sm-10" id="searchName" name="title"
                               placeholder="请输入标题">
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-warning btn-rounded btn-sm " type="button"
                                onclick="restForm('searchForm')"><i class="fa fa-refresh"></i>重置
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm" type="button" style="margin-left:10px"
                                id="btn_query"
                                onclick="formSearch('sysNewstable','/admin/sysNews/list' ,'searchForm')">
                            <i class="fa fa-search"></i>查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <table id="sysNewstable"></table>
</div>

<!--引入js-->
<div th:replace="admin/include/footer :: foot"></div>
<script th:src="@{/resources/common/crud.js}"></script>
<script th:inline="javascript">
    "use strict";

    var addFlag = [[${@permission.hasPermi('sysNews:add')}]];
    var editFlag = [[${@permission.hasPermi('sysNews:edite')}]];
    var delFlag = [[${@permission.hasPermi('sysNews:delete')}]];
    var designFlag = [[${@permission.hasPermi('sysNews:design')}]];

    $(function () {
        var columns = [
            {
                field: 'ck',
                checkbox: true,
                align: 'center',
                valign: 'middle'
            },
            {
                field: "title",
                title: "标题",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "author",
                title: "作者",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "category",
                title: "内容类型",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "state",
                title: "状态",
                align: 'center',
                valign: 'middle',
                formatter: function (value, row, index) {
                    if (value == 0) {
                        return '保存'
                    } else {
                        return '正常'
                    }
                }
            },
            {
                field: "browseVolume",
                title: "浏览量",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "clicks",
                title: "点赞 点击",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "collection",
                title: "收藏次数",
                align: 'center',
                valign: 'middle'
            },
            {
                field: "roofPlacement",
                title: "是否置顶",
                align: 'center',
                valign: 'middle',
                formatter: function (value, row, index) {
                    if (value == 0) {
                        return '否'
                    } else {
                        return '是'
                    }
                }
            },
            {
                field: 'createTime',
                title: '创建时间',
                align: 'center',
                valign: 'middle'
            },
            {
                field: 'id',
                title: '操作',
                align: 'center',
                valign: 'middle',
                cellStyle:{
                    css:{"white-space":"nowrap"}
                },
                formatter: function (value, row, index) {
                    var actions = [];
                    if ("hidden" != editFlag) {
                        actions.push('<button id="btn_edit" type="button" class="btn btn-primary  btn-xs" onclick="updatelayer(\'' + value + '\',\'编辑\',\'/admin/sysNews/edit/\')"><i class="fa fa-edit"></i>编辑</button>');
                    }
                    if ("hidden" != designFlag) {
                        actions.push('<button id="btn_detail" type="button" class="btn btn-success  btn-xs" onclick="detaillayer(\'' + value + '\',\'详情\',\'/admin/sysNews/detail/\')"><i class="fa fa-eye"></i>详情</button>');
                    }
                    if ("hidden" != delFlag) {
                        actions.push('<button type="button" class="btn btn-danger  btn-xs" onclick="deletes(\'/admin/sysNews/delete\',\'' + value + '\')" ><i class="fa fa-trash-o"></i>删除</button>');
                    }
                    return actions.join('');
                }
            }
        ];
        var toolbar = [];
        toolbar.push('<div id="toolbar" class="btn-group">');
        if("hidden" != addFlag){
            toolbar.push('<button id="btn_add" type="button" class="btn btn-info" onclick="poplayer(2,\'新增\',\'/admin/sysNews/toForm\')"><i class="fa fa-plus"></i>新增</button>');
        }
        if("hidden" != delFlag){
            toolbar.push('<button id="btn_delete" type="button" class="btn btn-danger" onclick="deletes(\'/admin/sysNews/delete\')" ><i class="fa fa-trash-o"></i>删除</button>');
        }
        toolbar.push('</div>');
        createBootstrapTable($("#sysNewstable"), '/admin/sysNews/list?category=新闻资讯', columns, false, toolbar.join(''), 'server');
    });
</script>
</body>
</html>