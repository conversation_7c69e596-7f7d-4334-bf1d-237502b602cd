<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>欢迎页</title>
    <link th:replace="admin/include/head :: head">
    <style>
        .message {
            font-size: 18px;
            width: 460px;
            margin: 0 auto;
            margin-top: 30px;
            text-align: left;
            color: #fff;
            display: flex;
            flex-direction: column;
            gap: 30px;
        }
        .weixinContainer{
            display: flex;
        }
    </style>
</head>
<body style='background: url("/resources/awi/dist/img/welcome.jpg")top center no-repeat;background-size: cover'>
<section class="content">
    <h1 class="text-center" style="color: #ffffff">河南省建筑起重机械管理信息系统</h1>
    <div class="row">

        <div class="message">
            <div>
                操作指南：
                <a href="/resources/awi/dist/doc/instructionManual.doc" download="建筑起重机械管理信息系统操作指南" style="color:#ffffff">
                    建筑起重机械管理信息系统操作指南
                </a>
            </div>
            <div>
                操作指南在线文档：
                <a href="https://www.kdocs.cn/public/transfer?i=313812505418&s=ckxECzEl1p7G&t=OQfEHZilliZY" target="_blank" style="color:#ffffff">
                    建筑起重机械管理信息系统操作指南
                </a>
            </div>
            <div>
                系统操作常见问题：
                <a href="https://kdocs.cn/l/cqkUk3IuD3Vu" target="_blank" style="color:#ffffff">
                    系统操作常见问题解答
                </a>
            </div>
            <div>技术支持电话：0371-63284897；0371-55673016</div>
<!--            <div class="weixinContainer">-->
<!--                <span>平台服务微信群：</span>-->
<!--                <img style="width: 100px" th:src="@{/common/img/weixinImage.png}"/>-->
<!--            </div>-->
        </div>
    </div>
</section>
<div th:replace="admin/include/footer :: foot"></div>
</body>
<script th:src="@{/resources/awi/plugins/chartjs/Chart.js}"></script>
<script type="text/javascript" language="javascript">

    $(function () {
        /**
         * 初始化数量值
         * <AUTHOR>
         * @date 2019-01-25
         */
        // initCount('/flowable/taskCountQuery','todo-taskCount');
        // initCount('/bumf2Base/bumfToDoCount','todo-bumfCount');
        // initCount('/msg/notReadCount','toRead-msgCount');+
        // initCount('/sysUser/count','linkMan-userCount');
        // // 加载内容列表
        // contentList('1','tntz');
        // contentList('2','jrys');
        // contentList('4','jsyw');
        // contentList('3','qkzb');
        // contentList('8','zszx');
    })

    var months = ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"];
    var weekdays = new Array("星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六");
    var currentTime;

    function showDate() {
        var date = new Date();

        var year = date.getFullYear(); //getYear();
        var month = undefined;
        var day = date.getDate();
        var weekday = undefined;
        for (var i = 0; i < months.length; i++) {
            if (date.getMonth() == i) {
                month = i + 1; //months[i];
            }
        }

        switch (date.getDay()) {
            case 0:
                weekday = weekdays[0];
                break;
            case 1:
                weekday = weekdays[2];
                break;
            case 2:
                weekday = weekdays[2];
                break;
            case 3:
                weekday = weekdays[3];
                break;
            case 4:
                weekday = weekdays[4];
                break;
            case 5:
                weekday = weekdays[5];
                break;
            case 6:
                weekday = weekdays[6];
                break;

        }

        document.getElementById("date").innerHTML = +year + "年" + month + "月" + day + "日  " + weekday + " ";
    }

    //显示时间
    function showTime() {
        var date = new Date();
        var hour = date.getHours();
        var mins = date.getMinutes();
        var sec = date.getSeconds();

        hour = checkTime(hour);
        mins = checkTime(mins);
        sec = checkTime(sec);

        var result = hour + ":" + mins + ":" + sec;
        currentTime = result;
        document.getElementById("time").innerHTML = result;
        //每隔一段时间就重复执行这个函数,实现和setInterval相同的功能
        //setTimeout(showTime,500);
        //setTimeout("showTime()",500);
    }

    //将小于10的数字前面添加个0
    function checkTime(time) {
        if (time < 10) {
            time = "0" + time;
        }
        return time;
    }

    //这个函数是按照间隔指定的时间来重复执行的
    // setInterval(showTime, 1000);
    /**
     * 文章列表
     * @param typeId 类别id
     * @param ulId  html id
     */
    function contentList(typeId, ulId) {
        $.post("/cmsContent/indexContent", {'category': typeId}, function (res) {
            if (res.code == 0) {
                var resp = res.data;
                for (var i in resp) {
                    var re = resp[i];
                    var title = re.title;
                    if (title.length > 20) {
                        title = title.substr(0, 20) + '...';
                    }
                    $("#" + ulId).append('<li><a href="/cmsContent/toView?msgDetailUrl=/cmsContent/get?id=' + re.id + '" target="_self" class="a" style="font-size:14px;line-height:14px; font-family:宋体">' + title + '</a></li>')
                }
            } else {
                layer.msg('文章加载失败!')
            }
        })
    }

    /**
     * 设置数量值
     * <AUTHOR>
     * @date 2019-01-25
     */
    function initCount(url, labelId) {
        $.post(url, function (res) {
            if (res.code == 0) {
                $("#" + labelId).text(res.data);
            } else {
                layer.msg('数量加载失败!')
            }
        })
    }

    /**
     * 快速跳转 链接
     * 跳转到 信息发布--内容列表，指定类别查询
     * <AUTHOR>
     * @date 2019-01-25
     */
    function jumpCmsContent(_id, _title, _url) {
        // 先根据 菜单id查找该菜单tab标签
        var $menuTab = parent.findTabPanel(_id);
        // 如果该菜单的tab标签已经存在就删除
        if (null != $menuTab) {
            parent.closeTabByPageId(_id)
        }
        // 重新创建tab标签
        parent.addTabs({id: _id, title: _title, close: true, url: _url, urlType: 'abosulte'});
    }
</script>
</html>
