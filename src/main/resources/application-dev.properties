# ================================================
#                   ????
# ================================================
#????
system.name=?????????????????
#??
system.version=1.0.1
#????
system.copyrightYear=2019
# ??ip????
system.addressEnabled=true
#????????????
system.pageSize=10
# ================================================
#                   ????
# ================================================
#????
#project.indexUrl=/index
project.indexUrl=/admin/loginPage
#??????
project.loginPage=/admin/loginPage
#????
project.loginPath=/admin/login
#????
project.logoutPath=/admin/logout
#???????
project.loginIndexPath=/admin/index
project.hasPermissionErrorPath=/admin/hasPermissionError
#??????
project.loginInterceptPaths=/admin/**
#????????
project.loginInterceptExcludePaths=/admin/html/**,/admin/login,/admin/loginPage,/admin/hasPermissionError,/admin/sysDict/getDataDictByType/**,/admin/toRegister,/admin/sysUser/register,/admin/sysUser/checkLoginName,\
  /admin/filingEquipmentFiling/toList,/admin/filingEquipmentFiling/list,\
  /admin/filingEquipmentUseRegistration/list,/admin/filingEquipmentUseRegistration/toList,\
  /admin/filingEquipmentFiling/indexList,/admin/filingEquipmentInstallationForm/allList,/admin/filingEquipmentFilingApplication/syncFilingApplication,/admin/equipmentFilingCancellation/syncFilingCancel,/admin/filingEquipmentRegistrationForm/syncFilingUse,/admin/filingEquipmentUseCancellation/syncUseCancel,/admin/filingEquipmentInstallationForm/syncInstal,\
  /FilingTripartite/**
#??????
project.loggerInterceptPaths=/admin/**,/api/**
#??????
project.loggerInterceptExcludePaths=/admin/loginPage,/admin/login
#??app????????? ?/api/?  ??
project.appInterceptPath=/api/**
#??app????????? ?/api/?  ??
project.appInterceptExcludePaths=/api/html/**
#????api???? true ????false????
project.appCheckFlag=false
#??app?????????
project.appKey=123456879xintong
#????????
project.uploadFileAbsolutePath=D:/uploadFiles/
#????????
project.uploadFileRelativePath=/uploadFile/
project.uploadFileTypes=jpg,png,gif,pdf,doc,docx,zip,rar,xls,xlsx
#?????????
project.captchaEnabled=true
#?????
project.captchaType=math
#ckeditor????
ck.http =  http://127.0.0.1:8085
spring.mvc.favicon.enabled=false
# ================================================
#                   ??????
# ================================================
server.port=8085
# ??contextPath
server.servlet.context-path=/
# tomcat?URI??
server.tomcat.uri-encoding=utf-8
# tomcat?????????200
server.tomcat.max-threads=800
# Tomcat?????????????25
server.tomcat.min-spare-threads=30
#server.error.path=/sys/error
# ================================================
#                   ????
# ================================================
logging.level.com.tzstcl=debug
logging.level.org.springframework=debug
logging.level.org.spring.springboot.dao=debug
logging.file=../logs/buildingRenovation.log
#????{maxRetryCount}???10??
user.password.maxRetryCount=5
# ================================================
#                   ???????
# ================================================
# ????????
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# ?????
spring.datasource.druid.master.url=******************************************************************************************************************************************************
spring.datasource.druid.master.username=root
spring.datasource.druid.master.password=123456
# ?????
# ??????/??????????????
spring.datasource.druid.slave.enabled=false
spring.datasource.druid.slave.url=******************************************************************************************************************************************************
spring.datasource.druid.slave.username=root
spring.datasource.druid.slave.password=123456

# ?????
spring.datasource.druid.initial-size=10
# ???????
spring.datasource.druid.max-active=100
# ???????
spring.datasource.druid.min-idle=10
# ?????????????
spring.datasource.druid.max-wait=60000
# ??PSCache??????????PSCache???
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20
# ???????????????????????????????
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
# ??????????????????????
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.validation-query=SELECT 1 FROM DUAL
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.url-pattern=/monitor/druid/*
spring.datasource.druid.filter.stat.log-slow-sql=true
spring.datasource.druid.filter.stat.slow-sql-millis=1000
spring.datasource.druid.filter.stat.merge-sql=false
spring.datasource.druid.filter.wall.config.multi-statement-allow=true
# ================================================
#                   Spring??
# ================================================
#????
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=1024000000
spring.servlet.multipart.max-request-size=1024000000
spring.servlet.multipart.location=d:/temp
spring.main.allow-bean-definition-overriding=true
#???
spring.devtools.restart.enabled=true
# ================================================
#                   FreeMarker??
# ================================================
# ????????
spring.freemarker.cache=true
# ????
spring.freemarker.charset=UTF-8
# ?????????
spring.freemarker.content-type=text/html
# ???? ??? ""
spring.freemarker.prefix=
# ???? ??? .ftl
spring.freemarker.suffix=.ftl
# ================================================
#                   Thymeleaf??
# ================================================
# ????thymeleaf????
spring.thymeleaf.enabled=true
# ????????????????????false????????true?
spring.thymeleaf.cache=false
# Check that the templates location exists.
spring.thymeleaf.check-template-location=true
# ?????????????text/html
spring.thymeleaf.servlet.content-type=text/html
# ??????????UTF-8
spring.thymeleaf.encoding=UTF-8
# ??????????????,??
#spring.thymeleaf.view-names=
# ??????????????,??
#spring.thymeleaf.excluded-view-names=
# ??????????HTML5
spring.thymeleaf.mode=HTML
# ?????SpringBoot???????classpath:/template/???
spring.thymeleaf.prefix=classpath:/templates/
# ????????.html
spring.thymeleaf.suffix=.html
# ?????????????
#spring.thymeleaf.template-resolver-order=
# ================================================
#                   mybatis??
# ================================================
#mybatis.type-aliases-package=com.tzstcl
mybatis.mapper-locations=classpath*:mybatis/**/*Mapper.xml
# ?????????
mybatis.configLocation=classpath:mybatis/mybatis-config.xml


# PageHelper
pagehelper.helper-dialect=mysql
pagehelper.reasonable=false
pagehelper.support-methods-arguments=false
pagehelper.params=count=countSql
# ???????  ??????? ?? implements WebMvcConfigurer
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jackson.default-property-inclusion=non_null
# ================================================
#                   quartz??
# ================================================
#??????MEMORY?
spring.quartz.job-store-type=jdbc
#????????????3????ALWAYS?EMBEDDED?NEVER?
#spring.quartz.jdbc.initialize-schema=NEVER
#quartz????
spring.quartz.properties.org.quartz.scheduler.instanceName=clusteredScheduler
spring.quartz.properties.org.quartz.scheduler.instanceId=AUTO
spring.quartz.properties.org.quartz.jobStore.tablePrefix=qrtz_
spring.quartz.properties.org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.StdJDBCDelegate
spring.quartz.properties.org.quartz.jobStore.class=org.quartz.impl.jdbcjobstore.JobStoreTX
spring.quartz.properties.org.quartz.jobStore.isClustered=true
spring.quartz.properties.org.quartz.jobStore.clusterCheckinInterval=10000
spring.quartz.properties.org.quartz.jobStore.useProperties=false
spring.quartz.properties.org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
spring.quartz.properties.org.quartz.threadPool.threadCount=10
spring.quartz.properties.org.quartz.threadPool.threadPriority=5
spring.quartz.properties.org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread=true
# ================================================
#                   redis????
# ================================================
spring.cache.type=redis
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.database=2
spring.redis.password=
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-wait=1ms
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
spring.redis.lettuce.shutdown-timeout=100ms
# ================================================
#                   Shiro??
# ================================================
shiro.session.timeout=30
# ================================================
#                   ??XSS??
# ================================================
xss.enable=true
# ?????????????
xss.excludes=/system/notice/*
# ????
xss.urlPatterns=/system/*,/monitor/*,/tool/*
# ================================================
#                   httpclient??
# ================================================
httpclient.connectTimeout=20000
httpclient.requestTimeout=20000
httpclient.socketTimeout=30000
httpclient.defaultMaxPerRoute=100
httpclient.maxTotalConnections=300
httpclient.validateAfterInactivity=20000

# ================================================
#                       ??????????
# ================================================
aliyun.sms.accessKeyId=LTAIcG3aE5wM9qIf
aliyun.sms.accessKeySecret=b5PrEWO0FkEW4CNnljJvFJVhw3xNEk
#?????-??????????
aliyun.sms.signName=??????????
aliyun.sms.captcha.templateCode=SMS_160576731
aliyun.sms.workflow.templateCode=SMS_160576687




# ================================================
#            ????
# ================================================
#???token??
nationl.token.url=http://219.142.101.192/epoint-sso-web/rest/oauth2/token
#????
nation.check.url=http://219.142.101.192/share/zjbzasdzzz/rest/dzzzqzjxsyjdzrest/qzjxsydjz_check
#????
nation.encode.url=http://219.142.101.192/share/zjbzasdzzz/rest/dzzzqzjxsyjdzrest/qzjxsydjz_fm
#????
nation.update.url=http://219.142.101.192/share/zjbzasdzzz/rest/dzzzqzjxsyjdzrest/qzjxsydjz_update
#????
nation.collect.url=http://219.142.101.192/share/zjbzasdzzz/rest/dzzzqzjxsyjdzrest/qzjxsydjz_gj
#????
nation.person.url=http://219.142.101.192/share/zjbzasdzzz/rest/dzzzqzjxsyjdzrest/qzjxsydjz_ryxx
## appkey
nation.key=0d92a7fa-d2ca-43e7-94ed-150c765b263c
#appSecret
nation.Secret=efdb9dc3-c130-4133-b63c-f4e9dbb08bae


# appkey
nation.building.key=51277878-52ea-42b0-a83e-51ba0cf285da
#appSecret
nation.building.Secret=07ac945e-272e-4b0a-8156-6b6457d1796e
#??
#????????????
nation.building.check.url=http://219.142.101.192/share/zjbqzjxbadzzz/rest/dzzzdatarest/getqzjxbadetail
#????????????
nation.building.preBussinessVer.url=http://219.142.101.192/share/zjbqzjxbadzzz/rest/dzzzdatarest/ywblcheck
#??????????
nation.building.bussinessDataVer.url=http://219.142.101.192/share/zjbqzjxbadzzz/rest/dzzzdatarest/ywsjcheck
#??????????
nation.building.assignCode.url=http://219.142.101.192/share/zjbqzjxbadzzz/rest/dzzzdatarest/dzzzfm
# ??????
nation.building.collection.url=http://219.142.101.192/share/zjbqzjxbadzzz/rest/dzzzdatarest/dzzzwjgj
#??????????
nation.building.changeInfo.url=http://219.142.101.192/share/zjbqzjxbadzzz/rest/dzzzdatarest/infoupdate
#??????????
nation.building.changeStatus.url=http://219.142.101.192/share/zjbqzjxbadzzz/rest/dzzzdatarest/statusupdate
#??????????
nation.building.amend.url=http://219.142.101.192/share/zjbqzjxbadzzz/rest/dzzzdatarest/ywsjcorrect
#??sso??
national.quality.afety.push.SSOUrl=http://219.142.101.192/epoint-sso-web

certFileUrl=http://127.0.0.1:8085
certPreviewUrl=https://hngcjs.hnjs.henan.gov.cn/qzj/preview/onlinePreview?url=
