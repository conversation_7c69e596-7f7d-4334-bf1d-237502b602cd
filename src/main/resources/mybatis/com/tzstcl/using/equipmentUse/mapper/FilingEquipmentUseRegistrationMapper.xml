<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.equipmentUse.mapper.FilingEquipmentUseRegistrationMapper">
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.equipmentUse.model.FilingEquipmentUseRegistration">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="use_unit" property="useUnit" jdbcType="VARCHAR"/>
        <result column="user_contact" property="userContact" jdbcType="VARCHAR"/>
        <result column="user_contact_number" property="userContactNumber" jdbcType="VARCHAR"/>
        <result column="property_unit" property="propertyUnit" jdbcType="VARCHAR"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="specification_model" property="specificationModel" jdbcType="VARCHAR"/>
        <result column="factory_time" property="factoryTime" jdbcType="VARCHAR"/>
        <result column="device_filing_code" property="deviceFilingCode" jdbcType="VARCHAR"/>
        <result column="engineering_name" property="engineeringName" jdbcType="VARCHAR"/>
        <result column="project_manager" property="projectManager" jdbcType="VARCHAR"/>
        <result column="installation_unit" property="installationUnit" jdbcType="VARCHAR"/>
        <result column="installation_unit_qualification_level" property="installationUnitQualificationLevel"
                jdbcType="VARCHAR"/>
        <result column="site_installation_leader" property="siteInstallationLeader" jdbcType="VARCHAR"/>
        <result column="installation_unit_qualification_certificate_number"
                property="installationUnitQualificationCertificateNumber" jdbcType="VARCHAR"/>
        <result column="installation_unit_license_safety_permit_number"
                property="installationUnitLicenseSafetyPermitNumber" jdbcType="VARCHAR"/>
        <result column="installation_start_time" property="installationStartTime" jdbcType="DATE"/>
        <result column="installation_end_time" property="installationEndTime" jdbcType="DATE"/>
        <result column="testing_unit" property="testingUnit" jdbcType="VARCHAR"/>
        <result column="testing_date" property="testingDate" jdbcType="DATE"/>
        <result column="testing_leadere" property="testingLeadere" jdbcType="VARCHAR"/>
        <result column="joint_inspection_date" property="jointInspectionDate" jdbcType="DATE"/>
        <result column="first_installation_height" property="firstInstallationHeight" jdbcType="VARCHAR"/>
        <result column="final_use_height" property="finalUseHeight" jdbcType="VARCHAR"/>
        <result column="equipment_type" property="equipmentType" jdbcType="CHAR"/>
        <result column="cancellation_status" property="cancellationStatus" jdbcType="INTEGER"/>
        <result column="use_filing_code" property="useFilingCode" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="del_flag" property="delFlag" jdbcType="CHAR"/>
        <result column="registration_date" property="registrationDate" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        use_unit,
        user_contact,
        user_contact_number,
        property_unit,
        device_name,
        specification_model,
        factory_time,
        device_filing_code,
        engineering_name,
        project_manager,
        installation_unit,
        installation_unit_qualification_level,
        site_installation_leader,
        installation_unit_qualification_certificate_number,
        installation_unit_license_safety_permit_number,
        installation_start_time,
        installation_end_time,
        testing_unit,
        testing_date,
        testing_leadere,
        joint_inspection_date,
        first_installation_height,
        final_use_height,
        equipment_type,
        cancellation_status,
        use_filing_code,
        registration_date,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from filing_equipment_use_registration
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap"
            parameterType="com.tzstcl.filing.equipmentUse.model.FilingEquipmentUseRegistration">
        select
        <include refid="Base_Column_List"/>
        from filing_equipment_use_registration
        where
        id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap"
            parameterType="com.tzstcl.filing.equipmentUse.model.FilingEquipmentUseRegistration">
        select
        <include refid="Base_Column_List"/>
        from filing_equipment_use_registration
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="useUnit != null ">
                and use_unit like concat('%', #{useUnit}, '%')
            </if>
            <if test="userContact != null ">
                and user_contact=#{userContact}
            </if>
            <if test="userContactNumber != null ">
                and user_contact_number=#{userContactNumber}
            </if>
            <if test="propertyUnit != null ">
                and property_unit like concat('%', #{propertyUnit}, '%')
            </if>
            <if test="deviceName != null ">
                and device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="specificationModel != null ">
                and specification_model=#{specificationModel}
            </if>
            <if test="factoryTime != null ">
                and factory_time=#{factoryTime}
            </if>
            <if test="deviceFilingCode != null ">
                and device_filing_code=#{deviceFilingCode}
            </if>
            <if test="engineeringName != null ">
                and engineering_name like concat('%', #{engineeringName}, '%')
            </if>
            <if test="projectManager != null ">
                and project_manager=#{projectManager}
            </if>
            <if test="installationUnit != null ">
                and installation_unit like concat('%', #{installationUnit}, '%')
            </if>
            <if test="installationUnitQualificationLevel != null ">
                and installation_unit_qualification_level=#{installationUnitQualificationLevel}
            </if>
            <if test="siteInstallationLeader != null ">
                and site_installation_leader=#{siteInstallationLeader}
            </if>
            <if test="installationUnitQualificationCertificateNumber != null ">
                and installation_unit_qualification_certificate_number=#{installationUnitQualificationCertificateNumber}
            </if>
            <if test="installationUnitLicenseSafetyPermitNumber != null ">
                and installation_unit_license_safety_permit_number=#{installationUnitLicenseSafetyPermitNumber}
            </if>
            <if test="installationStartTime != null ">
                and installation_start_time=#{installationStartTime}
            </if>
            <if test="installationEndTime != null ">
                and installation_end_time=#{installationEndTime}
            </if>
            <if test="testingUnit != null ">
                and testing_unit=#{testingUnit}
            </if>
            <if test="testingDate != null ">
                and testing_date=#{testingDate}
            </if>
            <if test="testingLeadere != null ">
                and testing_leadere=#{testingLeadere}
            </if>
            <if test="jointInspectionDate != null ">
                and joint_inspection_date=#{jointInspectionDate}
            </if>
            <if test="firstInstallationHeight != null ">
                and first_installation_height=#{firstInstallationHeight}
            </if>
            <if test="finalUseHeight != null ">
                and final_use_height=#{finalUseHeight}
            </if>
            <if test="equipmentType != null ">
                and equipment_type=#{equipmentType}
            </if>
            <if test="cancellationStatus !=null">
                and cancellation_status =#{cancellationStatus}
            </if>
            <if test="useFilingCode != null ">
                and use_filing_code=#{useFilingCode}
            </if>
        </where>
        order by update_time desc
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.equipmentUse.model.FilingEquipmentUseRegistration">
        insert into filing_equipment_use_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="useUnit != null">
                use_unit,
            </if>
            <if test="userContact != null">
                user_contact,
            </if>
            <if test="userContactNumber != null">
                user_contact_number,
            </if>
            <if test="propertyUnit != null">
                property_unit,
            </if>
            <if test="deviceName != null">
                device_name,
            </if>
            <if test="specificationModel != null">
                specification_model,
            </if>
            <if test="factoryTime != null">
                factory_time,
            </if>
            <if test="deviceFilingCode != null">
                device_filing_code,
            </if>
            <if test="engineeringName != null">
                engineering_name,
            </if>
            <if test="projectManager != null">
                project_manager,
            </if>
            <if test="installationUnit != null">
                installation_unit,
            </if>
            <if test="installationUnitQualificationLevel != null">
                installation_unit_qualification_level,
            </if>
            <if test="siteInstallationLeader != null">
                site_installation_leader,
            </if>
            <if test="installationUnitQualificationCertificateNumber != null">
                installation_unit_qualification_certificate_number,
            </if>
            <if test="installationUnitLicenseSafetyPermitNumber != null">
                installation_unit_license_safety_permit_number,
            </if>
            <if test="installationStartTime != null">
                installation_start_time,
            </if>
            <if test="installationEndTime != null">
                installation_end_time,
            </if>
            <if test="testingUnit != null">
                testing_unit,
            </if>
            <if test="testingDate != null">
                testing_date,
            </if>
            <if test="testingLeadere != null">
                testing_leadere,
            </if>
            <if test="jointInspectionDate != null">
                joint_inspection_date,
            </if>
            <if test="firstInstallationHeight != null">
                first_installation_height,
            </if>
            <if test="finalUseHeight != null">
                final_use_height,
            </if>
            <if test="equipmentType != null">
                equipment_type,
            </if>
            <if test="cancellationStatus != null">
                cancellation_status,
            </if>
            <if test="useFilingCode != null">
                use_filing_code,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="registrationDate != null">
                registration_date,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="useUnit != null ">
                #{useUnit},
            </if>
            <if test="userContact != null ">
                #{userContact},
            </if>
            <if test="userContactNumber != null ">
                #{userContactNumber},
            </if>
            <if test="propertyUnit != null ">
                #{propertyUnit},
            </if>
            <if test="deviceName != null ">
                #{deviceName},
            </if>
            <if test="specificationModel != null ">
                #{specificationModel},
            </if>
            <if test="factoryTime != null ">
                #{factoryTime},
            </if>
            <if test="deviceFilingCode != null ">
                #{deviceFilingCode},
            </if>
            <if test="engineeringName != null ">
                #{engineeringName},
            </if>
            <if test="projectManager != null ">
                #{projectManager},
            </if>
            <if test="installationUnit != null ">
                #{installationUnit},
            </if>
            <if test="installationUnitQualificationLevel != null ">
                #{installationUnitQualificationLevel},
            </if>
            <if test="siteInstallationLeader != null ">
                #{siteInstallationLeader},
            </if>
            <if test="installationUnitQualificationCertificateNumber != null ">
                #{installationUnitQualificationCertificateNumber},
            </if>
            <if test="installationUnitLicenseSafetyPermitNumber != null ">
                #{installationUnitLicenseSafetyPermitNumber},
            </if>
            <if test="installationStartTime != null ">
                #{installationStartTime},
            </if>
            <if test="installationEndTime != null ">
                #{installationEndTime},
            </if>
            <if test="testingUnit != null ">
                #{testingUnit},
            </if>
            <if test="testingDate != null ">
                #{testingDate},
            </if>
            <if test="testingLeadere != null ">
                #{testingLeadere},
            </if>
            <if test="jointInspectionDate != null ">
                #{jointInspectionDate},
            </if>
            <if test="firstInstallationHeight != null ">
                #{firstInstallationHeight},
            </if>
            <if test="finalUseHeight != null ">
                #{finalUseHeight},
            </if>
            <if test="equipmentType != null ">
                #{equipmentType},
            </if>
            <if test="cancellationStatus != null">
                #{cancellationStatus},
            </if>
            <if test="useFilingCode != null ">
                #{useFilingCode},
            </if>
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if>
            <if test="registrationDate != null">
                #{registrationDate},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.equipmentUse.model.FilingEquipmentUseRegistration">
        update filing_equipment_use_registration
        <set>
            <if test="useUnit != null">
                use_unit=#{useUnit},
            </if>
            <if test="userContact != null">
                user_contact=#{userContact},
            </if>
            <if test="userContactNumber != null">
                user_contact_number=#{userContactNumber},
            </if>
            <if test="propertyUnit != null">
                property_unit=#{propertyUnit},
            </if>
            <if test="deviceName != null">
                device_name=#{deviceName},
            </if>
            <if test="specificationModel != null">
                specification_model=#{specificationModel},
            </if>
            <if test="factoryTime != null">
                factory_time=#{factoryTime},
            </if>
            <if test="deviceFilingCode != null">
                device_filing_code=#{deviceFilingCode},
            </if>
            <if test="engineeringName != null">
                engineering_name=#{engineeringName},
            </if>
            <if test="projectManager != null">
                project_manager=#{projectManager},
            </if>
            <if test="installationUnit != null">
                installation_unit=#{installationUnit},
            </if>
            <if test="installationUnitQualificationLevel != null">
                installation_unit_qualification_level=#{installationUnitQualificationLevel},
            </if>
            <if test="siteInstallationLeader != null">
                site_installation_leader=#{siteInstallationLeader},
            </if>
            <if test="installationUnitQualificationCertificateNumber != null">
                installation_unit_qualification_certificate_number=#{installationUnitQualificationCertificateNumber},
            </if>
            <if test="installationUnitLicenseSafetyPermitNumber != null">
                installation_unit_license_safety_permit_number=#{installationUnitLicenseSafetyPermitNumber},
            </if>
            <if test="installationStartTime != null">
                installation_start_time=#{installationStartTime},
            </if>
            <if test="installationEndTime != null">
                installation_end_time=#{installationEndTime},
            </if>
            <if test="testingUnit != null">
                testing_unit=#{testingUnit},
            </if>
            <if test="testingDate != null">
                testing_date=#{testingDate},
            </if>
            <if test="testingLeadere != null">
                testing_leadere=#{testingLeadere},
            </if>
            <if test="jointInspectionDate != null">
                joint_inspection_date=#{jointInspectionDate},
            </if>
            <if test="firstInstallationHeight != null">
                first_installation_height=#{firstInstallationHeight},
            </if>
            <if test="finalUseHeight != null">
                final_use_height=#{finalUseHeight},
            </if>
            <if test="equipmentType != null">
                equipment_type=#{equipmentType},
            </if>
            <if test="cancellationStatus != null">
                cancellation_status = #{cancellationStatus},
            </if>
            <if test="useFilingCode != null">
                use_filing_code=#{useFilingCode},
            </if>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
            <if test="registrationDate != null">
                registration_date = #{registrationDate},
            </if>
        </set>
        WHERE id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update filing_equipment_use_registration set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update filing_equipment_use_registration set
        del_flag='1'
        WHERE id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
            #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into filing_equipment_use_registration(id, use_unit, user_contact, user_contact_number, property_unit,
        device_name, specification_model, factory_time, device_filing_code, engineering_name, project_manager,
        installation_unit, installation_unit_qualification_level, site_installation_leader,
        installation_unit_qualification_certificate_number, installation_unit_license_safety_permit_number,
        installation_start_time, installation_end_time, testing_unit, testing_date, testing_leadere,
        joint_inspection_date, first_installation_height, final_use_height, equipment_type, audit_reject_reason,
        audit_status, audit_time, use_filing_code, create_by, create_time, update_by, update_time, remarks, del_flag)
        values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id}, #{item.useUnit}, #{item.userContact}, #{item.userContactNumber}, #{item.propertyUnit},
            #{item.deviceName}, #{item.specificationModel}, #{item.factoryTime}, #{item.deviceFilingCode},
            #{item.engineeringName}, #{item.projectManager}, #{item.installationUnit},
            #{item.installationUnitQualificationLevel}, #{item.siteInstallationLeader},
            #{item.installationUnitQualificationCertificateNumber}, #{item.installationUnitLicenseSafetyPermitNumber},
            #{item.installationStartTime}, #{item.installationEndTime}, #{item.testingUnit}, #{item.testingDate},
            #{item.testingLeadere}, #{item.jointInspectionDate}, #{item.firstInstallationHeight},
            #{item.finalUseHeight}, #{item.equipmentType}, #{item.auditRejectReason}, #{item.auditStatus},
            #{item.auditTime}, #{item.useFilingCode}, #{item.createBy}, #{item.createTime}, #{item.updateBy},
            #{item.updateTime}, #{item.remarks}, #{item.delFlag})
        </foreach>
    </insert>
    <insert id="insertApplication"
            parameterType="com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationForm">
        insert into filing_equipment_use_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            cancellation_status,
            <if test="id != null">
                id,
            </if>
            <if test="useUnit != null">
                use_unit,
            </if>
            <if test="userContact != null">
                user_contact,
            </if>
            <if test="userContactNumber != null">
                user_contact_number,
            </if>
            <if test="propertyUnit != null">
                property_unit,
            </if>
            <if test="deviceName != null">
                device_name,
            </if>
            <if test="specificationModel != null">
                specification_model,
            </if>
            <if test="factoryTime != null">
                factory_time,
            </if>
            <if test="deviceFilingCode != null">
                device_filing_code,
            </if>
            <if test="engineeringName != null">
                engineering_name,
            </if>
            <if test="projectManager != null">
                project_manager,
            </if>
            <if test="installationUnit != null">
                installation_unit,
            </if>
            <if test="installationUnitQualificationLevel != null">
                installation_unit_qualification_level,
            </if>
            <if test="siteInstallationLeader != null">
                site_installation_leader,
            </if>
            <if test="installationUnitQualificationCertificateNumber != null">
                installation_unit_qualification_certificate_number,
            </if>
            <if test="installationUnitLicenseSafetyPermitNumber != null">
                installation_unit_license_safety_permit_number,
            </if>
            <if test="installationStartTime != null">
                installation_start_time,
            </if>
            <if test="installationEndTime != null">
                installation_end_time,
            </if>
            <if test="testingUnit != null">
                testing_unit,
            </if>
            <if test="testingDate != null">
                testing_date,
            </if>
            <if test="testingLeadere != null">
                testing_leadere,
            </if>
            <if test="jointInspectionDate != null">
                joint_inspection_date,
            </if>
            <if test="firstInstallationHeight != null">
                first_installation_height,
            </if>
            <if test="finalUseHeight != null">
                final_use_height,
            </if>
            <if test="equipmentType != null">
                equipment_type,
            </if>
            <if test="useFilingCode != null">
                use_filing_code,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                registration_date,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
        0,
            <if test="id != null">
                #{id},
            </if>
            <if test="useUnit != null">
                #{useUnit},
            </if>
            <if test="userContact != null">
                #{userContact},
            </if>
            <if test="userContactNumber != null">
                #{userContactNumber},
            </if>
            <if test="propertyUnit != null">
                #{propertyUnit},
            </if>
            <if test="deviceName != null">
                #{deviceName},
            </if>
            <if test="specificationModel != null">
                #{specificationModel},
            </if>
            <if test="factoryTime != null">
                #{factoryTime},
            </if>
            <if test="deviceFilingCode != null">
                #{deviceFilingCode},
            </if>
            <if test="engineeringName != null">
                #{engineeringName},
            </if>
            <if test="projectManager != null">
                #{projectManager},
            </if>
            <if test="installationUnit != null">
                #{installationUnit},
            </if>
            <if test="installationUnitQualificationLevel != null">
                #{installationUnitQualificationLevel},
            </if>
            <if test="siteInstallationLeader != null">
                #{siteInstallationLeader},
            </if>
            <if test="installationUnitQualificationCertificateNumber != null">
                #{installationUnitQualificationCertificateNumber},
            </if>
            <if test="installationUnitLicenseSafetyPermitNumber != null">
                #{installationUnitLicenseSafetyPermitNumber},
            </if>
            <if test="installationStartTime != null">
                #{installationStartTime},
            </if>
            <if test="installationEndTime != null">
                #{installationEndTime},
            </if>
            <if test="testingUnit != null">
                #{testingUnit},
            </if>
            <if test="testingDate != null">
                #{testingDate},
            </if>
            <if test="testingLeadere != null">
                #{testingLeadere},
            </if>
            <if test="jointInspectionDate != null">
                #{jointInspectionDate},
            </if>
            <if test="firstInstallationHeight != null">
                #{firstInstallationHeight},
            </if>
            <if test="finalUseHeight != null">
                #{finalUseHeight},
            </if>
            <if test="equipmentType != null">
                #{equipmentType},
            </if>
            <if test="useFilingCode != null">
                #{useFilingCode},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="remarks != null">
                #{remarks},
            </if>
            <if test="delFlag != null">
                #{delFlag},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>
    <select id="getCode" resultType="java.lang.Integer">
        select `value` from code where type = 'use' and del_flag = 0
    </select>
        <select id="getByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
            select
            <include refid="Base_Column_List" />
            from filing_equipment_use_registration
            where use_filing_code = #{code}
        </select>
    <update id="addCode" parameterType="java.lang.Integer">
        update code set `value` = #{code}  where type = 'use'
    </update>

</mapper>
