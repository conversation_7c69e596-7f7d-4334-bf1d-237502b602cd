<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.equipmentUseApplication.mapper.FilingEquipmentRegistrationFormMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationForm" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="application_date" property="applicationDate" jdbcType="DATE" />
        <result column="use_unit" property="useUnit" jdbcType="VARCHAR" />
        <result column="user_contact" property="userContact" jdbcType="VARCHAR" />
        <result column="user_contact_number" property="userContactNumber" jdbcType="VARCHAR" />
        <result column="property_unit" property="propertyUnit" jdbcType="VARCHAR" />
        <result column="device_name" property="deviceName" jdbcType="VARCHAR" />
        <result column="specification_model" property="specificationModel" jdbcType="VARCHAR" />
        <result column="factory_time" property="factoryTime" jdbcType="VARCHAR" />
        <result column="device_filing_code" property="deviceFilingCode" jdbcType="VARCHAR" />
        <result column="engineering_name" property="engineeringName" jdbcType="VARCHAR" />
        <result column="project_manager" property="projectManager" jdbcType="VARCHAR" />
        <result column="installation_unit" property="installationUnit" jdbcType="VARCHAR" />
        <result column="installation_unit_qualification_level" property="installationUnitQualificationLevel" jdbcType="VARCHAR" />
        <result column="site_installation_leader" property="siteInstallationLeader" jdbcType="VARCHAR" />
        <result column="installation_unit_qualification_certificate_number" property="installationUnitQualificationCertificateNumber" jdbcType="VARCHAR" />
        <result column="installation_unit_license_safety_permit_number" property="installationUnitLicenseSafetyPermitNumber" jdbcType="VARCHAR" />
        <result column="installation_start_time" property="installationStartTime" jdbcType="DATE" />
        <result column="installation_end_time" property="installationEndTime" jdbcType="DATE" />
        <result column="testing_unit" property="testingUnit" jdbcType="VARCHAR" />
        <result column="testing_date" property="testingDate" jdbcType="DATE" />
        <result column="testing_leadere" property="testingLeadere" jdbcType="VARCHAR" />
        <result column="joint_inspection_date" property="jointInspectionDate" jdbcType="DATE" />
        <result column="first_installation_height" property="firstInstallationHeight" jdbcType="VARCHAR" />
        <result column="final_use_height" property="finalUseHeight" jdbcType="VARCHAR" />
        <result column="filing_application_form_path" property="filingApplicationFormPath" jdbcType="VARCHAR" />
        <result column="equipment_record_certificate" property="equipmentRecordCertificate" jdbcType="VARCHAR" />
        <result column="equipment_leasing_contract" property="equipmentLeasingContract" jdbcType="VARCHAR" />
        <result column="inspection_report_path" property="inspectionReportPath" jdbcType="VARCHAR" />
        <result column="installation_acceptance_report" property="installationAcceptanceReport" jdbcType="VARCHAR" />
        <result column="operators_qualification_certificate" property="operatorsQualificationCertificate" jdbcType="VARCHAR" />
        <result column="equipment_maintenance_management_system_path" property="equipmentMaintenanceManagementSystemPath" jdbcType="VARCHAR" />
        <result column="safety_accidents_emergency_plan_path" property="safetyAccidentsEmergencyPlanPath" jdbcType="VARCHAR" />
        <result column="audit_status" property="auditStatus" jdbcType="INTEGER" />
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
        <result column="audit_reject_reason" property="auditRejectReason" jdbcType="VARCHAR" />
        <result column="equipment_type" property="equipmentType" jdbcType="CHAR" />
        <result column="use_filing_code" property="useFilingCode" jdbcType="VARCHAR" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
        <result column="installation_address" property="installationAddress" jdbcType="VARCHAR" />
        <result column="property_unit_code" property="propertyUnitCode" jdbcType="VARCHAR" />
        <result column="use_unit_code" property="useUnitCode" jdbcType="VARCHAR" />
        <result column="user_contact_card" property="userContactCard" jdbcType="VARCHAR" />
        <result column="produce_unit" property="produceUnit" jdbcType="VARCHAR" />
        <result column="produce_unit_code" property="produceUnitCode" jdbcType="VARCHAR" />
        <result column="upkeep_unit" property="upkeepUnit" jdbcType="VARCHAR" />
        <result column="upkeep_unit_code" property="upkeepUnitCode" jdbcType="VARCHAR" />
        <result column="installation_unit_code" property="installationUnitCode" jdbcType="VARCHAR" />
        <result column="site_installation_leader_card" property="siteInstallationLeaderCard" jdbcType="VARCHAR" />
        <result column="testing_unit_code" property="testingUnitCode" jdbcType="VARCHAR" />
        <result column="testing_leadere_card" property="testingLeadereCard" jdbcType="VARCHAR" />
        <result column="is_build_license" property="isBuildLicense" jdbcType="VARCHAR" />
        <result column="build_license_code" property="buildLicenseCode" jdbcType="VARCHAR" />
        <result column="factory_number" property="factoryNumber" jdbcType="VARCHAR" />
        <result column="business_id" property="businessId" jdbcType="INTEGER" />
        <result column="cert_id" property="certId" jdbcType="VARCHAR" />
        <result column="arm_length" property="armLength" jdbcType="VARCHAR" />
        <result column="max_weight" property="maxWeight" jdbcType="VARCHAR" />
        <result column="max_height" property="maxHeight" jdbcType="VARCHAR" />
        <result column="maximum_capacity" property="maximumCapacity" jdbcType="VARCHAR" />
        <result column="s_regular_weight" property="sRegularWeight" jdbcType="VARCHAR" />
        <result column="s_regular_speed" property="sRegularSpeed" jdbcType="VARCHAR" />
        <result column="w_regular_weight" property="wRegularWeight" jdbcType="VARCHAR" />
        <result column="w_regular_speed" property="wRegularSpeed" jdbcType="VARCHAR" />
        <result column="engineering_address_detail" property="engineeringAddressDetail" jdbcType="VARCHAR" />
        <result column="engineering_area_code" property="engineeringAreaCode"/>
        <result column="installation_technician" property="installationTechnician"/>
        <result column="installation_technician_card" property="installationTechnicianCard"/>
        <result column="installation_manager" property="installationManager"/>
        <result column="installation_manager_card" property="installationManagerCard"/>
        <result column="use_produce_safe_manager" property="useProduceSafeManager"/>
        <result column="use_produce_safe_manager_card" property="useProduceSafeManagerCard"/>
        <result column="use_produce_device_manager" property="useProduceDeviceManager"/>
        <result column="use_produce_device_manager_card" property="useProduceDeviceManagerCard"/>
        <result column="use_signal_worker" property="useSignalWorker"/>
        <result column="use_signal_worker_card" property="useSignalWorkerCard"/>
        <result column="use_device_driver" property="useDeviceDriver"/>
        <result column="use_device_driver_card" property="useDeviceDriverCard"/>
        <result column="isUseCancel" property="isUseCancel"/>
        <result column="testing_content" property="testingContent"/>
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        user_id,
        application_date,
        use_unit,
        user_contact,
        user_contact_number,
        property_unit,
        device_name,
        specification_model,
        factory_time,
        device_filing_code,
        engineering_name,
        project_manager,
        installation_unit,
        installation_unit_qualification_level,
        site_installation_leader,
        installation_unit_qualification_certificate_number,
        installation_unit_license_safety_permit_number,
        installation_start_time,
        installation_end_time,
        testing_unit,
        testing_date,
        testing_leadere,
        joint_inspection_date,
        first_installation_height,
        final_use_height,
        filing_application_form_path,
        equipment_record_certificate,
        equipment_leasing_contract,
        inspection_report_path,
        installation_acceptance_report,
        operators_qualification_certificate,
        equipment_maintenance_management_system_path,
        safety_accidents_emergency_plan_path,
        audit_status,
        audit_time,
        audit_reject_reason,
        equipment_type,
        use_filing_code,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag,
        installation_address,
        property_unit_code,
        use_unit_code,
        user_contact_card,
        produce_unit,
        produce_unit_code,
        upkeep_unit,
        upkeep_unit_code,
        installation_unit_code,
        site_installation_leader_card,
        testing_unit_code,
        testing_leadere_card,
        is_build_license,
        build_license_code,
        factory_number,
        business_id,
        cert_id,
        arm_length,
        max_weight,
        max_height,
        maximum_capacity,
        s_regular_weight,
        s_regular_speed,
        w_regular_weight,
        w_regular_speed,
        engineering_address_detail,
        engineering_area_code,
        installation_technician,
        installation_technician_card,
        installation_manager,
        installation_manager_card,
        use_produce_safe_manager,
        use_produce_safe_manager_card,
        use_produce_device_manager,
        use_produce_device_manager_card,
        use_signal_worker,
        use_signal_worker_card,
        use_device_driver,
        use_device_driver_card,
        isUseCancel,
        testing_content
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_registration_form
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationForm">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_registration_form
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationForm">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_registration_form
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="userId != null ">
                and user_id=#{userId}
            </if>
            <if test="applicationDate != null ">
                and application_date=#{applicationDate}
            </if>
            <if test="useUnit != null ">
                and use_unit like concat('%', #{useUnit}, '%')
            </if>
            <if test="userContact != null ">
                and user_contact=#{userContact}
            </if>
            <if test="userContactNumber != null ">
                and user_contact_number=#{userContactNumber}
            </if>
            <if test="propertyUnit != null ">
                and property_unit like concat('%', #{propertyUnit}, '%')
            </if>
            <if test="deviceName != null ">
                and device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="specificationModel != null ">
                and specification_model=#{specificationModel}
            </if>
            <if test="factoryTime != null ">
                and factory_time=#{factoryTime}
            </if>
            <if test="deviceFilingCode != null ">
                and device_filing_code=#{deviceFilingCode}
            </if>
            <if test="engineeringName != null ">
                and engineering_name like concat('%', #{engineeringName}, '%')
            </if>
            <if test="projectManager != null ">
                and project_manager=#{projectManager}
            </if>
            <if test="installationUnit != null ">
                and installation_unit=#{installationUnit}
            </if>
            <if test="installationUnitQualificationLevel != null ">
                and installation_unit_qualification_level=#{installationUnitQualificationLevel}
            </if>
            <if test="siteInstallationLeader != null ">
                and site_installation_leader=#{siteInstallationLeader}
            </if>
            <if test="installationUnitQualificationCertificateNumber != null ">
                and installation_unit_qualification_certificate_number=#{installationUnitQualificationCertificateNumber}
            </if>
            <if test="installationUnitLicenseSafetyPermitNumber != null ">
                and installation_unit_license_safety_permit_number=#{installationUnitLicenseSafetyPermitNumber}
            </if>
            <if test="installationStartTime != null ">
                and installation_start_time=#{installationStartTime}
            </if>
            <if test="installationEndTime != null ">
                and installation_end_time=#{installationEndTime}
            </if>
            <if test="testingUnit != null ">
                and testing_unit=#{testingUnit}
            </if>
            <if test="testingDate != null ">
                and testing_date=#{testingDate}
            </if>
            <if test="testingLeadere != null ">
                and testing_leadere=#{testingLeadere}
            </if>
            <if test="jointInspectionDate != null ">
                and joint_inspection_date=#{jointInspectionDate}
            </if>
            <if test="firstInstallationHeight != null ">
                and first_installation_height=#{firstInstallationHeight}
            </if>
            <if test="finalUseHeight != null ">
                and final_use_height=#{finalUseHeight}
            </if>
            <if test="filingApplicationFormPath != null ">
                and filing_application_form_path=#{filingApplicationFormPath}
            </if>
            <if test="equipmentRecordCertificate != null ">
                and equipment_record_certificate=#{equipmentRecordCertificate}
            </if>
            <if test="equipmentLeasingContract != null ">
                and equipment_leasing_contract=#{equipmentLeasingContract}
            </if>
            <if test="equipmentType != null">
                and equipment_type = #{equipmentType}
            </if>
            <if test="inspectionReportPath != null ">
                and inspection_report_path=#{inspectionReportPath}
            </if>
            <if test="installationAcceptanceReport != null ">
                and installation_acceptance_report=#{installationAcceptanceReport}
            </if>
            <if test="operatorsQualificationCertificate != null ">
                and operators_qualification_certificate=#{operatorsQualificationCertificate}
            </if>
            <if test="equipmentMaintenanceManagementSystemPath != null ">
                and equipment_maintenance_management_system_path=#{equipmentMaintenanceManagementSystemPath}
            </if>
            <if test="safetyAccidentsEmergencyPlanPath != null ">
                and safety_accidents_emergency_plan_path=#{safetyAccidentsEmergencyPlanPath}
            </if>
            <if test="auditStatus != null ">
                and audit_status=#{auditStatus}
            </if>
            <if test="auditTime != null ">
                and audit_time=#{auditTime}
            </if>
            <if test="auditRejectReason != null ">
                and audit_reject_reason=#{auditRejectReason}
            </if>
            <if test="useFilingCode != null ">
                and use_filing_code=#{useFilingCode}
            </if>
            <if test="installationAddress != null "> and installation_address=#{installationAddress}</if>
            <if test="propertyUnitCode != null "> and property_unit_code=#{propertyUnitCode}</if>
            <if test="useUnitCode != null "> and use_unit_code=#{useUnitCode}</if>
            <if test="userContactCard != null "> and user_contact_card=#{userContactCard}</if>
            <if test="produceUnit != null "> and produce_unit=#{produceUnit}</if>
            <if test="produceUnitCode != null "> and produce_unit_code=#{produceUnitCode}</if>
            <if test="upkeepUnit != null "> and upkeep_unit=#{upkeepUnit}</if>
            <if test="upkeepUnitCode != null "> and upkeep_unit_code=#{upkeepUnitCode}</if>
            <if test="installationUnitCode != null "> and installation_unit_code=#{installationUnitCode}</if>
            <if test="siteInstallationLeaderCard != null "> and site_installation_leader_card=#{siteInstallationLeaderCard}</if>
            <if test="testingUnitCode != null "> and testing_unit_code=#{testingUnitCode}</if>
            <if test="testingLeadereCard != null "> and testing_leadere_card=#{testingLeadereCard}</if>
            <if test="isBuildLicense != null "> and is_build_license=#{isBuildLicense}</if>
            <if test="buildLicenseCode != null "> and build_license_code=#{buildLicenseCode}</if>
            <if test="factoryNumber != null "> and factory_number=#{factoryNumber}</if>
            <if test="businessId != null "> and business_id=#{businessId}</if>
            <if test="certId != null "> and cert_id=#{certId}</if>
            <if test="armLength != null "> and arm_length=#{armLength}</if>
            <if test="maxWeight != null "> and max_weight=#{maxWeight}</if>
            <if test="maxHeight != null "> and max_height=#{maxHeight}</if>
            <if test="maximumCapacity != null "> and maximum_capacity=#{maximumCapacity}</if>
            <if test="sRegularWeight != null "> and s_regular_weight=#{sRegularWeight}</if>
            <if test="sRegularSpeed != null "> and s_regular_speed=#{sRegularSpeed}</if>
            <if test="wRegularWeight != null "> and w_regular_weight=#{wRegularWeight}</if>
            <if test="wRegularSpeed != null "> and w_regular_speed=#{wRegularSpeed}</if>
            <if test="engineeringAddressDetail != null "> and engineering_address_detail=#{engineeringAddressDetail}</if>
            <if test="engineeringAreaCode != null "> and engineering_area_code=#{engineeringAreaCode}</if>
            <if test="installationTechnician != null "> and installation_technician=#{installationTechnician}</if>
            <if test="installationTechnicianCard != null "> and installation_technician_card=#{installationTechnicianCard}</if>
            <if test="installationManager != null "> and installation_manager=#{installationManager}</if>
            <if test="installationManagerCard != null "> and installation_manager_card=#{installationManagerCard}</if>
            <if test="useProduceSafeManager != null "> and use_produce_safe_manager=#{useProduceSafeManager}</if>
            <if test="useProduceSafeManagerCard != null "> and use_produce_safe_manager_card=#{useProduceSafeManagerCard}</if>
            <if test="useProduceDeviceManager != null "> and use_produce_device_manager=#{useProduceDeviceManager}</if>
            <if test="useProduceDeviceManagerCard != null "> and use_produce_device_manager_card=#{useProduceDeviceManagerCard}</if>
            <if test="useSignalWorker != null "> and use_signal_worker=#{useSignalWorker}</if>
            <if test="useSignalWorkerCard != null "> and use_signal_worker_card=#{useSignalWorkerCard}</if>
            <if test="useDeviceDriver != null "> and use_device_driver=#{useDeviceDriver}</if>
            <if test="useDeviceDriverCard != null "> and use_device_driver_card=#{useDeviceDriverCard}</if>
            <if test="isUseCancel != null "> and isUseCancel=#{isUseCancel}</if>
            <if test="testingContent != null "> and testing_content=#{testingContent}</if>
         </where>
        order by create_time desc
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationForm">
        insert into filing_equipment_registration_form
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="userId != null">
                    user_id,
                </if>
                <if test="applicationDate != null">
                    application_date,
                </if>
                <if test="useUnit != null">
                    use_unit,
                </if>
                <if test="userContact != null">
                    user_contact,
                </if>
                <if test="userContactNumber != null">
                    user_contact_number,
                </if>
                <if test="propertyUnit != null">
                    property_unit,
                </if>
                <if test="deviceName != null">
                    device_name,
                </if>
                <if test="specificationModel != null">
                    specification_model,
                </if>
                <if test="factoryTime != null">
                    factory_time,
                </if>
                <if test="deviceFilingCode != null">
                    device_filing_code,
                </if>
                <if test="engineeringName != null">
                    engineering_name,
                </if>
                <if test="projectManager != null">
                    project_manager,
                </if>
                <if test="installationUnit != null">
                    installation_unit,
                </if>
                <if test="installationUnitQualificationLevel != null">
                    installation_unit_qualification_level,
                </if>
                <if test="siteInstallationLeader != null">
                    site_installation_leader,
                </if>
                <if test="installationUnitQualificationCertificateNumber != null">
                    installation_unit_qualification_certificate_number,
                </if>
                <if test="installationUnitLicenseSafetyPermitNumber != null">
                    installation_unit_license_safety_permit_number,
                </if>
                <if test="installationStartTime != null">
                    installation_start_time,
                </if>
                <if test="installationEndTime != null">
                    installation_end_time,
                </if>
                <if test="testingUnit != null">
                    testing_unit,
                </if>
                <if test="testingDate != null">
                    testing_date,
                </if>
                <if test="testingLeadere != null">
                    testing_leadere,
                </if>
                <if test="jointInspectionDate != null">
                    joint_inspection_date,
                </if>
                <if test="firstInstallationHeight != null">
                    first_installation_height,
                </if>
                <if test="finalUseHeight != null">
                    final_use_height,
                </if>
                <if test="filingApplicationFormPath != null">
                    filing_application_form_path,
                </if>
                <if test="equipmentRecordCertificate != null">
                    equipment_record_certificate,
                </if>
                <if test="equipmentLeasingContract != null">
                    equipment_leasing_contract,
                </if>
                <if test="inspectionReportPath != null">
                    inspection_report_path,
                </if>
                <if test="installationAcceptanceReport != null">
                    installation_acceptance_report,
                </if>
                <if test="operatorsQualificationCertificate != null">
                    operators_qualification_certificate,
                </if>
                <if test="equipmentType != null">
                    equipment_type,
                </if>
                <if test="equipmentMaintenanceManagementSystemPath != null">
                    equipment_maintenance_management_system_path,
                </if>
                <if test="safetyAccidentsEmergencyPlanPath != null">
                    safety_accidents_emergency_plan_path,
                </if>
                <if test="auditStatus != null">
                    audit_status,
                </if>
                <if test="auditTime != null">
                    audit_time,
                </if>
                <if test="auditRejectReason != null">
                    audit_reject_reason,
                </if>
                <if test="useFilingCode != null">
                    use_filing_code,
                </if>
                <if test="createBy != null">
                    create_by,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="updateBy != null">
                    update_by,
                </if>
                <if test="updateTime != null">
                    update_time,
                </if>
                <if test="remarks != null">
                    remarks,
                </if>
                <if test="delFlag != null">
                    del_flag,
                </if>
            <if test="installationAddress != null"> installation_address,</if>
            <if test="propertyUnitCode != null"> property_unit_code,</if>
            <if test="useUnitCode != null"> use_unit_code,</if>
            <if test="userContactCard != null"> user_contact_card,</if>
            <if test="produceUnit != null"> produce_unit,</if>
            <if test="produceUnitCode != null"> produce_unit_code,</if>
            <if test="upkeepUnit != null"> upkeep_unit,</if>
            <if test="upkeepUnitCode != null"> upkeep_unit_code,</if>
            <if test="installationUnitCode != null"> installation_unit_code,</if>
            <if test="siteInstallationLeaderCard != null"> site_installation_leader_card,</if>
            <if test="testingUnitCode != null"> testing_unit_code,</if>
            <if test="testingLeadereCard != null"> testing_leadere_card,</if>
            <if test="isBuildLicense != null"> is_build_license,</if>
            <if test="buildLicenseCode != null"> build_license_code,</if>
            <if test="factoryNumber != null"> factory_number,</if>
            <if test="businessId != null"> business_id,</if>
            <if test="certId != null"> cert_id,</if>
            <if test="armLength != null"> arm_length,</if>
            <if test="maxWeight != null"> max_weight,</if>
            <if test="maxHeight != null"> max_height,</if>
            <if test="maximumCapacity != null"> maximum_capacity,</if>
            <if test="sRegularWeight != null"> s_regular_weight,</if>
            <if test="sRegularSpeed != null"> s_regular_speed,</if>
            <if test="wRegularWeight != null"> w_regular_weight,</if>
            <if test="wRegularSpeed != null"> w_regular_speed,</if>
            <if test="engineeringAddressDetail != null"> engineering_address_detail,</if>
            <if test="engineeringAreaCode != null"> engineering_area_code,</if>
            <if test="installationTechnician != null"> installation_technician,</if>
            <if test="installationTechnicianCard != null"> installation_technician_card,</if>
            <if test="installationManager != null"> installation_manager,</if>
            <if test="installationManagerCard != null"> installation_manager_card,</if>
            <if test="useProduceSafeManager != null"> use_produce_safe_manager,</if>
            <if test="useProduceSafeManagerCard != null"> use_produce_safe_manager_card,</if>
            <if test="useProduceDeviceManager != null"> use_produce_device_manager,</if>
            <if test="useProduceDeviceManagerCard != null"> use_produce_device_manager_card,</if>
            <if test="useSignalWorker != null"> use_signal_worker,</if>
            <if test="useSignalWorkerCard != null"> use_signal_worker_card,</if>
            <if test="useDeviceDriver != null"> use_device_driver,</if>
            <if test="useDeviceDriverCard != null"> use_device_driver_card,</if>
            <if test="isUseCancel != null"> isUseCancel,</if>
            <if test="testingContent != null"> testing_content,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="userId != null ">
                #{userId},
            </if>
            <if test="applicationDate != null ">
                #{applicationDate},
            </if>
            <if test="useUnit != null ">
                #{useUnit},
            </if>
            <if test="userContact != null ">
                #{userContact},
            </if>
            <if test="userContactNumber != null ">
                #{userContactNumber},
            </if>
            <if test="propertyUnit != null ">
                #{propertyUnit},
            </if>
            <if test="deviceName != null ">
                #{deviceName},
            </if>
            <if test="specificationModel != null ">
                #{specificationModel},
            </if>
            <if test="factoryTime != null ">
                #{factoryTime},
            </if>
            <if test="deviceFilingCode != null ">
                #{deviceFilingCode},
            </if>
            <if test="engineeringName != null ">
                #{engineeringName},
            </if>
            <if test="projectManager != null ">
                #{projectManager},
            </if>
            <if test="installationUnit != null ">
                #{installationUnit},
            </if>
            <if test="installationUnitQualificationLevel != null ">
                #{installationUnitQualificationLevel},
            </if>
            <if test="siteInstallationLeader != null ">
                #{siteInstallationLeader},
            </if>
            <if test="installationUnitQualificationCertificateNumber != null ">
                #{installationUnitQualificationCertificateNumber},
            </if>
            <if test="installationUnitLicenseSafetyPermitNumber != null ">
                #{installationUnitLicenseSafetyPermitNumber},
            </if>
            <if test="installationStartTime != null ">
                #{installationStartTime},
            </if>
            <if test="installationEndTime != null ">
                #{installationEndTime},
            </if>
            <if test="testingUnit != null ">
                #{testingUnit},
            </if>
            <if test="testingDate != null ">
                #{testingDate},
            </if>
            <if test="testingLeadere != null ">
                #{testingLeadere},
            </if>
            <if test="jointInspectionDate != null ">
                #{jointInspectionDate},
            </if>
            <if test="firstInstallationHeight != null ">
                #{firstInstallationHeight},
            </if>
            <if test="finalUseHeight != null ">
                #{finalUseHeight},
            </if>
            <if test="filingApplicationFormPath != null ">
                #{filingApplicationFormPath},
            </if>
            <if test="equipmentRecordCertificate != null ">
                #{equipmentRecordCertificate},
            </if>
            <if test="equipmentLeasingContract != null ">
                #{equipmentLeasingContract},
            </if>
            <if test="inspectionReportPath != null ">
                #{inspectionReportPath},
            </if>
            <if test="installationAcceptanceReport != null ">
                #{installationAcceptanceReport},
            </if>
            <if test="operatorsQualificationCertificate != null ">
                #{operatorsQualificationCertificate},
            </if>
            <if test="equipmentType != null">
                #{equipmentType},
            </if>
            <if test="equipmentMaintenanceManagementSystemPath != null ">
                #{equipmentMaintenanceManagementSystemPath},
            </if>
            <if test="safetyAccidentsEmergencyPlanPath != null ">
                #{safetyAccidentsEmergencyPlanPath},
            </if>
            <if test="auditStatus != null ">
                #{auditStatus},
            </if>
            <if test="auditTime != null ">
                #{auditTime},
            </if>
            <if test="auditRejectReason != null ">
                #{auditRejectReason},
            </if>
            <if test="useFilingCode != null ">
                #{useFilingCode},
            </if>
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if>
            <if test="installationAddress != null "> #{installationAddress},</if>
            <if test="propertyUnitCode != null "> #{propertyUnitCode},</if>
            <if test="useUnitCode != null "> #{useUnitCode},</if>
            <if test="userContactCard != null "> #{userContactCard},</if>
            <if test="produceUnit != null "> #{produceUnit},</if>
            <if test="produceUnitCode != null "> #{produceUnitCode},</if>
            <if test="upkeepUnit != null "> #{upkeepUnit},</if>
            <if test="upkeepUnitCode != null "> #{upkeepUnitCode},</if>
            <if test="installationUnitCode != null "> #{installationUnitCode},</if>
            <if test="siteInstallationLeaderCard != null "> #{siteInstallationLeaderCard},</if>
            <if test="testingUnitCode != null "> #{testingUnitCode},</if>
            <if test="testingLeadereCard != null "> #{testingLeadereCard},</if>
            <if test="isBuildLicense != null "> #{isBuildLicense},</if>
            <if test="buildLicenseCode != null "> #{buildLicenseCode},</if>
            <if test="factoryNumber != null "> #{factoryNumber},</if>
            <if test="businessId != null "> #{businessId},</if>
            <if test="certId != null "> #{certId},</if>
            <if test="armLength != null "> #{armLength},</if>
            <if test="maxWeight != null "> #{maxWeight},</if>
            <if test="maxHeight != null "> #{maxHeight},</if>
            <if test="maximumCapacity != null "> #{maximumCapacity},</if>
            <if test="sRegularWeight != null "> #{sRegularWeight},</if>
            <if test="sRegularSpeed != null "> #{sRegularSpeed},</if>
            <if test="wRegularWeight != null "> #{wRegularWeight},</if>
            <if test="wRegularSpeed != null "> #{wRegularSpeed},</if>
            <if test="engineeringAddressDetail != null "> #{engineeringAddressDetail},</if>
            <if test="engineeringAreaCode != null "> #{engineeringAreaCode},</if>
            <if test="installationTechnician != null "> #{installationTechnician},</if>
            <if test="installationTechnicianCard != null "> #{installationTechnicianCard},</if>
            <if test="installationManager != null "> #{installationManager},</if>
            <if test="installationManagerCard != null "> #{installationManagerCard},</if>
            <if test="useProduceSafeManager != null "> #{useProduceSafeManager},</if>
            <if test="useProduceSafeManagerCard != null "> #{useProduceSafeManagerCard},</if>
            <if test="useProduceDeviceManager != null "> #{useProduceDeviceManager},</if>
            <if test="useProduceDeviceManagerCard != null "> #{useProduceDeviceManagerCard},</if>
            <if test="useSignalWorker != null "> #{useSignalWorker},</if>
            <if test="useSignalWorkerCard != null "> #{useSignalWorkerCard},</if>
            <if test="useDeviceDriver != null "> #{useDeviceDriver},</if>
            <if test="useDeviceDriverCard != null "> #{useDeviceDriverCard},</if>
            <if test="isUseCancel != null "> #{isUseCancel},</if>
            <if test="testingContent != null "> #{testingContent},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.equipmentUseApplication.model.FilingEquipmentRegistrationForm">
        update filing_equipment_registration_form
        <set>
            <if test="userId != null">
                user_id=#{userId},
            </if>
            <if test="applicationDate != null">
                application_date=#{applicationDate},
            </if>
            <if test="useUnit != null">
                use_unit=#{useUnit},
            </if>
            <if test="userContact != null">
                user_contact=#{userContact},
            </if>
            <if test="userContactNumber != null">
                user_contact_number=#{userContactNumber},
            </if>
            <if test="propertyUnit != null">
                property_unit=#{propertyUnit},
            </if>
            <if test="deviceName != null">
                device_name=#{deviceName},
            </if>
            <if test="specificationModel != null">
                specification_model=#{specificationModel},
            </if>
            <if test="factoryTime != null">
                factory_time=#{factoryTime},
            </if>
            <if test="deviceFilingCode != null">
                device_filing_code=#{deviceFilingCode},
            </if>
            <if test="engineeringName != null">
                engineering_name=#{engineeringName},
            </if>
            <if test="projectManager != null">
                project_manager=#{projectManager},
            </if>
            <if test="installationUnit != null">
                installation_unit=#{installationUnit},
            </if>
            <if test="installationUnitQualificationLevel != null">
                installation_unit_qualification_level=#{installationUnitQualificationLevel},
            </if>
            <if test="siteInstallationLeader != null">
                site_installation_leader=#{siteInstallationLeader},
            </if>
            <if test="installationUnitQualificationCertificateNumber != null">
                installation_unit_qualification_certificate_number=#{installationUnitQualificationCertificateNumber},
            </if>
            <if test="installationUnitLicenseSafetyPermitNumber != null">
                installation_unit_license_safety_permit_number=#{installationUnitLicenseSafetyPermitNumber},
            </if>
            <if test="installationStartTime != null">
                installation_start_time=#{installationStartTime},
            </if>
            <if test="installationEndTime != null">
                installation_end_time=#{installationEndTime},
            </if>
            <if test="testingUnit != null">
                testing_unit=#{testingUnit},
            </if>
            <if test="testingDate != null">
                testing_date=#{testingDate},
            </if>
            <if test="testingLeadere != null">
                testing_leadere=#{testingLeadere},
            </if>
            <if test="jointInspectionDate != null">
                joint_inspection_date=#{jointInspectionDate},
            </if>
            <if test="firstInstallationHeight != null">
                first_installation_height=#{firstInstallationHeight},
            </if>
            <if test="finalUseHeight != null">
                final_use_height=#{finalUseHeight},
            </if>
            <if test="filingApplicationFormPath != null">
                filing_application_form_path=#{filingApplicationFormPath},
            </if>
            <if test="equipmentRecordCertificate != null">
                equipment_record_certificate=#{equipmentRecordCertificate},
            </if>
            <if test="equipmentLeasingContract != null">
                equipment_leasing_contract=#{equipmentLeasingContract},
            </if>
            <if test="inspectionReportPath != null">
                inspection_report_path=#{inspectionReportPath},
            </if>
            <if test="installationAcceptanceReport != null">
                installation_acceptance_report=#{installationAcceptanceReport},
            </if>
            <if test="operatorsQualificationCertificate != null">
                operators_qualification_certificate=#{operatorsQualificationCertificate},
            </if>
            <if test="equipmentType != null">
                equipment_type = #{equipmentType},
            </if>
            <if test="equipmentMaintenanceManagementSystemPath != null">
                equipment_maintenance_management_system_path=#{equipmentMaintenanceManagementSystemPath},
            </if>
            <if test="safetyAccidentsEmergencyPlanPath != null">
                safety_accidents_emergency_plan_path=#{safetyAccidentsEmergencyPlanPath},
            </if>
            <if test="auditStatus != null">
                audit_status=#{auditStatus},
            </if>
            <if test="auditTime != null">
                audit_time=#{auditTime},
            </if>
            <if test="auditRejectReason != null">
                audit_reject_reason=#{auditRejectReason},
            </if>
            <if test="useFilingCode != null">
                use_filing_code=#{useFilingCode},
            </if>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
            <if test="installationAddress != null"> installation_address=#{installationAddress},</if>
            <if test="propertyUnitCode != null"> property_unit_code=#{propertyUnitCode},</if>
            <if test="useUnitCode != null"> use_unit_code=#{useUnitCode},</if>
            <if test="userContactCard != null"> user_contact_card=#{userContactCard},</if>
            <if test="produceUnit != null"> produce_unit=#{produceUnit},</if>
            <if test="produceUnitCode != null"> produce_unit_code=#{produceUnitCode},</if>
            <if test="upkeepUnit != null"> upkeep_unit=#{upkeepUnit},</if>
            <if test="upkeepUnitCode != null"> upkeep_unit_code=#{upkeepUnitCode},</if>
            <if test="installationUnitCode != null"> installation_unit_code=#{installationUnitCode},</if>
            <if test="siteInstallationLeaderCard != null"> site_installation_leader_card=#{siteInstallationLeaderCard},</if>
            <if test="testingUnitCode != null"> testing_unit_code=#{testingUnitCode},</if>
            <if test="testingLeadereCard != null"> testing_leadere_card=#{testingLeadereCard},</if>
            <if test="isBuildLicense != null"> is_build_license=#{isBuildLicense},</if>
            <if test="buildLicenseCode != null"> build_license_code=#{buildLicenseCode},</if>
            <if test="factoryNumber != null"> factory_number=#{factoryNumber},</if>
            <if test="businessId != null"> business_id=#{businessId},</if>
            <if test="certId != null"> cert_id=#{certId},</if>
            <if test="armLength != null"> arm_length=#{armLength},</if>
            <if test="maxWeight != null"> max_weight=#{maxWeight},</if>
            <if test="maxHeight != null"> max_height=#{maxHeight},</if>
            <if test="maximumCapacity != null"> maximum_capacity=#{maximumCapacity},</if>
            <if test="sRegularWeight != null"> s_regular_weight=#{sRegularWeight},</if>
            <if test="sRegularSpeed != null"> s_regular_speed=#{sRegularSpeed},</if>
            <if test="wRegularWeight != null"> w_regular_weight=#{wRegularWeight},</if>
            <if test="wRegularSpeed != null"> w_regular_speed=#{wRegularSpeed},</if>
            <if test="engineeringAddressDetail != null"> engineering_address_detail=#{engineeringAddressDetail},</if>
            <if test="engineeringAreaCode != null"> engineering_area_code=#{engineeringAreaCode},</if>
            <if test="installationTechnician != null"> installation_technician=#{installationTechnician},</if>
            <if test="installationTechnicianCard != null"> installation_technician_card=#{installationTechnicianCard},</if>
            <if test="installationManager != null"> installation_manager=#{installationManager},</if>
            <if test="installationManagerCard != null"> installation_manager_card=#{installationManagerCard},</if>
            <if test="useProduceSafeManager != null"> use_produce_safe_manager=#{useProduceSafeManager},</if>
            <if test="useProduceSafeManagerCard != null"> use_produce_safe_manager_card=#{useProduceSafeManagerCard},</if>
            <if test="useProduceDeviceManager != null"> use_produce_device_manager=#{useProduceDeviceManager},</if>
            <if test="useProduceDeviceManagerCard != null"> use_produce_device_manager_card=#{useProduceDeviceManagerCard},</if>
            <if test="useSignalWorker != null"> use_signal_worker=#{useSignalWorker},</if>
            <if test="useSignalWorkerCard != null"> use_signal_worker_card=#{useSignalWorkerCard},</if>
            <if test="useDeviceDriver != null"> use_device_driver=#{useDeviceDriver},</if>
            <if test="useDeviceDriverCard != null"> use_device_driver_card=#{useDeviceDriverCard},</if>
            <if test="isUseCancel != null"> isUseCancel=#{isUseCancel},</if>
            <if test="testingContent != null"> testing_content=#{testingContent},</if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update filing_equipment_registration_form set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update filing_equipment_registration_form set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into filing_equipment_registration_form(id,  user_id,  application_date,  use_unit,  user_contact,  user_contact_number,  property_unit,  device_name,  specification_model,  factory_time,  device_filing_code,  engineering_name,  project_manager,  installation_unit,  installation_unit_qualification_level,  site_installation_leader,  installation_unit_qualification_certificate_number,  installation_unit_license_safety_permit_number,  installation_start_time,  installation_end_time,  testing_unit,  testing_date,  testing_leadere,  joint_inspection_date,  first_installation_height,  final_use_height,  filing_application_form_path,  equipment_record_certificate,  equipment_leasing_contract,  inspection_report_path,  installation_acceptance_report,  operators_qualification_certificate, equipment_type, equipment_maintenance_management_system_path,  safety_accidents_emergency_plan_path,  audit_status,  audit_time,  audit_reject_reason,  use_filing_code,  create_by,  create_time,  update_by,  update_time,  remarks,  del_flag,
        installation_address,property_unit_code,use_unit_code,user_contact_card,produce_unit,produce_unit_code,upkeep_unit,upkeep_unit_code,
        installation_unit_code,site_installation_leader_card,testing_unit_code,testing_leadere_card,is_build_license,build_license_code,factory_number,business_id,
        cert_id,arm_length,max_weight,max_height,maximum_capacity,s_regular_weight,s_regular_speed,w_regular_weight,w_regular_speed,engineering_address_detail,engineering_area_code,
        installation_technician,installation_technician_card,installation_manager,installation_manager_card,use_produce_safe_manager,use_produce_safe_manager_card,use_produce_device_manager,
        use_produce_device_manager_card,use_signal_worker,use_signal_worker_card,use_device_driver,use_device_driver_card,isUseCancel,testing_content) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.userId},  #{item.applicationDate},  #{item.useUnit},  #{item.userContact},  #{item.userContactNumber},  #{item.propertyUnit},  #{item.deviceName},  #{item.specificationModel},  #{item.factoryTime},  #{item.deviceFilingCode},  #{item.engineeringName},  #{item.projectManager},  #{item.installationUnit},  #{item.installationUnitQualificationLevel},  #{item.siteInstallationLeader},  #{item.installationUnitQualificationCertificateNumber},  #{item.installationUnitLicenseSafetyPermitNumber},  #{item.installationStartTime},  #{item.installationEndTime},  #{item.testingUnit},  #{item.testingDate},  #{item.testingLeadere},  #{item.jointInspectionDate},  #{item.firstInstallationHeight},  #{item.finalUseHeight},  #{item.filingApplicationFormPath},  #{item.equipmentRecordCertificate},  #{item.equipmentLeasingContract},  #{item.inspectionReportPath},  #{item.installationAcceptanceReport},  #{item.operatorsQualificationCertificate},#{item.equipmentType},  #{item.equipmentMaintenanceManagementSystemPath},  #{item.safetyAccidentsEmergencyPlanPath},  #{item.auditStatus},  #{item.auditTime},  #{item.auditRejectReason},  #{item.useFilingCode},  #{item.createBy},  #{item.createTime},  #{item.updateBy},  #{item.updateTime},  #{item.remarks},  #{item.delFlag},
            #{item.installationAddress},#{item.propertyUnitCode},#{item.useUnitCode},#{item.userContactCard},#{item.produceUnit},#{item.produceUnitCode},#{item.upkeepUnit},#{item.upkeepUnitCode},#{item.installationUnitCode},
            #{item.siteInstallationLeaderCard},#{item.testingUnitCode},#{item.testingLeadereCard},#{item.isBuildLicense},#{item.buildLicenseCode},#{item.factoryNumber},#{item.businessId},#{item.certId},#{item.armLength},#{item.maxWeight},
            #{item.maxHeight},#{item.maximumCapacity},#{item.sRegularWeight},#{item.sRegularSpeed},#{item.wRegularWeight},#{item.wRegularSpeed},#{item.engineeringAddressDetail},#{item.engineeringAreaCode},
            #{item.installationTechnician}, #{item.installationTechnicianCard}, #{item.installationManager}, #{item.installationManagerCard}, #{item.useProduceSafeManager}, #{item.useProduceSafeManagerCard}, #{item.useProduceDeviceManager},
             #{item.useProduceDeviceManagerCard}, #{item.useSignalWorker}, #{item.useSignalWorkerCard}, #{item.useDeviceDriver}, #{item.useDeviceDriverCard},#{item.isUseCancel},#{item.testingContent})
        </foreach>
    </insert>
    <select id="getByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_registration_form
        where use_filing_code = #{useFilingCode}
    </select>

    <select id="selectListByAreaCode" resultMap="BaseResultMap">
        select  * from filing_equipment_registration_form
        where engineering_area_code
        <foreach close=")" collection="areaList" item="areaCode" open="in (" separator=",">
            #{areaCode}
        </foreach>
        and audit_status = #{auditStatus}
        and del_flag='0'
    </select>
    <select id="selectListByCondition" resultMap="BaseResultMap">
        select  * from filing_equipment_registration_form
        where engineering_area_code
        <foreach close=")" collection="areaList" item="areaCode" open="in (" separator=",">
            #{areaCode}
        </foreach>
        and audit_status = '1'
        and del_flag='0'
        <if test="year != null and year != ''"> and date_format(audit_time, '%Y')=#{year}</if>
    </select>
    <select id="getOneByFilingCode" resultMap="BaseResultMap" parameterType="string">
        select  * from filing_equipment_registration_form
        where device_filing_code = #{deviceFilingCode} and del_flag='0'
    </select>
    <select id="selectListGroupByFilingCode" resultMap="BaseResultMap">
        select  r.* from filing_equipment_registration_form r
        left join filing_equipment_use_cancellation c on r.use_filing_code=c.use_filing_code
        where r.engineering_area_code
        <foreach close=")" collection="areaList" item="areaCode" open="in (" separator=",">
            #{areaCode}
        </foreach>
        and r.audit_status = '1'
        and r.del_flag='0'
        and c.audit_status not in ('1')
        <if test="year != null and year != ''"> and date_format(r.audit_time, '%Y')=#{year}</if>
        group by r.use_filing_code
    </select>
</mapper>
