<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.equipmentUseCancellation.mapper.FilingEquipmentUseCancellationMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.equipmentUseCancellation.model.FilingEquipmentUseCancellation" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="use_unit" property="useUnit" jdbcType="VARCHAR" />
        <result column="property_unit" property="propertyUnit" jdbcType="VARCHAR" />
        <result column="device_name" property="deviceName" jdbcType="VARCHAR" />
        <result column="specification_model" property="specificationModel" jdbcType="VARCHAR" />
        <result column="device_filing_code" property="deviceFilingCode" jdbcType="VARCHAR" />
        <result column="engineering_name" property="engineeringName" jdbcType="VARCHAR" />
        <result column="project_manager" property="projectManager" jdbcType="VARCHAR" />
        <result column="equipment_type" property="equipmentType" jdbcType="CHAR" />
        <result column="use_filing_code" property="useFilingCode" jdbcType="VARCHAR" />
        <result column="cancellation_application_path" property="cancellationApplicationPath" jdbcType="VARCHAR" />
        <result column="use_registration_path" property="useRegistrationPath" jdbcType="VARCHAR" />
        <result column="audit_reject_reason" property="auditRejectReason" jdbcType="VARCHAR" />
        <result column="audit_status" property="auditStatus" jdbcType="INTEGER" />
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
        <result column="business_id" property="businessId"/>
        <result column="engineering_area_code" property="engineeringAreaCode"/>
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        user_id,
        use_unit,
        property_unit,
        device_name,
        specification_model,
        device_filing_code,
        engineering_name,
        project_manager,
        equipment_type,
        use_filing_code,
        cancellation_application_path,
        use_registration_path,
        audit_reject_reason,
        audit_status,
        audit_time,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag,
        business_id,
        engineering_area_code
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_use_cancellation
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.equipmentUseCancellation.model.FilingEquipmentUseCancellation">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_use_cancellation
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.equipmentUseCancellation.model.FilingEquipmentUseCancellation">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_use_cancellation
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="userId != null ">
                and user_id=#{userId}
            </if>
            <if test="useUnit != null ">
                and use_unit like concat('%', #{useUnit}, '%')
            </if>
            <if test="propertyUnit != null ">
                and property_unit like concat('%', #{propertyUnit}, '%')
            </if>
            <if test="deviceName != null ">
                and device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="specificationModel != null ">
                and specification_model=#{specificationModel}
            </if>
            <if test="deviceFilingCode != null ">
                and device_filing_code=#{deviceFilingCode}
            </if>
            <if test="engineeringName != null ">
                and engineering_name like concat('%', #{engineeringName}, '%')
            </if>
            <if test="projectManager != null ">
                and project_manager=#{projectManager}
            </if>
            <if test="equipmentType != null ">
                and equipment_type=#{equipmentType}
            </if>
            <if test="useFilingCode != null ">
                and use_filing_code like concat('%', #{useFilingCode}, '%')
            </if>
            <if test="cancellationApplicationPath != null ">
                and cancellation_application_path=#{cancellationApplicationPath}
            </if>
            <if test="useRegistrationPath != null ">
                and use_registration_path=#{useRegistrationPath}
            </if>
            <if test="auditRejectReason != null ">
                and audit_reject_reason=#{auditRejectReason}
            </if>
            <if test="auditStatus != null ">
                and audit_status=#{auditStatus}
            </if>
            <if test="auditTime != null ">
                and audit_time=#{auditTime}
            </if>
            <if test="engineeringAreaCode != null "> and engineering_area_code=#{engineeringAreaCode}</if>
        </where>
        order by create_time desc
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.equipmentUseCancellation.model.FilingEquipmentUseCancellation">
        insert into filing_equipment_use_cancellation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="userId != null">
                    user_id,
                </if>
                <if test="useUnit != null">
                    use_unit,
                </if>
                <if test="propertyUnit != null">
                    property_unit,
                </if>
                <if test="deviceName != null">
                    device_name,
                </if>
                <if test="specificationModel != null">
                    specification_model,
                </if>
                <if test="deviceFilingCode != null">
                    device_filing_code,
                </if>
                <if test="engineeringName != null">
                    engineering_name,
                </if>
                <if test="projectManager != null">
                    project_manager,
                </if>
                <if test="equipmentType != null">
                    equipment_type,
                </if>
                <if test="useFilingCode != null">
                    use_filing_code,
                </if>
                <if test="cancellationApplicationPath != null">
                    cancellation_application_path,
                </if>
                <if test="useRegistrationPath != null">
                    use_registration_path,
                </if>
                <if test="auditRejectReason != null">
                    audit_reject_reason,
                </if>
                <if test="auditStatus != null">
                    audit_status,
                </if>
                <if test="auditTime != null">
                    audit_time,
                </if>
                <if test="createBy != null">
                    create_by,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="updateBy != null">
                    update_by,
                </if>
                <if test="updateTime != null">
                    update_time,
                </if>
                <if test="remarks != null">
                    remarks,
                </if>
                <if test="delFlag != null">
                    del_flag,
                </if>
            <if test="businessId != null"> business_id,</if>
            <if test="engineeringAreaCode != null"> engineering_area_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="userId != null ">
                #{userId},
            </if>
            <if test="useUnit != null ">
                #{useUnit},
            </if>
            <if test="propertyUnit != null ">
                #{propertyUnit},
            </if>
            <if test="deviceName != null ">
                #{deviceName},
            </if>
            <if test="specificationModel != null ">
                #{specificationModel},
            </if>
            <if test="deviceFilingCode != null ">
                #{deviceFilingCode},
            </if>
            <if test="engineeringName != null ">
                #{engineeringName},
            </if>
            <if test="projectManager != null ">
                #{projectManager},
            </if>
            <if test="equipmentType != null ">
                #{equipmentType},
            </if>
            <if test="useFilingCode != null ">
                #{useFilingCode},
            </if>
            <if test="cancellationApplicationPath != null ">
                #{cancellationApplicationPath},
            </if>
            <if test="useRegistrationPath != null ">
                #{useRegistrationPath},
            </if>
            <if test="auditRejectReason != null ">
                #{auditRejectReason},
            </if>
            <if test="auditStatus != null ">
                #{auditStatus},
            </if>
            <if test="auditTime != null ">
                #{auditTime},
            </if>
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if>
            <if test="businessId != null "> #{businessId},</if>
            <if test="engineeringAreaCode != null "> #{engineeringAreaCode},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.equipmentUseCancellation.model.FilingEquipmentUseCancellation">
        update filing_equipment_use_cancellation
        <set>
            <if test="userId != null">
                user_id=#{userId},
            </if>
            <if test="useUnit != null">
                use_unit=#{useUnit},
            </if>
            <if test="propertyUnit != null">
                property_unit=#{propertyUnit},
            </if>
            <if test="deviceName != null">
                device_name=#{deviceName},
            </if>
            <if test="specificationModel != null">
                specification_model=#{specificationModel},
            </if>
            <if test="deviceFilingCode != null">
                device_filing_code=#{deviceFilingCode},
            </if>
            <if test="engineeringName != null">
                engineering_name=#{engineeringName},
            </if>
            <if test="projectManager != null">
                project_manager=#{projectManager},
            </if>
            <if test="equipmentType != null">
                equipment_type=#{equipmentType},
            </if>
            <if test="useFilingCode != null">
                use_filing_code=#{useFilingCode},
            </if>
            <if test="cancellationApplicationPath != null">
                cancellation_application_path=#{cancellationApplicationPath},
            </if>
            <if test="useRegistrationPath != null">
                use_registration_path=#{useRegistrationPath},
            </if>
            <if test="auditRejectReason != null">
                audit_reject_reason=#{auditRejectReason},
            </if>
            <if test="auditStatus != null">
                audit_status=#{auditStatus},
            </if>
            <if test="auditTime != null">
                audit_time=#{auditTime},
            </if>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
            <if test="businessId != null"> business_id=#{businessId},</if>
            <if test="engineeringAreaCode != null"> engineering_area_code=#{engineeringAreaCode},</if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update filing_equipment_use_cancellation set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update filing_equipment_use_cancellation set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into filing_equipment_use_cancellation(id,  user_id,  use_unit,  property_unit,  device_name,  specification_model,  device_filing_code,  engineering_name,  project_manager,  equipment_type,  use_filing_code,  cancellation_application_path,  use_registration_path,  audit_reject_reason,  audit_status,  audit_time,  create_by,  create_time,  update_by,  update_time,  remarks,  del_flag,business_id,engineering_area_code) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.userId},  #{item.useUnit},  #{item.propertyUnit},  #{item.deviceName},  #{item.specificationModel},  #{item.deviceFilingCode},  #{item.engineeringName},  #{item.projectManager},  #{item.equipmentType},  #{item.useFilingCode},  #{item.cancellationApplicationPath},  #{item.useRegistrationPath},  #{item.auditRejectReason},  #{item.auditStatus},  #{item.auditTime},  #{item.createBy},  #{item.createTime},  #{item.updateBy},  #{item.updateTime},  #{item.remarks},  #{item.delFlag},#{item.businessId},#{item.engineeringAreaCode})
        </foreach>
    </insert>

    <select id="selectListByAreaCode" resultMap="BaseResultMap">
        select  c.*
        from filing_equipment_filing_cancellation c
        left JOIN filing_equipment_filing_application a on c.filing_code = a.filing_code
        where a.unit_area_code
        <foreach close=")" collection="areaList" item="areaCode" open="in (" separator=",">
            #{areaCode}
        </foreach>
        and c.audit_status = #{auditStatus}
        and c.del_flag='0'
    </select>

    <select id="selectListByCondition" resultMap="BaseResultMap">
        select  c.*
        from filing_equipment_filing_cancellation c
        left JOIN filing_equipment_filing_application a on c.filing_code = a.filing_code
        where a.unit_area_code
        <foreach close=")" collection="areaList" item="areaCode" open="in (" separator=",">
            #{areaCode}
        </foreach>
        and c.audit_status = '1'
        and c.del_flag='0'
        <if test="year != null and year != ''"> and date_format(c.audit_time, '%Y')=#{year}</if>
    </select>
    <select id="selectListGroupByFilingCode" resultMap="BaseResultMap">
        select  c.*
        from filing_equipment_filing_cancellation c
        left JOIN filing_equipment_filing_application a on c.filing_code = a.filing_code
        where a.unit_area_code
        <foreach close=")" collection="areaList" item="areaCode" open="in (" separator=",">
            #{areaCode}
        </foreach>
        and c.audit_status = '1'
        and c.del_flag='0'
        <if test="year != null and year != ''"> and date_format(c.audit_time, '%Y')=#{year}</if>
        group by c.filing_code
    </select>
    <select id="selectByDeviceFilingCode" resultMap="BaseResultMap" parameterType="string">
        select * from filing_equipment_filing_cancellation where device_filing_code = #{deviceFilingCode} and del_flag='0'
    </select>
</mapper>
