<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.sys.quartz.mapper.JobMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.sys.quartz.model.Job" >
        <result column="job_id" property="jobId" jdbcType="INTEGER" />
        <result column="job_name" property="jobName" jdbcType="VARCHAR" />
        <result column="job_class_path" property="jobClassPath" jdbcType="VARCHAR" />
        <result column="job_group" property="jobGroup" jdbcType="VARCHAR" />
        <result column="method_name" property="methodName" jdbcType="VARCHAR" />
        <result column="method_params" property="methodParams" jdbcType="VARCHAR" />
        <result column="cron_expression" property="cronExpression" jdbcType="VARCHAR" />
        <result column="misfire_policy" property="misfirePolicy" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="CHAR" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        job_id,
        job_class_path,
        job_name,
        job_group,
        method_name,
        method_params,
        cron_expression,
        misfire_policy,
        status,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from sys_job
        where job_id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.quartz.model.Job">
        select
        <include refid="Base_Column_List" />
        from sys_job
        where
        job_id = #{jobId}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.quartz.model.Job">
        select
        <include refid="Base_Column_List" />
        from sys_job
        <where>
            del_flag='0'
            <if test="id!= null and ''!=id">
                and id = #{id}
            </if>
            <if test="jobId != null">
                and job_id=#{jobId}
            </if>
            <if test="jobClassPath != null">
                and job_class_path=#{jobClassPath}
            </if>
            <if test="jobName != null">
                and job_name=#{jobName}
            </if>
            <if test="jobGroup != null">
                and job_group=#{jobGroup}
            </if>
            <if test="methodName != null">
                and method_name=#{methodName}
            </if>
            <if test="methodParams != null">
                and method_params=#{methodParams}
            </if>
            <if test="cronExpression != null">
                and cron_expression=#{cronExpression}
            </if>
            <if test="misfirePolicy != null">
                and misfire_policy=#{misfirePolicy}
            </if>
            <if test="status != null">
                and status=#{status}
            </if>

         </where>
    </select>

    <insert id="insert" parameterType="com.tzstcl.sys.quartz.model.Job">
        insert into sys_job
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="jobId != null">
                    job_id,
                </if>
                <if test="jobClassPath != null">
                    job_class_path,
                </if>

            <if test="jobName != null">
                    job_name,
                </if>
                <if test="jobGroup != null">
                    job_group,
                </if>
                <if test="methodName != null">
                    method_name,
                </if>
                <if test="methodParams != null">
                    method_params,
                </if>
                <if test="cronExpression != null">
                    cron_expression,
                </if>
                <if test="misfirePolicy != null">
                    misfire_policy,
                </if>
                <if test="status != null">
                    status,
                </if>
                <if test="createBy != null">
                    create_by,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="updateBy != null">
                    update_by,
                </if>
                <if test="updateTime != null">
                    update_time,
                </if>
                <if test="remarks != null">
                    remarks,
                </if>
                <if test="delFlag != null">
                    del_flag,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jobId != null">
                #{jobId},
            </if>
            <if test="jobClassPath != null">
                #{jobClassPath},
            </if>

            <if test="jobName != null">
                #{jobName},
            </if>
            <if test="jobGroup != null">
                #{jobGroup},
            </if>
            <if test="methodName != null">
                #{methodName},
            </if>
            <if test="methodParams != null">
                #{methodParams},
            </if>
            <if test="cronExpression != null">
                #{cronExpression},
            </if>
            <if test="misfirePolicy != null">
                #{misfirePolicy},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="remarks != null">
                #{remarks},
            </if>
            <if test="delFlag != null">
                #{delFlag},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.sys.quartz.model.Job">
        update sys_job
        <set>
            <if test="jobClassPath != null">
                job_class_path=#{jobClassPath},
            </if>
            <if test="jobName != null">
                job_name=#{jobName},
            </if>
            <if test="jobGroup != null">
                job_group=#{jobGroup},
            </if>
            <if test="methodName != null">
                method_name=#{methodName},
            </if>
            <if test="methodParams != null">
                method_params=#{methodParams},
            </if>
            <if test="cronExpression != null">
                cron_expression=#{cronExpression},
            </if>
            <if test="misfirePolicy != null">
                misfire_policy=#{misfirePolicy},
            </if>
            <if test="status != null">
                status=#{status},
            </if>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
        </set>
        WHERE    job_id = #{jobId}
    </update>

    <update id="delete" parameterType="java.lang.Long">
        update sys_job set
        del_flag='1'
        WHERE   job_id = #{jobId}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update sys_job set
        del_flag='1'
        WHERE   job_id
        <foreach close=")" collection="ids" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

</mapper>