<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.sys.user.mapper.SysPositionMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.sys.user.model.SysPosition" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="position_code" property="positionCode" jdbcType="VARCHAR" />
        <result column="position_name" property="positionName" jdbcType="VARCHAR" />
        <result column="sort" property="sort" jdbcType="INTEGER" />
        <result column="status" property="status" jdbcType="CHAR" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        position_code,
        position_name,
        sort,
        status,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from sys_position
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.user.model.SysPosition">
        select
        <include refid="Base_Column_List" />
        from sys_position
        where
        id = #{id}
    </select>


    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.user.model.SysPosition">
        select
        <include refid="Base_Column_List" />
        from sys_position
        <where>
            del_flag='0'
            <if test="id!= null and ''!=id">
                and id = #{id}
            </if>
            <if test="positionCode != null">
                and position_code=#{positionCode}
            </if>
            <if test="positionName != null">
                and position_name like concat('%',#{positionName},'%')
            </if>
            <if test="sort != null">
                and sort=#{sort}
            </if>
            <if test="status != null">
                and status=#{status}
            </if>
         </where>
         order by sort asc
    </select>

    <insert id="insert" parameterType="com.tzstcl.sys.user.model.SysPosition">
        insert into sys_position
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="positionCode != null">
                    position_code,
                </if>
                <if test="positionName != null">
                    position_name,
                </if>
                <if test="sort != null">
                    sort,
                </if>
                <if test="status != null">
                    status,
                </if>
                <if test="createBy != null">
                    create_by,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="updateBy != null">
                    update_by,
                </if>
                <if test="updateTime != null">
                    update_time,
                </if>
                <if test="remarks != null">
                    remarks,
                </if>
                <if test="delFlag != null">
                    del_flag,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="positionCode != null">
                #{positionCode},
            </if>
            <if test="positionName != null">
                #{positionName},
            </if>
            <if test="sort != null">
                #{sort},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="remarks != null">
                #{remarks},
            </if>
            <if test="delFlag != null">
                #{delFlag},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.sys.user.model.SysPosition">
        update sys_position
        <set>
            <if test="positionCode != null">
                position_code=#{positionCode},
            </if>
            <if test="positionName != null">
                position_name=#{positionName},
            </if>
            <if test="sort != null">
                sort=#{sort},
            </if>
            <if test="status != null">
                status=#{status},
            </if>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
        </set>
        WHERE   id = #{id}
    </update>

    <update id="delete" parameterType="java.lang.Long">
        update sys_position set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update sys_position set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

</mapper>