<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzstcl.sys.user.mapper.SysPostMapper">

    <sql id="postColumns">
          id as 'id',
          post_code as 'postCode',
          post_name as 'postName',
          post_sort as 'postSort',
          status,
          create_by as 'createBy',
          create_time as 'createTime',
          update_by as 'updateBy',
          update_time as 'updateTime',
          remarks,
          del_flag as 'delFlag'
    </sql>

    <select id="getOne" resultType="com.tzstcl.sys.user.model.SysPost">
         select <include refid="postColumns"/> from  sys_post where id = #{id}
     </select>

    <select id="selectByPageNumSize" resultType="com.tzstcl.sys.user.model.SysPost">
        select <include refid="postColumns"/> from sys_post
        <where>
            del_flag = '0'
        </where>
    </select>

    <select id="findListAll" resultType="com.tzstcl.sys.user.model.SysPost">
        select <include refid="postColumns"/> from sys_post
        <where>
            del_flag = '0'
            <if test="status!=null and status!=''">
                and status = #{status}
            </if>
            <if test="postName!=null and postName!=''">
               and post_name =#{postName}
            </if>
        </where>
        order by postSort asc
    </select>

    <insert id="save">
        insert into  sys_post(
          id,
          post_code,
          post_name,
          post_sort,
          status,
          create_by,
          create_time,
          update_by,
          update_time,
          remarks,
          del_flag
        ) values (
          #{id},
          #{postCode},
          #{postName},
          #{postSort},
          #{status},
          #{createBy},
          #{createTime},
          #{updateBy},
          #{updateTime},
          #{remarks},
          #{delFlag}
        )
    </insert>

    <update id="update">
        update sys_post set
          post_code = #{postCode},
          post_name = #{postName},
          post_sort = #{postSort},
          status = #{status},
          update_by = #{updateBy},
          update_time = #{updateTime},
          remarks = #{remarks}
        where id = #{id}
    </update>

    <update id="delete" parameterType="java.util.List">
        update sys_post set del_flag ='1'  where id in(
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>
</mapper>
