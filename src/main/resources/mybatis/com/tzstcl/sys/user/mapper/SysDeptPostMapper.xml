<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzstcl.sys.user.mapper.SysDeptPostMapper">

    <sql id="userRoleColumns">
        user_id as 'userId',
        dept_id as 'deptId',
        post_id as 'postId',
        position_id as 'positionId'
    </sql>

    <select id="get" resultType="com.tzstcl.sys.user.model.SysDeptPost">
        select
        <include refid="userRoleColumns"/>
        from sys_dept_post where user_id = #{id}
    </select>

    <insert id="save" parameterType="com.tzstcl.sys.user.model.SysDeptPost">
        insert into  sys_dept_post(
          user_id,
          dept_id,
          post_id,
          position_id
        ) values(
         #{userId},
         #{deptId},
         #{postId},
         #{positionId}
        )
    </insert>

    <delete id="deleteById">
        delete from sys_dept_post where user_id = #{id}
    </delete>
</mapper>
