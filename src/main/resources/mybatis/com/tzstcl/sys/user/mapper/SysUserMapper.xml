<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.sys.user.mapper.SysUserMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.sys.user.model.SysUser" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
        <result column="login_date" property="loginDate" jdbcType="TIMESTAMP" />
        <result column="login_flag" property="loginFlag" jdbcType="VARCHAR" />
        <result column="login_ip" property="loginIp" jdbcType="VARCHAR" />
        <result column="login_name" property="loginName" jdbcType="VARCHAR" />
        <result column="mobile" property="mobile" jdbcType="VARCHAR" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="password" property="password" jdbcType="VARCHAR" />
        <result column="photo" property="photo" jdbcType="VARCHAR" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="sort" property="sort" jdbcType="INTEGER" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="user_type" property="userType" jdbcType="CHAR" />
        <result column="dept_id" property="deptId" jdbcType="BIGINT" />
        <result column="dept_name" property="deptName" jdbcType="BIGINT" />
        <result column="dept_name" property="deptName"/>
        <result column="post_id" property="postId"/>
    </resultMap>
    <!--用户信息视图-->
    <resultMap id="UserInfoResultMap" type="com.tzstcl.sys.user.model.UserInfo" >
        <result column="user_id" property="userId" jdbcType="BIGINT" />
        <result column="user_name" property="userName" jdbcType="VARCHAR" />
        <result column="user_mobile" property="userMobile" jdbcType="VARCHAR" />
        <result column="dept_id" property="deptId" jdbcType="BIGINT" />
        <result column="post_id" property="postId" jdbcType="BIGINT" />
        <result column="dept_name" property="deptName" jdbcType="VARCHAR" />
        <result column="post_name" property="postName" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        create_by,
        create_time,
        del_flag,
        login_date,
        login_flag,
        login_ip,
        login_name,
        mobile,
        name,
        password,
        photo,
        remarks,
        sort,
        update_by,
        update_time,
        user_type,
        dept_id
    </sql>

    <select id="findByNameOrPass" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_user
        <where>
            del_flag='0'
            <if test="loginName!=null and loginName!=''">
                and login_name = #{loginName}
            </if>
            <if test="password!=null and password!=''">
                and password = #{password}
            </if>
        </where>
    </select>

    <select id="get" resultType="com.tzstcl.sys.user.model.SysUser">
        select
        <include refid="Base_Column_List" />
        from sys_user
        <where>
            del_flag = '0'
            <if test="loginName!=null and loginName!=''">
                and (login_name = #{loginName} or mobile= #{loginName})
            </if>
            <if test="mobile!=null and mobile!=''">
                and mobile = #{mobile}
            </if>
            <if test="id!=null and id!=''">
                and id = #{id}
            </if>
            <if test="name!=null and name!=''">
                and name = #{name}
            </if>
        </where>
    </select>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
            s.id,
            s.create_by,
            s.create_time,
            s.del_flag,
            s.login_date,
            s.login_flag,
            s.login_ip,
            s.login_name,
            s.mobile,
            s.name,
            s.password,
            s.photo,
            s.remarks,
            s.sort,
            s.update_by,
            s.update_time,
            s.user_type,
            s.dept_id,
            d.dept_name,
            p.post_id
        from sys_user s left join sys_dept d on s.dept_id = d.id left join sys_dept_post p on s.id = p.user_id
        where s.id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.user.model.SysUser">
        select
        <include refid="Base_Column_List" />
        from sys_user
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="loginDate != null ">
                and login_date=#{loginDate}
            </if>
            <if test="loginFlag != null ">
                and login_flag=#{loginFlag}
            </if>
            <if test="loginIp != null ">
                and login_ip=#{loginIp}
            </if>
            <if test="loginName != null ">
                and login_name=#{loginName}
            </if>
            <if test="mobile != null ">
                and mobile=#{mobile}
            </if>
            <if test="name != null ">
                and name=#{name}
            </if>
            <if test="password != null ">
                and password=#{password}
            </if>
            <if test="photo != null ">
                and photo=#{photo}
            </if>
            <if test="sort != null ">
                and sort=#{sort}
            </if>
            <if test="userType != null ">
                and user_type=#{userType}
            </if>
            <if test="deptId != null ">
                and dept_id=#{deptId}
            </if>
        </where>
    </select>

    <insert id="insert" parameterType="com.tzstcl.sys.user.model.SysUser">
        insert into sys_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="id != null">
                id,
            </if>
            <if test="loginDate != null">
                login_date,
            </if>
            <if test="loginFlag != null">
                login_flag,
            </if>
            <if test="loginIp != null">
                login_ip,
            </if>
            <if test="loginName != null">
                login_name,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="photo != null">
                photo,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="userType != null">
                user_type,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if>
            <if test="id != null ">
                #{id},
            </if>
            <if test="loginDate != null ">
                #{loginDate},
            </if>
            <if test="loginFlag != null ">
                #{loginFlag},
            </if>
            <if test="loginIp != null ">
                #{loginIp},
            </if>
            <if test="loginName != null ">
                #{loginName},
            </if>
            <if test="mobile != null ">
                #{mobile},
            </if>
            <if test="name != null ">
                #{name},
            </if>
            <if test="password != null ">
                #{password},
            </if>
            <if test="photo != null ">
                #{photo},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="sort != null ">
                #{sort},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="userType != null ">
                #{userType},
            </if>
            <if test="deptId != null ">
                #{deptId},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.sys.user.model.SysUser">
        update sys_user
        <set>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
            <if test="loginDate != null">
                login_date=#{loginDate},
            </if>
            <if test="loginFlag != null">
                login_flag=#{loginFlag},
            </if>
            <if test="loginIp != null">
                login_ip=#{loginIp},
            </if>
            <if test="loginName != null">
                login_name=#{loginName},
            </if>
            <if test="mobile != null">
                mobile=#{mobile},
            </if>
            <if test="name != null">
                name=#{name},
            </if>
            <if test="password != null and password != ''">
                password=#{password},
            </if>
            <if test="photo != null">
                photo=#{photo},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="sort != null">
                sort=#{sort},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="userType != null">
                user_type=#{userType},
            </if>
            <if test="deptId != null">
                dept_id=#{deptId},
            </if>
        </set>
        WHERE   id = #{id}
    </update>

    <update id="delete" parameterType="java.lang.Long">
        update sys_user set
            del_flag='1'
        WHERE  id = #{id}
    </update>

    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update sys_user set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
            #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into sys_user(create_by,  create_time,  del_flag,  id,  login_date,  login_flag,  login_ip,  login_name,  mobile,  name,  password,  photo,  remarks,  sort,  update_by,  update_time,  user_type,dept_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.createBy},  #{item.createTime},  #{item.delFlag},  #{item.id},  #{item.loginDate},  #{item.loginFlag},  #{item.loginIp},  #{item.loginName},  #{item.mobile},  #{item.name},  #{item.password},  #{item.photo},  #{item.remarks},  #{item.sort},  #{item.updateBy},  #{item.updateTime},  #{item.userType},#{item.deptId})
        </foreach>
    </insert>

    <select id="compareUser" resultType="com.tzstcl.sys.user.model.SysUser">
        select
        <include refid="Base_Column_List" />
        from sys_user
        where del_flag = '0' and login_name = #{loginName}
    </select>


    <select id="selectUserInfo" resultMap="UserInfoResultMap"  parameterType="com.tzstcl.sys.user.model.UserInfo" >
        select
        user_id,
        user_name,
        user_mobile,
        dept_id,
        post_id,
        dept_name,
        post_name
        from view_user_info
        <where>
            dept_id like concat( #{deptId},'%')
            <if test="userName!=null and userName!=''">
                and user_name like concat('%', #{userName},'%')
            </if>
            <if test="userMobile!=null and userMobile!=''">
                and user_mobile like concat('%',#{userMobile},'%')
            </if>
            <if test="postId!=null">
                and post_id = #{postId}
            </if>
            <if test="deptName!=null and deptName!=''">
                and dept_name like concat('%',#{deptName},'%')
            </if>
            <if test="postName!=null and postName!=''">
                and post_name like concat('%',#{postName},'%')
            </if>
        </where>
    </select>

    <select id="accountList" parameterType="com.tzstcl.sys.user.model.SysUser" resultMap="BaseResultMap">
        select
        user.*
        from sys_user user,
        sys_dept_post sdp
        where
        user.id = sdp.user_id
        and
        user.del_flag = '0'
        and
        user.user_type not in (0)
        and
        sdp.dept_id like concat(#{deptId},'%')
        <if test="name != null and name != ''">
            and name=#{name}
        </if>
        <if test="mobile != null and mobile != ''">
            and mobile=#{mobile}
        </if>
    </select>

    <select id="getDataForSelectByDept" parameterType="java.lang.Long" resultType="java.util.Map">
        select
            user.id,
            user.`name`,
            sdp.dept_id deptId,
            dept.dept_name deptName
        from
            sys_user user,
        sys_dept_post sdp,
        sys_dept dept
        where
            user.del_flag = 0
          and
            user.id = sdp.user_id
          and
            sdp.dept_id = dept.id
          and
            dept.id = #{deptId}
    </select>

    <select id="getAddressBook" parameterType="java.lang.Long" resultType="java.util.Map">
        select
        user.name,
        user.mobile,
        pmi.description,
        sdp.dept_id,
        pmi.org_relation
        from
        sys_user user,
        sys_dept_post sdp,
        party_member_info pmi
        where
        user.id = sdp.user_id
        and
        user.id = pmi.user_id
        and
        user.user_type not in (0)
        <if test="deptId != null and deptId != ''">
            and sdp.dept_id = #{deptId}
        </if>
        and pmi.del_flag = '0'
        and user.del_flag = '0'
    </select>

    <select id="getDeptAddressBook" parameterType="java.lang.Long" resultType="java.util.Map">
        select
            user.name,
            user.mobile,
            pmi.description,
            sdp.dept_id,
            pmi.org_relation
        from
            sys_user user,
        sys_dept_post sdp,
        party_member_info pmi
        where
            user.id = sdp.user_id
          and
            user.id = pmi.user_id
          and
            user.user_type not in (0)
          and
            sdp.dept_id like concat(#{deptId},'%')
          and pmi.del_flag = '0'
    </select>

    <select id="getDeptUserList" parameterType="java.lang.Long" resultType="java.util.Map">
        select
            user.id,
            user.name,
            sdp.dept_id
        from
            sys_user user,
        sys_dept_post sdp
        where
            user.id = sdp.user_id
          and
            user.user_type not in (0)
          and
            sdp.dept_id like concat(#{deptId},'%')
          and user.del_flag = '0'
    </select>

    <update id="updatePassword" parameterType="com.tzstcl.sys.user.model.SysUser">
        update
            sys_user
        set
            password = #{password}
        where
            id = #{id} and del_flag = '0'
    </update>

    <select id="checkLoginName" resultType="com.tzstcl.sys.user.model.SysUser">
        select
        <include refid="Base_Column_List" />
        from sys_user
        <where>
            login_name = #{loginName} and del_flag = '0'
        </where>
    </select>


</mapper>
