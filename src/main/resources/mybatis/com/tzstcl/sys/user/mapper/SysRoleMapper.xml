<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.sys.user.mapper.SysRoleMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.sys.user.model.SysRole" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="role_type" property="roleType" jdbcType="VARCHAR" />
        <result column="useable" property="useable" jdbcType="VARCHAR" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        name,
        role_type,
        useable,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from sys_role
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.user.model.SysRole">
        select
        <include refid="Base_Column_List" />
        from sys_role
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.user.model.SysRole">
        select
        <include refid="Base_Column_List" />
        from sys_role
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="name != null ">
                and name like concat('%', #{name},'%')
            </if>
            <if test="roleType != null ">
                and role_type=#{roleType}
            </if>
            <if test="useable != null ">
                and useable=#{useable}
            </if>
         </where>
    </select>

    <insert id="insert" parameterType="com.tzstcl.sys.user.model.SysRole">
        insert into sys_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="name != null">
                    name,
                </if>
                <if test="roleType != null">
                    role_type,
                </if>
                <if test="useable != null">
                    useable,
                </if>
                <if test="createBy != null">
                    create_by,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="updateBy != null">
                    update_by,
                </if>
                <if test="updateTime != null">
                    update_time,
                </if>
                <if test="remarks != null">
                    remarks,
                </if>
                <if test="delFlag != null">
                    del_flag,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="name != null ">
                #{name},
            </if>
            <if test="roleType != null ">
                #{roleType},
            </if>
            <if test="useable != null ">
                #{useable},
            </if>
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.sys.user.model.SysRole">
        update sys_role
        <set>
            <if test="name != null">
                name=#{name},
            </if>
            <if test="roleType != null">
                role_type=#{roleType},
            </if>
            <if test="useable != null">
                useable=#{useable},
            </if>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update sys_role set
        del_flag='1'
        WHERE id = #{id}
    </update>
	
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update sys_role set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into sys_role(id,  name,  role_type,  useable,  create_by,  create_time,  update_by,  update_time,  remarks,  del_flag) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.name},  #{item.roleType},  #{item.useable},  #{item.createBy},  #{item.createTime},  #{item.updateBy},  #{item.updateTime},  #{item.remarks},  #{item.delFlag})
        </foreach>
    </insert>

</mapper>