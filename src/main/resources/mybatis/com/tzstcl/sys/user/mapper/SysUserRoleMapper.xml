<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzstcl.sys.user.mapper.SysUserRoleMapper">

    <sql id="userRoleColumns">
        user_id as 'userId',
        role_id as 'roleId'
    </sql>

    <select id="findList" resultType="com.tzstcl.sys.user.model.SysUserRole">
        select <include refid="userRoleColumns"/> from sys_user_role where user_id = #{userId}
    </select>

    <insert id="save" parameterType="java.util.List">
        insert into  sys_user_role(
          user_id,
          role_id
        ) values
        <foreach collection="list" item="item" separator=",">
            (#{item.userId},#{item.roleId})
        </foreach>
    </insert>

    <insert id="insert" parameterType="java.util.List">
        insert into  sys_user_role(
        user_id,
        role_id
        ) values(
            #{userId},
            #{roleId}
            )
    </insert>

    <delete id="delete" parameterType="java.util.List">
        delete from sys_user_role where user_id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </delete>

    <delete id="deleteById">
        delete from sys_user_role where user_id = #{id}
    </delete>
</mapper>
