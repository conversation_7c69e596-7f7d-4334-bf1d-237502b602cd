<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzstcl.sys.user.mapper.SysMenuMapper">

    <sql id="menuColumns">
          id as 'id',
          parent_id as 'parentId',
          name,
          sort,
          href,
          icon,
          is_show as 'isShows',
          permission,
          menu_type as 'menuType',
          create_by as 'createBy',
          create_time as 'createTime',
          update_by as 'updateBy',
          update_time as 'updateTime',
          remarks,
          del_flag as 'delFlag'
    </sql>

    <sql id="menuColumnsAs">
          sm.id as 'id',
          sm.parent_id as 'parentId',
          sm.name,
          sm.sort,
          sm.href,
          sm.icon,
          sm.is_show as 'isShows',
          sm.permission,
          sm.menu_type as 'menuType',
          sm.create_by as 'createBy',
          sm.create_time as 'createTime',
          sm.update_by as 'updateBy',
          sm.update_time as 'updateTime',
          sm.remarks,
          sm.del_flag as 'delFlag'

    </sql>

    <select id="get" resultType="com.tzstcl.sys.user.model.SysMenu">
         select <include refid="menuColumns"/> from  sys_menu where id = #{id}
     </select>

    <select id="getOne" resultType="com.tzstcl.sys.user.model.SysMenu">
        select <include refid="menuColumns"/> from  sys_menu where id = #{id}
    </select>

    <select id="selectByPageNumSize" resultType="com.tzstcl.sys.user.model.SysMenu">
        select <include refid="menuColumns"/> from sys_menu
        <where>
            del_flag = '0'
        </where>
    </select>

    <select id="findListAll" resultType="com.tzstcl.sys.user.model.SysMenu">
        select <include refid="menuColumns"/> from sys_menu
        <where>
            del_flag = '0'
        </where>
        order by sort asc
    </select>

    <insert id="save">
        insert into  sys_menu(
        id,
        <if test="parentId != null ">
            parent_id,
        </if>
          name,
        <if test="sort != null ">
            sort,
        </if>
          href,
          icon,
          is_show,
          permission,
          menu_type,
          create_by,
          create_time,
          update_by,
          update_time,
          remarks,
          del_flag
        ) values (
             #{id},
        <if test="parentId != null ">
            #{parentId},
        </if>
          #{name},
        <if test="sort != null ">
            #{sort},
        </if>
          #{href},
          #{icon},
          #{isShows},
          #{permission},
          #{menuType},
          #{createBy},
          #{createTime},
          #{updateBy},
          #{updateTime},
          #{remarks},
          #{delFlag}
        )
    </insert>

    <update id="update">
        update sys_menu set
          parent_id = #{parentId},
          name = #{name},
          sort = #{sort},
          href = #{href},
          icon = #{icon},
          is_show = #{isShows},
          permission = #{permission},
          menu_type = #{menuType},
          update_by = #{updateBy},
          update_time = #{updateTime},
          remarks = #{remarks}
        where id = #{id}
    </update>

    <update id="delete" parameterType="java.util.List">
        update sys_menu set del_flag ='1'  where id in(
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
        or parent_id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <select id="findUserMenu" parameterType="java.lang.Long"  resultType="com.tzstcl.sys.user.model.SysMenu">
        SELECT
        <include refid="menuColumnsAs"/>
        FROM
        sys_menu sm
        WHERE sm.del_flag = '0' and sm.is_show='0' and menu_type!='2' and (sm.permission IN (
            SELECT    sp.permission
            FROM sys_role_permission srp left join sys_permission sp on srp.permission_id=sp.id and  sp.del_flag='0'
            WHERE
            srp.role_id IN ( SELECT sur.role_id FROM sys_user_role sur WHERE sur.user_id =#{id} )
        )          or sm.permission='' or sm.permission is null)
        ORDER BY sort ASC
    </select>

    <select id="findListAllMenu" resultType="com.tzstcl.sys.user.model.SysMenu">
        select <include refid="menuColumns"/> from sys_menu sm
        <where>
            sm.del_flag = '0' and sm.is_show='0' and menu_type!='2'
        </where>
        order by sort asc
    </select>
</mapper>
