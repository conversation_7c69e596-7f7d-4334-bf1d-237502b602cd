<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.sys.user.mapper.SysDeptMapper">

    <resultMap id="BaseResultMap" type="com.tzstcl.sys.user.model.SysDept">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="dept_name" property="deptName" jdbcType="VARCHAR"/>
        <result column="leader" property="leader" jdbcType="VARCHAR"/>
        <result column="order_num" property="orderNum" jdbcType="INTEGER"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="del_flag" property="delFlag" jdbcType="CHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="project_area_code" property="projectAreaCode" />
        <result column="name" property="areaName" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        dept_name,
        leader,
        order_num,
        parent_id,
        phone,
        remarks,
        status,
        create_by,
        create_time,
        del_flag,
        update_by,
        update_time,
        project_area_code
    </sql>

    <sql id="Base_Column_List2">
        select
        d.id,
        d.dept_name,
        d.leader,
        d.order_num,
        d.parent_id,
        d.phone,
        d.remarks,
        d.status,
        d.create_by,
        d.create_time,
        d.del_flag,
        d.update_by,
        d.update_time,
        d.project_area_code,a.name as areaName from sys_dept d left join area a on d.project_area_code = a.id
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long">
        <include refid="Base_Column_List2"/>
        where d.del_flag = '0' and d.id = #{id}
    </select>

    <select id="getOne" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.user.model.SysDept">
        <include refid="Base_Column_List2"/>
        where d.del_flag = '0' and d.id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.user.model.SysDept">
        <include refid="Base_Column_List2"/>
        <where>
            d.del_flag='0'
            <if test="id!= null">
                and d.id = #{id}
            </if>
            <if test="deptName != null ">
                and d.dept_name=#{deptName}
            </if>
            <if test="leader != null ">
                and d.leader=#{leader}
            </if>
            <if test="orderNum != null ">
                and d.order_num=#{orderNum}
            </if>
            <if test="parentId != null ">
                and d.parent_id=#{parentId}
            </if>
            <if test="phone != null ">
                and d.phone=#{phone}
            </if>
            <if test="status != null ">
                and d.status=#{status}
            </if>
            <if test="projectAreaCode != null">
                and d.project_area_code=#{projectAreaCode}
            </if>
        </where>
    </select>

    <select id="findListAll" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.user.model.SysDept">
        <include refid="Base_Column_List2"/>
        <where>
            d.del_flag = '0'
        </where>
        order by d.order_num asc
    </select>

    <select id="deptListForStatus" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.user.model.SysDept">
        <include refid="Base_Column_List2"/>
        where
        d.del_flag = '0'
        and
        d.status = '0'
        order by d.order_num asc
    </select>

    <insert id="insert" parameterType="com.tzstcl.sys.user.model.SysDept">
        insert into sys_dept
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="deptName != null">
                dept_name,
            </if>
            <if test="id != null">
                id,
            </if>
            <if test="leader != null">
                leader,
            </if>
            <if test="orderNum != null">
                order_num,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="projectAreaCode != null">
                project_area_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if>
            <if test="deptName != null ">
                #{deptName},
            </if>
            <if test="id != null ">
                #{id},
            </if>
            <if test="leader != null ">
                #{leader},
            </if>
            <if test="orderNum != null ">
                #{orderNum},
            </if>
            <if test="parentId != null ">
                #{parentId},
            </if>
            <if test="phone != null ">
                #{phone},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="status != null ">
                #{status},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="projectAreaCode != null">
                #{projectAreaCode},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.sys.user.model.SysDept">
        update sys_dept
        <set>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
            <if test="deptName != null">
                dept_name=#{deptName},
            </if>
            <if test="leader != null">
                leader=#{leader},
            </if>
            <if test="orderNum != null">
                order_num=#{orderNum},
            </if>
            <if test="parentId != null">
                parent_id=#{parentId},
            </if>
            <if test="phone != null">
                phone=#{phone},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="status != null">
                status=#{status},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="projectAreaCode != null">
                project_area_code=#{projectAreaCode},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="delete" parameterType="java.lang.Long">
        update sys_news_details set
        del_flag='1'
        WHERE   id = #{id}
    </update>

    <update id="deletes" parameterType="java.util.List">
        update sys_dept set del_flag ='1' where id in(
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
        or parent_id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update sys_dept set
        del_flag='1'
        WHERE id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
            #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into sys_dept(create_by, create_time, del_flag, dept_name, id, leader, order_num, parent_id, phone,
        remarks, status, update_by, update_time,project_area_code) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.createBy}, #{item.createTime}, #{item.delFlag}, #{item.deptName}, #{item.id}, #{item.leader},
            #{item.orderNum}, #{item.parentId}, #{item.phone}, #{item.remarks}, #{item.status}, #{item.updateBy},
            #{item.updateTime},#{item.projectAreaCode})
        </foreach>
    </insert>

    <select id="getDataForSelect" parameterType="java.lang.String" resultType="java.util.Map">
        select id, dept_name name
        from sys_dept
        where
        del_flag='0'
    </select>

    <select id="findListByDept" resultMap="BaseResultMap">
        <include refid="Base_Column_List2"/>
        where
        d.del_flag='0'
        and d.id like concat( #{deptId},'%')
    </select>
</mapper>
