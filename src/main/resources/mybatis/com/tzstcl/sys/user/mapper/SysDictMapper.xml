<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.sys.user.mapper.SysDictMapper" >

    <resultMap   id="BaseResultMap" type="com.tzstcl.sys.user.model.SysDict" >
        <id column="id" property="id"/>
        <result column="create_by" property="createBy"  />
        <result column="create_time" property="createTime"  />
        <result column="del_flag" property="delFlag"/>
        <result column="dict_label" property="dictLabel" />
        <result column="dict_name" property="dictName"  />
        <result column="dict_type" property="dictType" />
        <result column="dict_value" property="dictValue"  />
        <result column="remarks" property="remarks" />
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="update_by" property="updateBy"  />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List" >
        create_by,
        create_time,
        del_flag,
        dict_label,
        dict_name,
        dict_type,
        dict_value,
        id,
        remarks,
        sort,
        status,
        update_by,
        update_time
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from sys_dict
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.user.model.SysDict">
        select
        <include refid="Base_Column_List" />
        from sys_dict
        where
        where id = #{id}
    </select>
    <select id="getIn" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from sys_dict
        where
        dict_value in (${dictValueText})
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.user.model.SysDict">
        select
        <include refid="Base_Column_List" />
        from sys_dict
        <where>
            del_flag='0'
            <if test="id!= null and ''!=id">
                and id = #{id}
            </if>
            <if test="dictLabel != null  and dictLabel !=''">
                and dict_label=#{dictLabel}
            </if>
            <if test="dictName != null and dictName !=''">
                and dict_name=#{dictName}
            </if>
            <if test="dictType != null">
                and dict_type=#{dictType}
            </if>
            <if test="dictValue != null">
                and dict_value=#{dictValue}
            </if>
            <if test="sort != null">
                and sort=#{sort}
            </if>
            <if test="status != null">
                and status=#{status}
            </if>
         </where>
        order by dict_type desc,sort asc
    </select>

    <insert id="insert" parameterType="com.tzstcl.sys.user.model.SysDict">
        insert into sys_dict
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="createBy != null">
                    create_by,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="delFlag != null">
                    del_flag,
                </if>
                <if test="dictLabel != null">
                    dict_label,
                </if>
                <if test="dictName != null">
                    dict_name,
                </if>
                <if test="dictType != null">
                    dict_type,
                </if>
                <if test="dictValue != null">
                    dict_value,
                </if>
                <if test="id != null">
                    id,
                </if>
                <if test="remarks != null">
                    remarks,
                </if>
                <if test="sort != null">
                    sort,
                </if>
                <if test="status != null">
                    status,
                </if>
                <if test="updateBy != null">
                    update_by,
                </if>
                <if test="updateTime != null">
                    update_time,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="delFlag != null">
                #{delFlag},
            </if>
            <if test="dictLabel != null">
                #{dictLabel},
            </if>
            <if test="dictName != null">
                #{dictName},
            </if>
            <if test="dictType != null">
                #{dictType},
            </if>
            <if test="dictValue != null">
                #{dictValue},
            </if>
            <if test="id != null">
                #{id},
            </if>
            <if test="remarks != null">
                #{remarks},
            </if>
            <if test="sort != null">
                #{sort},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.sys.user.model.SysDict">
        update sys_dict
        <set>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
            <if test="dictLabel != null">
                dict_label=#{dictLabel},
            </if>
            <if test="dictName != null">
                dict_name=#{dictName},
            </if>
            <if test="dictType != null">
                dict_type=#{dictType},
            </if>
            <if test="dictValue != null">
                dict_value=#{dictValue},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="sort != null">
                sort=#{sort},
            </if>
            <if test="status != null">
                status=#{status},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
        </set>
        WHERE   id = #{id}
    </update>

    <update id="delete" parameterType="java.lang.Long">
        update sys_dict set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update sys_dict set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="ids" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>
    <select id="selectListByCache" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.user.model.SysDict">
        select
        dict_label,
        dict_name,
        dict_type,
        dict_value
        from sys_dict
        <where>
            del_flag='0'
            <if test="id!= null and ''!=id">
                and id = #{id}
            </if>
            <if test="dictLabel != null">
                and dict_label=#{dictLabel}
            </if>
            <if test="dictName != null">
                and dict_name=#{dictName}
            </if>
            <if test="dictType != null">
                and dict_type=#{dictType}
            </if>
            <if test="dictValue != null">
                and dict_value=#{dictValue}
            </if>

        </where>
        order by dict_type desc,sort asc
    </select>

    <select id="getDictByType" parameterType="java.lang.String" resultType="java.lang.String">
        select  dict_value
        from sys_dict where dict_type=#{dictType}
    </select>
    <select id="selectByValue" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from sys_dict
        <where>
            del_flag ='0'
        <if test="type !=null and type!=''">and dict_type=#{type}</if>
        <if test="value !=null and value!=''">and dict_value=#{value}</if>
        </where>
    </select>
</mapper>