<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.sys.user.mapper.SysRolePermissionMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.sys.user.model.SysRolePermission" >
        <result column="role_id" property="roleId" jdbcType="BIGINT" />
        <result column="permission_id" property="permissionId" jdbcType="BIGINT" />
        <result column="permission" property="permission" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        role_id,
        permission_id
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from sys_role_permission
        <where>
            <if test="roleId != null ">
                and role_id=#{roleId}
            </if>
            <if test="permissionId != null ">
                and permission_id=#{permissionId}
            </if>
        </where>
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.user.model.SysRolePermission">
        select
        <include refid="Base_Column_List" />
        from sys_role_permission
        where
        <where>
            <if test="roleId != null ">
                and role_id=#{roleId}
            </if>
            <if test="permissionId != null ">
                and permission_id=#{permissionId}
            </if>
        </where>
    </select>



    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.user.model.SysRolePermission">
        select
        <include refid="Base_Column_List" />
        from sys_role_permission
        <where>
            <if test="roleId != null ">
                and role_id=#{roleId}
            </if>
            <if test="permissionId != null ">
                and permission_id=#{permissionId}
            </if>
         </where>
    </select>

    <insert id="insert" parameterType="com.tzstcl.sys.user.model.SysRolePermission">
        insert into sys_role_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="roleId != null">
                    role_id,
                </if>
                <if test="permissionId != null">
                    permission_id,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleId != null ">
                #{roleId},
            </if>
            <if test="permissionId != null ">
                #{permissionId},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.sys.user.model.SysRolePermission">
        update sys_role_permission
        <set>
            <if test="roleId != null">
                role_id=#{roleId},
            </if>
            <if test="permissionId != null">
                permission_id=#{permissionId},
            </if>
        </set>
        WHERE   id = #{id}
    </update>


    <delete id="delete" parameterType="java.lang.Long">
        delete from  sys_role_permission   WHERE   role_id = #{roleId}
    </delete>

    <delete id="deleteBatchByIDs" parameterType="java.util.List">
        delete from  sys_role_permission
        WHERE   role_id
        <foreach close=")" collection="list" item="roleId" open="in (" separator=",">
        #{roleId}
        </foreach>
    </delete>

    <select id="findPermissionList"  parameterType="java.util.List"  resultMap="BaseResultMap" >
        select
        srp.role_id role_id,
        srp.permission_id permission_id,
        sp.permission permission
        from sys_role_permission srp left join sys_permission sp on srp.permission_id=sp.id and  sp.del_flag='0'
        WHERE   srp.role_id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
            #{id}
        </foreach>
    </select>
    <insert id="batchInsert" parameterType="java.util.List">
        insert into sys_role_permission  (  role_id,  permission_id)values
            <foreach collection="list" item="item" separator=",">
                (#{item.roleId},#{item.permissionId})
            </foreach>
    </insert>

</mapper>