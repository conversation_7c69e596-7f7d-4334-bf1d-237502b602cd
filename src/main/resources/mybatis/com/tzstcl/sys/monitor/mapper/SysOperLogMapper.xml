<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.sys.monitor.mapper.SysOperLogMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.sys.monitor.model.SysOperLog" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="log_type" property="logType" jdbcType="VARCHAR" />
        <result column="log_title" property="logTitle" jdbcType="VARCHAR" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_by_name" property="createByName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="request_uri" property="requestUri" jdbcType="VARCHAR" />
        <result column="request_method" property="requestMethod" jdbcType="VARCHAR" />
        <result column="request_params" property="requestParams" jdbcType="LONGVARCHAR" />
        <result column="diff_modify_data" property="diffModifyData" jdbcType="LONGVARCHAR" />
        <result column="biz_key" property="bizKey" jdbcType="VARCHAR" />
        <result column="biz_type" property="bizType" jdbcType="VARCHAR" />
        <result column="remote_addr" property="remoteAddr" jdbcType="VARCHAR" />
        <result column="server_addr" property="serverAddr" jdbcType="VARCHAR" />
        <result column="is_exception" property="isException" jdbcType="CHAR" />
        <result column="exception_info" property="exceptionInfo" jdbcType="LONGVARCHAR" />
        <result column="user_agent" property="userAgent" jdbcType="VARCHAR" />
        <result column="device_name" property="deviceName" jdbcType="VARCHAR" />
        <result column="browser_name" property="browserName" jdbcType="VARCHAR" />
        <result column="execute_time" property="executeTime" jdbcType="DECIMAL" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        log_type,
        log_title,
        create_by,
        create_by_name,
        create_time,
        request_uri,
        request_method,
        request_params,
        diff_modify_data,
        biz_key,
        biz_type,
        remote_addr,
        server_addr,
        is_exception,
        exception_info,
        user_agent,
        device_name,
        browser_name,
        execute_time
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from sys_oper_log
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.monitor.model.SysOperLog">
        select
        <include refid="Base_Column_List" />
        from sys_oper_log
        where
        id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.sys.monitor.model.SysOperLog">
        select
        <include refid="Base_Column_List" />
        from sys_oper_log
        <where>
            <if test="id!= null and ''!=id">
                and id = #{id}
            </if>
            <if test="logType != null">
                and log_type=#{logType}
            </if>
            <if test="logTitle != null">
                and log_title=#{logTitle}
            </if>
            <if test="createByName != null and createByName != ''">
                and create_by_name=#{createByName}
            </if>
            <if test="requestUri != null and requestUri != ''">
                and request_uri=#{requestUri}
            </if>
            <if test="requestMethod != null">
                and request_method=#{requestMethod}
            </if>
            <if test="requestParams != null">
                and request_params=#{requestParams}
            </if>
            <if test="diffModifyData != null">
                and diff_modify_data=#{diffModifyData}
            </if>
            <if test="bizKey != null">
                and biz_key=#{bizKey}
            </if>
            <if test="bizType != null">
                and biz_type=#{bizType}
            </if>
            <if test="remoteAddr != null">
                and remote_addr=#{remoteAddr}
            </if>
            <if test="serverAddr != null">
                and server_addr=#{serverAddr}
            </if>
            <if test="isException != null">
                and is_exception=#{isException}
            </if>
            <if test="exceptionInfo != null">
                and exception_info=#{exceptionInfo}
            </if>
            <if test="userAgent != null">
                and user_agent=#{userAgent}
            </if>
            <if test="deviceName != null">
                and device_name=#{deviceName}
            </if>
            <if test="browserName != null">
                and browser_name=#{browserName}
            </if>
            <if test="executeTime != null">
                and execute_time=#{executeTime}
            </if>
         </where>
        order by create_time desc
    </select>

    <insert id="insert" parameterType="com.tzstcl.sys.monitor.model.SysOperLog">
        insert into sys_oper_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="logType != null">
                    log_type,
                </if>
                <if test="logTitle != null">
                    log_title,
                </if>
                <if test="createBy != null">
                    create_by,
                </if>
                <if test="createByName != null">
                    create_by_name,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="requestUri != null">
                    request_uri,
                </if>
                <if test="requestMethod != null">
                    request_method,
                </if>
                <if test="requestParams != null">
                    request_params,
                </if>
                <if test="diffModifyData != null">
                    diff_modify_data,
                </if>
                <if test="bizKey != null">
                    biz_key,
                </if>
                <if test="bizType != null">
                    biz_type,
                </if>
                <if test="remoteAddr != null">
                    remote_addr,
                </if>
                <if test="serverAddr != null">
                    server_addr,
                </if>
                <if test="isException != null">
                    is_exception,
                </if>
                <if test="exceptionInfo != null">
                    exception_info,
                </if>
                <if test="userAgent != null">
                    user_agent,
                </if>
                <if test="deviceName != null">
                    device_name,
                </if>
                <if test="browserName != null">
                    browser_name,
                </if>
                <if test="executeTime != null">
                    execute_time,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="logType != null">
                #{logType},
            </if>
            <if test="logTitle != null">
                #{logTitle},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createByName != null">
                #{createByName},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="requestUri != null">
                #{requestUri},
            </if>
            <if test="requestMethod != null">
                #{requestMethod},
            </if>
            <if test="requestParams != null">
                #{requestParams},
            </if>
            <if test="diffModifyData != null">
                #{diffModifyData},
            </if>
            <if test="bizKey != null">
                #{bizKey},
            </if>
            <if test="bizType != null">
                #{bizType},
            </if>
            <if test="remoteAddr != null">
                #{remoteAddr},
            </if>
            <if test="serverAddr != null">
                #{serverAddr},
            </if>
            <if test="isException != null">
                #{isException},
            </if>
            <if test="exceptionInfo != null">
                #{exceptionInfo},
            </if>
            <if test="userAgent != null">
                #{userAgent},
            </if>
            <if test="deviceName != null">
                #{deviceName},
            </if>
            <if test="browserName != null">
                #{browserName},
            </if>
            <if test="executeTime != null">
                #{executeTime},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.sys.monitor.model.SysOperLog">
        update sys_oper_log
        <set>
            <if test="logType != null">
                log_type=#{logType},
            </if>
            <if test="logTitle != null">
                log_title=#{logTitle},
            </if>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createByName != null">
                create_by_name=#{createByName},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="requestUri != null">
                request_uri=#{requestUri},
            </if>
            <if test="requestMethod != null">
                request_method=#{requestMethod},
            </if>
            <if test="requestParams != null">
                request_params=#{requestParams},
            </if>
            <if test="diffModifyData != null">
                diff_modify_data=#{diffModifyData},
            </if>
            <if test="bizKey != null">
                biz_key=#{bizKey},
            </if>
            <if test="bizType != null">
                biz_type=#{bizType},
            </if>
            <if test="remoteAddr != null">
                remote_addr=#{remoteAddr},
            </if>
            <if test="serverAddr != null">
                server_addr=#{serverAddr},
            </if>
            <if test="isException != null">
                is_exception=#{isException},
            </if>
            <if test="exceptionInfo != null">
                exception_info=#{exceptionInfo},
            </if>
            <if test="userAgent != null">
                user_agent=#{userAgent},
            </if>
            <if test="deviceName != null">
                device_name=#{deviceName},
            </if>
            <if test="browserName != null">
                browser_name=#{browserName},
            </if>
            <if test="executeTime != null">
                execute_time=#{executeTime},
            </if>
        </set>
        WHERE   id = #{id}
    </update>

    <update id="delete" parameterType="java.lang.Long">
        update sys_oper_log set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update sys_oper_log set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="ids" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

</mapper>