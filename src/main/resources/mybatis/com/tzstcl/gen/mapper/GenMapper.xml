<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.gen.mapper.GenMapper">
    <resultMap id="BaseResultMap" type="com.tzstcl.gen.model.GenTableColumn">
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="isNull" property="isNull" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="VARCHAR"/>
        <result column="comments" property="comments" jdbcType="TIMESTAMP"/>
        <result column="jdbcType" property="jdbcType" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="getAllTable" resultType="map">
        select
          table_name tableName ,
          DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') createTime,
          table_comment tableComment
        from information_schema.tables
        where table_schema=database()
        order by create_time desc
    </select>

    <select id="getAllColumn" resultMap="BaseResultMap" parameterType="java.lang.String">
      SELECT
        t.COLUMN_NAME AS name,
        (CASE WHEN t.IS_NULLABLE = 'YES' THEN '1' ELSE '0' END) AS isNull,
        (t.ORDINAL_POSITION * 10) AS sort,
        t.COLUMN_COMMENT AS comments,
        t.COLUMN_TYPE AS jdbcType
       FROM information_schema.`COLUMNS` t
       WHERE t.TABLE_SCHEMA = (select database()) AND t.TABLE_NAME = upper(#{tableName})
    </select>

</mapper>