<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.equipmentFilingApplication.mapper.FilingEquipmentFilingApplicationMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="property_unit" property="propertyUnit" jdbcType="VARCHAR" />
        <result column="property_unit_code" property="propertyUnitCode" jdbcType="VARCHAR" />
        <result column="device_name" property="deviceName" jdbcType="VARCHAR" />
        <result column="specification_model" property="specificationModel" jdbcType="VARCHAR" />
        <result column="manufacturer" property="manufacturer" jdbcType="VARCHAR" />
        <result column="manufactureCorpCode" property="manufactureCorpCode" jdbcType="VARCHAR" />
        <result column="factory_time" property="factoryTime" jdbcType="VARCHAR" />
        <result column="manufacturing_license_number" property="manufacturingLicenseNumber" jdbcType="VARCHAR" />
        <result column="application_date" property="applicationDate" jdbcType="VARCHAR" />
        <result column="factory_license_number" property="factoryLicenseNumber" jdbcType="VARCHAR" />
        <result column="purchase_time" property="purchaseTime" jdbcType="VARCHAR" />
        <result column="unit_address" property="unitAddress" jdbcType="VARCHAR" />
        <result column="legal_representative" property="legalRepresentative" jdbcType="VARCHAR" />
        <result column="legal_representative_contact_number" property="legalRepresentativeContactNumber" jdbcType="VARCHAR" />
        <result column="technical_director" property="technicalDirector" jdbcType="VARCHAR" />
        <result column="technical_director_contact_number" property="technicalDirectorContactNumber" jdbcType="VARCHAR" />
        <result column="equipment_manager" property="equipmentManager" jdbcType="VARCHAR" />
        <result column="equipment_manager_contact_number" property="equipmentManagerContactNumber" jdbcType="VARCHAR" />
        <result column="lifting_weight" property="liftingWeight" jdbcType="VARCHAR" />
        <result column="equipment_type" property="equipmentType" jdbcType="CHAR" />
        <result column="product_certification_path" property="productCertificationPath" jdbcType="VARCHAR" />
        <result column="equipment_manufacturing_license_path" property="equipmentManufacturingLicensePath" jdbcType="VARCHAR" />
        <result column="filing_application_form_path" property="filingApplicationFormPath" jdbcType="VARCHAR" />
        <result column="purchase_sale_certificate_path" property="purchaseSaleCertificatePath" jdbcType="VARCHAR" />
        <result column="business_license_path" property="businessLicensePath" jdbcType="VARCHAR" />
        <result column="audit_status" property="auditStatus" jdbcType="CHAR" />
        <result column="audit_reject" property="auditReject" jdbcType="VARCHAR" />
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
        <result column="filing_code" property="filingCode" jdbcType="VARCHAR" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="INTEGER" />
        <result column="businessDataId" property="businessDataId" />
        <result column="unit_area_code" property="unitAreaCode"/>
        <result column="old_number_flag" property="oldNumberFlag"/>
        <result column="old_number" property="oldNumber"/>
        <result column="business_license_number" property="businessLicenseNumber"/>
        <result column="isFilingCancel" property="isFilingCancel"/>
        <result property="exFactoryPrice"    column="exFactoryPrice"    />
        <result property="designServiceLife"    column="designServiceLife"    />
        <result property="nextSaftyEvalTime"    column="nextSaftyEvalTime"    />
        <result property="ownerLPCodeType"    column="ownerLPCodeType"    />
        <result property="ownerLPCode"    column="ownerLPCode"    />
        <result property="ratedLoadMoment"    column="ratedLoadMoment"    />
        <result property="lengthOfCraneJib"    column="lengthOfCraneJib"    />
        <result property="maxWorkingRange"    column="maxWorkingRange"    />
        <result property="ratedLiftingCATWR"    column="ratedLiftingCATWR"    />
        <result property="nonTieInLoadLH"    column="nonTieInLoadLH"    />
        <result property="maxLiftingHeight"    column="maxLiftingHeight"    />
        <result property="standardSectionPOfTC"    column="standardSectionPOfTC"    />
        <result property="stdSectionMainSSOfTC"    column="stdSectionMainSSOfTC"    />
        <result property="reinforceSectionPOfTC"    column="reinforceSectionPOfTC"    />
        <result property="mainStrUniqueCode"    column="mainStrUniqueCode"    />
        <result property="constructionHUTypes"    column="constructionHUTypes"    />
        <result property="maxHoistingHeight"    column="maxHoistingHeight"    />
        <result property="ratedLiftingSpeed"    column="ratedLiftingSpeed"    />
        <result property="totalElectricMotorPower"    column="totalElectricMotorPower"    />
        <result property="antifallSafetyDeviceType"    column="antifallSafetyDeviceType"    />
        <result property="carrierUnitHD"    column="carrierUnitHD"    />
        <result property="otherCraneType"    column="otherCraneType"    />
        <result property="portalCraneSpan"    column="portalCraneSpan"    />
        <result property="firstFilingDate"    column="firstFilingDate"    />
        <result property="certState"    column="certState"    />
        <result property="certStateDesc"    column="certStateDesc"    />
        <result property="associatedCertId"    column="associatedCertId"    />
        <result property="operateType"    column="operateType"    />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        user_id,
        property_unit,
        property_unit_code,
        device_name,
        specification_model,
        manufacturer,
        manufactureCorpCode,
        factory_time,
        manufacturing_license_number,
        application_date,
        factory_license_number,
        purchase_time,
        unit_address,
        legal_representative,
        legal_representative_contact_number,
        technical_director,
        technical_director_contact_number,
        equipment_manager,
        equipment_manager_contact_number,
        lifting_weight,
        equipment_type,
        product_certification_path,
        equipment_manufacturing_license_path,
        filing_application_form_path,
        purchase_sale_certificate_path,
        business_license_path,
        audit_status,
        audit_reject,
        audit_time,
        filing_code,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag,
        businessDataId,
        unit_area_code,
        business_license_number,
        old_number,
        old_number_flag,
        isFilingCancel, exFactoryPrice, designServiceLife, nextSaftyEvalTime, ownerLPCodeType, ownerLPCode, ratedLoadMoment, lengthOfCraneJib, maxWorkingRange, ratedLiftingCATWR, nonTieInLoadLH, maxLiftingHeight, standardSectionPOfTC, stdSectionMainSSOfTC, reinforceSectionPOfTC, mainStrUniqueCode, constructionHUTypes, maxHoistingHeight, ratedLiftingSpeed, totalElectricMotorPower, antifallSafetyDeviceType, carrierUnitHD, otherCraneType, portalCraneSpan, firstFilingDate, certState, certStateDesc, associatedCertId, operateType
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_filing_application
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_filing_application
        where
        id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_filing_application
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="userId != null ">
                and user_id=#{userId}
            </if>
            <if test="propertyUnit != null ">
                and property_unit like concat('%', #{propertyUnit}, '%')
            </if>
            <if test="propertyUnitCode != null ">
                and property_unit_code=#{propertyUnitCode}
            </if>
            <if test="deviceName != null ">
                and device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="specificationModel != null ">
                and specification_model=#{specificationModel}
            </if>
            <if test="manufacturer != null ">
                and manufacturer=#{manufacturer}
            </if>
            <if test="factoryTime != null ">
                and factory_time=#{factoryTime}
            </if>
            <if test="manufacturingLicenseNumber != null ">
                and manufacturing_license_number=#{manufacturingLicenseNumber}
            </if>
            <if test="applicationDate != null ">
                and application_date=#{applicationDate}
            </if>
            <if test="factoryLicenseNumber != null ">
                and factory_license_number=#{factoryLicenseNumber}
            </if>
            <if test="purchaseTime != null ">
                and purchase_time=#{purchaseTime}
            </if>
            <if test="unitAddress != null ">
                and unit_address=#{unitAddress}
            </if>
            <if test="legalRepresentative != null ">
                and legal_representative=#{legalRepresentative}
            </if>
            <if test="legalRepresentativeContactNumber != null ">
                and legal_representative_contact_number=#{legalRepresentativeContactNumber}
            </if>
            <if test="technicalDirector != null ">
                and technical_director=#{technicalDirector}
            </if>
            <if test="technicalDirectorContactNumber != null ">
                and technical_director_contact_number=#{technicalDirectorContactNumber}
            </if>
            <if test="equipmentManager != null ">
                and equipment_manager=#{equipmentManager}
            </if>
            <if test="equipmentManagerContactNumber != null ">
                and equipment_manager_contact_number=#{equipmentManagerContactNumber}
            </if>
            <if test="liftingWeight != null ">
                and lifting_weight=#{liftingWeight}
            </if>
            <if test="productCertificationPath != null ">
                and product_certification_path=#{productCertificationPath}
            </if>
            <if test="equipmentManufacturingLicensePath != null ">
                and equipment_manufacturing_license_path=#{equipmentManufacturingLicensePath}
            </if>
            <if test="filingApplicationFormPath != null ">
                and filing_application_form_path=#{filingApplicationFormPath}
            </if>
            <if test="purchaseSaleCertificatePath != null ">
                and purchase_sale_certificate_path=#{purchaseSaleCertificatePath}
            </if>
            <if test="businessLicensePath != null ">
                and business_license_path=#{businessLicensePath}
            </if>
            <if test="auditStatus != null ">
                and audit_status=#{auditStatus}
            </if>
            <if test="auditReject != null ">
                and audit_reject=#{auditReject}
            </if>
            <if test="auditTime != null ">
                and audit_time=#{auditTime}
            </if>
            <if test="filingCode != null ">
                and filing_code=#{filingCode}
            </if>
            <if test="equipmentType != null">
                and equipment_type=#{equipmentType}
            </if>
            <if test="businessDataId != null">
                and businessDataId=#{businessDataId}
            </if>
            <if test="unitAreaCode != null"> and unit_area_code=#{unitAreaCode}</if>
            <if test="isFilingCancel != null"> and isFilingCancel=#{isFilingCancel}</if>
        </where>
        order by create_time desc
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication">
        insert into filing_equipment_filing_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="propertyUnit != null">
                property_unit,
            </if>
            <if test="propertyUnitCode != null">
                property_unit_code,
            </if>
            <if test="deviceName != null">
                device_name,
            </if>
            <if test="specificationModel != null">
                specification_model,
            </if>
            <if test="manufacturer != null">
                manufacturer,
            </if>
            <if test="manufactureCorpCode != null">
                manufactureCorpCode,
            </if>
            <if test="factoryTime != null">
                factory_time,
            </if>
            <if test="manufacturingLicenseNumber != null">
                manufacturing_license_number,
            </if>
            <if test="applicationDate != null">
                application_date,
            </if>
            <if test="factoryLicenseNumber != null">
                factory_license_number,
            </if>
            <if test="purchaseTime != null">
                purchase_time,
            </if>
            <if test="unitAddress != null">
                unit_address,
            </if>
            <if test="legalRepresentative != null">
                legal_representative,
            </if>
            <if test="legalRepresentativeContactNumber != null">
                legal_representative_contact_number,
            </if>
            <if test="technicalDirector != null">
                technical_director,
            </if>
            <if test="technicalDirectorContactNumber != null">
                technical_director_contact_number,
            </if>
            <if test="equipmentManager != null">
                equipment_manager,
            </if>
            <if test="equipmentManagerContactNumber != null">
                equipment_manager_contact_number,
            </if>
            <if test="liftingWeight != null">
                lifting_weight,
            </if>
            <if test="productCertificationPath != null">
                product_certification_path,
            </if>
            <if test="equipmentManufacturingLicensePath != null">
                equipment_manufacturing_license_path,
            </if>
            <if test="filingApplicationFormPath != null">
                filing_application_form_path,
            </if>
            <if test="purchaseSaleCertificatePath != null">
                purchase_sale_certificate_path,
            </if>
            <if test="businessLicensePath != null">
                business_license_path,
            </if>
            <if test="auditStatus != null">
                audit_status,
            </if>
            <if test="auditReject != null">
                audit_reject,
            </if>
            <if test="auditTime != null">
                audit_time,
            </if>
            <if test="filingCode != null">
                filing_code,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="equipmentType !=null">
                equipment_type,
            </if>
            <if test="businessDataId !=null">
                businessDataId,
            </if>
            <if test="unitAreaCode !=null"> unit_area_code,</if>
            <if test="oldNumberFlag !=null"> old_number_flag,</if>
            <if test="businessLicenseNumber !=null"> business_license_number,</if>
            <if test="oldNumber !=null"> old_number,</if>
            <if test="isFilingCancel !=null"> isFilingCancel,</if>
            <if test="exFactoryPrice != null">exFactoryPrice,</if>
            <if test="designServiceLife != null">designServiceLife,</if>
            <if test="nextSaftyEvalTime != null">nextSaftyEvalTime,</if>
            <if test="ownerLPCodeType != null">ownerLPCodeType,</if>
            <if test="ownerLPCode != null">ownerLPCode,</if>
            <if test="ratedLoadMoment != null">ratedLoadMoment,</if>
            <if test="lengthOfCraneJib != null">lengthOfCraneJib,</if>
            <if test="maxWorkingRange != null">maxWorkingRange,</if>
            <if test="ratedLiftingCATWR != null">ratedLiftingCATWR,</if>
            <if test="nonTieInLoadLH != null">nonTieInLoadLH,</if>
            <if test="maxLiftingHeight != null">maxLiftingHeight,</if>
            <if test="standardSectionPOfTC != null">standardSectionPOfTC,</if>
            <if test="stdSectionMainSSOfTC != null">stdSectionMainSSOfTC,</if>
            <if test="reinforceSectionPOfTC != null">reinforceSectionPOfTC,</if>
            <if test="mainStrUniqueCode != null">mainStrUniqueCode,</if>
            <if test="constructionHUTypes != null">constructionHUTypes,</if>
            <if test="maxHoistingHeight != null">maxHoistingHeight,</if>
            <if test="ratedLiftingSpeed != null">ratedLiftingSpeed,</if>
            <if test="totalElectricMotorPower != null">totalElectricMotorPower,</if>
            <if test="antifallSafetyDeviceType != null">antifallSafetyDeviceType,</if>
            <if test="carrierUnitHD != null">carrierUnitHD,</if>
            <if test="otherCraneType != null">otherCraneType,</if>
            <if test="portalCraneSpan != null">portalCraneSpan,</if>
            <if test="firstFilingDate != null">firstFilingDate,</if>
            <if test="certState != null">certState,</if>
            <if test="certStateDesc != null">certStateDesc,</if>
            <if test="associatedCertId != null">associatedCertId,</if>
            <if test="operateType != null">operateType,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="userId != null ">
                #{userId},
            </if>
            <if test="propertyUnit != null ">
                #{propertyUnit},
            </if>
            <if test="propertyUnitCode != null ">
                #{propertyUnitCode},
            </if>
            <if test="deviceName != null ">
                #{deviceName},
            </if>
            <if test="specificationModel != null ">
                #{specificationModel},
            </if>
            <if test="manufacturer != null ">
                #{manufacturer},
            </if>
            <if test="manufactureCorpCode != null ">
                #{manufactureCorpCode},
            </if>
            <if test="factoryTime != null ">
                #{factoryTime},
            </if>
            <if test="manufacturingLicenseNumber != null ">
                #{manufacturingLicenseNumber},
            </if>
            <if test="applicationDate != null ">
                #{applicationDate},
            </if>
            <if test="factoryLicenseNumber != null ">
                #{factoryLicenseNumber},
            </if>
            <if test="purchaseTime != null ">
                #{purchaseTime},
            </if>
            <if test="unitAddress != null ">
                #{unitAddress},
            </if>
            <if test="legalRepresentative != null ">
                #{legalRepresentative},
            </if>
            <if test="legalRepresentativeContactNumber != null ">
                #{legalRepresentativeContactNumber},
            </if>
            <if test="technicalDirector != null ">
                #{technicalDirector},
            </if>
            <if test="technicalDirectorContactNumber != null ">
                #{technicalDirectorContactNumber},
            </if>
            <if test="equipmentManager != null ">
                #{equipmentManager},
            </if>
            <if test="equipmentManagerContactNumber != null ">
                #{equipmentManagerContactNumber},
            </if>
            <if test="liftingWeight != null ">
                #{liftingWeight},
            </if>
            <if test="productCertificationPath != null ">
                #{productCertificationPath},
            </if>
            <if test="equipmentManufacturingLicensePath != null ">
                #{equipmentManufacturingLicensePath},
            </if>
            <if test="filingApplicationFormPath != null ">
                #{filingApplicationFormPath},
            </if>
            <if test="purchaseSaleCertificatePath != null ">
                #{purchaseSaleCertificatePath},
            </if>
            <if test="businessLicensePath != null ">
                #{businessLicensePath},
            </if>
            <if test="auditStatus != null ">
                #{auditStatus},
            </if>
            <if test="auditReject != null ">
                #{auditReject},
            </if>
            <if test="auditTime != null ">
                #{auditTime},
            </if>
            <if test="filingCode != null ">
                #{filingCode},
            </if>
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if>
            <if test="equipmentType!= null">
                #{equipmentType},
            </if>
            <if test="businessDataId !=null">
                #{businessDataId},
            </if>
            <if test="unitAreaCode!= null">
                #{unitAreaCode},
            </if>
            <if test="oldNumberFlag !=null"> #{oldNumberFlag},</if>
            <if test="businessLicenseNumber !=null">#{businessLicenseNumber},</if>
            <if test="oldNumber !=null"> #{oldNumber},</if>
            <if test="isFilingCancel !=null"> #{isFilingCancel},</if>
            <if test="exFactoryPrice != null">#{exFactoryPrice},</if>
            <if test="designServiceLife != null">#{designServiceLife},</if>
            <if test="nextSaftyEvalTime != null">#{nextSaftyEvalTime},</if>
            <if test="ownerLPCodeType != null">#{ownerLPCodeType},</if>
            <if test="ownerLPCode != null">#{ownerLPCode},</if>
            <if test="ratedLoadMoment != null">#{ratedLoadMoment},</if>
            <if test="lengthOfCraneJib != null">#{lengthOfCraneJib},</if>
            <if test="maxWorkingRange != null">#{maxWorkingRange},</if>
            <if test="ratedLiftingCATWR != null">#{ratedLiftingCATWR},</if>
            <if test="nonTieInLoadLH != null">#{nonTieInLoadLH},</if>
            <if test="maxLiftingHeight != null">#{maxLiftingHeight},</if>
            <if test="standardSectionPOfTC != null">#{standardSectionPOfTC},</if>
            <if test="stdSectionMainSSOfTC != null">#{stdSectionMainSSOfTC},</if>
            <if test="reinforceSectionPOfTC != null">#{reinforceSectionPOfTC},</if>
            <if test="mainStrUniqueCode != null">#{mainStrUniqueCode},</if>
            <if test="constructionHUTypes != null">#{constructionHUTypes},</if>
            <if test="maxHoistingHeight != null">#{maxHoistingHeight},</if>
            <if test="ratedLiftingSpeed != null">#{ratedLiftingSpeed},</if>
            <if test="totalElectricMotorPower != null">#{totalElectricMotorPower},</if>
            <if test="antifallSafetyDeviceType != null">#{antifallSafetyDeviceType},</if>
            <if test="carrierUnitHD != null">#{carrierUnitHD},</if>
            <if test="otherCraneType != null">#{otherCraneType},</if>
            <if test="portalCraneSpan != null">#{portalCraneSpan},</if>
            <if test="firstFilingDate != null">#{firstFilingDate},</if>
            <if test="certState != null">#{certState},</if>
            <if test="certStateDesc != null">#{certStateDesc},</if>
            <if test="associatedCertId != null">#{associatedCertId},</if>
            <if test="operateType != null">#{operateType},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication">
        update filing_equipment_filing_application
        <set>
            <if test="userId != null">
                user_id=#{userId},
            </if>
            <if test="propertyUnit != null">
                property_unit=#{propertyUnit},
            </if>
            <if test="propertyUnitCode != null">
                property_unit_code=#{propertyUnitCode},
            </if>
            <if test="deviceName != null">
                device_name=#{deviceName},
            </if>
            <if test="specificationModel != null">
                specification_model=#{specificationModel},
            </if>
            <if test="manufacturer != null">
                manufacturer=#{manufacturer},
            </if>
            <if test="manufactureCorpCode != null">
                manufactureCorpCode=#{manufactureCorpCode},
            </if>
            <if test="factoryTime != null">
                factory_time=#{factoryTime},
            </if>
            <if test="manufacturingLicenseNumber != null">
                manufacturing_license_number=#{manufacturingLicenseNumber},
            </if>
            <if test="applicationDate != null">
                application_date=#{applicationDate},
            </if>
            <if test="factoryLicenseNumber != null">
                factory_license_number=#{factoryLicenseNumber},
            </if>
            <if test="purchaseTime != null">
                purchase_time=#{purchaseTime},
            </if>
            <if test="unitAddress != null">
                unit_address=#{unitAddress},
            </if>
            <if test="legalRepresentative != null">
                legal_representative=#{legalRepresentative},
            </if>
            <if test="legalRepresentativeContactNumber != null">
                legal_representative_contact_number=#{legalRepresentativeContactNumber},
            </if>
            <if test="technicalDirector != null">
                technical_director=#{technicalDirector},
            </if>
            <if test="technicalDirectorContactNumber != null">
                technical_director_contact_number=#{technicalDirectorContactNumber},
            </if>
            <if test="equipmentManager != null">
                equipment_manager=#{equipmentManager},
            </if>
            <if test="equipmentManagerContactNumber != null">
                equipment_manager_contact_number=#{equipmentManagerContactNumber},
            </if>
            <if test="liftingWeight != null">
                lifting_weight=#{liftingWeight},
            </if>
            <if test="productCertificationPath != null">
                product_certification_path=#{productCertificationPath},
            </if>
            <if test="equipmentManufacturingLicensePath != null">
                equipment_manufacturing_license_path=#{equipmentManufacturingLicensePath},
            </if>
            <if test="filingApplicationFormPath != null">
                filing_application_form_path=#{filingApplicationFormPath},
            </if>
            <if test="purchaseSaleCertificatePath != null">
                purchase_sale_certificate_path=#{purchaseSaleCertificatePath},
            </if>
            <if test="businessLicensePath != null">
                business_license_path=#{businessLicensePath},
            </if>
            <if test="auditStatus != null">
                audit_status=#{auditStatus},
            </if>
            <if test="auditReject != null">
                audit_reject=#{auditReject},
            </if>
            <if test="auditTime != null">
                audit_time=#{auditTime},
            </if>
            <if test="filingCode != null">
                filing_code=#{filingCode},
            </if>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
            <if test="equipmentType!= null">
                equipment_type =#{equipmentType},
            </if>
            <if test="businessDataId!= null">
                businessDataId =#{businessDataId},
            </if>
            <if test="unitAreaCode!= null"> unit_area_code =#{unitAreaCode},</if>
            <if test="oldNumberFlag !=null"> old_number_flag=#{oldNumberFlag},</if>
            <if test="businessLicenseNumber !=null"> business_license_number=#{businessLicenseNumber},</if>
            <if test="oldNumber !=null"> old_number=#{oldNumber},</if>
            <if test="isFilingCancel !=null"> isFilingCancel=#{isFilingCancel},</if>
            <if test="exFactoryPrice != null">exFactoryPrice = #{exFactoryPrice},</if>
            <if test="designServiceLife != null">designServiceLife = #{designServiceLife},</if>
            <if test="nextSaftyEvalTime != null">nextSaftyEvalTime = #{nextSaftyEvalTime},</if>
            <if test="ownerLPCodeType != null">ownerLPCodeType = #{ownerLPCodeType},</if>
            <if test="ownerLPCode != null">ownerLPCode = #{ownerLPCode},</if>
            <if test="ratedLoadMoment != null">ratedLoadMoment = #{ratedLoadMoment},</if>
            <if test="lengthOfCraneJib != null">lengthOfCraneJib = #{lengthOfCraneJib},</if>
            <if test="maxWorkingRange != null">maxWorkingRange = #{maxWorkingRange},</if>
            <if test="ratedLiftingCATWR != null">ratedLiftingCATWR = #{ratedLiftingCATWR},</if>
            <if test="nonTieInLoadLH != null">nonTieInLoadLH = #{nonTieInLoadLH},</if>
            <if test="maxLiftingHeight != null">maxLiftingHeight = #{maxLiftingHeight},</if>
            <if test="standardSectionPOfTC != null">standardSectionPOfTC = #{standardSectionPOfTC},</if>
            <if test="stdSectionMainSSOfTC != null">stdSectionMainSSOfTC = #{stdSectionMainSSOfTC},</if>
            <if test="reinforceSectionPOfTC != null">reinforceSectionPOfTC = #{reinforceSectionPOfTC},</if>
            <if test="mainStrUniqueCode != null">mainStrUniqueCode = #{mainStrUniqueCode},</if>
            <if test="constructionHUTypes != null">constructionHUTypes = #{constructionHUTypes},</if>
            <if test="maxHoistingHeight != null">maxHoistingHeight = #{maxHoistingHeight},</if>
            <if test="ratedLiftingSpeed != null">ratedLiftingSpeed = #{ratedLiftingSpeed},</if>
            <if test="totalElectricMotorPower != null">totalElectricMotorPower = #{totalElectricMotorPower},</if>
            <if test="antifallSafetyDeviceType != null">antifallSafetyDeviceType = #{antifallSafetyDeviceType},</if>
            <if test="carrierUnitHD != null">carrierUnitHD = #{carrierUnitHD},</if>
            <if test="otherCraneType != null">otherCraneType = #{otherCraneType},</if>
            <if test="portalCraneSpan != null">portalCraneSpan = #{portalCraneSpan},</if>
            <if test="firstFilingDate != null">firstFilingDate = #{firstFilingDate},</if>
            <if test="certState != null">certState = #{certState},</if>
            <if test="certStateDesc != null">certStateDesc = #{certStateDesc},</if>
            <if test="associatedCertId != null">associatedCertId = #{associatedCertId},</if>
            <if test="operateType != null">operateType = #{operateType},</if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update filing_equipment_filing_application set
            del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update filing_equipment_filing_application set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
            #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into filing_equipment_filing_application(id,  user_id,  property_unit, property_unit_code,  device_name,  specification_model,  manufacturer,  factory_time,  manufacturing_license_number,  application_date,  factory_license_number,  purchase_time,  unit_address,  legal_representative,  legal_representative_contact_number,  technical_director,  technical_director_contact_number,  equipment_manager,  equipment_manager_contact_number,  lifting_weight,  product_certification_path,  equipment_manufacturing_license_path,  filing_application_form_path,  purchase_sale_certificate_path,  business_license_path,  audit_status,  audit_reject,  audit_time,  filing_code,  create_by,  create_time,  update_by,  update_time,  remarks,  del_flag,businessDataId, unit_area_code,old_number_flag,business_license_number, old_number,isFilingCancel) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.userId},  #{item.propertyUnit}, #{item.propertyUnitCode}, #{item.deviceName},  #{item.specificationModel},  #{item.manufacturer},  #{item.factoryTime},  #{item.manufacturingLicenseNumber},  #{item.applicationDate},  #{item.factoryLicenseNumber},  #{item.purchaseTime},  #{item.unitAddress},  #{item.legalRepresentative},  #{item.legalRepresentativeContactNumber},  #{item.technicalDirector},  #{item.technicalDirectorContactNumber},  #{item.equipmentManager},  #{item.equipmentManagerContactNumber},  #{item.liftingWeight},  #{item.productCertificationPath},  #{item.equipmentManufacturingLicensePath},  #{item.filingApplicationFormPath},  #{item.purchaseSaleCertificatePath},  #{item.businessLicensePath},  #{item.auditStatus},  #{item.auditReject},  #{item.auditTime},  #{item.filingCode},  #{item.createBy},  #{item.createTime},  #{item.updateBy},  #{item.updateTime},  #{item.remarks},  #{item.delFlag},#{item.unitAreaCode},#{item.oldNumberFlag},#{item.businessLicenseNumber},#{oldNumber},#{isFilingCancel})
        </foreach>
    </insert>
    <update id="addCode" parameterType="java.lang.Integer">
        update code set `value` = #{code}  where type = 'device'
    </update>
    <select id="getCode" resultType="java.lang.Integer">
        select `value` from code where type = 'device' and del_flag = 0
    </select>

    <select id="selectListByAreaCode" resultMap="BaseResultMap">
        select  * from filing_equipment_filing_application
        where unit_area_code
        <foreach close=")" collection="areaList" item="areaCode" open="in (" separator=",">
            #{areaCode}
        </foreach>
        and audit_status = #{auditStatus}
        and del_flag= 0
    </select>

    <select id="selectListByCondition" resultMap="BaseResultMap">
        select  * from filing_equipment_filing_application
        <where>
            del_flag= 0 and audit_status = '1'
            and unit_area_code
            <foreach close=")" collection="areaList" item="areaCode" open="in (" separator=",">
                #{areaCode}
            </foreach>
            <if test="year != null and year != ''"> and date_format(audit_time, '%Y')=#{year}</if>
        </where>
    </select>
    <select id="getByFilingCode" resultMap="BaseResultMap" parameterType="string">
        select  * from filing_equipment_filing_application where filing_code=#{filingCode}
    </select>
    <select id="getOneByFactoryNum" resultMap="BaseResultMap">
        select  * from filing_equipment_filing_application
        where
            del_flag= 0
          and manufacturer=#{manufacturer}
          and factory_license_number=#{factoryLicenseNumber}
    </select>
    <select id="selectByFilingCode" resultMap="BaseResultMap">
        select  * from filing_equipment_filing_application
        where
            del_flag= 0
          and filing_code = #{filingCode}
    </select>
    <select id="selectListGroupByFilingCode" resultMap="BaseResultMap">
        select  a.* from filing_equipment_filing_application a
            left join filing_equipment_filing_cancellation c on a.filing_code=c.filing_code
        <where>
            a.del_flag= 0 and a.audit_status = '1' and c.audit_status not in ('1')
            and a.unit_area_code
            <foreach close=")" collection="areaList" item="areaCode" open="in (" separator=",">
                #{areaCode}
            </foreach>
            <if test="year != null and year != ''"> and date_format(a.audit_time, '%Y')=#{year}</if>
        </where>
        group by a.filing_code
    </select>
</mapper>
