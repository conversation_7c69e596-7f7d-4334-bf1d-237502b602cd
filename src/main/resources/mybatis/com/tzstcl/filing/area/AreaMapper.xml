<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzstcl.filing.area.mapper.AreaMapper">

    <resultMap type="com.tzstcl.filing.area.model.Area" id="AreaResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="abbr"    column="abbr"    />
        <result property="sortBy"    column="sortBy"    />
        <result property="level"    column="level"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="parentId"    column="parentId"    />
        <result property="munCounty"    column="munCounty"    />
        <result property="areaDict"    column="area_dict"    />
    </resultMap>

    <sql id="selectAreaVo">
        select id, name, abbr, sortBy, level, longitude, latitude, parentId,munCounty,area_dict from area
    </sql>

    <select id="selectAreaList" parameterType="com.tzstcl.filing.area.model.Area" resultMap="AreaResult">
        <include refid="selectAreaVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat(#{name}, '%')</if>
            <if test="abbr != null  and abbr != ''"> and abbr = #{abbr}</if>
            <if test="sortBy != null "> and sortBy = #{sortBy}</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="longitude != null  and longitude != ''"> and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''"> and latitude = #{latitude}</if>
            <if test="parentId != null "> and parentId = #{parentId}</if>
            <if test="areaDict != null "> and area_dict = #{areaDict}</if>
        </where>
    </select>
    <select id="selectAreaIdsList" parameterType="com.tzstcl.filing.area.model.Area" resultType="java.lang.Long">
        select id from area
        <where>
            <if test="name != null  and name != ''"> and name like concat(#{name}, '%')</if>
            <if test="abbr != null  and abbr != ''"> and abbr = #{abbr}</if>
            <if test="sortBy != null "> and sortBy = #{sortBy}</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="longitude != null  and longitude != ''"> and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''"> and latitude = #{latitude}</if>
            <if test="parentId != null "> and parentId = #{parentId}</if>
            <if test="areaDict != null "> and area_dict = #{areaDict}</if>
        </where>
    </select>
    <select id="selectAreaChildren" parameterType="Long" resultMap="AreaResult">
        select id, name, level, parentId,munCounty from area where parentId = #{parentId}
    </select>

    <select id="selectAreaListByPId" parameterType="com.tzstcl.filing.area.model.Area" resultType="java.lang.String">
        select id from area
        <where>
            <if test="parentId != null "> and parentId = #{parentId}</if>
        </where>
    </select>

    <select id="selectAreaById" parameterType="Long" resultMap="AreaResult">
        <include refid="selectAreaVo"/>
        where id = #{id}
    </select>


    <select id="selectCityName" resultType="java.lang.String">
        select name
        from area
        where id = #{citynum}
    </select>
    <select id="selectAllProcince" resultType="com.tzstcl.filing.area.model.Area">
        SELECT id,name FROM area  where name like concat('%','省')
    </select>

    <insert id="insertArea" parameterType="com.tzstcl.filing.area.model.Area">
        insert into area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="abbr != null and abbr != ''">abbr,</if>
            <if test="sortBy != null">sortBy,</if>
            <if test="level != null">level,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="parentId != null">parentId,</if>
            <if test="areaDict != null">area_dict,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="abbr != null and abbr != ''">#{abbr},</if>
            <if test="sortBy != null">#{sortBy},</if>
            <if test="level != null">#{level},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="areaDict != null">#{areaDict},</if>
         </trim>
    </insert>

    <update id="updateArea" parameterType="com.tzstcl.filing.area.model.Area">
        update area
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="abbr != null and abbr != ''">abbr = #{abbr},</if>
            <if test="sortBy != null">sortBy = #{sortBy},</if>
            <if test="level != null">level = #{level},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="parentId != null">parentId = #{parentId},</if>
            <if test="areaDict != null">area_dict = #{areaDict},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAreaById" parameterType="Long">
        delete from area where id = #{id}
    </delete>

    <delete id="deleteAreaByIds" parameterType="String">
        delete from area where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getAllAreaCodes" parameterType="String" resultType="String">
        SELECT id FROM area WHERE parentId=#{areaCode}
        <if test="level != null and level != '' and level = 1">
            UNION (
            SELECT a2.id FROM area AS a1
            INNER JOIN
            area AS a2
            WHERE a1.id = a2.parentId AND a1.parentId=#{areaCode})
        </if>

    </select>

    <select id="selectAreaListByProvince" resultType="com.tzstcl.filing.area.model.Area">
        SELECT
            id,name
        FROM
            area
        WHERE
            parentId IN ( SELECT id FROM area WHERE parentId = '410000' )
          AND `level` = '3' UNION
        SELECT
            id,name
        FROM
            area
        WHERE
            parentId = '410000'
    </select>
    <select id="selectAreaByprovince" resultType="com.tzstcl.filing.area.model.Area">
        <include refid="selectAreaVo"/>
        <where>
            `level`='1'
            <if test="id != null  and id != ''"> and id like concat(#{id}, '%')</if>
        </where>
    </select>
    <select id="selectHeNan" resultType="com.tzstcl.filing.area.model.Area">
       <include refid="selectAreaVo"/>
        <where>
            level='2'
                and parentId='410000'
        </where>
    </select>
    <select id="getAreaNameByCode" resultType="java.lang.String">
        SELECT NAME
        FROM
            area
        WHERE
            id = #{areaCode}
    </select>

</mapper>
