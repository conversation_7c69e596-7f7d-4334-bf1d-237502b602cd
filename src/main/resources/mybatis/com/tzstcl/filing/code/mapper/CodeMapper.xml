<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.code.mapper.CodeMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.code.model.Code" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="device_type" property="deviceType" jdbcType="VARCHAR" />
        <result column="value" property="value" jdbcType="INTEGER" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
        <result column="project_area_code" property="projectAreaCode" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        `type`,
        device_type,
        `value`,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag,
        project_area_code
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from code
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.code.model.Code">
        select
        <include refid="Base_Column_List" />
        from code
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.code.model.Code">
        select
        <include refid="Base_Column_List" />
        from code
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="type != null ">
                and `type`=#{type}
            </if>
            <if test="deviceType != null ">
                and device_type=#{deviceType}
            </if>
            <if test="value != null ">
                and `value`=#{value}
            </if>
            <if test="projectAreaCode != null ">
                and `project_area_code`=#{projectAreaCode}
            </if>
         </where>
    </select>
    <select id="getCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from code where del_flag = '0' and `type` = #{codeType} and device_type = #{deviceType}
    </select>

    <select id="getCode2" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from code where del_flag = '0' and `type` = #{codeType} and device_type = #{deviceType} and project_area_code = #{projectAreaCode}
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.code.model.Code">
        insert into code
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="type != null">
                    `type`,
                </if>
                <if test="deviceType != null">
                    device_type,
                </if>
                <if test="value != null">
                    `value`,
                </if>
                <if test="createBy != null">
                    create_by,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="updateBy != null">
                    update_by,
                </if>
                <if test="updateTime != null">
                    update_time,
                </if>
                <if test="remarks != null">
                    remarks,
                </if>
                <if test="delFlag != null">
                    del_flag,
                </if>
            <if test="projectAreaCode != null">
                project_area_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="type != null ">
                #{type},
            </if>
            <if test="deviceType != null ">
                #{deviceType},
            </if>
            <if test="value != null ">
                #{value},
            </if>
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if>
            <if test="projectAreaCode != null ">
                #{projectAreaCode},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.code.model.Code">
        update code
        <set>
            <if test="type != null">
                `type`=#{type},
            </if>
            <if test="deviceType != null">
                device_type=#{deviceType},
            </if>
            <if test="value != null">
                `value`=#{value},
            </if>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
            <if test="projectAreaCode != null">
                project_area_code=#{projectAreaCode},
            </if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update code set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update code set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into code(id,  `type`,  device_type,  `value`,  create_by,  create_time,  update_by,  update_time,  remarks,  del_flag, project_area_code) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.type},  #{item.deviceType},  #{item.value},  #{item.createBy},  #{item.createTime},  #{item.updateBy},  #{item.updateTime},  #{item.remarks},  #{item.delFlag},  #{item.projectAreaCode})
        </foreach>
    </insert>

</mapper>
