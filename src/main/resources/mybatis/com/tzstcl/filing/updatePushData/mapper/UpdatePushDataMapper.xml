<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.updatePushData.mapper.UpdatePushDataMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.updatePushData.model.UpdatePushData" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="business_data_id" property="businessDataId" jdbcType="VARCHAR" />
        <result column="cert_id" property="certId" jdbcType="VARCHAR" />
        <result column="area_code" property="areaCode" jdbcType="VARCHAR" />
        <result column="cert_num" property="certNum" jdbcType="VARCHAR" />
        <result column="device_category_code" property="deviceCategoryCode" jdbcType="VARCHAR" />
        <result column="device_model" property="deviceModel" jdbcType="VARCHAR" />
        <result column="factory_num" property="factoryNum" jdbcType="VARCHAR" />
        <result column="manufacture_corp_code" property="manufactureCorpCode" jdbcType="VARCHAR" />
        <result column="cert_status" property="certStatus" jdbcType="VARCHAR" />
        <result column="cert_status_description" property="certStatusDescription" jdbcType="VARCHAR" />
        <result column="operate_type" property="operateType" jdbcType="VARCHAR" />
        <result column="creat_date" property="creatDate" jdbcType="TIMESTAMP" />
        <result column="creat_at" property="creatAt" jdbcType="VARCHAR" />
        <result column="push_flag" property="pushFlag" jdbcType="VARCHAR" />
        <result column="push_date" property="pushDate" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        business_data_id,
        cert_id,
        area_code,
        cert_num,
        device_category_code,
        device_model,
        factory_num,
        manufacture_corp_code,
        cert_status,
        cert_status_description,
        operate_type,
        creat_date,
        creat_at,
        push_flag,
        push_date
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from update_push_data
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.updatePushData.model.UpdatePushData">
        select
        <include refid="Base_Column_List" />
        from update_push_data
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.updatePushData.model.UpdatePushData">
        select
        <include refid="Base_Column_List" />
        from update_push_data
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="businessDataId != null ">
                and business_data_id=#{businessDataId}
            </if>
            <if test="certId != null ">
                and cert_id=#{certId}
            </if>
            <if test="areaCode != null ">
                and area_code=#{areaCode}
            </if>
            <if test="certNum != null ">
                and cert_num=#{certNum}
            </if>
            <if test="deviceCategoryCode != null ">
                and device_category_code=#{deviceCategoryCode}
            </if>
            <if test="deviceModel != null ">
                and device_model=#{deviceModel}
            </if>
            <if test="factoryNum != null ">
                and factory_num=#{factoryNum}
            </if>
            <if test="manufactureCorpCode != null ">
                and manufacture_corp_code=#{manufactureCorpCode}
            </if>
            <if test="certStatus != null ">
                and cert_status=#{certStatus}
            </if>
            <if test="certStatusDescription != null ">
                and cert_status_description=#{certStatusDescription}
            </if>
            <if test="operateType != null ">
                and operate_type=#{operateType}
            </if>
            <if test="creatDate != null ">
                and creat_date=#{creatDate}
            </if>
            <if test="creatAt != null ">
                and creat_at=#{creatAt}
            </if>
            <if test="pushFlag != null ">
                and push_flag=#{pushFlag}
            </if>
            <if test="pushDate != null ">
                and push_date=#{pushDate}
            </if>
         </where>
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.updatePushData.model.UpdatePushData">
        insert into update_push_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="businessDataId != null">
                    business_data_id,
                </if>
                <if test="certId != null">
                    cert_id,
                </if>
                <if test="areaCode != null">
                    area_code,
                </if>
                <if test="certNum != null">
                    cert_num,
                </if>
                <if test="deviceCategoryCode != null">
                    device_category_code,
                </if>
                <if test="deviceModel != null">
                    device_model,
                </if>
                <if test="factoryNum != null">
                    factory_num,
                </if>
                <if test="manufactureCorpCode != null">
                    manufacture_corp_code,
                </if>
                <if test="certStatus != null">
                    cert_status,
                </if>
                <if test="certStatusDescription != null">
                    cert_status_description,
                </if>
                <if test="operateType != null">
                    operate_type,
                </if>
                <if test="creatDate != null">
                    creat_date,
                </if>
                <if test="creatAt != null">
                    creat_at,
                </if>
                <if test="pushFlag != null">
                    push_flag,
                </if>
                <if test="pushDate != null">
                    push_date,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="businessDataId != null ">
                #{businessDataId},
            </if>
            <if test="certId != null ">
                #{certId},
            </if>
            <if test="areaCode != null ">
                #{areaCode},
            </if>
            <if test="certNum != null ">
                #{certNum},
            </if>
            <if test="deviceCategoryCode != null ">
                #{deviceCategoryCode},
            </if>
            <if test="deviceModel != null ">
                #{deviceModel},
            </if>
            <if test="factoryNum != null ">
                #{factoryNum},
            </if>
            <if test="manufactureCorpCode != null ">
                #{manufactureCorpCode},
            </if>
            <if test="certStatus != null ">
                #{certStatus},
            </if>
            <if test="certStatusDescription != null ">
                #{certStatusDescription},
            </if>
            <if test="operateType != null ">
                #{operateType},
            </if>
            <if test="creatDate != null ">
                #{creatDate},
            </if>
            <if test="creatAt != null ">
                #{creatAt},
            </if>
            <if test="pushFlag != null ">
                #{pushFlag},
            </if>
            <if test="pushDate != null ">
                #{pushDate},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.updatePushData.model.UpdatePushData">
        update update_push_data
        <set>
            <if test="businessDataId != null">
                business_data_id=#{businessDataId},
            </if>
            <if test="certId != null">
                cert_id=#{certId},
            </if>
            <if test="areaCode != null">
                area_code=#{areaCode},
            </if>
            <if test="certNum != null">
                cert_num=#{certNum},
            </if>
            <if test="deviceCategoryCode != null">
                device_category_code=#{deviceCategoryCode},
            </if>
            <if test="deviceModel != null">
                device_model=#{deviceModel},
            </if>
            <if test="factoryNum != null">
                factory_num=#{factoryNum},
            </if>
            <if test="manufactureCorpCode != null">
                manufacture_corp_code=#{manufactureCorpCode},
            </if>
            <if test="certStatus != null">
                cert_status=#{certStatus},
            </if>
            <if test="certStatusDescription != null">
                cert_status_description=#{certStatusDescription},
            </if>
            <if test="operateType != null">
                operate_type=#{operateType},
            </if>
            <if test="creatDate != null">
                creat_date=#{creatDate},
            </if>
            <if test="creatAt != null">
                creat_at=#{creatAt},
            </if>
            <if test="pushFlag != null">
                push_flag=#{pushFlag},
            </if>
            <if test="pushDate != null">
                push_date=#{pushDate},
            </if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update update_push_data set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update update_push_data set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into update_push_data(id,  business_data_id,  cert_id,  area_code,  cert_num,  device_category_code,  device_model,  factory_num,  manufacture_corp_code,  cert_status,  cert_status_description,  operate_type,  creat_date,  creat_at,  push_flag,  push_date) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.businessDataId},  #{item.certId},  #{item.areaCode},  #{item.certNum},  #{item.deviceCategoryCode},  #{item.deviceModel},  #{item.factoryNum},  #{item.manufactureCorpCode},  #{item.certStatus},  #{item.certStatusDescription},  #{item.operateType},  #{item.creatDate},  #{item.creatAt},  #{item.pushFlag},  #{item.pushDate})
        </foreach>
    </insert>

</mapper>