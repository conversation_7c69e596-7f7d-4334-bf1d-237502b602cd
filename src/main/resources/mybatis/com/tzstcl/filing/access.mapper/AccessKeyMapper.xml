<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.access.mapper.AccessKeyMapper">

    <resultMap type="com.tzstcl.filing.access.model.AccessKey" id="AccessKeyResult">
        <result property="accessId"    column="access_id"    />
        <result property="appId"    column="app_id"    />
        <result property="appKey"    column="app_key"    />
        <result property="personName"    column="person_name"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="area"    column="area"    />
        <result property="areaCode"    column="area_code"    />
        <result property="pushTime"    column="push_time"    />
        <result property="provinceCode"    column="province_code"    />
        <result property="cityCode"    column="city_code"    />
        <result property="systemName"    column="system_name"    />
    </resultMap>

    <sql id="selectAccessKeyVo">
        select access_id, app_id, app_key, person_name, province, city, area, area_code, push_time,province_code, city_code,system_name from access_key
    </sql>

    <select id="selectAccessKeyList" parameterType="com.tzstcl.filing.access.model.AccessKey" resultMap="AccessKeyResult">
        <include refid="selectAccessKeyVo"/>
        <where>
            <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
            <if test="appKey != null  and appKey != ''"> and app_key = #{appKey}</if>
            <if test="personName != null  and personName != ''"> and person_name like concat('%', #{personName}, '%')</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="area != null  and area != ''"> and area = #{area}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="pushTime != null "> and push_time = #{pushTime}</if>
            <if test="provinceCode != null "> and province_code = #{provinceCode}</if>
            <if test="cityCode != null "> and city_code = #{cityCode}</if>
            <if test="systemName != null "> and system_name = #{systemName}</if>
        </where>
    </select>

    <select id="select" parameterType="com.tzstcl.filing.access.model.AccessKey" resultMap="AccessKeyResult">
        <include refid="selectAccessKeyVo"/>
        <where>
            <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
            <if test="appKey != null  and appKey != ''"> and app_key = #{appKey}</if>
            <if test="personName != null  and personName != ''"> and person_name like concat('%', #{personName}, '%')</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="area != null  and area != ''"> and area = #{area}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="pushTime != null "> and push_time = #{pushTime}</if>
            <if test="provinceCode != null "> and province_code = #{provinceCode}</if>
            <if test="cityCode != null "> and city_code = #{cityCode}</if>
            <if test="systemName != null "> and system_name = #{systemName}</if>
        </where>
    </select>

    <select id="selectAccessKeyById" parameterType="Long" resultMap="AccessKeyResult">
        <include refid="selectAccessKeyVo"/>
        where access_id = #{accessId}
    </select>

    <select id="getOne" parameterType="Long" resultMap="AccessKeyResult">
        <include refid="selectAccessKeyVo"/>
        where access_id = #{accessId}
    </select>

    <select id="selectAccessKeyByAppId" resultMap="AccessKeyResult" parameterType="java.lang.String">
        <include refid="selectAccessKeyVo"/> where app_id like #{appId} limit 1
    </select>

    <insert id="insertAccessKey" parameterType="com.tzstcl.filing.access.model.AccessKey" useGeneratedKeys="true" keyProperty="accessId">
        insert into access_key
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">app_id,</if>
            <if test="appKey != null">app_key,</if>
            <if test="personName != null">person_name,</if>
            <if test="province != null">province,</if>
            <if test="provinceCode != null">province_code,</if>
            <if test="city != null">city,</if>
            <if test="cityCode != null">city_code,</if>
            <if test="area != null">area,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="pushTime != null">push_time,</if>
            <if test="systemName != null">system_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="appKey != null">#{appKey},</if>
            <if test="personName != null">#{personName},</if>
            <if test="province != null">#{province},</if>
            <if test="provinceCode != null">#{provinceCode},</if>
            <if test="city != null">#{city},</if>
            <if test="cityCode != null">#{cityCode},</if>
            <if test="area != null">#{area},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="pushTime != null">#{pushTime},</if>
            <if test="systemName != null">#{systemName},</if>
        </trim>
    </insert>

    <insert id="insert" parameterType="com.tzstcl.filing.access.model.AccessKey" useGeneratedKeys="true" keyProperty="accessId">
        insert into access_key
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">app_id,</if>
            <if test="appKey != null">app_key,</if>
            <if test="personName != null">person_name,</if>
            <if test="province != null">province,</if>
            <if test="provinceCode != null">province_code,</if>
            <if test="city != null">city,</if>
            <if test="cityCode != null">city_code,</if>
            <if test="area != null">area,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="pushTime != null">push_time,</if>
            <if test="systemName != null">system_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="appKey != null">#{appKey},</if>
            <if test="personName != null">#{personName},</if>
            <if test="province != null">#{province},</if>
            <if test="provinceCode != null">#{provinceCode},</if>
            <if test="city != null">#{city},</if>
            <if test="cityCode != null">#{cityCode},</if>
            <if test="area != null">#{area},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="pushTime != null">#{pushTime},</if>
            <if test="systemName != null">#{systemName},</if>
        </trim>
    </insert>

    <update id="updateAccessKey" parameterType="com.tzstcl.filing.access.model.AccessKey">
        update access_key
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null">app_id = #{appId},</if>
            <if test="appKey != null">app_key = #{appKey},</if>
            <if test="personName != null">person_name = #{personName},</if>
            <if test="province != null">province = #{province},</if>
            <if test="provinceCode != null">province_code = #{provinceCode},</if>
            <if test="city != null">city = #{city},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="area != null">area = #{area},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="pushTime != null">push_time = #{pushTime},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
        </trim>
        where access_id = #{accessId}
    </update>

    <update id="update" parameterType="com.tzstcl.filing.access.model.AccessKey">
        update access_key
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null">app_id = #{appId},</if>
            <if test="appKey != null">app_key = #{appKey},</if>
            <if test="personName != null">person_name = #{personName},</if>
            <if test="province != null">province = #{province},</if>
            <if test="provinceCode != null">province_code = #{provinceCode},</if>
            <if test="city != null">city = #{city},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="area != null">area = #{area},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="pushTime != null">push_time = #{pushTime},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
        </trim>
        where access_id = #{accessId}
    </update>

    <delete id="deleteAccessKeyById" parameterType="Long">
        delete from access_key where access_id = #{accessId}
    </delete>

    <delete id="delete" parameterType="Long">
        delete from access_key where access_id = #{accessId}
    </delete>

    <delete id="deleteAccessKeyByIds" parameterType="String">
        delete from access_key where access_id in
        <foreach item="accessId" collection="array" open="(" separator="," close=")">
            #{accessId}
        </foreach>
    </delete>

</mapper>
