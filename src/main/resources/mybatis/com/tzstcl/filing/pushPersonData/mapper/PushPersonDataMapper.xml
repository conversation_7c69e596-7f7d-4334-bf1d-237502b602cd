<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.pushPersonData.mapper.PushPersonDataMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.pushPersonData.model.PushPersonData" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="business_data_id" property="businessDataId" jdbcType="VARCHAR" />

        <result column="cert_num" property="certNum" jdbcType="VARCHAR" />
        <result column="cert_id" property="certId" jdbcType="VARCHAR" />
        <result column="corp_code" property="corpCode" jdbcType="VARCHAR" />
        <result column="corp_name" property="corpName" jdbcType="VARCHAR" />
        <result column="person_type" property="personType" jdbcType="VARCHAR" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="identity_card" property="identityCard" jdbcType="VARCHAR" />
        <result column="identity_card_type" property="identityCardType" jdbcType="VARCHAR" />
        <result column="data_flag" property="dataFlag" jdbcType="VARCHAR" />
        <result column="data_remark" property="dataRemark" jdbcType="VARCHAR" />
        <result column="push_flag" property="pushFlag" jdbcType="VARCHAR" />
        <result column="push_time" property="pushTime" jdbcType="VARCHAR" />
        <result column="creat_time" property="creatTime" jdbcType="TIMESTAMP" />
        <result column="creat_at" property="creatAt" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        business_data_id,
        cert_num,
        cert_id,
        id,
        corp_code,
        corp_name,
        person_type,
        name,
        identity_card,
        identity_card_type,
        data_flag,
        data_remark,
        push_flag,
        push_time,
        creat_time,
        creat_at
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from push_person_data
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.pushPersonData.model.PushPersonData">
        select
        <include refid="Base_Column_List" />
        from push_person_data
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.pushPersonData.model.PushPersonData">
        select
        <include refid="Base_Column_List" />
        from push_person_data
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="businessDataId != null ">
                and business_data_id=#{businessDataId}
            </if>
            <if test="certNum != null ">
                and cert_num=#{certNum}
            </if>
            <if test="certId != null ">
                and cert_id=#{certId}
            </if>
            <if test="corpCode != null ">
                and corp_code=#{corpCode}
            </if>
            <if test="corpName != null ">
                and corp_name=#{corpName}
            </if>
            <if test="personType != null ">
                and person_type=#{personType}
            </if>
            <if test="name != null ">
                and name=#{name}
            </if>
            <if test="identityCard != null ">
                and identity_card=#{identityCard}
            </if>
            <if test="identityCardType != null ">
                and identity_card_type=#{identityCardType}
            </if>
            <if test="dataFlag != null ">
                and data_flag=#{dataFlag}
            </if>
            <if test="dataRemark != null ">
                and data_remark=#{dataRemark}
            </if>
            <if test="pushFlag != null ">
                and push_flag=#{pushFlag}
            </if>
            <if test="pushTime != null ">
                and push_time=#{pushTime}
            </if>
            <if test="creatTime != null ">
                and creat_time=#{creatTime}
            </if>
            <if test="creatAt != null ">
                and creat_at=#{creatAt}
            </if>
         </where>
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.pushPersonData.model.PushPersonData">
        insert into push_person_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="businessDataId != null">
                    business_data_id,
                </if>
                <if test="certNum != null">
                    cert_num,
                </if>
                <if test="certId != null">
                    cert_id,
                </if>
                <if test="id != null">
                    id,
                </if>
                <if test="corpCode != null">
                    corp_code,
                </if>
                <if test="corpName != null">
                    corp_name,
                </if>
                <if test="personType != null">
                    person_type,
                </if>
                <if test="name != null">
                    name,
                </if>
                <if test="identityCard != null">
                    identity_card,
                </if>
                <if test="identityCardType != null">
                    identity_card_type,
                </if>
                <if test="dataFlag != null">
                    data_flag,
                </if>
                <if test="dataRemark != null">
                    data_remark,
                </if>
                <if test="pushFlag != null">
                    push_flag,
                </if>
                <if test="pushTime != null">
                    push_time,
                </if>
                <if test="creatTime != null">
                    creat_time,
                </if>
                <if test="creatAt != null">
                    creat_at,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessDataId != null ">
                #{businessDataId},
            </if>
            <if test="certNum != null ">
                #{certNum},
            </if>
            <if test="certId != null ">
                #{certId},
            </if>
            <if test="id != null ">
                #{id},
            </if>
            <if test="corpCode != null ">
                #{corpCode},
            </if>
            <if test="corpName != null ">
                #{corpName},
            </if>
            <if test="personType != null ">
                #{personType},
            </if>
            <if test="name != null ">
                #{name},
            </if>
            <if test="identityCard != null ">
                #{identityCard},
            </if>
            <if test="identityCardType != null ">
                #{identityCardType},
            </if>
            <if test="dataFlag != null ">
                #{dataFlag},
            </if>
            <if test="dataRemark != null ">
                #{dataRemark},
            </if>
            <if test="pushFlag != null ">
                #{pushFlag},
            </if>
            <if test="pushTime != null ">
                #{pushTime},
            </if>
            <if test="creatTime != null ">
                #{creatTime},
            </if>
            <if test="creatAt != null ">
                #{creatAt},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.pushPersonData.model.PushPersonData">
        update push_person_data
        <set>
            <if test="businessDataId != null">
                business_data_id=#{businessDataId},
            </if>
            <if test="certNum != null">
                cert_num=#{certNum},
            </if>
            <if test="certId != null">
                cert_id=#{certId},
            </if>
            <if test="corpCode != null">
                corp_code=#{corpCode},
            </if>
            <if test="corpName != null">
                corp_name=#{corpName},
            </if>
            <if test="personType != null">
                person_type=#{personType},
            </if>
            <if test="name != null">
                name=#{name},
            </if>
            <if test="identityCard != null">
                identity_card=#{identityCard},
            </if>
            <if test="identityCardType != null">
                identity_card_type=#{identityCardType},
            </if>
            <if test="dataFlag != null">
                data_flag=#{dataFlag},
            </if>
            <if test="dataRemark != null">
                data_remark=#{dataRemark},
            </if>
            <if test="pushFlag != null">
                push_flag=#{pushFlag},
            </if>
            <if test="pushTime != null">
                push_time=#{pushTime},
            </if>
            <if test="creatTime != null">
                creat_time=#{creatTime},
            </if>
            <if test="creatAt != null">
                creat_at=#{creatAt},
            </if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update push_person_data set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update push_person_data set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into push_person_data(business_data_id,  cert_num,  cert_id,  id,  corp_code,  corp_name,  person_type,  name,  identity_card,  identity_card_type,  data_flag,  data_remark,  push_flag,  push_time,  creat_time,  creat_at) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.businessDataId},  #{item.certNum},  #{item.certId},  #{item.id},  #{item.corpCode},  #{item.corpName},  #{item.personType},  #{item.name},  #{item.identityCard},  #{item.identityCardType},  #{item.dataFlag},  #{item.dataRemark},  #{item.pushFlag},  #{item.pushTime},  #{item.creatTime},  #{item.creatAt})
        </foreach>
    </insert>

</mapper>
