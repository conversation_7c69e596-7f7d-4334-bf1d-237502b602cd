<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.installation.mapper.FilingEquipmentInstallationFormMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.installation.model.FilingEquipmentInstallationForm" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="application_date" property="applicationDate" jdbcType="DATE" />
        <result column="equipment_filing_code" property="equipmentFilingCode" jdbcType="VARCHAR" />
        <result column="device_name" property="deviceName" jdbcType="VARCHAR" />
        <result column="specification_model" property="specificationModel" jdbcType="VARCHAR" />
        <result column="engineering_name" property="engineeringName" jdbcType="VARCHAR" />
        <result column="installation_unit" property="installationUnit" jdbcType="VARCHAR" />
        <result column="site_leader" property="siteLeader" jdbcType="VARCHAR" />
        <result column="installation_time" property="installationTime" jdbcType="DATE" />
        <result column="installation_height" property="installationHeight" jdbcType="VARCHAR" />
        <result column="construction_site" property="constructionSite" jdbcType="VARCHAR" />
        <result column="job_content" property="jobContent" jdbcType="VARCHAR" />
        <result column="installation_leader" property="installationLeader" jdbcType="VARCHAR" />
        <result column="tech_leader" property="techLeader" jdbcType="VARCHAR" />
        <result column="notification_book_file_path" property="notificationBookFilePath" jdbcType="VARCHAR" />
        <result column="equipment_filing_license_file_path" property="equipmentFilingLicenseFilePath" jdbcType="VARCHAR" />
        <result column="audit_form_file_path" property="auditFormFilePath" jdbcType="VARCHAR" />
        <result column="qualifi_license_file_path" property="qualifiLicenseFilePath" jdbcType="VARCHAR" />
        <result column="license_safety_permit_file_path" property="licenseSafetyPermitFilePath" jdbcType="VARCHAR" />
        <result column="operators_list_file_path" property="operatorsListFilePath" jdbcType="VARCHAR" />
        <result column="operators_license_file_path" property="operatorsLicenseFilePath" jdbcType="VARCHAR" />
        <result column="special_construction_scheme_file_path" property="specialConstructionSchemeFilePath" jdbcType="VARCHAR" />
        <result column="installation_contract_file_path" property="installationContractFilePath" jdbcType="VARCHAR" />
        <result column="security_protocol_file_path" property="securityProtocolFilePath" jdbcType="VARCHAR" />
        <result column="emergency_rescue_plan_file_path" property="emergencyRescuePlanFilePath" jdbcType="VARCHAR" />
        <result column="equipment_qualification_certificate_file_path" property="equipmentQualificationCertificateFilePath" jdbcType="VARCHAR" />
        <result column="operator_qualification_certificate_file_path" property="operatorQualificationCertificateFilePath" jdbcType="VARCHAR" />
        <result column="audit_status" property="auditStatus" jdbcType="INTEGER" />
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
        <result column="audit_reject_reason" property="auditRejectReason" jdbcType="VARCHAR" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
        <result column="work_type" property="workType" jdbcType="CHAR" />
        <result column="site_leader_contact_number" property="siteLeaderContactNumber"/>
        <result column="installation_leader_contact_number" property="installationLeaderContactNumber"/>
        <result column="tech_leader_contact_number" property="techLeaderContactNumber" />
        <result column="business_id" property="businessId" />
        <result column="engineering_address_detail" property="engineeringAddressDetail" />
        <result column="engineering_area_code" property="engineeringAreaCode" />
        <result column="equipment_type" property="equipmentType" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        user_id,
        application_date,
        equipment_filing_code,
        device_name,
        specification_model,
        engineering_name,
        installation_unit,
        site_leader,
        installation_time,
        installation_height,
        construction_site,
        job_content,
        installation_leader,
        tech_leader,
        notification_book_file_path,
        equipment_filing_license_file_path,
        audit_form_file_path,
        qualifi_license_file_path,
        license_safety_permit_file_path,
        operators_list_file_path,
        operators_license_file_path,
        special_construction_scheme_file_path,
        installation_contract_file_path,
        security_protocol_file_path,
        emergency_rescue_plan_file_path,
        equipment_qualification_certificate_file_path,
        operator_qualification_certificate_file_path,
        audit_status,
        audit_time,
        audit_reject_reason,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag,
        work_type,
        site_leader_contact_number,
        installation_leader_contact_number,
        tech_leader_contact_number,
        business_id,
        engineering_address_detail,
        engineering_area_code,
        equipment_type
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_installation_form
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.installation.model.FilingEquipmentInstallationForm">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_installation_form
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.installation.model.FilingEquipmentInstallationForm">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_installation_form
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="userId != null ">
                and user_id=#{userId}
            </if>
            <if test="applicationDate != null ">
                and application_date=#{applicationDate}
            </if>
            <if test="equipmentFilingCode != null ">
                and equipment_filing_code=#{equipmentFilingCode}
            </if>
            <if test="deviceName != null ">
                and device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="specificationModel != null ">
                and specification_model=#{specificationModel}
            </if>
            <if test="engineeringName != null ">
                and engineering_name like concat('%', #{engineeringName}, '%')
            </if>
            <if test="installationUnit != null ">
                and installation_unit like concat('%', #{installationUnit}, '%')
            </if>
            <if test="siteLeader != null ">
                and site_leader=#{siteLeader}
            </if>
            <if test="installationTime != null ">
                and installation_time=#{installationTime}
            </if>
            <if test="installationHeight != null ">
                and installation_height=#{installationHeight}
            </if>
            <if test="constructionSite != null ">
                and construction_site like concat('%', #{constructionSite}, '%')
            </if>
            <if test="jobContent != null ">
                and job_content like concat('%', #{jobContent}, '%')
            </if>
            <if test="installationLeader != null ">
                and installation_leader=#{installationLeader}
            </if>
            <if test="techLeader != null ">
                and tech_leader=#{techLeader}
            </if>
            <if test="notificationBookFilePath != null ">
                and notification_book_file_path=#{notificationBookFilePath}
            </if>
            <if test="equipmentFilingLicenseFilePath != null ">
                and equipment_filing_license_file_path=#{equipmentFilingLicenseFilePath}
            </if>
            <if test="auditFormFilePath != null ">
                and audit_form_file_path=#{auditFormFilePath}
            </if>
            <if test="qualifiLicenseFilePath != null ">
                and qualifi_license_file_path=#{qualifiLicenseFilePath}
            </if>
            <if test="licenseSafetyPermitFilePath != null ">
                and license_safety_permit_file_path=#{licenseSafetyPermitFilePath}
            </if>
            <if test="operatorsListFilePath != null ">
                and operators_list_file_path=#{operatorsListFilePath}
            </if>
            <if test="operatorsLicenseFilePath != null ">
                and operators_license_file_path=#{operatorsLicenseFilePath}
            </if>
            <if test="specialConstructionSchemeFilePath != null ">
                and special_construction_scheme_file_path=#{specialConstructionSchemeFilePath}
            </if>
            <if test="installationContractFilePath != null ">
                and installation_contract_file_path=#{installationContractFilePath}
            </if>
            <if test="securityProtocolFilePath != null ">
                and security_protocol_file_path=#{securityProtocolFilePath}
            </if>
            <if test="emergencyRescuePlanFilePath != null ">
                and emergency_rescue_plan_file_path=#{emergencyRescuePlanFilePath}
            </if>
            <if test="equipmentQualificationCertificateFilePath != null ">
                and equipment_qualification_certificate_file_path=#{equipmentQualificationCertificateFilePath}
            </if>
            <if test="operatorQualificationCertificateFilePath != null ">
                and operator_qualification_certificate_file_path=#{operatorQualificationCertificateFilePath}
            </if>
            <if test="auditStatus != null ">
                and audit_status=#{auditStatus}
            </if>
            <if test="auditTime != null ">
                and audit_time=#{auditTime}
            </if>
            <if test="auditRejectReason != null ">
                and audit_reject_reason=#{auditRejectReason}
            </if><if test="workType != null ">
            and work_type=#{workType}
        </if>
            <if test="siteLeaderContactNumber != null ">
                and site_leader_contact_number=#{siteLeaderContactNumber}
            </if>
            <if test="techLeaderContactNumber != null ">
                and tech_leader_contact_number=#{techLeaderContactNumber}
            </if>
            <if test="installationLeaderContactNumber != null ">
                and installation_leader_contact_number=#{installationLeaderContactNumber}
            </if>
            <if test="businessId != null "> and business_id=#{businessId}</if>
            <if test="engineeringAddressDetail != null "> and engineering_address_detail=#{engineeringAddressDetail}</if>
            <if test="engineeringAreaCode != null "> and engineering_area_code=#{engineeringAreaCode}</if>
            <if test="equipmentType != null "> and equipment_type=#{equipmentType}</if>
        </where>
        order by create_time desc
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.installation.model.FilingEquipmentInstallationForm">
        insert into filing_equipment_installation_form
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="userId != null">
                    user_id,
                </if>
                <if test="applicationDate != null">
                    application_date,
                </if>
                <if test="equipmentFilingCode != null">
                    equipment_filing_code,
                </if>
                <if test="deviceName != null">
                    device_name,
                </if>
                <if test="specificationModel != null">
                    specification_model,
                </if>
                <if test="engineeringName != null">
                    engineering_name,
                </if>
                <if test="installationUnit != null">
                    installation_unit,
                </if>
                <if test="siteLeader != null">
                    site_leader,
                </if>
                <if test="installationTime != null">
                    installation_time,
                </if>
                <if test="installationHeight != null">
                    installation_height,
                </if>
                <if test="constructionSite != null">
                    construction_site,
                </if>
                <if test="jobContent != null">
                    job_content,
                </if>
                <if test="installationLeader != null">
                    installation_leader,
                </if>
                <if test="techLeader != null">
                    tech_leader,
                </if>
                <if test="notificationBookFilePath != null">
                    notification_book_file_path,
                </if>
                <if test="equipmentFilingLicenseFilePath != null">
                    equipment_filing_license_file_path,
                </if>
                <if test="auditFormFilePath != null">
                    audit_form_file_path,
                </if>
                <if test="qualifiLicenseFilePath != null">
                    qualifi_license_file_path,
                </if>
                <if test="licenseSafetyPermitFilePath != null">
                    license_safety_permit_file_path,
                </if>
                <if test="operatorsListFilePath != null">
                    operators_list_file_path,
                </if>
                <if test="operatorsLicenseFilePath != null">
                    operators_license_file_path,
                </if>
                <if test="specialConstructionSchemeFilePath != null">
                    special_construction_scheme_file_path,
                </if>
                <if test="installationContractFilePath != null">
                    installation_contract_file_path,
                </if>
                <if test="securityProtocolFilePath != null">
                    security_protocol_file_path,
                </if>
                <if test="emergencyRescuePlanFilePath != null">
                    emergency_rescue_plan_file_path,
                </if>
                <if test="equipmentQualificationCertificateFilePath != null">
                    equipment_qualification_certificate_file_path,
                </if>
                <if test="operatorQualificationCertificateFilePath != null">
                    operator_qualification_certificate_file_path,
                </if>
                <if test="auditStatus != null">
                    audit_status,
                </if>
                <if test="auditTime != null">
                    audit_time,
                </if>
                <if test="auditRejectReason != null">
                    audit_reject_reason,
                </if>
                <if test="createBy != null">
                    create_by,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="updateBy != null">
                    update_by,
                </if>
                <if test="updateTime != null">
                    update_time,
                </if>
                <if test="remarks != null">
                    remarks,
                </if>
                <if test="delFlag != null">
                    del_flag,
                </if><if test="workType != null">
            work_type,
                </if>
            <if test="siteLeaderContactNumber != null">
                site_leader_contact_number,
            </if>
            <if test="techLeaderContactNumber != null">
                tech_leader_contact_number,
            </if>
            <if test="installationLeaderContactNumber != null">
                installation_leader_contact_number,
            </if>
            <if test="businessId != null"> business_id,</if>
            <if test="engineeringAddressDetail != null"> engineering_address_detail,</if>
            <if test="engineeringAreaCode != null"> engineering_area_code,</if>
            <if test="equipmentType != null"> equipment_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="userId != null ">
                #{userId},
            </if>
            <if test="applicationDate != null ">
                #{applicationDate},
            </if>
            <if test="equipmentFilingCode != null ">
                #{equipmentFilingCode},
            </if>
            <if test="deviceName != null ">
                #{deviceName},
            </if>
            <if test="specificationModel != null ">
                #{specificationModel},
            </if>
            <if test="engineeringName != null ">
                #{engineeringName},
            </if>
            <if test="installationUnit != null ">
                #{installationUnit},
            </if>
            <if test="siteLeader != null ">
                #{siteLeader},
            </if>
            <if test="installationTime != null ">
                #{installationTime},
            </if>
            <if test="installationHeight != null ">
                #{installationHeight},
            </if>
            <if test="constructionSite != null ">
                #{constructionSite},
            </if>
            <if test="jobContent != null ">
                #{jobContent},
            </if>
            <if test="installationLeader != null ">
                #{installationLeader},
            </if>
            <if test="techLeader != null ">
                #{techLeader},
            </if>
            <if test="notificationBookFilePath != null ">
                #{notificationBookFilePath},
            </if>
            <if test="equipmentFilingLicenseFilePath != null ">
                #{equipmentFilingLicenseFilePath},
            </if>
            <if test="auditFormFilePath != null ">
                #{auditFormFilePath},
            </if>
            <if test="qualifiLicenseFilePath != null ">
                #{qualifiLicenseFilePath},
            </if>
            <if test="licenseSafetyPermitFilePath != null ">
                #{licenseSafetyPermitFilePath},
            </if>
            <if test="operatorsListFilePath != null ">
                #{operatorsListFilePath},
            </if>
            <if test="operatorsLicenseFilePath != null ">
                #{operatorsLicenseFilePath},
            </if>
            <if test="specialConstructionSchemeFilePath != null ">
                #{specialConstructionSchemeFilePath},
            </if>
            <if test="installationContractFilePath != null ">
                #{installationContractFilePath},
            </if>
            <if test="securityProtocolFilePath != null ">
                #{securityProtocolFilePath},
            </if>
            <if test="emergencyRescuePlanFilePath != null ">
                #{emergencyRescuePlanFilePath},
            </if>
            <if test="equipmentQualificationCertificateFilePath != null ">
                #{equipmentQualificationCertificateFilePath},
            </if>
            <if test="operatorQualificationCertificateFilePath != null ">
                #{operatorQualificationCertificateFilePath},
            </if>
            <if test="auditStatus != null ">
                #{auditStatus},
            </if>
            <if test="auditTime != null ">
                #{auditTime},
            </if>
            <if test="auditRejectReason != null ">
                #{auditRejectReason},
            </if>
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if><if test="workType != null ">
                #{workType},
            </if>
            <if test="siteLeaderContactNumber != null ">
                #{siteLeaderContactNumber},
            </if>
            <if test="techLeaderContactNumber != null ">
                #{techLeaderContactNumber},
            </if>
            <if test="installationLeaderContactNumber != null ">
                #{installationLeaderContactNumber},
            </if>
            <if test="businessId != null "> #{businessId},</if>
            <if test="engineeringAddressDetail != null "> #{engineeringAddressDetail},</if>
            <if test="engineeringAreaCode != null "> #{engineeringAreaCode},</if>
            <if test="equipmentType != null "> #{equipmentType},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.installation.model.FilingEquipmentInstallationForm">
        update filing_equipment_installation_form
        <set>
            <if test="userId != null">
                user_id=#{userId},
            </if>
            <if test="applicationDate != null">
                application_date=#{applicationDate},
            </if>
            <if test="equipmentFilingCode != null">
                equipment_filing_code=#{equipmentFilingCode},
            </if>
            <if test="deviceName != null">
                device_name=#{deviceName},
            </if>
            <if test="specificationModel != null">
                specification_model=#{specificationModel},
            </if>
            <if test="engineeringName != null">
                engineering_name=#{engineeringName},
            </if>
            <if test="installationUnit != null">
                installation_unit=#{installationUnit},
            </if>
            <if test="siteLeader != null">
                site_leader=#{siteLeader},
            </if>
            <if test="installationTime != null">
                installation_time=#{installationTime},
            </if>
            <if test="installationHeight != null">
                installation_height=#{installationHeight},
            </if>
            <if test="constructionSite != null">
                construction_site=#{constructionSite},
            </if>
            <if test="jobContent != null">
                job_content=#{jobContent},
            </if>
            <if test="installationLeader != null">
                installation_leader=#{installationLeader},
            </if>
            <if test="techLeader != null">
                tech_leader=#{techLeader},
            </if>
            <if test="notificationBookFilePath != null">
                notification_book_file_path=#{notificationBookFilePath},
            </if>
            <if test="equipmentFilingLicenseFilePath != null">
                equipment_filing_license_file_path=#{equipmentFilingLicenseFilePath},
            </if>
            <if test="auditFormFilePath != null">
                audit_form_file_path=#{auditFormFilePath},
            </if>
            <if test="qualifiLicenseFilePath != null">
                qualifi_license_file_path=#{qualifiLicenseFilePath},
            </if>
            <if test="licenseSafetyPermitFilePath != null">
                license_safety_permit_file_path=#{licenseSafetyPermitFilePath},
            </if>
            <if test="operatorsListFilePath != null">
                operators_list_file_path=#{operatorsListFilePath},
            </if>
            <if test="operatorsLicenseFilePath != null">
                operators_license_file_path=#{operatorsLicenseFilePath},
            </if>
            <if test="specialConstructionSchemeFilePath != null">
                special_construction_scheme_file_path=#{specialConstructionSchemeFilePath},
            </if>
            <if test="installationContractFilePath != null">
                installation_contract_file_path=#{installationContractFilePath},
            </if>
            <if test="securityProtocolFilePath != null">
                security_protocol_file_path=#{securityProtocolFilePath},
            </if>
            <if test="emergencyRescuePlanFilePath != null">
                emergency_rescue_plan_file_path=#{emergencyRescuePlanFilePath},
            </if>
            <if test="equipmentQualificationCertificateFilePath != null">
                equipment_qualification_certificate_file_path=#{equipmentQualificationCertificateFilePath},
            </if>
            <if test="operatorQualificationCertificateFilePath != null">
                operator_qualification_certificate_file_path=#{operatorQualificationCertificateFilePath},
            </if>
            <if test="auditStatus != null">
                audit_status=#{auditStatus},
            </if>
            <if test="auditTime != null">
                audit_time=#{auditTime},
            </if>
            <if test="auditRejectReason != null">
                audit_reject_reason=#{auditRejectReason},
            </if>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
            <if test="workType != null">
                work_type=#{workType},
            </if>
            <if test="siteLeaderContactNumber != null">
                site_leader_contact_number=#{siteLeaderContactNumber},
            </if>
            <if test="techLeaderContactNumber != null">
                tech_leader_contact_number=#{techLeaderContactNumber},
            </if>
            <if test="installationLeaderContactNumber != null">
                installation_leader_contact_number=#{installationLeaderContactNumber},
            </if>
            <if test="businessId != null"> business_id=#{businessId},</if>
            <if test="engineeringAddressDetail != null"> engineering_address_detail=#{engineeringAddressDetail},</if>
            <if test="engineeringAreaCode != null"> engineering_area_code=#{engineeringAreaCode},</if>
            <if test="equipmentType != null"> equipment_type=#{equipmentType},</if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update filing_equipment_installation_form set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update filing_equipment_installation_form set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into filing_equipment_installation_form(id,  user_id,  application_date,  equipment_filing_code,  device_name,  specification_model,  engineering_name,  installation_unit,  site_leader,  installation_time,  installation_height,  construction_site,  job_content,  installation_leader,  tech_leader,  notification_book_file_path,  equipment_filing_license_file_path,  audit_form_file_path,  qualifi_license_file_path,  license_safety_permit_file_path,  operators_list_file_path,  operators_license_file_path,  special_construction_scheme_file_path,  installation_contract_file_path,  security_protocol_file_path,  emergency_rescue_plan_file_path,  equipment_qualification_certificate_file_path,  operator_qualification_certificate_file_path,  audit_status,  audit_time,  audit_reject_reason,  create_by,  create_time,  update_by,  update_time,  remarks,  del_flag, work_type, site_leader_contact_number, tech_leader_contact_number, installationLeaderContactNumber,business_id,engineering_address_detail,engineering_area_code,equipment_type) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.userId},  #{item.applicationDate},  #{item.equipmentFilingCode},  #{item.deviceName},  #{item.specificationModel},  #{item.engineeringName},  #{item.installationUnit},  #{item.siteLeader},  #{item.installationTime},  #{item.installationHeight},  #{item.constructionSite},  #{item.jobContent},  #{item.installationLeader},  #{item.techLeader},  #{item.notificationBookFilePath},  #{item.equipmentFilingLicenseFilePath},  #{item.auditFormFilePath},  #{item.qualifiLicenseFilePath},  #{item.licenseSafetyPermitFilePath},  #{item.operatorsListFilePath},  #{item.operatorsLicenseFilePath},  #{item.specialConstructionSchemeFilePath},  #{item.installationContractFilePath},  #{item.securityProtocolFilePath},  #{item.emergencyRescuePlanFilePath},  #{item.equipmentQualificationCertificateFilePath},  #{item.operatorQualificationCertificateFilePath},  #{item.auditStatus},  #{item.auditTime},  #{item.auditRejectReason},  #{item.createBy},  #{item.createTime},  #{item.updateBy},  #{item.updateTime},  #{item.remarks},  #{item.delFlag},#{item.workType},#{item.siteLeaderContactNumber},#{item.techLeaderContactNumber},#{item.installationLeaderContactNumber},#{item.businessId},#{item.engineeringAddressDetail},#{item.engineeringAreaCode},#{item.equipmentType})
        </foreach>
    </insert>

    <select id="selectListByAreaCode" resultMap="BaseResultMap">
        select  * from filing_equipment_installation_form
        where engineering_area_code
        <foreach close=")" collection="areaList" item="areaCode" open="in (" separator=",">
            #{areaCode}
        </foreach>
        and audit_status = #{auditStatus}
        and work_type = #{workType}
        and del_flag='0'
    </select>

    <select id="selectListByCondition" resultMap="BaseResultMap">
        select  * from filing_equipment_installation_form
        where engineering_area_code
        <foreach close=")" collection="areaList" item="areaCode" open="in (" separator=",">
            #{areaCode}
        </foreach>
        and audit_status = 1
        and work_type = #{workType}
        and del_flag='0'
        <if test="year != null and year != ''"> and date_format(audit_time, '%Y')=#{year}</if>
    </select>
    <select id="getOneByFilingCode" resultMap="BaseResultMap">
        select  * from filing_equipment_installation_form
        where equipment_filing_code = #{equipmentFilingCode} and work_type = #{workType} and del_flag='0'
    </select>
    <select id="selectListGroupByFilingCode" resultMap="BaseResultMap">
        select  * from filing_equipment_installation_form
        where engineering_area_code
        <foreach close=")" collection="areaList" item="areaCode" open="in (" separator=",">
            #{areaCode}
        </foreach>
        and audit_status = 1
        and work_type = #{workType}
        and del_flag='0'
        <if test="year != null and year != ''"> and date_format(audit_time, '%Y')=#{year}</if>
        group by equipment_filing_code
    </select>
    <select id="selectByFilingCode" resultMap="BaseResultMap" parameterType="string">
        select  * from filing_equipment_installation_form
        where
            equipment_filing_code = #{equipmentFilingCode}
        and del_flag='0'
    </select>
</mapper>
