<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.buildingCraneCert.mapper.BuildingCraneCertMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.buildingCraneCert.model.BuildingCraneCert" >
        <result property="id"    column="id"    />
        <result property="businessDataId"    column="businessDataId"    />
        <result property="areaCode"    column="areaCode"    />
        <result property="certNum"    column="certNum"    />
        <result property="oldCertNum"    column="oldCertNum"    />
        <result property="issuAuthName"    column="issuAuthName"    />
        <result property="issuAuthCode"    column="issuAuthCode"    />
        <result property="issuDate"    column="issuDate"    />
        <result property="equipmentName"    column="equipmentName"    />
        <result property="equipmentCategoryCode"    column="equipmentCategoryCode"    />
        <result property="equipmentSpecifications"    column="equipmentSpecifications"    />
        <result property="exFactoryNumber"    column="exFactoryNumber"    />
        <result property="exFactoryDate"    column="exFactoryDate"    />
        <result property="exFactoryPrice"    column="exFactoryPrice"    />
        <result property="designServiceLife"    column="designServiceLife"    />
        <result property="nextSaftyEvalTime"    column="nextSaftyEvalTime"    />
        <result property="manufactureCorpName"    column="manufactureCorpName"    />
        <result property="manufactureCorpCode"    column="manufactureCorpCode"    />
        <result property="manufactureLicCode"    column="manufactureLicCode"    />
        <result property="exFactoryCertPhoto"    column="exFactoryCertPhoto"    />
        <result property="ownerCorpName"    column="ownerCorpName"    />
        <result property="ownerCorpCode"    column="ownerCorpCode"    />
        <result property="ownerRegAddress"    column="ownerRegAddress"    />
        <result property="ownerLegalPerson"    column="ownerLegalPerson"    />
        <result property="ownerLPCodeType"    column="ownerLPCodeType"    />
        <result property="ownerLPCode"    column="ownerLPCode"    />
        <result property="ratedCapacity"    column="ratedCapacity"    />
        <result property="ratedLoadMoment"    column="ratedLoadMoment"    />
        <result property="lengthOfCraneJib"    column="lengthOfCraneJib"    />
        <result property="maxWorkingRange"    column="maxWorkingRange"    />
        <result property="ratedLiftingCATWR"    column="ratedLiftingCATWR"    />
        <result property="nonTieInLoadLH"    column="nonTieInLoadLH"    />
        <result property="maxLiftingHeight"    column="maxLiftingHeight"    />
        <result property="standardSectionPOfTC"    column="standardSectionPOfTC"    />
        <result property="stdSectionMainSSOfTC"    column="stdSectionMainSSOfTC"    />
        <result property="reinforceSectionPOfTC"    column="reinforceSectionPOfTC"    />
        <result property="mainStrUniqueCode"    column="mainStrUniqueCode"    />
        <result property="constructionHUTypes"    column="constructionHUTypes"    />
        <result property="ratedLiftingSpeed"    column="ratedLiftingSpeed"    />
        <result property="maxHoistingHeight"    column="maxHoistingHeight"    />
        <result property="totalElectricMotorPower"    column="totalElectricMotorPower"    />
        <result property="antifallSafetyDeviceType"    column="antifallSafetyDeviceType"    />
        <result property="carrierUnitHD"    column="carrierUnitHD"    />
        <result property="otherCraneType"    column="otherCraneType"    />
        <result property="portalCraneSpan"    column="portalCraneSpan"    />
        <result property="isFirstFiling"    column="isFirstFiling"    />
        <result property="equipmentUniqueCode"    column="equipmentUniqueCode"    />
        <result property="firstFilingDate"    column="firstFilingDate"    />
        <result property="certState"    column="certState"    />
        <result property="certStateDesc"    column="certStateDesc"    />
        <result property="associatedCertId"    column="associatedCertId"    />
        <result property="businessInformation"    column="businessInformation"    />
        <result property="operateType"    column="operateType"    />
        <result property="pushFlag"    column="pushFlag"    />
        <result property="pushTime"    column="pushTime"    />
    </resultMap>

    <sql id="Base_Column_List" >
        id, businessDataId, areaCode, certNum, oldCertNum, issuAuthName, issuAuthCode, issuDate,
        equipmentName, equipmentCategoryCode, equipmentSpecifications, exFactoryNumber, exFactoryDate,
        exFactoryPrice, designServiceLife, nextSaftyEvalTime, manufactureCorpName, manufactureCorpCode,
        manufactureLicCode, exFactoryCertPhoto, ownerCorpName, ownerCorpCode, ownerRegAddress,
        ownerLegalPerson, ownerLPCodeType, ownerLPCode, ratedCapacity, ratedLoadMoment, lengthOfCraneJib,
        maxWorkingRange, ratedLiftingCATWR, nonTieInLoadLH, maxLiftingHeight, standardSectionPOfTC,
        stdSectionMainSSOfTC, reinforceSectionPOfTC, mainStrUniqueCode, constructionHUTypes,
        ratedLiftingSpeed, maxHoistingHeight, totalElectricMotorPower, antifallSafetyDeviceType,
        carrierUnitHD, otherCraneType, portalCraneSpan,isFirstFiling, equipmentUniqueCode,
        firstFilingDate, certState, certStateDesc, associatedCertId, businessInformation, operateType
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from building_crane_cert
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.buildingCraneCert.model.BuildingCraneCert">
        select
        <include refid="Base_Column_List" />
        from building_crane_cert
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.buildingCraneCert.model.BuildingCraneCert">
        select
        <include refid="Base_Column_List" />
        from building_crane_cert
        <where>
            del_flag='0'
            <if test="businessDataId != null  and businessDataId != ''"> and businessDataId = #{businessDataId}</if>
            <if test="areaCode != null  and areaCode != ''"> and areaCode = #{areaCode}</if>
            <if test="certNum != null  and certNum != ''"> and certNum = #{certNum}</if>
            <if test="oldCertNum != null  and oldCertNum != ''"> and oldCertNum = #{oldCertNum}</if>
            <if test="issuAuthName != null  and issuAuthName != ''"> and issuAuthName like concat('%', #{issuAuthName}, '%')</if>
            <if test="issuAuthCode != null  and issuAuthCode != ''"> and issuAuthCode = #{issuAuthCode}</if>
            <if test="issuDate != null  and issuDate != ''"> and issuDate = #{issuDate}</if>
            <if test="equipmentName != null  and equipmentName != ''"> and equipmentName like concat('%', #{equipmentName}, '%')</if>
            <if test="equipmentCategoryCode != null  and equipmentCategoryCode != ''"> and equipmentCategoryCode = #{equipmentCategoryCode}</if>
            <if test="equipmentSpecifications != null  and equipmentSpecifications != ''"> and equipmentSpecifications = #{equipmentSpecifications}</if>
            <if test="exFactoryNumber != null  and exFactoryNumber != ''"> and exFactoryNumber = #{exFactoryNumber}</if>
            <if test="exFactoryDate != null  and exFactoryDate != ''"> and exFactoryDate = #{exFactoryDate}</if>
            <if test="exFactoryPrice != null  and exFactoryPrice != ''"> and exFactoryPrice = #{exFactoryPrice}</if>
            <if test="designServiceLife != null  and designServiceLife != ''"> and designServiceLife = #{designServiceLife}</if>
            <if test="nextSaftyEvalTime != null  and nextSaftyEvalTime != ''"> and nextSaftyEvalTime = #{nextSaftyEvalTime}</if>
            <if test="manufactureCorpName != null  and manufactureCorpName != ''"> and manufactureCorpName like concat('%', #{manufactureCorpName}, '%')</if>
            <if test="manufactureCorpCode != null  and manufactureCorpCode != ''"> and manufactureCorpCode = #{manufactureCorpCode}</if>
            <if test="manufactureLicCode != null  and manufactureLicCode != ''"> and manufactureLicCode = #{manufactureLicCode}</if>
            <if test="exFactoryCertPhoto != null  and exFactoryCertPhoto != ''"> and exFactoryCertPhoto = #{exFactoryCertPhoto}</if>
            <if test="ownerCorpName != null  and ownerCorpName != ''"> and ownerCorpName like concat('%', #{ownerCorpName}, '%')</if>
            <if test="ownerCorpCode != null  and ownerCorpCode != ''"> and ownerCorpCode = #{ownerCorpCode}</if>
            <if test="ownerRegAddress != null  and ownerRegAddress != ''"> and ownerRegAddress = #{ownerRegAddress}</if>
            <if test="ownerLegalPerson != null  and ownerLegalPerson != ''"> and ownerLegalPerson = #{ownerLegalPerson}</if>
            <if test="ownerLPCodeType != null  and ownerLPCodeType != ''"> and ownerLPCodeType = #{ownerLPCodeType}</if>
            <if test="ownerLPCode != null  and ownerLPCode != ''"> and ownerLPCode = #{ownerLPCode}</if>
            <if test="ratedCapacity != null  and ratedCapacity != ''"> and ratedCapacity = #{ratedCapacity}</if>
            <if test="ratedLoadMoment != null  and ratedLoadMoment != ''"> and ratedLoadMoment = #{ratedLoadMoment}</if>
            <if test="lengthOfCraneJib != null  and lengthOfCraneJib != ''"> and lengthOfCraneJib = #{lengthOfCraneJib}</if>
            <if test="maxWorkingRange != null  and maxWorkingRange != ''"> and maxWorkingRange = #{maxWorkingRange}</if>
            <if test="ratedLiftingCATWR != null  and ratedLiftingCATWR != ''"> and ratedLiftingCATWR = #{ratedLiftingCATWR}</if>
            <if test="nonTieInLoadLH != null  and nonTieInLoadLH != ''"> and nonTieInLoadLH = #{nonTieInLoadLH}</if>
            <if test="maxLiftingHeight != null  and maxLiftingHeight != ''"> and maxLiftingHeight = #{maxLiftingHeight}</if>
            <if test="standardSectionPOfTC != null  and standardSectionPOfTC != ''"> and standardSectionPOfTC = #{standardSectionPOfTC}</if>
            <if test="stdSectionMainSSOfTC != null  and stdSectionMainSSOfTC != ''"> and stdSectionMainSSOfTC = #{stdSectionMainSSOfTC}</if>
            <if test="reinforceSectionPOfTC != null  and reinforceSectionPOfTC != ''"> and reinforceSectionPOfTC = #{reinforceSectionPOfTC}</if>
            <if test="mainStrUniqueCode != null  and mainStrUniqueCode != ''"> and mainStrUniqueCode = #{mainStrUniqueCode}</if>
            <if test="constructionHUTypes != null  and constructionHUTypes != ''"> and constructionHUTypes = #{constructionHUTypes}</if>
            <if test="ratedLiftingSpeed != null  and ratedLiftingSpeed != ''"> and ratedLiftingSpeed = #{ratedLiftingSpeed}</if>
            <if test="maxHoistingHeight != null  and maxHoistingHeight != ''"> and maxHoistingHeight = #{maxHoistingHeight}</if>
            <if test="totalElectricMotorPower != null  and totalElectricMotorPower != ''"> and totalElectricMotorPower = #{totalElectricMotorPower}</if>
            <if test="antifallSafetyDeviceType != null  and antifallSafetyDeviceType != ''"> and antifallSafetyDeviceType = #{antifallSafetyDeviceType}</if>
            <if test="carrierUnitHD != null  and carrierUnitHD != ''"> and carrierUnitHD = #{carrierUnitHD}</if>
            <if test="otherCraneType != null  and otherCraneType != ''"> and otherCraneType = #{otherCraneType}</if>
            <if test="portalCraneSpan != null  and portalCraneSpan != ''"> and portalCraneSpan = #{portalCraneSpan}</if>
            <if test="isFirstFiling != null  and isFirstFiling != ''"> and isFirstFiling = #{isFirstFiling}</if>
            <if test="equipmentUniqueCode != null  and equipmentUniqueCode != ''"> and equipmentUniqueCode = #{equipmentUniqueCode}</if>
            <if test="firstFilingDate != null  and firstFilingDate != ''"> and firstFilingDate = #{firstFilingDate}</if>
            <if test="certState != null  and certState != ''"> and certState = #{certState}</if>
            <if test="certStateDesc != null  and certStateDesc != ''"> and certStateDesc = #{certStateDesc}</if>
            <if test="associatedCertId != null  and associatedCertId != ''"> and associatedCertId = #{associatedCertId}</if>
            <if test="businessInformation != null  and businessInformation != ''"> and businessInformation = #{businessInformation}</if>
            <if test="operateType != null  and operateType != ''"> and operateType = #{operateType}</if>
            <if test="pushFlag != null  and pushFlag != ''"> and pushFlag = #{pushFlag}</if>
            <if test="pushTime != null "> and pushTime = #{pushTime}</if>
         </where>
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.buildingCraneCert.model.BuildingCraneCert">
        insert into building_crane_cert
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
            <if test="businessDataId != null">businessDataId,</if>
            <if test="areaCode != null">areaCode,</if>
            <if test="certNum != null">certNum,</if>
            <if test="oldCertNum != null">oldCertNum,</if>
            <if test="issuAuthName != null">issuAuthName,</if>
            <if test="issuAuthCode != null">issuAuthCode,</if>
            <if test="issuDate != null">issuDate,</if>
            <if test="equipmentName != null">equipmentName,</if>
            <if test="equipmentCategoryCode != null">equipmentCategoryCode,</if>
            <if test="equipmentSpecifications != null">equipmentSpecifications,</if>
            <if test="exFactoryNumber != null">exFactoryNumber,</if>
            <if test="exFactoryDate != null">exFactoryDate,</if>
            <if test="exFactoryPrice != null">exFactoryPrice,</if>
            <if test="designServiceLife != null">designServiceLife,</if>
            <if test="nextSaftyEvalTime != null">nextSaftyEvalTime,</if>
            <if test="manufactureCorpName != null">manufactureCorpName,</if>
            <if test="manufactureCorpCode != null">manufactureCorpCode,</if>
            <if test="manufactureLicCode != null">manufactureLicCode,</if>
            <if test="exFactoryCertPhoto != null">exFactoryCertPhoto,</if>
            <if test="ownerCorpName != null">ownerCorpName,</if>
            <if test="ownerCorpCode != null">ownerCorpCode,</if>
            <if test="ownerRegAddress != null">ownerRegAddress,</if>
            <if test="ownerLegalPerson != null">ownerLegalPerson,</if>
            <if test="ownerLPCodeType != null">ownerLPCodeType,</if>
            <if test="ownerLPCode != null">ownerLPCode,</if>
            <if test="ratedCapacity != null">ratedCapacity,</if>
            <if test="ratedLoadMoment != null">ratedLoadMoment,</if>
            <if test="lengthOfCraneJib != null">lengthOfCraneJib,</if>
            <if test="maxWorkingRange != null">maxWorkingRange,</if>
            <if test="ratedLiftingCATWR != null">ratedLiftingCATWR,</if>
            <if test="nonTieInLoadLH != null">nonTieInLoadLH,</if>
            <if test="maxLiftingHeight != null">maxLiftingHeight,</if>
            <if test="standardSectionPOfTC != null">standardSectionPOfTC,</if>
            <if test="stdSectionMainSSOfTC != null">stdSectionMainSSOfTC,</if>
            <if test="reinforceSectionPOfTC != null">reinforceSectionPOfTC,</if>
            <if test="mainStrUniqueCode != null">mainStrUniqueCode,</if>
            <if test="constructionHUTypes != null">constructionHUTypes,</if>
            <if test="ratedLiftingSpeed != null">ratedLiftingSpeed,</if>
            <if test="maxHoistingHeight != null">maxHoistingHeight,</if>
            <if test="totalElectricMotorPower != null">totalElectricMotorPower,</if>
            <if test="antifallSafetyDeviceType != null">antifallSafetyDeviceType,</if>
            <if test="carrierUnitHD != null">carrierUnitHD,</if>
            <if test="otherCraneType != null">otherCraneType,</if>
            <if test="portalCraneSpan != null">portalCraneSpan,</if>
            <if test="isFirstFiling != null">isFirstFiling,</if>
            <if test="equipmentUniqueCode != null">equipmentUniqueCode,</if>
            <if test="firstFilingDate != null">firstFilingDate,</if>
            <if test="certState != null">certState,</if>
            <if test="certStateDesc != null">certStateDesc,</if>
            <if test="associatedCertId != null">associatedCertId,</if>
            <if test="businessInformation != null">businessInformation,</if>
            <if test="operateType != null">operateType,</if>
            <if test="pushFlag != null">pushFlag,</if>
            <if test="pushTime != null">pushTime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="businessDataId != null">#{businessDataId},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="certNum != null">#{certNum},</if>
            <if test="oldCertNum != null">#{oldCertNum},</if>
            <if test="issuAuthName != null">#{issuAuthName},</if>
            <if test="issuAuthCode != null">#{issuAuthCode},</if>
            <if test="issuDate != null">#{issuDate},</if>
            <if test="equipmentName != null">#{equipmentName},</if>
            <if test="equipmentCategoryCode != null">#{equipmentCategoryCode},</if>
            <if test="equipmentSpecifications != null">#{equipmentSpecifications},</if>
            <if test="exFactoryNumber != null">#{exFactoryNumber},</if>
            <if test="exFactoryDate != null">#{exFactoryDate},</if>
            <if test="exFactoryPrice != null">#{exFactoryPrice},</if>
            <if test="designServiceLife != null">#{designServiceLife},</if>
            <if test="nextSaftyEvalTime != null">#{nextSaftyEvalTime},</if>
            <if test="manufactureCorpName != null">#{manufactureCorpName},</if>
            <if test="manufactureCorpCode != null">#{manufactureCorpCode},</if>
            <if test="manufactureLicCode != null">#{manufactureLicCode},</if>
            <if test="exFactoryCertPhoto != null">#{exFactoryCertPhoto},</if>
            <if test="ownerCorpName != null">#{ownerCorpName},</if>
            <if test="ownerCorpCode != null">#{ownerCorpCode},</if>
            <if test="ownerRegAddress != null">#{ownerRegAddress},</if>
            <if test="ownerLegalPerson != null">#{ownerLegalPerson},</if>
            <if test="ownerLPCodeType != null">#{ownerLPCodeType},</if>
            <if test="ownerLPCode != null">#{ownerLPCode},</if>
            <if test="ratedCapacity != null">#{ratedCapacity},</if>
            <if test="ratedLoadMoment != null">#{ratedLoadMoment},</if>
            <if test="lengthOfCraneJib != null">#{lengthOfCraneJib},</if>
            <if test="maxWorkingRange != null">#{maxWorkingRange},</if>
            <if test="ratedLiftingCATWR != null">#{ratedLiftingCATWR},</if>
            <if test="nonTieInLoadLH != null">#{nonTieInLoadLH},</if>
            <if test="maxLiftingHeight != null">#{maxLiftingHeight},</if>
            <if test="standardSectionPOfTC != null">#{standardSectionPOfTC},</if>
            <if test="stdSectionMainSSOfTC != null">#{stdSectionMainSSOfTC},</if>
            <if test="reinforceSectionPOfTC != null">#{reinforceSectionPOfTC},</if>
            <if test="mainStrUniqueCode != null">#{mainStrUniqueCode},</if>
            <if test="constructionHUTypes != null">#{constructionHUTypes},</if>
            <if test="ratedLiftingSpeed != null">#{ratedLiftingSpeed},</if>
            <if test="maxHoistingHeight != null">#{maxHoistingHeight},</if>
            <if test="totalElectricMotorPower != null">#{totalElectricMotorPower},</if>
            <if test="antifallSafetyDeviceType != null">#{antifallSafetyDeviceType},</if>
            <if test="carrierUnitHD != null">#{carrierUnitHD},</if>
            <if test="otherCraneType != null">#{otherCraneType},</if>
            <if test="portalCraneSpan != null">#{portalCraneSpan},</if>
            <if test="isFirstFiling != null">#{isFirstFiling},</if>
            <if test="equipmentUniqueCode != null">#{equipmentUniqueCode},</if>
            <if test="firstFilingDate != null">#{firstFilingDate},</if>
            <if test="certState != null">#{certState},</if>
            <if test="certStateDesc != null">#{certStateDesc},</if>
            <if test="associatedCertId != null">#{associatedCertId},</if>
            <if test="businessInformation != null">#{businessInformation},</if>
            <if test="operateType != null">#{operateType},</if>
            <if test="pushFlag != null">#{pushFlag},</if>
            <if test="pushTime != null">#{pushTime},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.buildingCraneCert.model.BuildingCraneCert">
        update building_crane_cert
        <set>
            <if test="businessDataId != null">businessDataId = #{businessDataId},</if>
            <if test="areaCode != null">areaCode = #{areaCode},</if>
            <if test="certNum != null">certNum = #{certNum},</if>
            <if test="oldCertNum != null">oldCertNum = #{oldCertNum},</if>
            <if test="issuAuthName != null">issuAuthName = #{issuAuthName},</if>
            <if test="issuAuthCode != null">issuAuthCode = #{issuAuthCode},</if>
            <if test="issuDate != null">issuDate = #{issuDate},</if>
            <if test="equipmentName != null">equipmentName = #{equipmentName},</if>
            <if test="equipmentCategoryCode != null">equipmentCategoryCode = #{equipmentCategoryCode},</if>
            <if test="equipmentSpecifications != null">equipmentSpecifications = #{equipmentSpecifications},</if>
            <if test="exFactoryNumber != null">exFactoryNumber = #{exFactoryNumber},</if>
            <if test="exFactoryDate != null">exFactoryDate = #{exFactoryDate},</if>
            <if test="exFactoryPrice != null">exFactoryPrice = #{exFactoryPrice},</if>
            <if test="designServiceLife != null">designServiceLife = #{designServiceLife},</if>
            <if test="nextSaftyEvalTime != null">nextSaftyEvalTime = #{nextSaftyEvalTime},</if>
            <if test="manufactureCorpName != null">manufactureCorpName = #{manufactureCorpName},</if>
            <if test="manufactureCorpCode != null">manufactureCorpCode = #{manufactureCorpCode},</if>
            <if test="manufactureLicCode != null">manufactureLicCode = #{manufactureLicCode},</if>
            <if test="exFactoryCertPhoto != null">exFactoryCertPhoto = #{exFactoryCertPhoto},</if>
            <if test="ownerCorpName != null">ownerCorpName = #{ownerCorpName},</if>
            <if test="ownerCorpCode != null">ownerCorpCode = #{ownerCorpCode},</if>
            <if test="ownerRegAddress != null">ownerRegAddress = #{ownerRegAddress},</if>
            <if test="ownerLegalPerson != null">ownerLegalPerson = #{ownerLegalPerson},</if>
            <if test="ownerLPCodeType != null">ownerLPCodeType = #{ownerLPCodeType},</if>
            <if test="ownerLPCode != null">ownerLPCode = #{ownerLPCode},</if>
            <if test="ratedCapacity != null">ratedCapacity = #{ratedCapacity},</if>
            <if test="ratedLoadMoment != null">ratedLoadMoment = #{ratedLoadMoment},</if>
            <if test="lengthOfCraneJib != null">lengthOfCraneJib = #{lengthOfCraneJib},</if>
            <if test="maxWorkingRange != null">maxWorkingRange = #{maxWorkingRange},</if>
            <if test="ratedLiftingCATWR != null">ratedLiftingCATWR = #{ratedLiftingCATWR},</if>
            <if test="nonTieInLoadLH != null">nonTieInLoadLH = #{nonTieInLoadLH},</if>
            <if test="maxLiftingHeight != null">maxLiftingHeight = #{maxLiftingHeight},</if>
            <if test="standardSectionPOfTC != null">standardSectionPOfTC = #{standardSectionPOfTC},</if>
            <if test="stdSectionMainSSOfTC != null">stdSectionMainSSOfTC = #{stdSectionMainSSOfTC},</if>
            <if test="reinforceSectionPOfTC != null">reinforceSectionPOfTC = #{reinforceSectionPOfTC},</if>
            <if test="mainStrUniqueCode != null">mainStrUniqueCode = #{mainStrUniqueCode},</if>
            <if test="constructionHUTypes != null">constructionHUTypes = #{constructionHUTypes},</if>
            <if test="ratedLiftingSpeed != null">ratedLiftingSpeed = #{ratedLiftingSpeed},</if>
            <if test="maxHoistingHeight != null">maxHoistingHeight = #{maxHoistingHeight},</if>
            <if test="totalElectricMotorPower != null">totalElectricMotorPower = #{totalElectricMotorPower},</if>
            <if test="antifallSafetyDeviceType != null">antifallSafetyDeviceType = #{antifallSafetyDeviceType},</if>
            <if test="carrierUnitHD != null">carrierUnitHD = #{carrierUnitHD},</if>
            <if test="otherCraneType != null">otherCraneType = #{otherCraneType},</if>
            <if test="portalCraneSpan != null">portalCraneSpan = #{portalCraneSpan},</if>
            <if test="isFirstFiling != null">isFirstFiling = #{isFirstFiling},</if>
            <if test="equipmentUniqueCode != null">equipmentUniqueCode = #{equipmentUniqueCode},</if>
            <if test="firstFilingDate != null">firstFilingDate = #{firstFilingDate},</if>
            <if test="certState != null">certState = #{certState},</if>
            <if test="certStateDesc != null">certStateDesc = #{certStateDesc},</if>
            <if test="associatedCertId != null">associatedCertId = #{associatedCertId},</if>
            <if test="businessInformation != null">businessInformation = #{businessInformation},</if>
            <if test="operateType != null">operateType = #{operateType},</if>
            <if test="pushFlag != null">pushFlag = #{pushFlag},</if>
            <if test="pushTime != null">pushTime = #{pushTime},</if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update building_crane_cert set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update building_crane_cert set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into building_crane_cert(id,  businessDataId,  areaCode,  certNum,  oldCertNum,  issuAuthName,  issuAuthCode,  issuDate,  equipmentName,  equipmentCategoryCode,  equipmentSpecifications,  exFactoryNumber,  exFactoryDate,  exFactoryPrice,  designServiceLife,  nextSaftyEvalTime,  manufactureCorpName,  manufactureCorpCode,  manufactureLicCode,  exFactoryCertPhoto,  ownerCorpName,  ownerCorpCode,  ownerRegAddress,  ownerLegalPerson,  ownerLPCodeType,  ownerLPCode,  ratedCapacity,  ratedLoadMoment,  lengthOfCraneJib,  maxWorkingRange,  ratedLiftingCATWR,  nonTieInLoadLH,  maxLiftingHeight,  standardSectionPOfTC,  stdSectionMainSSOfTC,  reinforceSectionPOfTC,  mainStrUniqueCode,  constructionHUTypes,  ratedLiftingSpeed,  maxHoistingHeight,  totalElectricMotorPower,  antifallSafetyDeviceType,  carrierUnitHD,  otherCraneType,  portalCraneSpan,  isFirstFiling,  equipmentUniqueCode,  firstFilingDate,  certState,  certStateDesc,  associatedCertId,  businessInformation,  operateType,pushFlag,pushTime) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.businessDataId},  #{item.areaCode},  #{item.certNum},  #{item.oldCertNum},  #{item.issuAuthName},  #{item.issuAuthCode},  #{item.issuDate},  #{item.equipmentName},  #{item.equipmentCategoryCode},  #{item.equipmentSpecifications},  #{item.exFactoryNumber},  #{item.exFactoryDate},  #{item.exFactoryPrice},  #{item.designServiceLife},  #{item.nextSaftyEvalTime},
             #{item.manufactureCorpName},  #{item.manufactureCorpCode},  #{item.manufactureLicCode},  #{item.exFactoryCertPhoto},  #{item.ownerCorpName},  #{item.ownerCorpCode},  #{item.ownerRegAddress},  #{item.ownerLegalPerson},  #{item.ownerLPCodeType},  #{item.ownerLPCode},  #{item.ratedCapacity},  #{item.ratedLoadMoment},  #{item.lengthOfCraneJib},  #{item.maxWorkingRange},  #{item.ratedLiftingCATWR},  #{item.nonTieInLoadLH},  #{item.maxLiftingHeight},  #{item.standardSectionPOfTC},  #{item.stdSectionMainSSOfTC},  #{item.reinforceSectionPOfTC},  #{item.mainStrUniqueCode},  #{item.constructionHUTypes},  #{item.ratedLiftingSpeed},  #{item.maxHoistingHeight},  #{item.totalElectricMotorPower},  #{item.antifallSafetyDeviceType},  #{item.carrierUnitHD},  #{item.otherCraneType},  #{item.portalCraneSpan},  #{item.isFirstFiling},  #{item.equipmentUniqueCode},  #{item.firstFilingDate},  #{item.certState},  #{item.certStateDesc},  #{item.associatedCertId},  #{item.businessInformation},  #{item.operateType},#{item.pushFlag},#{item.pushTime})
        </foreach>
    </insert>

</mapper>
