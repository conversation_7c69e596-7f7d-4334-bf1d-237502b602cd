<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.collectionCert.mapper.ZjtQzjMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.collectionCert.model.ZjtQzj" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="fmcode" property="fmcode" jdbcType="VARCHAR" />
        <result column="cert_id" property="certId" jdbcType="VARCHAR" />
        <result column="cert_preview_url" property="certPreviewUrl" jdbcType="VARCHAR" />
        <result column="sync_flag" property="syncFlag" jdbcType="VARCHAR" />
        <result column="sysnc_date" property="sysncDate" jdbcType="TIMESTAMP" />
        <result column="cert_num" property="certNum" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        fmcode,
        cert_id,
        cert_preview_url,
        sync_flag,
        sysnc_date,
        cert_num
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from zjt_qzj
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.collectionCert.model.ZjtQzj">
        select
        <include refid="Base_Column_List" />
        from zjt_qzj
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.collectionCert.model.ZjtQzj">
        select
        <include refid="Base_Column_List" />
        from zjt_qzj
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="fmcode != null ">
                and fmcode=#{fmcode}
            </if>
            <if test="certId != null ">
                and cert_id=#{certId}
            </if>
            <if test="certPreviewUrl != null ">
                and cert_preview_url=#{certPreviewUrl}
            </if>
            <if test="syncFlag != null ">
                and sync_flag=#{syncFlag}
            </if>
            <if test="sysncDate != null ">
                and sysnc_date=#{sysncDate}
            </if>
            <if test="certNum != null ">
                and cert_num=#{certNum}
            </if>
         </where>
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.collectionCert.model.ZjtQzj">
        insert into zjt_qzj
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="fmcode != null">
                    fmcode,
                </if>
                <if test="certId != null">
                    cert_id,
                </if>
                <if test="certPreviewUrl != null">
                    cert_preview_url,
                </if>
                <if test="syncFlag != null">
                    sync_flag,
                </if>
                <if test="sysncDate != null">
                    sysnc_date,
                </if>
                <if test="certNum != null">
                    cert_num,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="fmcode != null ">
                #{fmcode},
            </if>
            <if test="certId != null ">
                #{certId},
            </if>
            <if test="certPreviewUrl != null ">
                #{certPreviewUrl},
            </if>
            <if test="syncFlag != null ">
                #{syncFlag},
            </if>
            <if test="sysncDate != null ">
                #{sysncDate},
            </if>
            <if test="certNum != null ">
                #{certNum},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.collectionCert.model.ZjtQzj">
        update zjt_qzj
        <set>
            <if test="fmcode != null">
                fmcode=#{fmcode},
            </if>
            <if test="certId != null">
                cert_id=#{certId},
            </if>
            <if test="certPreviewUrl != null">
                cert_preview_url=#{certPreviewUrl},
            </if>
            <if test="syncFlag != null">
                sync_flag=#{syncFlag},
            </if>
            <if test="sysncDate != null">
                sysnc_date=#{sysncDate},
            </if>
            <if test="certNum != null">
                cert_num=#{certNum},
            </if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update zjt_qzj set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update zjt_qzj set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into zjt_qzj(id,  fmcode,  cert_id,  cert_preview_url,  sync_flag,  sysnc_date,  cert_num) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.fmcode},  #{item.certId},  #{item.certPreviewUrl},  #{item.syncFlag},  #{item.sysncDate},  #{item.certNum})
        </foreach>
    </insert>

</mapper>