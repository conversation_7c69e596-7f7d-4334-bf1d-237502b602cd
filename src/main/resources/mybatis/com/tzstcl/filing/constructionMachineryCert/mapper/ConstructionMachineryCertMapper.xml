<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.constructionMachineryCert.mapper.ConstructionMachineryCertMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.constructionMachineryCert.model.ConstructionMachineryCert" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="businessdataid" property="businessdataid" jdbcType="CHAR" />
        <result column="certid" property="certid" jdbcType="CHAR" />
        <result column="areacode" property="areacode" jdbcType="CHAR" />
        <result column="certnum" property="certnum" jdbcType="VARCHAR" />
        <result column="equipmentuniquecode" property="equipmentuniquecode" jdbcType="CHAR" />
        <result column="equipmentcategorycode" property="equipmentcategorycode" jdbcType="CHAR" />
        <result column="equipmentspecifications" property="equipmentspecifications" jdbcType="VARCHAR" />
        <result column="exfactorynumber" property="exfactorynumber" jdbcType="VARCHAR" />
        <result column="manufacturecorpcode" property="manufacturecorpcode" jdbcType="CHAR" />
        <result column="ownerregaddress" property="ownerregaddress" jdbcType="VARCHAR" />
        <result column="ownerlegalperson" property="ownerlegalperson" jdbcType="VARCHAR" />
        <result column="ownerlpcodetype" property="ownerlpcodetype" jdbcType="CHAR" />
        <result column="ownerlpcode" property="ownerlpcode" jdbcType="VARCHAR" />
        <result column="nextsaftyevaltime" property="nextsaftyevaltime" jdbcType="VARCHAR" />
        <result column="lengthofcranejib" property="lengthofcranejib" jdbcType="VARCHAR" />
        <result column="maxworkingrange" property="maxworkingrange" jdbcType="VARCHAR" />
        <result column="ratedliftingcatwr" property="ratedliftingcatwr" jdbcType="VARCHAR" />
        <result column="nontieinloadlh" property="nontieinloadlh" jdbcType="VARCHAR" />
        <result column="maxliftingheight" property="maxliftingheight" jdbcType="VARCHAR" />
        <result column="maxhoistingheight" property="maxhoistingheight" jdbcType="VARCHAR" />
        <result column="operatetype" property="operatetype" jdbcType="CHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        businessdataid,
        certid,
        areacode,
        certnum,
        equipmentuniquecode,
        equipmentcategorycode,
        equipmentspecifications,
        exfactorynumber,
        manufacturecorpcode,
        ownerregaddress,
        ownerlegalperson,
        ownerlpcodetype,
        ownerlpcode,
        nextsaftyevaltime,
        lengthofcranejib,
        maxworkingrange,
        ratedliftingcatwr,
        nontieinloadlh,
        maxliftingheight,
        maxhoistingheight,
        operatetype
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from construction_machinery_cert
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.constructionMachineryCert.model.ConstructionMachineryCert">
        select
        <include refid="Base_Column_List" />
        from construction_machinery_cert
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.constructionMachineryCert.model.ConstructionMachineryCert">
        select
        <include refid="Base_Column_List" />
        from construction_machinery_cert
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="businessdataid != null ">
                and businessdataid=#{businessdataid}
            </if>
            <if test="certid != null ">
                and certid=#{certid}
            </if>
            <if test="areacode != null ">
                and areacode=#{areacode}
            </if>
            <if test="certnum != null ">
                and certnum=#{certnum}
            </if>
            <if test="equipmentuniquecode != null ">
                and equipmentuniquecode=#{equipmentuniquecode}
            </if>
            <if test="equipmentcategorycode != null ">
                and equipmentcategorycode=#{equipmentcategorycode}
            </if>
            <if test="equipmentspecifications != null ">
                and equipmentspecifications=#{equipmentspecifications}
            </if>
            <if test="exfactorynumber != null ">
                and exfactorynumber=#{exfactorynumber}
            </if>
            <if test="manufacturecorpcode != null ">
                and manufacturecorpcode=#{manufacturecorpcode}
            </if>
            <if test="ownerregaddress != null ">
                and ownerregaddress=#{ownerregaddress}
            </if>
            <if test="ownerlegalperson != null ">
                and ownerlegalperson=#{ownerlegalperson}
            </if>
            <if test="ownerlpcodetype != null ">
                and ownerlpcodetype=#{ownerlpcodetype}
            </if>
            <if test="ownerlpcode != null ">
                and ownerlpcode=#{ownerlpcode}
            </if>
            <if test="nextsaftyevaltime != null ">
                and nextsaftyevaltime=#{nextsaftyevaltime}
            </if>
            <if test="lengthofcranejib != null ">
                and lengthofcranejib=#{lengthofcranejib}
            </if>
            <if test="maxworkingrange != null ">
                and maxworkingrange=#{maxworkingrange}
            </if>
            <if test="ratedliftingcatwr != null ">
                and ratedliftingcatwr=#{ratedliftingcatwr}
            </if>
            <if test="nontieinloadlh != null ">
                and nontieinloadlh=#{nontieinloadlh}
            </if>
            <if test="maxliftingheight != null ">
                and maxliftingheight=#{maxliftingheight}
            </if>
            <if test="maxhoistingheight != null ">
                and maxhoistingheight=#{maxhoistingheight}
            </if>
            <if test="operatetype != null ">
                and operatetype=#{operatetype}
            </if>
         </where>
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.constructionMachineryCert.model.ConstructionMachineryCert">
        insert into construction_machinery_cert
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="businessdataid != null">
                    businessdataid,
                </if>
                <if test="certid != null">
                    certid,
                </if>
                <if test="areacode != null">
                    areacode,
                </if>
                <if test="certnum != null">
                    certnum,
                </if>
                <if test="equipmentuniquecode != null">
                    equipmentuniquecode,
                </if>
                <if test="equipmentcategorycode != null">
                    equipmentcategorycode,
                </if>
                <if test="equipmentspecifications != null">
                    equipmentspecifications,
                </if>
                <if test="exfactorynumber != null">
                    exfactorynumber,
                </if>
                <if test="manufacturecorpcode != null">
                    manufacturecorpcode,
                </if>
                <if test="ownerregaddress != null">
                    ownerregaddress,
                </if>
                <if test="ownerlegalperson != null">
                    ownerlegalperson,
                </if>
                <if test="ownerlpcodetype != null">
                    ownerlpcodetype,
                </if>
                <if test="ownerlpcode != null">
                    ownerlpcode,
                </if>
                <if test="nextsaftyevaltime != null">
                    nextsaftyevaltime,
                </if>
                <if test="lengthofcranejib != null">
                    lengthofcranejib,
                </if>
                <if test="maxworkingrange != null">
                    maxworkingrange,
                </if>
                <if test="ratedliftingcatwr != null">
                    ratedliftingcatwr,
                </if>
                <if test="nontieinloadlh != null">
                    nontieinloadlh,
                </if>
                <if test="maxliftingheight != null">
                    maxliftingheight,
                </if>
                <if test="maxhoistingheight != null">
                    maxhoistingheight,
                </if>
                <if test="operatetype != null">
                    operatetype,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="businessdataid != null ">
                #{businessdataid},
            </if>
            <if test="certid != null ">
                #{certid},
            </if>
            <if test="areacode != null ">
                #{areacode},
            </if>
            <if test="certnum != null ">
                #{certnum},
            </if>
            <if test="equipmentuniquecode != null ">
                #{equipmentuniquecode},
            </if>
            <if test="equipmentcategorycode != null ">
                #{equipmentcategorycode},
            </if>
            <if test="equipmentspecifications != null ">
                #{equipmentspecifications},
            </if>
            <if test="exfactorynumber != null ">
                #{exfactorynumber},
            </if>
            <if test="manufacturecorpcode != null ">
                #{manufacturecorpcode},
            </if>
            <if test="ownerregaddress != null ">
                #{ownerregaddress},
            </if>
            <if test="ownerlegalperson != null ">
                #{ownerlegalperson},
            </if>
            <if test="ownerlpcodetype != null ">
                #{ownerlpcodetype},
            </if>
            <if test="ownerlpcode != null ">
                #{ownerlpcode},
            </if>
            <if test="nextsaftyevaltime != null ">
                #{nextsaftyevaltime},
            </if>
            <if test="lengthofcranejib != null ">
                #{lengthofcranejib},
            </if>
            <if test="maxworkingrange != null ">
                #{maxworkingrange},
            </if>
            <if test="ratedliftingcatwr != null ">
                #{ratedliftingcatwr},
            </if>
            <if test="nontieinloadlh != null ">
                #{nontieinloadlh},
            </if>
            <if test="maxliftingheight != null ">
                #{maxliftingheight},
            </if>
            <if test="maxhoistingheight != null ">
                #{maxhoistingheight},
            </if>
            <if test="operatetype != null ">
                #{operatetype},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.constructionMachineryCert.model.ConstructionMachineryCert">
        update construction_machinery_cert
        <set>
            <if test="businessdataid != null">
                businessdataid=#{businessdataid},
            </if>
            <if test="certid != null">
                certid=#{certid},
            </if>
            <if test="areacode != null">
                areacode=#{areacode},
            </if>
            <if test="certnum != null">
                certnum=#{certnum},
            </if>
            <if test="equipmentuniquecode != null">
                equipmentuniquecode=#{equipmentuniquecode},
            </if>
            <if test="equipmentcategorycode != null">
                equipmentcategorycode=#{equipmentcategorycode},
            </if>
            <if test="equipmentspecifications != null">
                equipmentspecifications=#{equipmentspecifications},
            </if>
            <if test="exfactorynumber != null">
                exfactorynumber=#{exfactorynumber},
            </if>
            <if test="manufacturecorpcode != null">
                manufacturecorpcode=#{manufacturecorpcode},
            </if>
            <if test="ownerregaddress != null">
                ownerregaddress=#{ownerregaddress},
            </if>
            <if test="ownerlegalperson != null">
                ownerlegalperson=#{ownerlegalperson},
            </if>
            <if test="ownerlpcodetype != null">
                ownerlpcodetype=#{ownerlpcodetype},
            </if>
            <if test="ownerlpcode != null">
                ownerlpcode=#{ownerlpcode},
            </if>
            <if test="nextsaftyevaltime != null">
                nextsaftyevaltime=#{nextsaftyevaltime},
            </if>
            <if test="lengthofcranejib != null">
                lengthofcranejib=#{lengthofcranejib},
            </if>
            <if test="maxworkingrange != null">
                maxworkingrange=#{maxworkingrange},
            </if>
            <if test="ratedliftingcatwr != null">
                ratedliftingcatwr=#{ratedliftingcatwr},
            </if>
            <if test="nontieinloadlh != null">
                nontieinloadlh=#{nontieinloadlh},
            </if>
            <if test="maxliftingheight != null">
                maxliftingheight=#{maxliftingheight},
            </if>
            <if test="maxhoistingheight != null">
                maxhoistingheight=#{maxhoistingheight},
            </if>
            <if test="operatetype != null">
                operatetype=#{operatetype},
            </if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update construction_machinery_cert set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update construction_machinery_cert set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into construction_machinery_cert(id,  businessdataid,  certid,  areacode,  certnum,  equipmentuniquecode,  equipmentcategorycode,  equipmentspecifications,  exfactorynumber,  manufacturecorpcode,  ownerregaddress,  ownerlegalperson,  ownerlpcodetype,  ownerlpcode,  nextsaftyevaltime,  lengthofcranejib,  maxworkingrange,  ratedliftingcatwr,  nontieinloadlh,  maxliftingheight,  maxhoistingheight,  operatetype,pushFlag,pushTime) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.businessdataid},  #{item.certid},  #{item.areacode},  #{item.certnum},  #{item.equipmentuniquecode},  #{item.equipmentcategorycode},  #{item.equipmentspecifications},  #{item.exfactorynumber},  #{item.manufacturecorpcode},  #{item.ownerregaddress},  #{item.ownerlegalperson},  #{item.ownerlpcodetype},  #{item.ownerlpcode},  #{item.nextsaftyevaltime},  #{item.lengthofcranejib},  #{item.maxworkingrange},  #{item.ratedliftingcatwr},  #{item.nontieinloadlh},  #{item.maxliftingheight},  #{item.maxhoistingheight},  #{item.operatetype},#{item.pushFlag},#{item.pushTime})
        </foreach>
    </insert>

</mapper>
