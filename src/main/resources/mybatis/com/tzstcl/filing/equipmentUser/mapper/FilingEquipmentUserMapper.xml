<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.equipmentUser.mapper.FilingEquipmentUserMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.equipmentUser.model.FilingEquipmentUser" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="use_registration_id" property="useRegistrationId" jdbcType="VARCHAR" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="worker_type" property="workerType" jdbcType="VARCHAR" />
        <result column="appointment_certificate_code" property="appointmentCertificateCode" jdbcType="VARCHAR" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        use_registration_id,
        name,
        worker_type,
        appointment_certificate_code,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_user
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.equipmentUser.model.FilingEquipmentUser">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_user
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.equipmentUser.model.FilingEquipmentUser">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_user
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="useRegistrationId != null ">
                and use_registration_id=#{useRegistrationId}
            </if>
            <if test="name != null ">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="workerType != null ">
                and worker_type=#{workerType}
            </if>
            <if test="appointmentCertificateCode != null ">
                and appointment_certificate_code=#{appointmentCertificateCode}
            </if>
         </where>
        order by create_time desc
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.equipmentUser.model.FilingEquipmentUser">
        insert into filing_equipment_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="useRegistrationId != null">
                    use_registration_id,
                </if>
                <if test="name != null">
                    name,
                </if>
                <if test="workerType != null">
                    worker_type,
                </if>
                <if test="appointmentCertificateCode != null">
                    appointment_certificate_code,
                </if>
                <if test="createBy != null">
                    create_by,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="updateBy != null">
                    update_by,
                </if>
                <if test="updateTime != null">
                    update_time,
                </if>
                <if test="remarks != null">
                    remarks,
                </if>
                <if test="delFlag != null">
                    del_flag,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="useRegistrationId != null ">
                #{useRegistrationId},
            </if>
            <if test="name != null ">
                #{name},
            </if>
            <if test="workerType != null ">
                #{workerType},
            </if>
            <if test="appointmentCertificateCode != null ">
                #{appointmentCertificateCode},
            </if>
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.equipmentUser.model.FilingEquipmentUser">
        update filing_equipment_user
        <set>
            <if test="useRegistrationId != null">
                use_registration_id=#{useRegistrationId},
            </if>
            <if test="name != null">
                name=#{name},
            </if>
            <if test="workerType != null">
                worker_type=#{workerType},
            </if>
            <if test="appointmentCertificateCode != null">
                appointment_certificate_code=#{appointmentCertificateCode},
            </if>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update filing_equipment_user set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update filing_equipment_user set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into filing_equipment_user(id,  use_registration_id,  name,  worker_type,  appointment_certificate_code,  create_by,  create_time,  update_by,  update_time,  remarks,  del_flag) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.useRegistrationId},  #{item.name},  #{item.workerType},  #{item.appointmentCertificateCode},  #{item.createBy},  #{item.createTime},  #{item.updateBy},  #{item.updateTime},  #{item.remarks},  #{item.delFlag})
        </foreach>
    </insert>

</mapper>
