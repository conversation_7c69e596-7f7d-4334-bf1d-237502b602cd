<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.file.mapper.FilingFileMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.file.model.FilingFile" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
        <result column="file_md5" property="fileMd5" jdbcType="VARCHAR" />
        <result column="file_name" property="fileName" jdbcType="VARCHAR" />
        <result column="file_type" property="fileType" jdbcType="VARCHAR" />
        <result column="file_suffix" property="fileSuffix" jdbcType="VARCHAR" />
        <result column="file_tag" property="fileTag" jdbcType="VARCHAR" />
        <result column="file_path" property="filePath" jdbcType="VARCHAR" />
        <result column="file_url" property="fileUrl" jdbcType="VARCHAR" />
        <result column="connect_id" property="connectId" jdbcType="VARCHAR" />
        <result column="upload_id" property="uploadId" jdbcType="BIGINT" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag,
        file_md5,
        file_name,
        file_type,
        file_suffix,
        file_tag,
        file_path,
        file_url,
        connect_id,
        upload_id
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from filing_file
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.file.model.FilingFile">
        select
        <include refid="Base_Column_List" />
        from filing_file
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.file.model.FilingFile">
        select
        <include refid="Base_Column_List" />
        from filing_file
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="fileMd5 != null ">
                and file_md5=#{fileMd5}
            </if>
            <if test="fileName != null ">
                and file_name=#{fileName}
            </if>
            <if test="fileType != null ">
                and file_type=#{fileType}
            </if>
            <if test="fileSuffix != null ">
                and file_suffix=#{fileSuffix}
            </if>
            <if test="fileTag != null ">
                and file_tag=#{fileTag}
            </if>
            <if test="filePath != null ">
                and file_path=#{filePath}
            </if>
            <if test="fileUrl != null ">
                and file_url=#{fileUrl}
            </if>
            <if test="connectId != null ">
                and connect_id=#{connectId}
            </if>
            <if test="uploadId != null ">
                and upload_id=#{uploadId}
            </if>
         </where>
    </select>
    <select id="getFiles" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from filing_file where locate(#{id},connect_id) &gt;0 and del_flag = '0'
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.file.model.FilingFile">
        insert into filing_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="createBy != null">
                    create_by,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="updateBy != null">
                    update_by,
                </if>
                <if test="updateTime != null">
                    update_time,
                </if>
                <if test="remarks != null">
                    remarks,
                </if>
                <if test="delFlag != null">
                    del_flag,
                </if>
                <if test="fileMd5 != null">
                    file_md5,
                </if>
                <if test="fileName != null">
                    file_name,
                </if>
                <if test="fileType != null">
                    file_type,
                </if>
                <if test="fileSuffix != null">
                    file_suffix,
                </if>
                <if test="fileTag != null">
                    file_tag,
                </if>
                <if test="filePath != null">
                    file_path,
                </if>
                <if test="fileUrl != null">
                    file_url,
                </if>
                <if test="connectId != null">
                    connect_id,
                </if>
                <if test="uploadId != null">
                    upload_id,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if>
            <if test="fileMd5 != null ">
                #{fileMd5},
            </if>
            <if test="fileName != null ">
                #{fileName},
            </if>
            <if test="fileType != null ">
                #{fileType},
            </if>
            <if test="fileSuffix != null ">
                #{fileSuffix},
            </if>
            <if test="fileTag != null ">
                #{fileTag},
            </if>
            <if test="filePath != null ">
                #{filePath},
            </if>
            <if test="fileUrl != null ">
                #{fileUrl},
            </if>
            <if test="connectId != null ">
                #{connectId},
            </if>
            <if test="uploadId != null ">
                #{uploadId},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.file.model.FilingFile">
        update filing_file
        <set>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
            <if test="fileMd5 != null">
                file_md5=#{fileMd5},
            </if>
            <if test="fileName != null">
                file_name=#{fileName},
            </if>
            <if test="fileType != null">
                file_type=#{fileType},
            </if>
            <if test="fileSuffix != null">
                file_suffix=#{fileSuffix},
            </if>
            <if test="fileTag != null">
                file_tag=#{fileTag},
            </if>
            <if test="filePath != null">
                file_path=#{filePath},
            </if>
            <if test="fileUrl != null">
                file_url=#{fileUrl},
            </if>
            <if test="connectId != null">
                connect_id=#{connectId},
            </if>
            <if test="uploadId != null">
                upload_id=#{uploadId},
            </if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update filing_file set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update filing_file set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>
    <update id="updateFile">
        update filing_file
        <set>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
            <if test="fileMd5 != null">
                file_md5=#{fileMd5},
            </if>
            <if test="fileName != null">
                file_name=#{fileName},
            </if>
            <if test="fileType != null">
                file_type=#{fileType},
            </if>
            <if test="fileSuffix != null">
                file_suffix=#{fileSuffix},
            </if>
            <if test="fileTag != null">
                file_tag=#{fileTag},
            </if>
            <if test="filePath != null">
                file_path=#{filePath},
            </if>
            <if test="fileUrl != null">
                file_url=#{fileUrl},
            </if>
            <if test="uploadId != null">
                upload_id=#{uploadId},
            </if>
        </set>
        WHERE connect_id=#{connectId}
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into filing_file(id,  create_by,  create_time,  update_by,  update_time,  remarks,  del_flag,  file_md5,  file_name,  file_type,  file_suffix,  file_tag,  file_path,  file_url,  connect_id,  upload_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.createBy},  #{item.createTime},  #{item.updateBy},  #{item.updateTime},  #{item.remarks},  #{item.delFlag},  #{item.fileMd5},  #{item.fileName},  #{item.fileType},  #{item.fileSuffix},  #{item.fileTag},  #{item.filePath},  #{item.fileUrl},  #{item.connectId},  #{item.uploadId})
        </foreach>
    </insert>

</mapper>