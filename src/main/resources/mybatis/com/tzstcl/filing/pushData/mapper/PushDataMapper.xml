<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.pushData.mapper.PushDataMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.pushData.model.PushData" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="business_data_id" property="businessDataId" jdbcType="VARCHAR" />
        <result column="area_code" property="areaCode" jdbcType="VARCHAR" />
        <result column="cert_num" property="certNum" jdbcType="VARCHAR" />
        <result column="issu_auth_name" property="issuAuthName" jdbcType="VARCHAR" />
        <result column="issu_auth_code" property="issuAuthCode" jdbcType="VARCHAR" />
        <result column="issu_date" property="issuDate" jdbcType="VARCHAR" />
        <result column="device_category_code" property="deviceCategoryCode" jdbcType="VARCHAR" />
        <result column="device_model" property="deviceModel" jdbcType="VARCHAR" />
        <result column="factory_num" property="factoryNum" jdbcType="VARCHAR" />
        <result column="record_num" property="recordNum" jdbcType="VARCHAR" />
        <result column="manufacture_corp_name" property="manufactureCorpName" jdbcType="VARCHAR" />
        <result column="manufacture_corp_code" property="manufactureCorpCode" jdbcType="VARCHAR" />
        <result column="property_corp_name" property="propertyCorpName" jdbcType="VARCHAR" />
        <result column="property_corp_code" property="propertyCorpCode" jdbcType="VARCHAR" />
        <result column="project_name" property="projectName" jdbcType="VARCHAR" />
        <result column="project_location" property="projectLocation" jdbcType="VARCHAR" />
        <result column="project_area_code" property="projectAreaCode" jdbcType="VARCHAR" />
        <result column="apply_construction_permit" property="applyConstructionPermit" jdbcType="VARCHAR" />
        <result column="construction_permit_num" property="constructionPermitNum" jdbcType="VARCHAR" />
        <result column="use_corp_list" property="useCorpList" jdbcType="LONGVARCHAR" />
        <result column="maintenance_corp_name" property="maintenanceCorpName" jdbcType="VARCHAR" />
        <result column="maintenance_corp_code" property="maintenanceCorpCode" jdbcType="VARCHAR" />
        <result column="use_corp_manager" property="useCorpManager" jdbcType="VARCHAR" />
        <result column="use_corp_manager_id" property="useCorpManagerId" jdbcType="VARCHAR" />
        <result column="install_corp_name" property="installCorpName" jdbcType="VARCHAR" />
        <result column="install_corp_code" property="installCorpCode" jdbcType="VARCHAR" />
        <result column="test_corp_name" property="testCorpName" jdbcType="VARCHAR" />
        <result column="test_corp_code" property="testCorpCode" jdbcType="VARCHAR" />
        <result column="test_date" property="testDate" jdbcType="VARCHAR" />
        <result column="cert_status" property="certStatus" jdbcType="VARCHAR" />
        <result column="cert_status_description" property="certStatusDescription" jdbcType="VARCHAR" />
        <result column="associated_cert_id" property="associatedCertId" jdbcType="VARCHAR" />
        <result column="business_information" property="businessInformation" jdbcType="VARCHAR" />
        <result column="operate_type" property="operateType" jdbcType="VARCHAR" />
        <result column="push_flag" property="pushFlag" jdbcType="VARCHAR" />
        <result column="push_time" property="pushTime" jdbcType="TIMESTAMP" />

        <result column="install_date" property="installDate" jdbcType="VARCHAR" />
        <result column="install_position" property="installPosition" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        business_data_id,
        area_code,
        cert_num,
        issu_auth_name,
        issu_auth_code,
        issu_date,
        device_category_code,
        device_model,
        factory_num,
        record_num,
        manufacture_corp_name,
        manufacture_corp_code,
        property_corp_name,
        property_corp_code,
        project_name,
        project_location,
        project_area_code,
        apply_construction_permit,
        construction_permit_num,
        use_corp_list,
        maintenance_corp_name,
        maintenance_corp_code,
        use_corp_manager,
        use_corp_manager_id,
        install_corp_name,
        install_corp_code,
        test_corp_name,
        test_corp_code,
        test_date,
        cert_status,
        cert_status_description,
        associated_cert_id,
        business_information,
        operate_type,
        push_flag,
        push_time,
        id,
        install_date,
        install_position
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from push_data
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.pushData.model.PushData">
        select
        <include refid="Base_Column_List" />
        from push_data
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.pushData.model.PushData">
        select
        <include refid="Base_Column_List" />
        from push_data
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="businessDataId != null ">
                and business_data_id=#{businessDataId}
            </if>
            <if test="areaCode != null ">
                and area_code=#{areaCode}
            </if>
            <if test="certNum != null ">
                and cert_num=#{certNum}
            </if>
            <if test="issuAuthName != null ">
                and issu_auth_name=#{issuAuthName}
            </if>
            <if test="issuAuthCode != null ">
                and issu_auth_code=#{issuAuthCode}
            </if>
            <if test="issuDate != null ">
                and issu_date=#{issuDate}
            </if>
            <if test="deviceCategoryCode != null ">
                and device_category_code=#{deviceCategoryCode}
            </if>
            <if test="deviceModel != null ">
                and device_model=#{deviceModel}
            </if>
            <if test="factoryNum != null ">
                and factory_num=#{factoryNum}
            </if>
            <if test="recordNum != null ">
                and record_num=#{recordNum}
            </if>
            <if test="manufactureCorpName != null ">
                and manufacture_corp_name=#{manufactureCorpName}
            </if>
            <if test="manufactureCorpCode != null ">
                and manufacture_corp_code=#{manufactureCorpCode}
            </if>
            <if test="propertyCorpName != null ">
                and property_corp_name=#{propertyCorpName}
            </if>
            <if test="propertyCorpCode != null ">
                and property_corp_code=#{propertyCorpCode}
            </if>
            <if test="projectName != null ">
                and project_name=#{projectName}
            </if>
            <if test="projectLocation != null ">
                and project_location=#{projectLocation}
            </if>
            <if test="projectAreaCode != null ">
                and project_area_code=#{projectAreaCode}
            </if>
            <if test="applyConstructionPermit != null ">
                and apply_construction_permit=#{applyConstructionPermit}
            </if>
            <if test="constructionPermitNum != null ">
                and construction_permit_num=#{constructionPermitNum}
            </if>
            <if test="useCorpList != null ">
                and use_corp_list=#{useCorpList}
            </if>
            <if test="maintenanceCorpName != null ">
                and maintenance_corp_name=#{maintenanceCorpName}
            </if>
            <if test="maintenanceCorpCode != null ">
                and maintenance_corp_code=#{maintenanceCorpCode}
            </if>
            <if test="useCorpManager != null ">
                and use_corp_manager=#{useCorpManager}
            </if>
            <if test="useCorpManagerId != null ">
                and use_corp_manager_id=#{useCorpManagerId}
            </if>
            <if test="installCorpName != null ">
                and install_corp_name=#{installCorpName}
            </if>
            <if test="installCorpCode != null ">
                and install_corp_code=#{installCorpCode}
            </if>
            <if test="testCorpName != null ">
                and test_corp_name=#{testCorpName}
            </if>
            <if test="testCorpCode != null ">
                and test_corp_code=#{testCorpCode}
            </if>
            <if test="testDate != null ">
                and test_date=#{testDate}
            </if>
            <if test="certStatus != null ">
                and cert_status=#{certStatus}
            </if>
            <if test="certStatusDescription != null ">
                and cert_status_description=#{certStatusDescription}
            </if>
            <if test="associatedCertId != null ">
                and associated_cert_id=#{associatedCertId}
            </if>
            <if test="businessInformation != null ">
                and business_information=#{businessInformation}
            </if>
            <if test="operateType != null ">
                and operate_type=#{operateType}
            </if>
            <if test="pushFlag != null ">
                and push_flag=#{pushFlag}
            </if>
            <if test="pushTime != null ">
                and push_time=#{pushTime}
            </if>
            <if test="installDate != null ">
                and install_date=#{installDate}
            </if>
            <if test="installPosition != null ">
                and install_position=#{installPosition}
            </if>
         </where>
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.pushData.model.PushData">
        insert into push_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="businessDataId != null">
                    business_data_id,
                </if>
                <if test="areaCode != null">
                    area_code,
                </if>
                <if test="certNum != null">
                    cert_num,
                </if>
                <if test="issuAuthName != null">
                    issu_auth_name,
                </if>
                <if test="issuAuthCode != null">
                    issu_auth_code,
                </if>
                <if test="issuDate != null">
                    issu_date,
                </if>
                <if test="deviceCategoryCode != null">
                    device_category_code,
                </if>
                <if test="deviceModel != null">
                    device_model,
                </if>
                <if test="factoryNum != null">
                    factory_num,
                </if>
                <if test="recordNum != null">
                    record_num,
                </if>
                <if test="manufactureCorpName != null">
                    manufacture_corp_name,
                </if>
                <if test="manufactureCorpCode != null">
                    manufacture_corp_code,
                </if>
                <if test="propertyCorpName != null">
                    property_corp_name,
                </if>
                <if test="propertyCorpCode != null">
                    property_corp_code,
                </if>
                <if test="projectName != null">
                    project_name,
                </if>
                <if test="projectLocation != null">
                    project_location,
                </if>
                <if test="projectAreaCode != null">
                    project_area_code,
                </if>
                <if test="applyConstructionPermit != null">
                    apply_construction_permit,
                </if>
                <if test="constructionPermitNum != null">
                    construction_permit_num,
                </if>
                <if test="useCorpList != null">
                    use_corp_list,
                </if>
                <if test="maintenanceCorpName != null">
                    maintenance_corp_name,
                </if>
                <if test="maintenanceCorpCode != null">
                    maintenance_corp_code,
                </if>
                <if test="useCorpManager != null">
                    use_corp_manager,
                </if>
                <if test="useCorpManagerId != null">
                    use_corp_manager_id,
                </if>
                <if test="installCorpName != null">
                    install_corp_name,
                </if>
                <if test="installCorpCode != null">
                    install_corp_code,
                </if>
                <if test="testCorpName != null">
                    test_corp_name,
                </if>
                <if test="testCorpCode != null">
                    test_corp_code,
                </if>
                <if test="testDate != null">
                    test_date,
                </if>
                <if test="certStatus != null">
                    cert_status,
                </if>
                <if test="certStatusDescription != null">
                    cert_status_description,
                </if>
                <if test="associatedCertId != null">
                    associated_cert_id,
                </if>
                <if test="businessInformation != null">
                    business_information,
                </if>
                <if test="operateType != null">
                    operate_type,
                </if>
                <if test="pushFlag != null">
                    push_flag,
                </if>
                <if test="pushTime != null">
                    push_time,
                </if>
                <if test="id != null">
                    id,
                </if>
                <if test="installDate != null">
                    install_date,
                </if>
                <if test="installPosition != null">
                    install_position,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessDataId != null ">
                #{businessDataId},
            </if>
            <if test="areaCode != null ">
                #{areaCode},
            </if>
            <if test="certNum != null ">
                #{certNum},
            </if>
            <if test="issuAuthName != null ">
                #{issuAuthName},
            </if>
            <if test="issuAuthCode != null ">
                #{issuAuthCode},
            </if>
            <if test="issuDate != null ">
                #{issuDate},
            </if>
            <if test="deviceCategoryCode != null ">
                #{deviceCategoryCode},
            </if>
            <if test="deviceModel != null ">
                #{deviceModel},
            </if>
            <if test="factoryNum != null ">
                #{factoryNum},
            </if>
            <if test="recordNum != null ">
                #{recordNum},
            </if>
            <if test="manufactureCorpName != null ">
                #{manufactureCorpName},
            </if>
            <if test="manufactureCorpCode != null ">
                #{manufactureCorpCode},
            </if>
            <if test="propertyCorpName != null ">
                #{propertyCorpName},
            </if>
            <if test="propertyCorpCode != null ">
                #{propertyCorpCode},
            </if>
            <if test="projectName != null ">
                #{projectName},
            </if>
            <if test="projectLocation != null ">
                #{projectLocation},
            </if>
            <if test="projectAreaCode != null ">
                #{projectAreaCode},
            </if>
            <if test="applyConstructionPermit != null ">
                #{applyConstructionPermit},
            </if>
            <if test="constructionPermitNum != null ">
                #{constructionPermitNum},
            </if>
            <if test="useCorpList != null ">
                #{useCorpList},
            </if>
            <if test="maintenanceCorpName != null ">
                #{maintenanceCorpName},
            </if>
            <if test="maintenanceCorpCode != null ">
                #{maintenanceCorpCode},
            </if>
            <if test="useCorpManager != null ">
                #{useCorpManager},
            </if>
            <if test="useCorpManagerId != null ">
                #{useCorpManagerId},
            </if>
            <if test="installCorpName != null ">
                #{installCorpName},
            </if>
            <if test="installCorpCode != null ">
                #{installCorpCode},
            </if>
            <if test="testCorpName != null ">
                #{testCorpName},
            </if>
            <if test="testCorpCode != null ">
                #{testCorpCode},
            </if>
            <if test="testDate != null ">
                #{testDate},
            </if>
            <if test="certStatus != null ">
                #{certStatus},
            </if>
            <if test="certStatusDescription != null ">
                #{certStatusDescription},
            </if>
            <if test="associatedCertId != null ">
                #{associatedCertId},
            </if>
            <if test="businessInformation != null ">
                #{businessInformation},
            </if>
            <if test="operateType != null ">
                #{operateType},
            </if>
            <if test="pushFlag != null ">
                #{pushFlag},
            </if>
            <if test="pushTime != null ">
                #{pushTime},
            </if>
            <if test="id != null ">
                #{id},
            </if>
            <if test="installDate != null ">
                #{installDate},
            </if>
            <if test="installPosition != null ">
                #{installPosition},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.pushData.model.PushData">
        update push_data
        <set>
            <if test="businessDataId != null">
                business_data_id=#{businessDataId},
            </if>
            <if test="areaCode != null">
                area_code=#{areaCode},
            </if>
            <if test="certNum != null">
                cert_num=#{certNum},
            </if>
            <if test="issuAuthName != null">
                issu_auth_name=#{issuAuthName},
            </if>
            <if test="issuAuthCode != null">
                issu_auth_code=#{issuAuthCode},
            </if>
            <if test="issuDate != null">
                issu_date=#{issuDate},
            </if>
            <if test="deviceCategoryCode != null">
                device_category_code=#{deviceCategoryCode},
            </if>
            <if test="deviceModel != null">
                device_model=#{deviceModel},
            </if>
            <if test="factoryNum != null">
                factory_num=#{factoryNum},
            </if>
            <if test="recordNum != null">
                record_num=#{recordNum},
            </if>
            <if test="manufactureCorpName != null">
                manufacture_corp_name=#{manufactureCorpName},
            </if>
            <if test="manufactureCorpCode != null">
                manufacture_corp_code=#{manufactureCorpCode},
            </if>
            <if test="propertyCorpName != null">
                property_corp_name=#{propertyCorpName},
            </if>
            <if test="propertyCorpCode != null">
                property_corp_code=#{propertyCorpCode},
            </if>
            <if test="projectName != null">
                project_name=#{projectName},
            </if>
            <if test="projectLocation != null">
                project_location=#{projectLocation},
            </if>
            <if test="projectAreaCode != null">
                project_area_code=#{projectAreaCode},
            </if>
            <if test="applyConstructionPermit != null">
                apply_construction_permit=#{applyConstructionPermit},
            </if>
            <if test="constructionPermitNum != null">
                construction_permit_num=#{constructionPermitNum},
            </if>
            <if test="useCorpList != null">
                use_corp_list=#{useCorpList},
            </if>
            <if test="maintenanceCorpName != null">
                maintenance_corp_name=#{maintenanceCorpName},
            </if>
            <if test="maintenanceCorpCode != null">
                maintenance_corp_code=#{maintenanceCorpCode},
            </if>
            <if test="useCorpManager != null">
                use_corp_manager=#{useCorpManager},
            </if>
            <if test="useCorpManagerId != null">
                use_corp_manager_id=#{useCorpManagerId},
            </if>
            <if test="installCorpName != null">
                install_corp_name=#{installCorpName},
            </if>
            <if test="installCorpCode != null">
                install_corp_code=#{installCorpCode},
            </if>
            <if test="testCorpName != null">
                test_corp_name=#{testCorpName},
            </if>
            <if test="testCorpCode != null">
                test_corp_code=#{testCorpCode},
            </if>
            <if test="testDate != null">
                test_date=#{testDate},
            </if>
            <if test="certStatus != null">
                cert_status=#{certStatus},
            </if>
            <if test="certStatusDescription != null">
                cert_status_description=#{certStatusDescription},
            </if>
            <if test="associatedCertId != null">
                associated_cert_id=#{associatedCertId},
            </if>
            <if test="businessInformation != null">
                business_information=#{businessInformation},
            </if>
            <if test="operateType != null">
                operate_type=#{operateType},
            </if>
            <if test="pushFlag != null">
                push_flag=#{pushFlag},
            </if>
            <if test="pushTime != null">
                push_time=#{pushTime},
            </if>
            <if test="installDate != null">
                install_date=#{installDate},
            </if>
            <if test="installPosition != null">
                install_position=#{installPosition},
            </if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update push_data set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update push_data set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into push_data(business_data_id,  area_code,  cert_num,  issu_auth_name,  issu_auth_code,  issu_date,  device_category_code,  device_model,  factory_num,  record_num,  manufacture_corp_name,  manufacture_corp_code,  property_corp_name,  property_corp_code,  project_name,  project_location,  project_area_code,  apply_construction_permit,  construction_permit_num,  use_corp_list,  maintenance_corp_name,  maintenance_corp_code,  use_corp_manager,  use_corp_manager_id,  install_corp_name,  install_corp_code,  test_corp_name,  test_corp_code,  test_date,  cert_status,  cert_status_description,  associated_cert_id,  business_information,  operate_type,  push_flag,  push_time,  id,  install_date,  install_position) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.businessDataId},  #{item.areaCode},  #{item.certNum},  #{item.issuAuthName},  #{item.issuAuthCode},  #{item.issuDate},  #{item.deviceCategoryCode},  #{item.deviceModel},  #{item.factoryNum},  #{item.recordNum},  #{item.manufactureCorpName},  #{item.manufactureCorpCode},  #{item.propertyCorpName},  #{item.propertyCorpCode},  #{item.projectName},  #{item.projectLocation},  #{item.projectAreaCode},  #{item.applyConstructionPermit},  #{item.constructionPermitNum},  #{item.useCorpList},  #{item.maintenanceCorpName},  #{item.maintenanceCorpCode},  #{item.useCorpManager},  #{item.useCorpManagerId},  #{item.installCorpName},  #{item.installCorpCode},  #{item.testCorpName},  #{item.testCorpCode},  #{item.testDate},  #{item.certStatus},  #{item.certStatusDescription},  #{item.associatedCertId},  #{item.businessInformation},  #{item.operateType},  #{item.pushFlag},  #{item.pushTime},  #{item.id},  #{item.installDate},  #{item.installPosition})
        </foreach>
    </insert>

</mapper>
