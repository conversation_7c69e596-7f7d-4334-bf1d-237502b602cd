<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.pushResult.mapper.PushResultMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.pushResult.model.PushResult" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="association_id" property="associationId" jdbcType="VARCHAR" />
        <result column="return_code" property="returnCode" jdbcType="VARCHAR" />
        <result column="return_msg" property="returnMsg" jdbcType="VARCHAR" />
        <result column="return_data" property="returnData" jdbcType="LONGVARCHAR" />
        <result column="create_date" property="createDate" jdbcType="VARCHAR" />
        <result column="creat_at" property="creatAt" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        association_id,
        return_code,
        return_msg,
        return_data,
        create_date,
        creat_at
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from push_result
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.pushResult.model.PushResult">
        select
        <include refid="Base_Column_List" />
        from push_result
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.pushResult.model.PushResult">
        select
        <include refid="Base_Column_List" />
        from push_result
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="associationId != null ">
                and association_id=#{associationId}
            </if>
            <if test="returnCode != null ">
                and return_code=#{returnCode}
            </if>
            <if test="returnMsg != null ">
                and return_msg=#{returnMsg}
            </if>
            <if test="returnData != null ">
                and return_data=#{returnData}
            </if>
            <if test="createDate != null ">
                and create_date=#{createDate}
            </if>
            <if test="creatAt != null ">
                and creat_at=#{creatAt}
            </if>
         </where>
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.pushResult.model.PushResult">
        insert into push_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="associationId != null">
                    association_id,
                </if>
                <if test="returnCode != null">
                    return_code,
                </if>
                <if test="returnMsg != null">
                    return_msg,
                </if>
                <if test="returnData != null">
                    return_data,
                </if>
                <if test="createDate != null">
                    create_date,
                </if>
                <if test="creatAt != null">
                    creat_at,
                </if>
            <if test="type != null">
                type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="associationId != null ">
                #{associationId},
            </if>
            <if test="returnCode != null ">
                #{returnCode},
            </if>
            <if test="returnMsg != null ">
                #{returnMsg},
            </if>
            <if test="returnData != null ">
                #{returnData},
            </if>
            <if test="createDate != null ">
                #{createDate},
            </if>
            <if test="creatAt != null ">
                #{creatAt},
            </if>
            <if test="type != null">
                #{type},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.pushResult.model.PushResult">
        update push_result
        <set>
            <if test="associationId != null">
                association_id=#{associationId},
            </if>
            <if test="returnCode != null">
                return_code=#{returnCode},
            </if>
            <if test="returnMsg != null">
                return_msg=#{returnMsg},
            </if>
            <if test="returnData != null">
                return_data=#{returnData},
            </if>
            <if test="createDate != null">
                create_date=#{createDate},
            </if>
            <if test="creatAt != null">
                creat_at=#{creatAt},
            </if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update push_result set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update push_result set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into push_result(id,  association_id,  return_code,  return_msg,  return_data,  create_date,  creat_at) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.associationId},  #{item.returnCode},  #{item.returnMsg},  #{item.returnData},  #{item.createDate},  #{item.creatAt})
        </foreach>
    </insert>

</mapper>
