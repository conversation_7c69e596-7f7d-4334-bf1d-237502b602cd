<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.equipmentFiling.mapper.FilingEquipmentFilingMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="property_unit" property="propertyUnit" jdbcType="VARCHAR" />
        <result column="property_unit_code" property="propertyUnitCode" jdbcType="VARCHAR" />
        <result column="device_name" property="deviceName" jdbcType="VARCHAR" />
        <result column="specification_model" property="specificationModel" jdbcType="VARCHAR" />
        <result column="manufacturer" property="manufacturer" jdbcType="VARCHAR" />
        <result column="factory_time" property="factoryTime" jdbcType="VARCHAR" />
        <result column="manufacturing_license_number" property="manufacturingLicenseNumber" jdbcType="VARCHAR" />
        <result column="filing_date" property="filingDate" jdbcType="DATE" />
        <result column="factory_license_number" property="factoryLicenseNumber" jdbcType="VARCHAR" />
        <result column="purchase_time" property="purchaseTime" jdbcType="VARCHAR" />
        <result column="unit_address" property="unitAddress" jdbcType="VARCHAR" />
        <result column="legal_representative" property="legalRepresentative" jdbcType="VARCHAR" />
        <result column="legal_representative_contact_number" property="legalRepresentativeContactNumber" jdbcType="VARCHAR" />
        <result column="technical_director" property="technicalDirector" jdbcType="VARCHAR" />
        <result column="technical_director_contact_number" property="technicalDirectorContactNumber" jdbcType="VARCHAR" />
        <result column="equipment_manager" property="equipmentManager" jdbcType="VARCHAR" />
        <result column="equipment_manager_contact_number" property="equipmentManagerContactNumber" jdbcType="VARCHAR" />
        <result column="lifting_weight" property="liftingWeight" jdbcType="VARCHAR" />
        <result column="equipment_type" property="equipmentType" jdbcType="CHAR" />
        <result column="filing_code" property="filingCode" jdbcType="VARCHAR" />
        <result column="cancellation_status" property="cancellationStatus" jdbcType="INTEGER" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        property_unit,
        property_unit_code,
        device_name,
        specification_model,
        manufacturer,
        factory_time,
        manufacturing_license_number,
        filing_date,
        factory_license_number,
        purchase_time,
        unit_address,
        legal_representative,
        legal_representative_contact_number,
        technical_director,
        technical_director_contact_number,
        equipment_manager,
        equipment_manager_contact_number,
        lifting_weight,
        equipment_type,
        filing_code,
        cancellation_status,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_filing
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_filing
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_filing
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="propertyUnit != null ">
                and property_unit like concat('%', #{propertyUnit}, '%')
            </if>
            <if test="propertyUnitCode != null ">
                and property_unit_code=#{propertyUnitCode}
            </if>
            <if test="deviceName != null ">
                and device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="specificationModel != null ">
                and specification_model=#{specificationModel}
            </if>
            <if test="manufacturer != null ">
                and manufacturer=#{manufacturer}
            </if>
            <if test="factoryTime != null ">
                and factory_time=#{factoryTime}
            </if>
            <if test="manufacturingLicenseNumber != null ">
                and manufacturing_license_number=#{manufacturingLicenseNumber}
            </if>
            <if test="filingDate != null ">
                and filing_date=#{filingDate}
            </if>
            <if test="factoryLicenseNumber != null ">
                and factory_license_number=#{factoryLicenseNumber}
            </if>
            <if test="purchaseTime != null ">
                and purchase_time=#{purchaseTime}
            </if>
            <if test="unitAddress != null ">
                and unit_address=#{unitAddress}
            </if>
            <if test="legalRepresentative != null ">
                and legal_representative=#{legalRepresentative}
            </if>
            <if test="legalRepresentativeContactNumber != null ">
                and legal_representative_contact_number=#{legalRepresentativeContactNumber}
            </if>
            <if test="technicalDirector != null ">
                and technical_director=#{technicalDirector}
            </if>
            <if test="technicalDirectorContactNumber != null ">
                and technical_director_contact_number=#{technicalDirectorContactNumber}
            </if>
            <if test="equipmentManager != null ">
                and equipment_manager=#{equipmentManager}
            </if>
            <if test="equipmentManagerContactNumber != null ">
                and equipment_manager_contact_number=#{equipmentManagerContactNumber}
            </if>
            <if test="liftingWeight != null ">
                and lifting_weight=#{liftingWeight}
            </if><if test="equipmentType != null ">
                and equipment_type=#{equipmentType}
            </if>
            <if test="filingCode != null ">
                and filing_code=#{filingCode}
            </if>
            <if test="cancellationStatus != null ">
                and cancellation_status=#{cancellationStatus}
            </if>
         </where>
         order by update_time desc
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling">
        insert into filing_equipment_filing
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="propertyUnit != null">
                    property_unit,
                </if>
                <if test="propertyUnitCode != null">
                    property_unit_code,
                </if>
                <if test="deviceName != null">
                    device_name,
                </if>
                <if test="specificationModel != null">
                    specification_model,
                </if>
                <if test="manufacturer != null">
                    manufacturer,
                </if>
                <if test="factoryTime != null">
                    factory_time,
                </if>
                <if test="manufacturingLicenseNumber != null">
                    manufacturing_license_number,
                </if>
                <if test="filingDate != null">
                    filing_date,
                </if>
                <if test="factoryLicenseNumber != null">
                    factory_license_number,
                </if>
                <if test="purchaseTime != null">
                    purchase_time,
                </if>
                <if test="unitAddress != null">
                    unit_address,
                </if>
                <if test="legalRepresentative != null">
                    legal_representative,
                </if>
                <if test="legalRepresentativeContactNumber != null">
                    legal_representative_contact_number,
                </if>
                <if test="technicalDirector != null">
                    technical_director,
                </if>
                <if test="technicalDirectorContactNumber != null">
                    technical_director_contact_number,
                </if>
                <if test="equipmentManager != null">
                    equipment_manager,
                </if>
                <if test="equipmentManagerContactNumber != null">
                    equipment_manager_contact_number,
                </if>
                <if test="liftingWeight != null">
                    lifting_weight,
                </if>
                <if test="equipmentType != null">
                    equipment_type,
                </if>
                <if test="filingCode != null">
                    filing_code,
                </if>
                <if test="cancellationStatus != null">
                    cancellation_status,
                </if>
                <if test="createBy != null">
                    create_by,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="updateBy != null">
                    update_by,
                </if>
                <if test="updateTime != null">
                    update_time,
                </if>
                <if test="remarks != null">
                    remarks,
                </if>
                <if test="delFlag != null">
                    del_flag,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="propertyUnit != null ">
                #{propertyUnit},
            </if>
            <if test="propertyUnitCode != null ">
                #{propertyUnitCode},
            </if>
            <if test="deviceName != null ">
                #{deviceName},
            </if>
            <if test="specificationModel != null ">
                #{specificationModel},
            </if>
            <if test="manufacturer != null ">
                #{manufacturer},
            </if>
            <if test="factoryTime != null ">
                #{factoryTime},
            </if>
            <if test="manufacturingLicenseNumber != null ">
                #{manufacturingLicenseNumber},
            </if>
            <if test="filingDate != null ">
                #{filingDate},
            </if>
            <if test="factoryLicenseNumber != null ">
                #{factoryLicenseNumber},
            </if>
            <if test="purchaseTime != null ">
                #{purchaseTime},
            </if>
            <if test="unitAddress != null ">
                #{unitAddress},
            </if>
            <if test="legalRepresentative != null ">
                #{legalRepresentative},
            </if>
            <if test="legalRepresentativeContactNumber != null ">
                #{legalRepresentativeContactNumber},
            </if>
            <if test="technicalDirector != null ">
                #{technicalDirector},
            </if>
            <if test="technicalDirectorContactNumber != null ">
                #{technicalDirectorContactNumber},
            </if>
            <if test="equipmentManager != null ">
                #{equipmentManager},
            </if>
            <if test="equipmentManagerContactNumber != null ">
                #{equipmentManagerContactNumber},
            </if>
            <if test="liftingWeight != null ">
                #{liftingWeight},
            </if>
            <if test="equipmentType != null">
                #{equipmentType},
            </if>
            <if test="filingCode != null ">
                #{filingCode},
            </if>
            <if test="cancellationStatus != null ">
                #{cancellationStatus},
            </if>
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.equipmentFiling.model.FilingEquipmentFiling">
        update filing_equipment_filing
        <set>
            <if test="propertyUnit != null">
                property_unit=#{propertyUnit},
            </if>
            <if test="propertyUnitCode != null">
                property_unit_code=#{propertyUnitCode},
            </if>
            <if test="deviceName != null">
                device_name=#{deviceName},
            </if>
            <if test="specificationModel != null">
                specification_model=#{specificationModel},
            </if>
            <if test="manufacturer != null">
                manufacturer=#{manufacturer},
            </if>
            <if test="factoryTime != null">
                factory_time=#{factoryTime},
            </if>
            <if test="manufacturingLicenseNumber != null">
                manufacturing_license_number=#{manufacturingLicenseNumber},
            </if>
            <if test="filingDate != null">
                filing_date=#{filingDate},
            </if>
            <if test="factoryLicenseNumber != null">
                factory_license_number=#{factoryLicenseNumber},
            </if>
            <if test="purchaseTime != null">
                purchase_time=#{purchaseTime},
            </if>
            <if test="unitAddress != null">
                unit_address=#{unitAddress},
            </if>
            <if test="legalRepresentative != null">
                legal_representative=#{legalRepresentative},
            </if>
            <if test="legalRepresentativeContactNumber != null">
                legal_representative_contact_number=#{legalRepresentativeContactNumber},
            </if>
            <if test="technicalDirector != null">
                technical_director=#{technicalDirector},
            </if>
            <if test="technicalDirectorContactNumber != null">
                technical_director_contact_number=#{technicalDirectorContactNumber},
            </if>
            <if test="equipmentManager != null">
                equipment_manager=#{equipmentManager},
            </if>
            <if test="equipmentManagerContactNumber != null">
                equipment_manager_contact_number=#{equipmentManagerContactNumber},
            </if>
            <if test="liftingWeight != null">
                lifting_weight=#{liftingWeight},
            </if>
            <if test="equipmentType != null">
                equipment_type = #{equipmentType},
            </if>
            <if test="filingCode != null">
                filing_code=#{filingCode},
            </if>
            <if test="cancellationStatus != null">
                cancellation_status=#{cancellationStatus},
            </if>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update filing_equipment_filing set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update filing_equipment_filing set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into filing_equipment_filing(id,  property_unit, property_unit_code,  device_name,  specification_model,  manufacturer,  factory_time,  manufacturing_license_number,  filing_date,  factory_license_number,  purchase_time,  unit_address,  legal_representative,  legal_representative_contact_number,  technical_director,  technical_director_contact_number,  equipment_manager,  equipment_manager_contact_number,  lifting_weight,  filing_code,  cancellation_status,  create_by,  create_time,  update_by,  update_time,  remarks,  del_flag) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.propertyUnit}, #{item.propertyUnitCode},  #{item.deviceName},  #{item.specificationModel},  #{item.manufacturer},  #{item.factoryTime},  #{item.manufacturingLicenseNumber},  #{item.filingDate},  #{item.factoryLicenseNumber},  #{item.purchaseTime},  #{item.unitAddress},  #{item.legalRepresentative},  #{item.legalRepresentativeContactNumber},  #{item.technicalDirector},  #{item.technicalDirectorContactNumber},  #{item.equipmentManager},  #{item.equipmentManagerContactNumber},  #{item.liftingWeight},  #{item.filingCode},  #{item.cancellationStatus},  #{item.createBy},  #{item.createTime},  #{item.updateBy},  #{item.updateTime},  #{item.remarks},  #{item.delFlag})
        </foreach>
    </insert>

    <insert id="insertApplication" parameterType="com.tzstcl.filing.equipmentFilingApplication.model.FilingEquipmentFilingApplication">
        insert into filing_equipment_filing
        <trim prefix="(" suffix=")" suffixOverrides=",">
            cancellation_status,
            <if test="id != null">
                id,
            </if>
            <if test="propertyUnit != null">
                property_unit,
            </if>
            <if test="propertyUnitCode != null">
                property_unit_code,
            </if>
            <if test="deviceName != null">
                device_name,
            </if>
            <if test="specificationModel != null">
                specification_model,
            </if>
            <if test="manufacturer != null">
                manufacturer,
            </if>
            <if test="factoryTime != null">
                factory_time,
            </if>
            <if test="manufacturingLicenseNumber != null">
                manufacturing_license_number,
            </if>
            <if test="updateTime != null">
                filing_date,
            </if>
            <if test="factoryLicenseNumber != null">
                factory_license_number,
            </if>
            <if test="purchaseTime != null">
                purchase_time,
            </if>
            <if test="unitAddress != null">
                unit_address,
            </if>
            <if test="legalRepresentative != null">
                legal_representative,
            </if>
            <if test="legalRepresentativeContactNumber != null">
                legal_representative_contact_number,
            </if>
            <if test="technicalDirector != null">
                technical_director,
            </if>
            <if test="technicalDirectorContactNumber != null">
                technical_director_contact_number,
            </if>
            <if test="equipmentManager != null">
                equipment_manager,
            </if>
            <if test="equipmentManagerContactNumber != null">
                equipment_manager_contact_number,
            </if>
            <if test="liftingWeight != null">
                lifting_weight,
            </if>
            <if test="equipmentType != null">
                equipment_type,
            </if>
            <if test="filingCode != null">
                filing_code,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            0,
            <if test="id != null ">
                #{id},
            </if>
            <if test="propertyUnit != null ">
                #{propertyUnit},
            </if>
            <if test="propertyUnitCode != null ">
                #{propertyUnitCode},
            </if>
            <if test="deviceName != null ">
                #{deviceName},
            </if>
            <if test="specificationModel != null ">
                #{specificationModel},
            </if>
            <if test="manufacturer != null ">
                #{manufacturer},
            </if>
            <if test="factoryTime != null ">
                #{factoryTime},
            </if>
            <if test="manufacturingLicenseNumber != null ">
                #{manufacturingLicenseNumber},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="factoryLicenseNumber != null ">
                #{factoryLicenseNumber},
            </if>
            <if test="purchaseTime != null ">
                #{purchaseTime},
            </if>
            <if test="unitAddress != null ">
                #{unitAddress},
            </if>
            <if test="legalRepresentative != null ">
                #{legalRepresentative},
            </if>
            <if test="legalRepresentativeContactNumber != null ">
                #{legalRepresentativeContactNumber},
            </if>
            <if test="technicalDirector != null ">
                #{technicalDirector},
            </if>
            <if test="technicalDirectorContactNumber != null ">
                #{technicalDirectorContactNumber},
            </if>
            <if test="equipmentManager != null ">
                #{equipmentManager},
            </if>
            <if test="equipmentManagerContactNumber != null ">
                #{equipmentManagerContactNumber},
            </if>
            <if test="liftingWeight != null ">
                #{liftingWeight},
            </if>
            <if test="equipmentType != null">
                #{equipmentType},
            </if>
            <if test="filingCode != null ">
                #{filingCode},
            </if>
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if>
        </trim>
    </insert>
    <select id="getByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_filing
        where filing_code = #{filingCode}
    </select>
    <select id="getList"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from filing_equipment_filing where cancellation_status = '1' order by update_time desc
    </select>
    <update id="cancel" parameterType="java.lang.String">
        update filing_equipment_filing set cancellation_status = '1'  where filing_code = #{filingCode}
    </update>
</mapper>
