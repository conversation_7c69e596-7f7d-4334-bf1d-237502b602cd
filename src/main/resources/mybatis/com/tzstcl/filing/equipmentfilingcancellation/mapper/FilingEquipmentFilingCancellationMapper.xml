<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.filing.equipmentfilingcancellation.mapper.FilingEquipmentFilingCancellationMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellation" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="property_unit" property="propertyUnit" jdbcType="VARCHAR" />
        <result column="property_unit_code" property="propertyUnitCode" jdbcType="VARCHAR" />
        <result column="device_name" property="deviceName" jdbcType="VARCHAR" />
        <result column="specification_model" property="specificationModel" jdbcType="VARCHAR" />
        <result column="manufacturer" property="manufacturer" jdbcType="VARCHAR" />
        <result column="factory_time" property="factoryTime" jdbcType="VARCHAR" />
        <result column="manufacturing_license_number" property="manufacturingLicenseNumber" jdbcType="VARCHAR" />
        <result column="application_date" property="applicationDate" jdbcType="VARCHAR" />
        <result column="factory_license_number" property="factoryLicenseNumber" jdbcType="VARCHAR" />
        <result column="purchase_time" property="purchaseTime" jdbcType="VARCHAR" />
        <result column="unit_address" property="unitAddress" jdbcType="VARCHAR" />
        <result column="legal_representative" property="legalRepresentative" jdbcType="VARCHAR" />
        <result column="legal_representative_contact_number" property="legalRepresentativeContactNumber" jdbcType="VARCHAR" />
        <result column="technical_director" property="technicalDirector" jdbcType="VARCHAR" />
        <result column="technical_director_contact_number" property="technicalDirectorContactNumber" jdbcType="VARCHAR" />
        <result column="equipment_manager" property="equipmentManager" jdbcType="VARCHAR" />
        <result column="equipment_manager_contact_number" property="equipmentManagerContactNumber" jdbcType="VARCHAR" />
        <result column="lifting_weight" property="liftingWeight" jdbcType="VARCHAR" />
        <result column="equipment_type" property="equipmentType" jdbcType="CHAR" />
        <result column="cancellation_reason" property="cancellationReason" jdbcType="VARCHAR" />
        <result column="cancellation_application_form_path" property="cancellationApplicationFormPath" jdbcType="VARCHAR" />
        <result column="filing_code" property="filingCode" jdbcType="VARCHAR" />
        <result column="filing_paper" property="filingPaper" jdbcType="VARCHAR" />
        <result column="filing_card" property="filingCard" jdbcType="VARCHAR" />
        <result column="audit_status" property="auditStatus" jdbcType="CHAR" />
        <result column="audit_reject" property="auditReject" jdbcType="VARCHAR" />
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="INTEGER" />
        <result column="business_id" property="businessId" />
        <result column="unit_area_code" property="unitAreaCode" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        user_id,
        property_unit,
        property_unit_code,
        device_name,
        specification_model,
        manufacturer,
        factory_time,
        manufacturing_license_number,
        application_date,
        factory_license_number,
        purchase_time,
        unit_address,
        legal_representative,
        legal_representative_contact_number,
        technical_director,
        technical_director_contact_number,
        equipment_manager,
        equipment_manager_contact_number,
        lifting_weight,
        equipment_type,
        cancellation_reason,
        cancellation_application_form_path,
        filing_code,
        filing_paper,
        filing_card,
        audit_status,
        audit_reject,
        audit_time,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag,
        business_id,
        unit_area_code
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_filing_cancellation
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellation">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_filing_cancellation
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellation">
        select
        <include refid="Base_Column_List" />
        from filing_equipment_filing_cancellation
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="userId != null ">
                and user_id=#{userId}
            </if>
            <if test="propertyUnit != null ">
                and property_unit like concat('%', #{propertyUnit}, '%')
            </if>
            <if test="propertyUnitCode != null ">
                and property_unit_code=#{propertyUnitCode}
            </if>
            <if test="deviceName != null ">
                and device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="specificationModel != null ">
                and specification_model=#{specificationModel}
            </if>
            <if test="manufacturer != null ">
                and manufacturer=#{manufacturer}
            </if>
            <if test="factoryTime != null ">
                and factory_time=#{factoryTime}
            </if>
            <if test="manufacturingLicenseNumber != null ">
                and manufacturing_license_number=#{manufacturingLicenseNumber}
            </if>
            <if test="applicationDate != null ">
                and application_date=#{applicationDate}
            </if>
            <if test="factoryLicenseNumber != null ">
                and factory_license_number=#{factoryLicenseNumber}
            </if>
            <if test="purchaseTime != null ">
                and purchase_time=#{purchaseTime}
            </if>
            <if test="unitAddress != null ">
                and unit_address=#{unitAddress}
            </if>
            <if test="legalRepresentative != null ">
                and legal_representative=#{legalRepresentative}
            </if>
            <if test="legalRepresentativeContactNumber != null ">
                and legal_representative_contact_number=#{legalRepresentativeContactNumber}
            </if>
            <if test="technicalDirector != null ">
                and technical_director=#{technicalDirector}
            </if>
            <if test="technicalDirectorContactNumber != null ">
                and technical_director_contact_number=#{technicalDirectorContactNumber}
            </if>
            <if test="equipmentManager != null ">
                and equipment_manager=#{equipmentManager}
            </if>
            <if test="equipmentManagerContactNumber != null ">
                and equipment_manager_contact_number=#{equipmentManagerContactNumber}
            </if>
            <if test="liftingWeight != null ">
                and lifting_weight=#{liftingWeight}
            </if>
            <if test="equipmentType != null">
                and equipment_type = #{equipmentType}
            </if>
            <if test="cancellationReason != null ">
                and cancellation_reason=#{cancellationReason}
            </if>
            <if test="cancellationApplicationFormPath != null ">
                and cancellation_application_form_path=#{cancellationApplicationFormPath}
            </if>
            <if test="filingCode != null ">
                and filing_code like concat('%', #{filingCode}, '%')
            </if>
            <if test="filingPaper != null ">
                and filing_paper=#{filingPaper}
            </if>
            <if test="filingCard != null ">
                and filing_card=#{filingCard}
            </if>
            <if test="auditStatus != null ">
                and audit_status=#{auditStatus}
            </if>
            <if test="auditReject != null ">
                and audit_reject=#{auditReject}
            </if>
            <if test="auditTime != null ">
                and audit_time=#{auditTime}
            </if>
            <if test="businessId != null">
                and business_id=#{businessId}
            </if>
            <if test="unitAreaCode != null">
                and unit_area_code=#{unitAreaCode}
            </if>
         </where>
        order by create_time desc
    </select>

    <insert id="insert" parameterType="com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellation">
        insert into filing_equipment_filing_cancellation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="userId != null">
                    user_id,
                </if>
                <if test="propertyUnit != null">
                    property_unit,
                </if>
                <if test="propertyUnitCode != null">
                    property_unit_code,
                </if>
                <if test="deviceName != null">
                    device_name,
                </if>
                <if test="specificationModel != null">
                    specification_model,
                </if>
                <if test="manufacturer != null">
                    manufacturer,
                </if>
                <if test="factoryTime != null">
                    factory_time,
                </if>
                <if test="manufacturingLicenseNumber != null">
                    manufacturing_license_number,
                </if>
                <if test="applicationDate != null">
                    application_date,
                </if>
                <if test="factoryLicenseNumber != null">
                    factory_license_number,
                </if>
                <if test="purchaseTime != null">
                    purchase_time,
                </if>
                <if test="unitAddress != null">
                    unit_address,
                </if>
                <if test="legalRepresentative != null">
                    legal_representative,
                </if>
                <if test="legalRepresentativeContactNumber != null">
                    legal_representative_contact_number,
                </if>
                <if test="technicalDirector != null">
                    technical_director,
                </if>
                <if test="technicalDirectorContactNumber != null">
                    technical_director_contact_number,
                </if>
                <if test="equipmentManager != null">
                    equipment_manager,
                </if>
                <if test="equipmentManagerContactNumber != null">
                    equipment_manager_contact_number,
                </if>
                <if test="liftingWeight != null">
                    lifting_weight,
                </if>
                <if test="equipmentType != null">
                    equipment_type,
                </if>
                <if test="cancellationReason != null">
                    cancellation_reason,
                </if>
                <if test="cancellationApplicationFormPath != null">
                    cancellation_application_form_path,
                </if>
                <if test="filingCode != null">
                    filing_code,
                </if>
                <if test="filingPaper != null">
                    filing_paper,
                </if>
                <if test="filingCard != null">
                    filing_card,
                </if>
                <if test="auditStatus != null">
                    audit_status,
                </if>
                <if test="auditReject != null">
                    audit_reject,
                </if>
                <if test="auditTime != null">
                    audit_time,
                </if>
                <if test="createBy != null">
                    create_by,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="updateBy != null">
                    update_by,
                </if>
                <if test="updateTime != null">
                    update_time,
                </if>
                <if test="remarks != null">
                    remarks,
                </if>
                <if test="delFlag != null">
                    del_flag,
                </if>
            <if test="businessId !=null">
                business_id,
            </if>
            <if test="unitAreaCode !=null">
                unit_area_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="userId != null ">
                #{userId},
            </if>
            <if test="propertyUnit != null ">
                #{propertyUnit},
            </if>
            <if test="propertyUnitCode != null ">
                #{propertyUnitCode},
            </if>
            <if test="deviceName != null ">
                #{deviceName},
            </if>
            <if test="specificationModel != null ">
                #{specificationModel},
            </if>
            <if test="manufacturer != null ">
                #{manufacturer},
            </if>
            <if test="factoryTime != null ">
                #{factoryTime},
            </if>
            <if test="manufacturingLicenseNumber != null ">
                #{manufacturingLicenseNumber},
            </if>
            <if test="applicationDate != null ">
                #{applicationDate},
            </if>
            <if test="factoryLicenseNumber != null ">
                #{factoryLicenseNumber},
            </if>
            <if test="purchaseTime != null ">
                #{purchaseTime},
            </if>
            <if test="unitAddress != null ">
                #{unitAddress},
            </if>
            <if test="legalRepresentative != null ">
                #{legalRepresentative},
            </if>
            <if test="legalRepresentativeContactNumber != null ">
                #{legalRepresentativeContactNumber},
            </if>
            <if test="technicalDirector != null ">
                #{technicalDirector},
            </if>
            <if test="technicalDirectorContactNumber != null ">
                #{technicalDirectorContactNumber},
            </if>
            <if test="equipmentManager != null ">
                #{equipmentManager},
            </if>
            <if test="equipmentManagerContactNumber != null ">
                #{equipmentManagerContactNumber},
            </if>
            <if test="liftingWeight != null ">
                #{liftingWeight},
            </if>
            <if test="equipmentType != null">
                #{equipmentType},
            </if>
            <if test="cancellationReason != null ">
                #{cancellationReason},
            </if>
            <if test="cancellationApplicationFormPath != null ">
                #{cancellationApplicationFormPath},
            </if>
            <if test="filingCode != null ">
                #{filingCode},
            </if>
            <if test="filingPaper != null ">
                #{filingPaper},
            </if>
            <if test="filingCard != null ">
                #{filingCard},
            </if>
            <if test="auditStatus != null ">
                #{auditStatus},
            </if>
            <if test="auditReject != null ">
                #{auditReject},
            </if>
            <if test="auditTime != null ">
                #{auditTime},
            </if>
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if>
            <if test="businessId!= null">
                #{businessId},
            </if>
            <if test="unitAreaCode!= null">
                #{unitAreaCode},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.filing.equipmentfilingcancellation.model.FilingEquipmentFilingCancellation">
        update filing_equipment_filing_cancellation
        <set>
            <if test="userId != null">
                user_id=#{userId},
            </if>
            <if test="propertyUnit != null">
                property_unit=#{propertyUnit},
            </if>
            <if test="propertyUnitCode != null">
                property_unit_code=#{propertyUnitCode},
            </if>
            <if test="deviceName != null">
                device_name=#{deviceName},
            </if>
            <if test="specificationModel != null">
                specification_model=#{specificationModel},
            </if>
            <if test="manufacturer != null">
                manufacturer=#{manufacturer},
            </if>
            <if test="factoryTime != null">
                factory_time=#{factoryTime},
            </if>
            <if test="manufacturingLicenseNumber != null">
                manufacturing_license_number=#{manufacturingLicenseNumber},
            </if>
            <if test="applicationDate != null">
                application_date=#{applicationDate},
            </if>
            <if test="factoryLicenseNumber != null">
                factory_license_number=#{factoryLicenseNumber},
            </if>
            <if test="purchaseTime != null">
                purchase_time=#{purchaseTime},
            </if>
            <if test="unitAddress != null">
                unit_address=#{unitAddress},
            </if>
            <if test="legalRepresentative != null">
                legal_representative=#{legalRepresentative},
            </if>
            <if test="legalRepresentativeContactNumber != null">
                legal_representative_contact_number=#{legalRepresentativeContactNumber},
            </if>
            <if test="technicalDirector != null">
                technical_director=#{technicalDirector},
            </if>
            <if test="technicalDirectorContactNumber != null">
                technical_director_contact_number=#{technicalDirectorContactNumber},
            </if>
            <if test="equipmentManager != null">
                equipment_manager=#{equipmentManager},
            </if>
            <if test="equipmentManagerContactNumber != null">
                equipment_manager_contact_number=#{equipmentManagerContactNumber},
            </if>
            <if test="liftingWeight != null">
                lifting_weight=#{liftingWeight},
            </if>
            <if test="equipmentType != null">
                equipment_type =#{equipmentType},
            </if>
            <if test="cancellationReason != null">
                cancellation_reason=#{cancellationReason},
            </if>
            <if test="cancellationApplicationFormPath != null">
                cancellation_application_form_path=#{cancellationApplicationFormPath},
            </if>
            <if test="filingCode != null">
                filing_code=#{filingCode},
            </if>
            <if test="filingPaper != null">
                filing_paper=#{filingPaper},
            </if>
            <if test="filingCard != null">
                filing_card=#{filingCard},
            </if>
            <if test="auditStatus != null">
                audit_status=#{auditStatus},
            </if>
            <if test="auditReject != null">
                audit_reject=#{auditReject},
            </if>
            <if test="auditTime != null">
                audit_time=#{auditTime},
            </if>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
            <if test="businessId!= null">
                business_id =#{businessId},
            </if>
            <if test="unitAreaCode!= null">
                unit_area_code =#{unitAreaCode},
            </if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update filing_equipment_filing_cancellation set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update filing_equipment_filing_cancellation set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into filing_equipment_filing_cancellation(id,  user_id,  property_unit, property_unit_code, device_name,  specification_model,  manufacturer,  factory_time,  manufacturing_license_number,  application_date,  factory_license_number,  purchase_time,  unit_address,  legal_representative,  legal_representative_contact_number,  technical_director,  technical_director_contact_number,  equipment_manager,  equipment_manager_contact_number,  lifting_weight, equipment_type, cancellation_reason,  cancellation_application_form_path,  filing_code,  filing_paper,  filing_card,  audit_status,  audit_reject,audit_time,  create_by,  create_time,  update_by,  update_time,  remarks,  del_flag,business_id,unit_area_code) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.userId},  #{item.propertyUnit}, #{item.propertyUnitCode},  #{item.deviceName},  #{item.specificationModel},  #{item.manufacturer},  #{item.factoryTime},  #{item.manufacturingLicenseNumber},  #{item.applicationDate},  #{item.factoryLicenseNumber},  #{item.purchaseTime},  #{item.unitAddress},  #{item.legalRepresentative},  #{item.legalRepresentativeContactNumber},  #{item.technicalDirector},  #{item.technicalDirectorContactNumber},  #{item.equipmentManager},  #{item.equipmentManagerContactNumber},  #{item.liftingWeight}, #{item.equipmentType}, #{item.cancellationReason},  #{item.cancellationApplicationFormPath},  #{item.filingCode},  #{item.filingPaper},  #{item.filingCard},  #{item.auditStatus},  #{item.auditReject},#{item.#{item.auditReject},},  #{item.createBy},  #{item.createTime},  #{item.updateBy},  #{item.updateTime},  #{item.remarks},  #{item.delFlag}, #{item.businessId},#{item.unitAreaCode})
        </foreach>
    </insert>

    <select id="selectListByAreaCode" resultMap="BaseResultMap">
        select  c.*
        from filing_equipment_filing_cancellation c
        left JOIN filing_equipment_filing_application a on c.filing_code = a.filing_code
        where a.unit_area_code
        <foreach close=")" collection="areaList" item="areaCode" open="in (" separator=",">
            #{areaCode}
        </foreach>
        and c.audit_status = #{auditStatus}
        and c.del_flag=0
    </select>

    <select id="selectListByCondition" resultMap="BaseResultMap">
        select  c.* from filing_equipment_filing_cancellation c
        left JOIN filing_equipment_filing_application a on c.filing_code = a.filing_code
        <where>
            c.del_flag= 0 and c.audit_status = '1'
            and a.unit_area_code
            <foreach close=")" collection="areaList" item="areaCode" open="in (" separator=",">
                #{areaCode}
            </foreach>
            <if test="year != null and year != ''"> and date_format(c.audit_time, '%Y')=#{year}</if>
        </where>
    </select>
    <select id="getOneByCode" resultMap="BaseResultMap">
        select * from  filing_equipment_filing_cancellation where audit_status='1' and del_flag='0' and filing_code=#{filingCode}
    </select>
    <select id="selectListGroupByFilingCode" resultMap="BaseResultMap">
        select  c.* from filing_equipment_filing_cancellation c
        left JOIN filing_equipment_filing_application a on c.filing_code = a.filing_code
        <where>
            c.del_flag= 0 and c.audit_status = '1'
            and a.unit_area_code
            <foreach close=")" collection="areaList" item="areaCode" open="in (" separator=",">
                #{areaCode}
            </foreach>
            <if test="year != null and year != ''"> and date_format(c.audit_time, '%Y')=#{year}</if>
        </where>
        group by c.filing_code
    </select>
</mapper>
