<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.archetype.detail.mapper.SysNewsDetailsMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.archetype.detail.model.SysNewsDetails" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="cover_picture" property="coverPicture" jdbcType="VARCHAR" />
        <result column="content_id" property="contentId" jdbcType="VARCHAR" />
        <result column="details" property="details" jdbcType="LONGVARCHAR" />
        <result column="files" property="files" jdbcType="LONGVARCHAR" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        cover_picture,
        content_id,
        details,
        files,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from sys_news_details
        where content_id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.archetype.detail.model.SysNewsDetails">
        select
        <include refid="Base_Column_List" />
        from sys_news_details
        where
             id = #{id}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.archetype.detail.model.SysNewsDetails">
        select
        <include refid="Base_Column_List" />
        from sys_news_details
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="coverPicture != null ">
                and cover_picture=#{coverPicture}
            </if>
            <if test="contentId != null ">
                and content_id=#{contentId}
            </if>
            <if test="details != null ">
                and details=#{details}
            </if>
            <if test="files != null ">
                and files=#{files}
            </if>
         </where>
    </select>

    <insert id="insert" parameterType="com.tzstcl.archetype.detail.model.SysNewsDetails">
        insert into sys_news_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="coverPicture != null">
                    cover_picture,
                </if>
                <if test="contentId != null">
                    content_id,
                </if>
                <if test="details != null">
                    details,
                </if>
                <if test="files != null">
                    files,
                </if>
                <if test="createBy != null">
                    create_by,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="updateBy != null">
                    update_by,
                </if>
                <if test="updateTime != null">
                    update_time,
                </if>
                <if test="remarks != null">
                    remarks,
                </if>
                <if test="delFlag != null">
                    del_flag,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="coverPicture != null ">
                #{coverPicture},
            </if>
            <if test="contentId != null ">
                #{contentId},
            </if>
            <if test="details != null ">
                #{details},
            </if>
            <if test="files != null ">
                #{files},
            </if>
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.archetype.detail.model.SysNewsDetails">
        update sys_news_details
        <set>
            <if test="coverPicture != null">
                cover_picture=#{coverPicture},
            </if>
            <if test="contentId != null">
                content_id=#{contentId},
            </if>
            <if test="details != null">
                details=#{details},
            </if>
            <if test="files != null">
                files=#{files},
            </if>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update sys_news_details set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update sys_news_details set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into sys_news_details(id,  cover_picture,  content_id,  details,  files,  create_by,  create_time,  update_by,  update_time,  remarks,  del_flag) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.coverPicture},  #{item.contentId},  #{item.details},  #{item.files},  #{item.createBy},  #{item.createTime},  #{item.updateBy},  #{item.updateTime},  #{item.remarks},  #{item.delFlag})
        </foreach>
    </insert>

</mapper>