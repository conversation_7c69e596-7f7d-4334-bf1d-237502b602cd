<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tzstcl.archetype.cms.mapper.SysNewsMapper" >
    <resultMap id="BaseResultMap" type="com.tzstcl.archetype.cms.model.SysNews" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="title" property="title" jdbcType="VARCHAR" />
        <result column="author" property="author" jdbcType="VARCHAR" />
        <result column="category" property="category" jdbcType="VARCHAR" />
        <result column="description" property="description" jdbcType="VARCHAR" />
        <result column="state" property="state" jdbcType="VARCHAR" />
        <result column="browse_volume" property="browseVolume" jdbcType="INTEGER" />
        <result column="clicks" property="clicks" jdbcType="INTEGER" />
        <result column="collection" property="collection" jdbcType="INTEGER" />
        <result column="roof_placement" property="roofPlacement" jdbcType="VARCHAR" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        title,
        author,
        category,
        description,
        state,
        browse_volume,
        clicks,
        collection,
        roof_placement,
        create_by,
        create_time,
        update_by,
        update_time,
        remarks,
        del_flag
    </sql>

    <select id="getOne" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from sys_news
        where id = #{id}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="com.tzstcl.archetype.cms.model.SysNews">
        select
        <include refid="Base_Column_List" />
        from sys_news
        where
             id = #{id}
    </select>

    <!--增加浏览量-->
    <update id="addBrowseVolume" parameterType="com.tzstcl.archetype.cms.model.SysNews">
        update sys_news set
        browse_volume = browse_volume +1
        WHERE   id = #{id}
    </update>

    <!--首页新闻列表-->
    <select id="getNewsList" resultMap="BaseResultMap" parameterType="com.tzstcl.archetype.cms.model.SysNews">
        select
        <include refid="Base_Column_List" />
        from sys_news
        where  del_flag='0' and status ='1' and category = #{category}
        ORDER BY sort
        LIMIT 4
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="com.tzstcl.archetype.cms.model.SysNews">
        select
        <include refid="Base_Column_List" />
        from sys_news
        <where>
            del_flag='0'
            <if test="id!= null">
                and id = #{id}
            </if>
            <if test="title != null ">
                and title like concat('%',#{title},'%')
            </if>
            <if test="author != null ">
                and author=#{author}
            </if>
            <if test="category != null ">
                and category=#{category}
            </if>
            <if test="description != null ">
                and description=#{description}
            </if>
            <if test="state != null ">
                and state=#{state}
            </if>
            <if test="browseVolume != null ">
                and browse_volume=#{browseVolume}
            </if>
            <if test="clicks != null ">
                and clicks=#{clicks}
            </if>
            <if test="collection != null ">
                and collection=#{collection}
            </if>
            <if test="roofPlacement != null ">
                and roof_placement=#{roofPlacement}
            </if>
         </where>
    </select>

    <insert id="insert" parameterType="com.tzstcl.archetype.cms.model.SysNews">
        insert into sys_news
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="title != null">
                    title,
                </if>
                <if test="author != null">
                    author,
                </if>
                <if test="category != null">
                    category,
                </if>
                <if test="description != null">
                    description,
                </if>
                <if test="state != null">
                    state,
                </if>
                <if test="browseVolume != null">
                    browse_volume,
                </if>
                <if test="clicks != null">
                    clicks,
                </if>
                <if test="collection != null">
                    collection,
                </if>
                <if test="roofPlacement != null">
                    roof_placement,
                </if>
                <if test="createBy != null">
                    create_by,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="updateBy != null">
                    update_by,
                </if>
                <if test="updateTime != null">
                    update_time,
                </if>
                <if test="remarks != null">
                    remarks,
                </if>
                <if test="delFlag != null">
                    del_flag,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id},
            </if>
            <if test="title != null ">
                #{title},
            </if>
            <if test="author != null ">
                #{author},
            </if>
            <if test="category != null ">
                #{category},
            </if>
            <if test="description != null ">
                #{description},
            </if>
            <if test="state != null ">
                #{state},
            </if>
            <if test="browseVolume != null ">
                #{browseVolume},
            </if>
            <if test="clicks != null ">
                #{clicks},
            </if>
            <if test="collection != null ">
                #{collection},
            </if>
            <if test="roofPlacement != null ">
                #{roofPlacement},
            </if>
            <if test="createBy != null ">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null ">
                #{updateBy},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="remarks != null ">
                #{remarks},
            </if>
            <if test="delFlag != null ">
                #{delFlag},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.tzstcl.archetype.cms.model.SysNews">
        update sys_news
        <set>
            <if test="title != null">
                title=#{title},
            </if>
            <if test="author != null">
                author=#{author},
            </if>
            <if test="category != null">
                category=#{category},
            </if>
            <if test="description != null">
                description=#{description},
            </if>
            <if test="state != null">
                state=#{state},
            </if>
            <if test="browseVolume != null">
                browse_volume=#{browseVolume},
            </if>
            <if test="clicks != null">
                clicks=#{clicks},
            </if>
            <if test="collection != null">
                collection=#{collection},
            </if>
            <if test="roofPlacement != null">
                roof_placement=#{roofPlacement},
            </if>
            <if test="createBy != null">
                create_by=#{createBy},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateBy != null">
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="remarks != null">
                remarks=#{remarks},
            </if>
            <if test="delFlag != null">
                del_flag=#{delFlag},
            </if>
        </set>
        WHERE   id = #{id}
    </update>


    <update id="delete" parameterType="java.lang.Long">
        update sys_news set
        del_flag='1'
        WHERE   id = #{id}
    </update>
    <update id="deleteBatchByIDs" parameterType="java.util.List">
        update sys_news set
        del_flag='1'
        WHERE   id
        <foreach close=")" collection="list" item="id" open="in (" separator=",">
        #{id}
        </foreach>
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into sys_news(id,  title,  author,  category,  description,  state,  browse_volume,  clicks,  collection,  roof_placement,  create_by,  create_time,  update_by,  update_time,  remarks,  del_flag) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},  #{item.title},  #{item.author},  #{item.category},  #{item.description},  #{item.state},  #{item.browseVolume},  #{item.clicks},  #{item.collection},  #{item.roofPlacement},  #{item.createBy},  #{item.createTime},  #{item.updateBy},  #{item.updateTime},  #{item.remarks},  #{item.delFlag})
        </foreach>
    </insert>

</mapper>