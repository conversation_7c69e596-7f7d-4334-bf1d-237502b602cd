<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="cacheEnabled"             value="false" />  <!-- 全局映射器启用缓存 -->
        <setting name="useGeneratedKeys"         value="true" />  <!-- 允许 JDBC 支持自动生成主键 -->
<!--        <setting name="defaultExecutorType"      value="BATCH" /> &lt;!&ndash; 配置默认的执行器 &ndash;&gt;-->
        <setting name="logImpl"                  value="STDOUT_LOGGING" /> <!-- 指定 MyBatis 所用日志的具体实现 -->
        <!-- <setting name="mapUnderscoreToCamelCase" value="true"/>  驼峰式命名 -->
        <setting name="localCacheScope" value="STATEMENT"/>
    </settings>
    <!--<plugins>-->
        <!--<plugin interceptor="com.github.pagehelper.PageInterceptor">-->
            <!--&lt;!&ndash; 4.0.0以后版本可以不设置该参数 &ndash;&gt;-->
            <!--&lt;!&ndash;<property name="dialect" value="mysql"/>&ndash;&gt;-->
            <!--&lt;!&ndash; 该参数默认为false &ndash;&gt;-->
            <!--&lt;!&ndash; 设置为true时，会将RowBounds第一个参数offset当成pageNum页码使用 &ndash;&gt;-->
            <!--&lt;!&ndash; 和startPage中的pageNum效果一样&ndash;&gt;-->
            <!--<property name="offsetAsPageNum" value="true"/>-->
            <!--&lt;!&ndash; 该参数默认为false &ndash;&gt;-->
            <!--&lt;!&ndash; 设置为true时，使用RowBounds分页会进行count查询 &ndash;&gt;-->
            <!--<property name="rowBoundsWithCount" value="true"/>-->
            <!--&lt;!&ndash; 设置为true时，如果pageSize=0或者RowBounds.limit = 0就会查询出全部的结果 &ndash;&gt;-->
            <!--&lt;!&ndash; （相当于没有执行分页查询，但是返回结果仍然是Page类型）&ndash;&gt;-->
            <!--<property name="pageSizeZero" value="true"/>-->
            <!--&lt;!&ndash; 3.3.0版本可用 - 分页参数合理化，默认false禁用 &ndash;&gt;-->
            <!--&lt;!&ndash; 启用合理化时，如果pageNum<1会查询第一页，如果pageNum>pages会查询最后一页 &ndash;&gt;-->
            <!--&lt;!&ndash; 禁用合理化时，如果pageNum<1或pageNum>pages会返回空数据 &ndash;&gt;-->
            <!--<property name="reasonable" value="true"/>-->
            <!--&lt;!&ndash; 3.5.0版本可用 - 为了支持startPage(Map<String,Object> params)方法 &ndash;&gt;-->
            <!--&lt;!&ndash; 增加了一个`params`参数来配置参数映射，用于从Map或ServletRequest中取值 &ndash;&gt;-->
            <!--&lt;!&ndash; 可以配置pageNum,pageSize,count,pageSizeZero,reasonable,orderBy,不配置映射的用默认值 &ndash;&gt;-->
            <!--&lt;!&ndash; 不理解该含义的前提下，不要随便复制该配置 &ndash;&gt;-->
            <!--<property name="params" value="pageNum=pageHelperStart;pageSize=pageHelperRows;"/>-->
            <!--&lt;!&ndash; 支持通过Mapper接口参数来传递分页参数 &ndash;&gt;-->
            <!--<property name="supportMethodsArguments" value="false"/>-->
            <!--&lt;!&ndash; always总是返回PageInfo类型,check检查返回类型是否为PageInfo,none返回Page &ndash;&gt;-->
            <!--<property name="returnPageInfo" value="none"/>-->
        <!--</plugin>-->
    <!--</plugins>-->
</configuration>
